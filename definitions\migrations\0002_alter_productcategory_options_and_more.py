# Generated by Django 5.2.4 on 2025-07-13 03:13

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='productcategory',
            options={'ordering': ['sort_order', 'name'], 'verbose_name': 'فئة صنف', 'verbose_name_plural': 'فئات الأصناف'},
        ),
        migrations.AlterModelOptions(
            name='productdefinition',
            options={'ordering': ['code'], 'verbose_name': 'تعريف صنف', 'verbose_name_plural': 'تعريفات الأصناف'},
        ),
        migrations.AlterField(
            model_name='productcategory',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.productcategory', verbose_name='الفئة الأب'),
        ),
        migrations.AlterField(
            model_name='productdefinition',
            name='main_unit',
            field=models.CharField(choices=[('piece', 'قطعة'), ('kg', 'كيلوجرام'), ('gram', 'جرام'), ('liter', 'لتر'), ('meter', 'متر'), ('box', 'صندوق'), ('carton', 'كرتونة'), ('dozen', 'دستة')], max_length=20, verbose_name='الوحدة الأساسية'),
        ),
        migrations.AlterField(
            model_name='productdefinition',
            name='product_type',
            field=models.CharField(choices=[('product', 'منتج'), ('service', 'خدمة'), ('raw_material', 'مادة خام'), ('finished_good', 'منتج تام'), ('semi_finished', 'منتج نصف مصنع'), ('consumable', 'مستهلكات')], max_length=20, verbose_name='نوع الصنف'),
        ),
        migrations.AlterField(
            model_name='productdefinition',
            name='sub_unit',
            field=models.CharField(blank=True, choices=[('piece', 'قطعة'), ('kg', 'كيلوجرام'), ('gram', 'جرام'), ('liter', 'لتر'), ('meter', 'متر'), ('box', 'صندوق'), ('carton', 'كرتونة'), ('dozen', 'دستة')], max_length=20, verbose_name='الوحدة الفرعية'),
        ),
        migrations.CreateModel(
            name='AssetBrand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الماركة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الماركة')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('country_of_origin', models.CharField(blank=True, max_length=100, verbose_name='بلد المنشأ')),
                ('website', models.URLField(blank=True, verbose_name='الموقع الإلكتروني')),
                ('contact_info', models.TextField(blank=True, verbose_name='معلومات الاتصال')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'ماركة أصول',
                'verbose_name_plural': 'ماركات الأصول',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='AssetGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المجموعة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المجموعة')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('depreciation_method', models.CharField(choices=[('straight_line', 'القسط الثابت'), ('declining_balance', 'الرصيد المتناقص'), ('sum_of_years', 'مجموع سنوات الاستخدام')], default='straight_line', max_length=20, verbose_name='طريقة الإهلاك')),
                ('depreciation_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الإهلاك %')),
                ('useful_life_years', models.PositiveIntegerField(default=5, verbose_name='العمر الافتراضي (سنوات)')),
                ('salvage_value_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='قيمة الخردة %')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'مجموعة أصول',
                'verbose_name_plural': 'مجموعات الأصول',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='BankDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود البنك')),
                ('name', models.CharField(max_length=100, verbose_name='اسم البنك')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('swift_code', models.CharField(blank=True, max_length=20, verbose_name='كود SWIFT')),
                ('routing_number', models.CharField(blank=True, max_length=20, verbose_name='رقم التوجيه')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, verbose_name='الموقع الإلكتروني')),
                ('contact_person', models.CharField(blank=True, max_length=100, verbose_name='الشخص المسؤول')),
                ('contact_phone', models.CharField(blank=True, max_length=20, verbose_name='هاتف المسؤول')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تعريف بنك',
                'verbose_name_plural': 'تعريفات البنوك',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CurrencyDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=3, unique=True, verbose_name='كود العملة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العملة')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('symbol', models.CharField(max_length=10, verbose_name='رمز العملة')),
                ('exchange_rate', models.DecimalField(decimal_places=6, default=1, max_digits=15, verbose_name='سعر الصرف')),
                ('is_base_currency', models.BooleanField(default=False, verbose_name='العملة الأساسية')),
                ('decimal_places', models.PositiveIntegerField(default=2, validators=[django.core.validators.MaxValueValidator(6)], verbose_name='عدد الخانات العشرية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تعريف عملة',
                'verbose_name_plural': 'تعريفات العملات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CashBoxDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الخزينة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الخزينة')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الافتتاحي')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الحالي')),
                ('minimum_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأدنى للرصيد')),
                ('maximum_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأقصى للرصيد')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('allow_negative_balance', models.BooleanField(default=False, verbose_name='السماح بالرصيد السالب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('responsible_person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_cashboxes', to=settings.AUTH_USER_MODEL, verbose_name='المسؤول')),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.currencydefinition', verbose_name='العملة')),
            ],
            options={
                'verbose_name': 'تعريف خزينة',
                'verbose_name_plural': 'تعريفات الخزائن',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود النوع')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النوع')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_tax_deductible', models.BooleanField(default=True, verbose_name='قابل للخصم الضريبي')),
                ('requires_approval', models.BooleanField(default=False, verbose_name='يتطلب موافقة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'نوع مصروف',
                'verbose_name_plural': 'أنواع المصروفات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ExpenseName',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المصروف')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المصروف')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('default_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='المبلغ الافتراضي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('expense_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expense_names', to='definitions.expensetype', verbose_name='نوع المصروف')),
            ],
            options={
                'verbose_name': 'اسم مصروف',
                'verbose_name_plural': 'أسماء المصروفات',
                'ordering': ['expense_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='PersonDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الشخص')),
                ('name', models.CharField(max_length=200, verbose_name='الاسم')),
                ('name_en', models.CharField(blank=True, max_length=200, verbose_name='الاسم بالإنجليزية')),
                ('person_type', models.CharField(choices=[('customer', 'عميل'), ('supplier', 'مورد'), ('employee', 'موظف'), ('contractor', 'مقاول'), ('agent', 'وكيل'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الشخص')),
                ('national_id', models.CharField(blank=True, max_length=20, verbose_name='رقم الهوية')),
                ('passport_number', models.CharField(blank=True, max_length=20, verbose_name='رقم الجواز')),
                ('tax_number', models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي')),
                ('commercial_register', models.CharField(blank=True, max_length=50, verbose_name='السجل التجاري')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='الهاتف')),
                ('mobile', models.CharField(blank=True, max_length=20, verbose_name='الجوال')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, verbose_name='الموقع الإلكتروني')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='المدينة')),
                ('country', models.CharField(default='السعودية', max_length=100, verbose_name='الدولة')),
                ('postal_code', models.CharField(blank=True, max_length=20, verbose_name='الرمز البريدي')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الائتماني')),
                ('payment_terms', models.PositiveIntegerField(default=0, verbose_name='شروط الدفع (أيام)')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم %')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تعريف شخص',
                'verbose_name_plural': 'تعريفات الأشخاص',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='PrinterDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الطابعة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الطابعة')),
                ('printer_type', models.CharField(choices=[('thermal', 'طابعة حرارية'), ('inkjet', 'طابعة نفث حبر'), ('laser', 'طابعة ليزر'), ('dot_matrix', 'طابعة نقطية'), ('label', 'طابعة ملصقات')], max_length=20, verbose_name='نوع الطابعة')),
                ('connection_type', models.CharField(choices=[('usb', 'USB'), ('network', 'شبكة'), ('bluetooth', 'بلوتوث'), ('serial', 'منفذ تسلسلي'), ('parallel', 'منفذ متوازي')], max_length=20, verbose_name='نوع الاتصال')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('port', models.PositiveIntegerField(blank=True, null=True, verbose_name='المنفذ')),
                ('device_path', models.CharField(blank=True, max_length=200, verbose_name='مسار الجهاز')),
                ('paper_width', models.PositiveIntegerField(default=80, verbose_name='عرض الورق (مم)')),
                ('paper_height', models.PositiveIntegerField(default=297, verbose_name='طول الورق (مم)')),
                ('dpi', models.PositiveIntegerField(default=203, verbose_name='دقة الطباعة (DPI)')),
                ('auto_cut', models.BooleanField(default=True, verbose_name='القطع التلقائي')),
                ('cash_drawer', models.BooleanField(default=False, verbose_name='درج النقدية')),
                ('encoding', models.CharField(default='utf-8', max_length=20, verbose_name='ترميز النص')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('department', models.CharField(blank=True, max_length=100, verbose_name='القسم')),
                ('default_for_receipts', models.BooleanField(default=False, verbose_name='افتراضية للإيصالات')),
                ('default_for_reports', models.BooleanField(default=False, verbose_name='افتراضية للتقارير')),
                ('default_for_labels', models.BooleanField(default=False, verbose_name='افتراضية للملصقات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تعريف طابعة',
                'verbose_name_plural': 'تعريفات الطابعات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProfitCenter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المركز')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المركز')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('manager_name', models.CharField(blank=True, max_length=100, verbose_name='اسم المدير')),
                ('target_revenue', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الهدف من الإيرادات')),
                ('target_profit', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الهدف من الربح')),
                ('target_profit_margin', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='هامش الربح المستهدف %')),
                ('fiscal_year_start', models.DateField(blank=True, null=True, verbose_name='بداية السنة المالية')),
                ('fiscal_year_end', models.DateField(blank=True, null=True, verbose_name='نهاية السنة المالية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'مركز ربحية',
                'verbose_name_plural': 'مراكز الربحية',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='RevenueType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود النوع')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النوع')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_taxable', models.BooleanField(default=True, verbose_name='خاضع للضريبة')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=15, max_digits=5, verbose_name='معدل الضريبة %')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'نوع إيراد',
                'verbose_name_plural': 'أنواع الإيرادات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='RevenueName',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الإيراد')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الإيراد')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('default_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='المبلغ الافتراضي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('revenue_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='revenue_names', to='definitions.revenuetype', verbose_name='نوع الإيراد')),
            ],
            options={
                'verbose_name': 'اسم إيراد',
                'verbose_name_plural': 'أسماء الإيرادات',
                'ordering': ['revenue_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='WarehouseDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المخزن')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المخزن')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('warehouse_type', models.CharField(choices=[('main', 'مخزن رئيسي'), ('branch', 'مخزن فرعي'), ('raw_materials', 'مخزن مواد خام'), ('finished_goods', 'مخزن منتجات تامة'), ('damaged', 'مخزن تالف'), ('quarantine', 'مخزن حجر صحي')], max_length=20, verbose_name='نوع المخزن')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='الهاتف')),
                ('manager_name', models.CharField(blank=True, max_length=100, verbose_name='اسم المدير')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('allow_negative_stock', models.BooleanField(default=False, verbose_name='السماح بالمخزون السالب')),
                ('auto_reorder', models.BooleanField(default=False, verbose_name='إعادة الطلب التلقائي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تعريف مخزن',
                'verbose_name_plural': 'تعريفات المخازن',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='ProductCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code_type', models.CharField(choices=[('supplier', 'كود المورد'), ('manufacturer', 'كود الشركة المصنعة'), ('customer', 'كود العميل'), ('internal', 'كود داخلي'), ('old_system', 'كود النظام القديم')], max_length=20, verbose_name='نوع الكود')),
                ('code', models.CharField(max_length=100, verbose_name='الكود')),
                ('description', models.CharField(blank=True, max_length=200, verbose_name='الوصف')),
                ('supplier_name', models.CharField(blank=True, max_length=100, verbose_name='اسم المورد')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alternative_codes', to='definitions.productdefinition', verbose_name='الصنف')),
            ],
            options={
                'verbose_name': 'كود صنف بديل',
                'verbose_name_plural': 'أكواد الأصناف البديلة',
                'unique_together': {('code_type', 'code')},
            },
        ),
        migrations.CreateModel(
            name='ProductLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('zone', models.CharField(blank=True, max_length=50, verbose_name='المنطقة')),
                ('aisle', models.CharField(blank=True, max_length=20, verbose_name='الممر')),
                ('rack', models.CharField(blank=True, max_length=20, verbose_name='الرف')),
                ('shelf', models.CharField(blank=True, max_length=20, verbose_name='الطابق')),
                ('bin', models.CharField(blank=True, max_length=20, verbose_name='الصندوق')),
                ('location_code', models.CharField(max_length=100, verbose_name='كود الموقع')),
                ('minimum_stock', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأدنى')),
                ('maximum_stock', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأقصى')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_default', models.BooleanField(default=False, verbose_name='الموقع الافتراضي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='الصنف')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'موقع صنف',
                'verbose_name_plural': 'مواقع الأصناف',
                'unique_together': {('warehouse', 'product', 'location_code')},
            },
        ),
        migrations.CreateModel(
            name='WarehouseLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, verbose_name='كود المكان')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المكان')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('max_capacity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='السعة القصوى')),
                ('max_weight', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الوزن الأقصى')),
                ('max_items', models.PositiveIntegerField(blank=True, null=True, verbose_name='عدد الأصناف الأقصى')),
                ('current_capacity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='السعة الحالية')),
                ('current_weight', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الوزن الحالي')),
                ('current_items', models.PositiveIntegerField(default=0, verbose_name='عدد الأصناف الحالي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='definitions.warehousedefinition', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'مكان في المخزن',
                'verbose_name_plural': 'أماكن المخازن',
                'ordering': ['warehouse', 'code'],
                'unique_together': {('warehouse', 'code')},
            },
        ),
    ]
