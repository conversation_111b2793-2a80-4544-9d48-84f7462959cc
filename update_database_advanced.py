import sqlite3
import os

# الاتصال بقاعدة البيانات
db_path = os.path.join(os.getcwd(), 'db.sqlite3')

if not os.path.exists(db_path):
    print("❌ ملف قاعدة البيانات غير موجود!")
    exit(1)

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print("🔄 تحديث قاعدة البيانات للإعدادات المتقدمة...")

try:
    # قائمة الحقول الجديدة المطلوب إضافتها
    new_fields = [
        # إعدادات التنسيق
        ('country', 'VARCHAR(50)', 'Egypt'),
        ('date_format', 'VARCHAR(20)', 'd/m/Y'),
        ('time_format', 'VARCHAR(10)', '24'),
        ('decimal_places', 'INTEGER', '2'),
        ('thousand_separator', 'VARCHAR(5)', ','),
        ('decimal_separator', 'VARCHAR(5)', '.'),
        
        # إعدادات الأمان المتقدمة
        ('lockout_duration', 'INTEGER', '15'),
        ('require_2fa', 'BOOLEAN', '0'),
        ('password_expiry_days', 'INTEGER', '90'),
        ('auto_logout_inactive', 'BOOLEAN', '1'),
        
        # إعدادات البريد المتقدمة
        ('email_use_ssl', 'BOOLEAN', '0'),
        ('email_host_user', 'VARCHAR(100)', ''),
        ('email_host_password', 'VARCHAR(100)', ''),
        ('email_from_name', 'VARCHAR(100)', 'نظام أوساريك'),
        ('email_signature', 'TEXT', ''),
        
        # إعدادات النسخ الاحتياطي المتقدمة
        ('backup_retention_days', 'INTEGER', '30'),
        ('backup_location', 'VARCHAR(200)', 'local'),
        ('backup_compress', 'BOOLEAN', '1'),
        ('backup_encrypt', 'BOOLEAN', '0'),
        
        # إعدادات الواجهة المتقدمة
        ('secondary_color', 'VARCHAR(7)', '#764ba2'),
        ('theme_mode', 'VARCHAR(10)', 'light'),
        ('show_breadcrumbs', 'BOOLEAN', '1'),
        ('enable_animations', 'BOOLEAN', '1'),
        ('compact_mode', 'BOOLEAN', '0'),
        ('custom_css', 'TEXT', ''),
        ('custom_js', 'TEXT', ''),
        
        # إعدادات الإشعارات المتقدمة
        ('sms_notifications', 'BOOLEAN', '0'),
        ('push_notifications', 'BOOLEAN', '1'),
        ('notification_sound', 'BOOLEAN', '1'),
        ('notification_frequency', 'VARCHAR(20)', 'instant'),
        
        # إعدادات التقارير
        ('default_report_format', 'VARCHAR(10)', 'pdf'),
        ('auto_generate_reports', 'BOOLEAN', '0'),
        ('report_retention_days', 'INTEGER', '90'),
        
        # إعدادات الأداء
        ('enable_caching', 'BOOLEAN', '1'),
        ('cache_timeout', 'INTEGER', '300'),
        ('enable_compression', 'BOOLEAN', '1'),
        ('max_file_upload_size', 'INTEGER', '10'),
    ]
    
    # فحص الحقول الموجودة
    cursor.execute("PRAGMA table_info(system_settings_systemsettings)")
    existing_columns = [column[1] for column in cursor.fetchall()]
    
    # إضافة الحقول الجديدة
    added_fields = 0
    for field_name, field_type, default_value in new_fields:
        if field_name not in existing_columns:
            try:
                # إضافة الحقل
                cursor.execute(f"""
                    ALTER TABLE system_settings_systemsettings 
                    ADD COLUMN {field_name} {field_type} DEFAULT {default_value if field_type != 'TEXT' else "''"}
                """)
                print(f"✅ تم إضافة الحقل: {field_name}")
                added_fields += 1
            except Exception as e:
                print(f"⚠️ خطأ في إضافة الحقل {field_name}: {e}")
        else:
            print(f"⏭️ الحقل {field_name} موجود بالفعل")
    
    # تحديث القيم الافتراضية للإعدادات الحالية
    cursor.execute("SELECT COUNT(*) FROM system_settings_systemsettings")
    count = cursor.fetchone()[0]
    
    if count > 0:
        print("🔄 تحديث القيم الافتراضية...")
        
        # تحديث الإعدادات الأساسية للمصر
        cursor.execute("""
            UPDATE system_settings_systemsettings 
            SET 
                country = 'Egypt',
                currency_code = 'EGP',
                currency_symbol = 'ج.م',
                system_timezone = 'Africa/Cairo',
                company_name = COALESCE(NULLIF(company_name, ''), 'شركة أوساريك'),
                email_from_name = COALESCE(NULLIF(email_from_name, ''), 'نظام أوساريك')
            WHERE id = 1
        """)
        
        print("✅ تم تحديث الإعدادات الأساسية")
    else:
        # إدراج سجل جديد بالقيم الافتراضية
        cursor.execute("""
            INSERT INTO system_settings_systemsettings 
            (id, company_name, country, currency_code, currency_symbol, system_timezone, created_at, updated_at) 
            VALUES (1, 'شركة أوساريك', 'Egypt', 'EGP', 'ج.م', 'Africa/Cairo', datetime('now'), datetime('now'))
        """)
        print("✅ تم إنشاء سجل إعدادات جديد")
    
    conn.commit()
    
    print(f"\n🎉 تم تحديث قاعدة البيانات بنجاح!")
    print(f"📊 تم إضافة {added_fields} حقل جديد")
    print(f"✅ إجمالي الحقول: {len(existing_columns) + added_fields}")
    
    # عرض الإعدادات الحالية
    cursor.execute("SELECT company_name, country, currency_code, currency_symbol, system_timezone FROM system_settings_systemsettings WHERE id = 1")
    settings = cursor.fetchone()
    
    if settings:
        print(f"\n📋 الإعدادات الحالية:")
        print(f"🏢 اسم الشركة: {settings[0]}")
        print(f"🌍 الدولة: {settings[1]}")
        print(f"💰 العملة: {settings[2]} ({settings[3]})")
        print(f"🕐 المنطقة الزمنية: {settings[4]}")
    
except Exception as e:
    print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
    conn.rollback()
    
finally:
    conn.close()

print("\n📋 يمكنك الآن الوصول إلى:")
print("🔗 http://127.0.0.1:8000/settings/system/")
print("🔗 جميع الإعدادات المتقدمة متاحة الآن!")
