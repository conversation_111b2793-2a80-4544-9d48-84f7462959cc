from django.urls import path
from . import views

app_name = 'system_settings'

urlpatterns = [
    # لوحة التحكم
    path('', views.settings_dashboard, name='dashboard'),
    
    # إعدادات النظام
    path('system/', views.system_settings_view, name='system_settings'),
    
    # إدارة المستخدمين
    path('users/', views.users_management, name='users_management'),
    path('users/create/', views.user_create, name='user_create'),

    # إدارة الصلاحيات
    path('permissions/', views.permissions_management, name='permissions_management'),
    path('users/<int:user_id>/', views.user_detail, name='user_detail'),
    path('users/<int:user_id>/edit/', views.user_edit, name='user_edit'),
    path('users/<int:user_id>/delete/', views.user_delete, name='user_delete'),

    # الإشعارات
    path('notifications/', views.notifications_view, name='notifications'),
    path('notifications/ajax/', views.get_notifications_ajax, name='notifications_ajax'),
    path('notifications/<int:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),
    path('notifications/mark-all-read/', views.mark_all_notifications_read, name='mark_all_notifications_read'),
    path('notifications/<int:notification_id>/delete/', views.delete_notification, name='delete_notification'),
]
