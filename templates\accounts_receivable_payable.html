{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة المديونيات - الحسابات المدينة والدائنة{% endblock %}

{% block extra_css %}
<style>
    .debt-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .debt-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .debt-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
    }

    .debt-card.receivable::before {
        background: linear-gradient(90deg, #27ae60, #2ecc71);
    }

    .debt-card.payable::before {
        background: linear-gradient(90deg, #e74c3c, #c0392b);
    }

    .debt-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-left: 5px solid;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    }

    .stat-card.total-receivable { border-left-color: #27ae60; }
    .stat-card.total-payable { border-left-color: #e74c3c; }
    .stat-card.overdue-receivable { border-left-color: #f39c12; }
    .stat-card.overdue-payable { border-left-color: #8e44ad; }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .stat-label {
        color: #7f8c8d;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .client-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border-left: 4px solid;
    }

    .client-card.customer { border-left-color: #27ae60; }
    .client-card.supplier { border-left-color: #e74c3c; }

    .client-card:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    }

    .client-name {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .debt-amount {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .debt-amount.positive { color: #27ae60; }
    .debt-amount.negative { color: #e74c3c; }

    .debt-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #ecf0f1;
    }

    .debt-status {
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-current { background: #d4edda; color: #155724; }
    .status-overdue { background: #f8d7da; color: #721c24; animation: pulse 2s infinite; }
    .status-critical { background: #f5c6cb; color: #721c24; animation: glow 2s infinite; }

    @keyframes glow {
        0% { box-shadow: 0 0 5px rgba(231, 76, 60, 0.3); }
        50% { box-shadow: 0 0 20px rgba(231, 76, 60, 0.6); }
        100% { box-shadow: 0 0 5px rgba(231, 76, 60, 0.3); }
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn-action {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
        transition: all 0.3s ease;
        text-decoration: none;
        border: none;
        cursor: pointer;
    }

    .btn-view {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
    }

    .btn-pay {
        background: linear-gradient(135deg, #27ae60, #229954);
        color: white;
    }

    .btn-collect {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        color: white;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        color: white;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .tabs-container {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 25px;
    }

    .nav-tabs {
        border-bottom: none;
        background: #f8f9fa;
    }

    .nav-tabs .nav-link {
        border: none;
        color: #6c757d;
        font-weight: 600;
        padding: 15px 25px;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link.active {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 0;
    }

    .tab-content {
        padding: 25px;
    }

    .alert-debt {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        border: 2px solid #ffc107;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        animation: glow-warning 3s infinite;
    }

    @keyframes glow-warning {
        0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.3); }
        50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.6); }
        100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.3); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- رأس الصفحة -->
    <div class="debt-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="bi bi-calculator"></i>
                    إدارة المديونيات
                </h1>
                <p class="mb-0">الحسابات المدينة والدائنة مع التنبيهات الذكية</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <h3>{{ total_clients }}</h3>
                    <small>إجمالي العملاء والموردين</small>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card total-receivable">
            <div class="stat-number text-success">{{ total_receivable|floatformat:0 }}</div>
            <div class="stat-label">إجمالي المدين (ج.م)</div>
            <small class="text-muted">مستحق لنا من العملاء</small>
        </div>
        <div class="stat-card total-payable">
            <div class="stat-number text-danger">{{ total_payable|floatformat:0 }}</div>
            <div class="stat-label">إجمالي الدائن (ج.م)</div>
            <small class="text-muted">مستحق علينا للموردين</small>
        </div>
        <div class="stat-card overdue-receivable">
            <div class="stat-number text-warning">{{ overdue_receivable|floatformat:0 }}</div>
            <div class="stat-label">متأخرات العملاء (ج.م)</div>
            <small class="text-muted">مبالغ متأخرة الاستحقاق</small>
        </div>
        <div class="stat-card overdue-payable">
            <div class="stat-number text-info">{{ overdue_payable|floatformat:0 }}</div>
            <div class="stat-label">متأخرات الموردين (ج.م)</div>
            <small class="text-muted">مبالغ متأخرة علينا</small>
        </div>
    </div>

    <!-- تنبيهات المديونيات -->
    {% if critical_debts %}
    <div class="alert-debt">
        <h5><i class="bi bi-exclamation-triangle"></i> تنبيه: مديونيات حرجة!</h5>
        <p class="mb-2">يوجد {{ critical_debts|length }} حساب يتطلب متابعة عاجلة</p>
        <small>إجمالي المبالغ الحرجة: {{ critical_amount|floatformat:2 }} ج.م</small>
    </div>
    {% endif %}

    <!-- فلاتر البحث -->
    <div class="filter-section">
        <form method="get" class="row align-items-end">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" value="{{ request.GET.search }}" 
                       placeholder="اسم العميل أو المورد">
            </div>
            <div class="col-md-2">
                <label class="form-label">النوع</label>
                <select name="type" class="form-select">
                    <option value="">الكل</option>
                    <option value="customer" {% if request.GET.type == 'customer' %}selected{% endif %}>عملاء</option>
                    <option value="supplier" {% if request.GET.type == 'supplier' %}selected{% endif %}>موردين</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="current" {% if request.GET.status == 'current' %}selected{% endif %}>جارية</option>
                    <option value="overdue" {% if request.GET.status == 'overdue' %}selected{% endif %}>متأخرة</option>
                    <option value="critical" {% if request.GET.status == 'critical' %}selected{% endif %}>حرجة</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الترتيب</label>
                <select name="sort" class="form-select">
                    <option value="name">الاسم</option>
                    <option value="amount" {% if request.GET.sort == 'amount' %}selected{% endif %}>المبلغ</option>
                    <option value="overdue" {% if request.GET.sort == 'overdue' %}selected{% endif %}>التأخير</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search"></i> بحث
                </button>
                <a href="{% url 'accounts_receivable_payable' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                </a>
            </div>
        </form>
    </div>

    <!-- التبويبات -->
    <div class="tabs-container">
        <ul class="nav nav-tabs" id="debtTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="receivable-tab" data-bs-toggle="tab" data-bs-target="#receivable" type="button" role="tab">
                    <i class="bi bi-arrow-down-circle"></i>
                    الحسابات المدينة ({{ customers_count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="payable-tab" data-bs-toggle="tab" data-bs-target="#payable" type="button" role="tab">
                    <i class="bi bi-arrow-up-circle"></i>
                    الحسابات الدائنة ({{ suppliers_count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary" type="button" role="tab">
                    <i class="bi bi-graph-up"></i>
                    ملخص شامل
                </button>
            </li>
        </ul>
        
        <div class="tab-content" id="debtTabsContent">
            <!-- الحسابات المدينة -->
            <div class="tab-pane fade show active" id="receivable" role="tabpanel">
                <h5 class="mb-4">
                    <i class="bi bi-people"></i>
                    العملاء المدينون
                </h5>
                
                {% for customer_data in customers_data %}
                <div class="client-card customer">
                    <div class="client-name">
                        <i class="bi bi-person"></i>
                        {{ customer_data.customer.name }}
                    </div>
                    
                    <div class="debt-amount positive">
                        {{ customer_data.total_debt|floatformat:2 }} ج.م
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">آخر فاتورة: {{ customer_data.last_invoice_date|default:"لا توجد" }}</small>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">حد الائتمان: {{ customer_data.customer.credit_limit|floatformat:2 }} ج.م</small>
                        </div>
                    </div>
                    
                    <div class="debt-details">
                        <div>
                            {% if customer_data.days_overdue > 30 %}
                                <span class="debt-status status-critical">حرج ({{ customer_data.days_overdue }} يوم)</span>
                            {% elif customer_data.days_overdue > 0 %}
                                <span class="debt-status status-overdue">متأخر ({{ customer_data.days_overdue }} يوم)</span>
                            {% else %}
                                <span class="debt-status status-current">جاري</span>
                            {% endif %}
                        </div>
                        
                        <div class="action-buttons">
                            <a href="{% url 'sales:customer_detail' customer_data.customer.pk %}" class="btn-action btn-view">
                                <i class="bi bi-eye"></i> عرض
                            </a>
                            <a href="{% url 'sales:customer_statement' customer_data.customer.pk %}" class="btn-action btn-collect">
                                <i class="bi bi-file-text"></i> كشف حساب
                            </a>
                            {% if customer_data.total_debt > 0 %}
                            <button class="btn-action btn-collect" onclick="collectPayment({{ customer_data.customer.pk }}, '{{ customer_data.customer.name }}', {{ customer_data.total_debt }})">
                                <i class="bi bi-cash"></i> تحصيل
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-muted py-5">
                    <i class="bi bi-inbox display-4"></i>
                    <p class="mt-3">لا توجد حسابات مدينة</p>
                </div>
                {% endfor %}
            </div>

            <!-- الحسابات الدائنة -->
            <div class="tab-pane fade" id="payable" role="tabpanel">
                <h5 class="mb-4">
                    <i class="bi bi-building"></i>
                    الموردون الدائنون
                </h5>
                
                {% for supplier_data in suppliers_data %}
                <div class="client-card supplier">
                    <div class="client-name">
                        <i class="bi bi-building"></i>
                        {{ supplier_data.supplier.name }}
                    </div>
                    
                    <div class="debt-amount negative">
                        {{ supplier_data.total_debt|floatformat:2 }} ج.م
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">آخر فاتورة: {{ supplier_data.last_invoice_date|default:"لا توجد" }}</small>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">شروط الدفع: {{ supplier_data.supplier.payment_terms|default:"30 يوم" }}</small>
                        </div>
                    </div>
                    
                    <div class="debt-details">
                        <div>
                            {% if supplier_data.days_overdue > 30 %}
                                <span class="debt-status status-critical">حرج ({{ supplier_data.days_overdue }} يوم)</span>
                            {% elif supplier_data.days_overdue > 0 %}
                                <span class="debt-status status-overdue">متأخر ({{ supplier_data.days_overdue }} يوم)</span>
                            {% else %}
                                <span class="debt-status status-current">جاري</span>
                            {% endif %}
                        </div>
                        
                        <div class="action-buttons">
                            <a href="{% url 'purchases:supplier_detail' supplier_data.supplier.pk %}" class="btn-action btn-view">
                                <i class="bi bi-eye"></i> عرض
                            </a>
                            <a href="{% url 'purchases:supplier_statement' supplier_data.supplier.pk %}" class="btn-action btn-pay">
                                <i class="bi bi-file-text"></i> كشف حساب
                            </a>
                            {% if supplier_data.total_debt > 0 %}
                            <button class="btn-action btn-pay" onclick="makePayment({{ supplier_data.supplier.pk }}, '{{ supplier_data.supplier.name }}', {{ supplier_data.total_debt }})">
                                <i class="bi bi-credit-card"></i> دفع
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-muted py-5">
                    <i class="bi bi-inbox display-4"></i>
                    <p class="mt-3">لا توجد حسابات دائنة</p>
                </div>
                {% endfor %}
            </div>

            <!-- ملخص شامل -->
            <div class="tab-pane fade" id="summary" role="tabpanel">
                <h5 class="mb-4">
                    <i class="bi bi-graph-up"></i>
                    الملخص المالي الشامل
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="debt-card receivable">
                            <h6>الحسابات المدينة</h6>
                            <div class="debt-amount positive">{{ total_receivable|floatformat:2 }} ج.م</div>
                            <p class="text-muted mb-2">مستحق لنا من {{ customers_count }} عميل</p>
                            <small>متأخرات: {{ overdue_receivable|floatformat:2 }} ج.م</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="debt-card payable">
                            <h6>الحسابات الدائنة</h6>
                            <div class="debt-amount negative">{{ total_payable|floatformat:2 }} ج.م</div>
                            <p class="text-muted mb-2">مستحق علينا لـ {{ suppliers_count }} مورد</p>
                            <small>متأخرات: {{ overdue_payable|floatformat:2 }} ج.م</small>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="debt-card">
                            <h6>صافي المركز المالي</h6>
                            {% with net_position=total_receivable|add:total_payable|floatformat:2 %}
                            <div class="debt-amount {% if net_position > 0 %}positive{% else %}negative{% endif %}">
                                {{ net_position }} ج.م
                            </div>
                            {% endwith %}
                            <p class="text-muted">
                                {% if net_position > 0 %}
                                    المركز المالي إيجابي - لدينا مستحقات أكثر من المديونيات
                                {% else %}
                                    المركز المالي سلبي - المديونيات أكثر من المستحقات
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للتحصيل والدفع -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentModalTitle">إدارة المدفوعات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="paymentModalBody">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function collectPayment(customerId, customerName, amount) {
    document.getElementById('paymentModalTitle').textContent = 'تحصيل من العميل';
    document.getElementById('paymentModalBody').innerHTML = `
        <form method="post" action="/sales/customers/${customerId}/collect/">
            {% csrf_token %}
            <div class="mb-3">
                <label class="form-label">العميل</label>
                <input type="text" class="form-control" value="${customerName}" readonly>
            </div>
            <div class="mb-3">
                <label class="form-label">المبلغ المستحق</label>
                <input type="text" class="form-control" value="${amount} ج.م" readonly>
            </div>
            <div class="mb-3">
                <label class="form-label">المبلغ المحصل <span class="text-danger">*</span></label>
                <input type="number" name="amount" class="form-control" step="0.01" max="${amount}" required>
            </div>
            <div class="mb-3">
                <label class="form-label">طريقة التحصيل</label>
                <select name="payment_method" class="form-select" required>
                    <option value="cash">نقدي</option>
                    <option value="bank_transfer">تحويل بنكي</option>
                    <option value="check">شيك</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">ملاحظات</label>
                <textarea name="notes" class="form-control" rows="3"></textarea>
            </div>
            <div class="text-end">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" class="btn btn-success">تحصيل</button>
            </div>
        </form>
    `;
    new bootstrap.Modal(document.getElementById('paymentModal')).show();
}

function makePayment(supplierId, supplierName, amount) {
    document.getElementById('paymentModalTitle').textContent = 'دفع للمورد';
    document.getElementById('paymentModalBody').innerHTML = `
        <form method="post" action="/purchases/suppliers/${supplierId}/pay/">
            {% csrf_token %}
            <div class="mb-3">
                <label class="form-label">المورد</label>
                <input type="text" class="form-control" value="${supplierName}" readonly>
            </div>
            <div class="mb-3">
                <label class="form-label">المبلغ المستحق</label>
                <input type="text" class="form-control" value="${amount} ج.م" readonly>
            </div>
            <div class="mb-3">
                <label class="form-label">المبلغ المدفوع <span class="text-danger">*</span></label>
                <input type="number" name="amount" class="form-control" step="0.01" max="${amount}" required>
            </div>
            <div class="mb-3">
                <label class="form-label">طريقة الدفع</label>
                <select name="payment_method" class="form-select" required>
                    <option value="cash">نقدي</option>
                    <option value="bank_transfer">تحويل بنكي</option>
                    <option value="check">شيك</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">ملاحظات</label>
                <textarea name="notes" class="form-control" rows="3"></textarea>
            </div>
            <div class="text-end">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="submit" class="btn btn-primary">دفع</button>
            </div>
        </form>
    `;
    new bootstrap.Modal(document.getElementById('paymentModal')).show();
}
</script>
{% endblock %}
