{% extends 'base.html' %}

{% block title %}إدارة البنوك - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'banks:dashboard' %}">البنوك</a></li>
                    <li class="breadcrumb-item active">قائمة البنوك</li>
                </ol>
            </nav>
            <h1 class="page-title">إدارة البنوك</h1>
            <p class="page-subtitle">عرض وإدارة جميع البنوك والمؤسسات المالية</p>
        </div>
        <a href="{% url 'banks:bank_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>إضافة بنك جديد
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}" 
                               placeholder="البحث في أسماء البنوك أو الرموز...">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search me-1"></i>بحث
                        </button>
                        <a href="{% url 'banks:bank_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Banks Grid -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">قائمة البنوك</h5>
            <span class="badge bg-primary">{{ page_obj.paginator.count }} بنك</span>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="row g-4">
                    {% for bank in page_obj %}
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 bank-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="bank-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center">
                                            <i class="bi bi-bank"></i>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{% url 'banks:bank_edit' bank.pk %}">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><a class="dropdown-item" href="#">
                                                    <i class="bi bi-credit-card me-2"></i>الحسابات
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="{% url 'banks:bank_delete' bank.pk %}">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <h5 class="card-title">{{ bank.name }}</h5>
                                    <p class="text-muted small mb-2">الرمز: {{ bank.code }}</p>
                                    
                                    {% if bank.swift_code %}
                                        <p class="text-muted small mb-2">SWIFT: {{ bank.swift_code }}</p>
                                    {% endif %}
                                    
                                    <div class="mb-3">
                                        {% if bank.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-6">
                                            <div class="border-end">
                                                <h6 class="mb-0">0</h6>
                                                <small class="text-muted">الحسابات</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="mb-0">0 ج.م</h6>
                                            <small class="text-muted">إجمالي الرصيد</small>
                                        </div>
                                    </div>
                                    
                                    {% if bank.phone or bank.email %}
                                        <div class="mb-3">
                                            {% if bank.phone %}
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                                    <small>{{ bank.phone }}</small>
                                                </div>
                                            {% endif %}
                                            {% if bank.email %}
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-envelope me-2 text-muted"></i>
                                                    <small>{{ bank.email }}</small>
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                    
                                    <div class="d-flex gap-2">
                                        <a href="{% url 'banks:bank_edit' bank.pk %}" class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="bi bi-pencil me-1"></i>تعديل
                                        </a>
                                        <a href="#" class="btn btn-outline-success btn-sm">
                                            <i class="bi bi-credit-card"></i>
                                        </a>
                                        <a href="{% url 'banks:bank_delete' bank.pk %}" class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="صفحات البنوك" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-bank text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد بنوك</h4>
                    <p class="text-muted">لم يتم العثور على أي بنوك مطابقة لمعايير البحث.</p>
                    <a href="{% url 'banks:bank_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة أول بنك
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .bank-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .bank-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .bank-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .border-end {
        border-right: 1px solid #dee2e6 !important;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
</style>
{% endblock %}
