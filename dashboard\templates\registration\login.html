<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - أوساريك</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            display: flex;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-left {
            flex: 1;
            background: linear-gradient(135deg, #0d6efd 0%, #0dcaf0 100%);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .login-right {
            flex: 1;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .brand-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .brand-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .brand-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            color: #6c757d;
            margin-bottom: 2rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #0d6efd 0%, #0dcaf0 100%);
            border: none;
            border-radius: 10px;
            padding: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(13, 110, 253, 0.3);
            color: white;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            z-index: 10;
        }

        .signup-link {
            text-align: center;
            color: #6c757d;
        }

        .signup-link a {
            color: #0d6efd;
            text-decoration: none;
            font-weight: 600;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        .alert {
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }

        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                margin: 10px;
            }

            .login-left {
                padding: 2rem;
            }

            .login-right {
                padding: 2rem;
            }

            .brand-title {
                font-size: 2rem;
            }

            .login-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Left Side - Branding -->
        <div class="login-left">
            <div class="brand-logo">
                <i class="bi bi-building"></i>
            </div>
            <h1 class="brand-title">أوساريك</h1>
            <p class="brand-subtitle">
                نظام متكامل لإدارة الحسابات والمخزون<br>
                يساعدك على إدارة أعمالك بكفاءة وسهولة
            </p>
        </div>

        <!-- Right Side - Login Form -->
        <div class="login-right">
            <h2 class="login-title">مرحباً بعودتك</h2>
            <p class="login-subtitle">قم بتسجيل الدخول للوصول إلى حسابك</p>

            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}

            <form method="post">
                {% csrf_token %}

                <div class="form-floating">
                    <input type="text"
                           class="form-control {% if form.username.errors %}is-invalid{% endif %}"
                           id="id_username"
                           name="username"
                           placeholder="اسم المستخدم"
                           value="{{ form.username.value|default:'' }}"
                           required>
                    <label for="id_username">
                        <i class="bi bi-person me-2"></i>اسم المستخدم
                    </label>
                    {% if form.username.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.username.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-floating position-relative">
                    <input type="password"
                           class="form-control {% if form.password.errors %}is-invalid{% endif %}"
                           id="id_password"
                           name="password"
                           placeholder="كلمة المرور"
                           required>
                    <label for="id_password">
                        <i class="bi bi-lock me-2"></i>كلمة المرور
                    </label>
                    <span class="password-toggle" onclick="togglePassword()">
                        <i class="bi bi-eye" id="toggleIcon"></i>
                    </span>
                    {% if form.password.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <button type="submit" class="btn btn-login">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    تسجيل الدخول
                </button>
            </form>

            <div class="signup-link">
                ليس لديك حساب؟
                <a href="{% url 'customers:signup' %}">إنشاء حساب جديد</a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('id_password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        // Add loading state to login button
        document.querySelector('form').addEventListener('submit', function() {
            const button = document.querySelector('.btn-login');
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري تسجيل الدخول...';
            button.disabled = true;
        });

        // Auto-focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('id_username').focus();
        });
    </script>
</body>
</html>
