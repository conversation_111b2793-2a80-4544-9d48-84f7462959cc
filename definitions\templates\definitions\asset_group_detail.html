{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل مجموعة الأصول - {{ asset_group.name }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: #ffffff;
        min-height: 100vh;
    }

    .detail-container {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        color: #333;
    }

    .detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #e0e0e0;
    }

    .detail-title {
        font-size: 2rem;
        font-weight: 800;
        color: #333;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .btn {
        padding: 0.6rem 1.5rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .btn-primary {
        background: #007bff;
        color: white;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-danger {
        background: #dc3545;
        color: white;
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }

    .btn-danger:hover {
        background: #c82333;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    .detail-section {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
    }

    .detail-label {
        font-weight: 600;
        color: #666;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-value {
        color: #333;
        font-size: 1rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e0e0e0;
    }

    .detail-value.empty {
        color: #999;
        font-style: italic;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-badge.active {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    @media (max-width: 768px) {
        .detail-container {
            padding: 1.5rem;
        }
        
        .detail-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .detail-title {
            font-size: 1.5rem;
        }
        
        .action-buttons {
            width: 100%;
            justify-content: stretch;
        }
        
        .btn {
            flex: 1;
            justify-content: center;
        }
        
        .detail-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="detail-container">
                <div class="detail-header">
                    <div>
                        <h1 class="detail-title">
                            <i class="bi bi-collection"></i>
                            {{ asset_group.name }}
                        </h1>
                        <p class="text-muted mb-0">
                            <i class="bi bi-hash me-1"></i>
                            كود: {{ asset_group.code }}
                        </p>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-house me-2"></i>التعريفات
                        </a>
                        <a href="{% url 'definitions:asset_group_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>قائمة المجموعات
                        </a>
                        <a href="{% url 'definitions:asset_group_edit' asset_group.id %}" class="btn btn-primary">
                            <i class="bi bi-pencil me-2"></i>تعديل
                        </a>
                        <button type="button" class="btn btn-danger" onclick="deleteAssetGroup({{ asset_group.id }}, '{{ asset_group.name|escapejs }}')">
                            <i class="bi bi-trash me-2"></i>حذف
                        </button>
                    </div>
                </div>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Basic Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        المعلومات الأساسية
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-hash"></i>كود مجموعة الأصول
                            </div>
                            <div class="detail-value">{{ asset_group.code }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-collection"></i>الاسم
                            </div>
                            <div class="detail-value">{{ asset_group.name }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-translate"></i>الاسم بالإنجليزية
                            </div>
                            <div class="detail-value {% if not asset_group.name_en %}empty{% endif %}">
                                {{ asset_group.name_en|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-toggle-on"></i>الحالة
                            </div>
                            <div class="detail-value">
                                <span class="status-badge {% if asset_group.is_active %}active{% else %}inactive{% endif %}">
                                    {% if asset_group.is_active %}
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    {% else %}
                                        <i class="bi bi-x-circle me-1"></i>غير نشط
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                {% if asset_group.description or asset_group.notes %}
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-file-text"></i>
                        معلومات إضافية
                    </h3>
                    
                    <div class="detail-grid">
                        {% if asset_group.description %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-file-text"></i>الوصف
                            </div>
                            <div class="detail-value">{{ asset_group.description|linebreaks }}</div>
                        </div>
                        {% endif %}

                        {% if asset_group.notes %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-journal-text"></i>ملاحظات
                            </div>
                            <div class="detail-value">{{ asset_group.notes|linebreaks }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- System Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-gear"></i>
                        معلومات النظام
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-person-plus"></i>أنشأ بواسطة
                            </div>
                            <div class="detail-value">
                                {{ asset_group.created_by.get_full_name|default:asset_group.created_by.username }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-plus"></i>تاريخ الإنشاء
                            </div>
                            <div class="detail-value">
                                {{ asset_group.created_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>

                        {% if asset_group.updated_at %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-check"></i>آخر تحديث
                            </div>
                            <div class="detail-value">
                                {{ asset_group.updated_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteAssetGroup(groupId, groupName) {
    if (confirm('هل أنت متأكد من حذف مجموعة الأصول "' + groupName + '"؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        // إنشاء نموذج مخفي وإرساله
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/definitions/asset-groups/' + groupId + '/quick-delete/';
        
        // إضافة CSRF token
        var csrfToken = '{{ csrf_token }}';
        var csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        // إضافة النموذج للصفحة وإرساله
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
