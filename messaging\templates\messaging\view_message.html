<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ message.subject }} - الرسائل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            padding-top: 2rem;
        }
        
        .message-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .message-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
        }
        
        .message-body {
            padding: 2rem;
        }
        
        .message-meta {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .priority-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .priority-low { background: #6c757d; color: white; }
        .priority-normal { background: #007bff; color: white; }
        .priority-high { background: #ffc107; color: black; }
        .priority-urgent { background: #dc3545; color: white; }
        
        .reply-form {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            margin-top: 2rem;
        }
        
        .btn-action {
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            color: white;
            text-decoration: none;
        }
        
        .reply-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .reply-header {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- الرسالة الأصلية -->
                <div class="message-card">
                    <div class="message-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h1>{{ message.subject }}</h1>
                                <p class="mb-0">
                                    من: <strong>{{ message.sender.get_full_name|default:message.sender.username }}</strong>
                                    إلى: <strong>{{ message.recipient.get_full_name|default:message.recipient.username }}</strong>
                                </p>
                            </div>
                            <span class="priority-badge priority-{{ message.priority }}">
                                {% if message.priority == 'low' %}منخفضة
                                {% elif message.priority == 'normal' %}عادية
                                {% elif message.priority == 'high' %}عالية
                                {% elif message.priority == 'urgent' %}عاجلة
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    
                    <div class="message-body">
                        <div class="message-meta">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>تاريخ الإرسال:</strong> {{ message.created_at|date:"d/m/Y H:i" }}
                                </div>
                                <div class="col-md-6">
                                    <strong>الحالة:</strong> 
                                    {% if message.is_read %}
                                        <span class="text-success">مقروءة</span>
                                        {% if message.read_at %}
                                            ({{ message.read_at|date:"d/m/Y H:i" }})
                                        {% endif %}
                                    {% else %}
                                        <span class="text-warning">غير مقروءة</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="message-content">
                            <p style="white-space: pre-wrap; line-height: 1.6;">{{ message.content }}</p>
                        </div>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="d-flex gap-2 flex-wrap mt-4">
                            <a href="/messages/message/{{ message.id }}/reply/" class="btn-action btn-success">
                                <i class="bi bi-reply"></i>
                                رد
                            </a>
                            
                            {% if message.recipient == user %}
                            <button onclick="toggleStar({{ message.id }})" class="btn-action btn-primary">
                                <i class="bi bi-star{% if message.is_starred %}-fill{% endif %}"></i>
                                {% if message.is_starred %}إلغاء التمييز{% else %}تمييز{% endif %}
                            </button>
                            {% endif %}
                            
                            <a href="/messages/message/{{ message.id }}/delete/" class="btn-action btn-danger" 
                               onclick="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')">
                                <i class="bi bi-trash"></i>
                                حذف
                            </a>
                            
                            <a href="/messages/" class="btn-action btn-secondary">
                                <i class="bi bi-arrow-right"></i>
                                العودة للرسائل
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- الردود -->
                {% if replies %}
                <div class="message-card">
                    <div class="message-header">
                        <h3><i class="bi bi-chat-dots me-2"></i>الردود ({{ replies.count }})</h3>
                    </div>
                    <div class="message-body">
                        {% for reply in replies %}
                        <div class="reply-item">
                            <div class="reply-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <strong>{{ reply.sender.get_full_name|default:reply.sender.username }}</strong>
                                    <small class="text-muted">{{ reply.created_at|date:"d/m/Y H:i" }}</small>
                                </div>
                            </div>
                            <p style="white-space: pre-wrap; line-height: 1.6;">{{ reply.content }}</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- نموذج الرد السريع -->
                <div class="message-card">
                    <div class="message-header">
                        <h3><i class="bi bi-reply me-2"></i>رد سريع</h3>
                    </div>
                    <div class="message-body">
                        <form method="post" action="/messages/message/{{ message.id }}/reply/">
                            {% csrf_token %}
                            <div class="mb-3">
                                <textarea name="content" class="form-control" rows="4" 
                                          placeholder="اكتب ردك هنا..." required></textarea>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn-action btn-success">
                                    <i class="bi bi-send"></i>
                                    إرسال الرد
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleStar(messageId) {
            fetch(`/messages/message/${messageId}/star/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.error);
                }
            })
            .catch(error => {
                alert('حدث خطأ في الاتصال');
            });
        }
    </script>
</body>
</html>
