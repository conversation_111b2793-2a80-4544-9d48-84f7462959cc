# Generated by Django 5.2.4 on 2025-07-26 00:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchases', '0003_remove_stockmovement_created_by_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='purchaseinvoice',
            name='status',
            field=models.CharField(choices=[('draft', 'مسودة'), ('received', 'مستلمة'), ('partially_paid', 'مدفوعة جزئياً'), ('paid', 'مدفوعة بالكامل'), ('overdue', 'متأخرة'), ('cancelled', 'ملغية')], default='draft', max_length=20, verbose_name='الحالة'),
        ),
        migrations.CreateModel(
            name='SupplierPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_date', models.DateField(verbose_name='تاريخ الدفع')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ المدفوع')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك'), ('credit_card', 'بطاقة ائتمان'), ('other', 'أخرى')], max_length=20, verbose_name='طريقة الدفع')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='purchases.purchaseinvoice', verbose_name='فاتورة الشراء')),
            ],
            options={
                'verbose_name': 'دفعة مورد',
                'verbose_name_plural': 'دفعات الموردين',
                'ordering': ['-payment_date', '-created_at'],
            },
        ),
    ]
