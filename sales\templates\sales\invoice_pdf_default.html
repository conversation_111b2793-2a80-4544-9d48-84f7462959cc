<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة {{ invoice.invoice_number }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.6;
            color: #333;
            direction: rtl;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .company-info {
            font-size: 11px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .invoice-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }
        
        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }
        
        .invoice-info, .customer-info {
            width: 48%;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            color: #333;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .items-table th {
            background: #007bff;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            font-size: 11px;
        }
        
        .items-table td {
            padding: 10px 8px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
            font-size: 11px;
        }
        
        .items-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .items-table tbody tr:hover {
            background: #e3f2fd;
        }
        
        .product-name {
            font-weight: 600;
            color: #333;
        }
        
        .product-code {
            font-size: 10px;
            color: #666;
        }
        
        .totals-section {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
        }
        
        .totals-table {
            width: 300px;
            border-collapse: collapse;
        }
        
        .totals-table td {
            padding: 8px 15px;
            border: 1px solid #dee2e6;
        }
        
        .totals-table .label {
            background: #f8f9fa;
            font-weight: 600;
            text-align: right;
        }
        
        .totals-table .value {
            text-align: left;
            font-weight: 600;
        }
        
        .total-final {
            background: #007bff !important;
            color: white !important;
            font-size: 14px;
        }
        
        .notes-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-right: 4px solid #007bff;
        }
        
        .notes-title {
            font-weight: 600;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            color: white;
        }
        
        .status-draft { background: #6c757d; }
        .status-sent { background: #17a2b8; }
        .status-paid { background: #28a745; }
        .status-overdue { background: #dc3545; }
        .status-cancelled { background: #6c757d; }
        
        @media print {
            body { margin: 0; }
            .invoice-container { padding: 0; }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="header">
            <div class="company-name">{{ company_name }}</div>
            <div class="company-info">
                {% if company_address %}{{ company_address }}<br>{% endif %}
                {% if company_phone %}هاتف: {{ company_phone }}<br>{% endif %}
                {% if company_email %}بريد إلكتروني: {{ company_email }}{% endif %}
            </div>
            <div class="invoice-title">
                فاتورة {{ invoice.get_invoice_type_display }} رقم {{ invoice.invoice_number }}
                <span class="status-badge status-{{ invoice.status }}">{{ invoice.get_status_display }}</span>
            </div>
        </div>

        <!-- تفاصيل الفاتورة -->
        <div class="invoice-details">
            <div class="invoice-info">
                <div class="section-title">تفاصيل الفاتورة</div>
                <div class="info-row">
                    <span class="info-label">رقم الفاتورة:</span>
                    <span class="info-value">{{ invoice.invoice_number }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ الفاتورة:</span>
                    <span class="info-value">{{ invoice.invoice_date }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ الاستحقاق:</span>
                    <span class="info-value">{{ invoice.due_date }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">طريقة الدفع:</span>
                    <span class="info-value">{{ invoice.get_payment_method_display }}</span>
                </div>
                {% if invoice.representative %}
                <div class="info-row">
                    <span class="info-label">المندوب:</span>
                    <span class="info-value">{{ invoice.representative.full_name }}</span>
                </div>
                {% endif %}
                {% if invoice.vehicle %}
                <div class="info-row">
                    <span class="info-label">السيارة:</span>
                    <span class="info-value">{{ invoice.vehicle.plate_number }}</span>
                </div>
                {% endif %}
            </div>

            <div class="customer-info">
                <div class="section-title">معلومات العميل</div>
                <div class="info-row">
                    <span class="info-label">اسم العميل:</span>
                    <span class="info-value">{{ invoice.customer.name }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">نوع العميل:</span>
                    <span class="info-value">{{ invoice.customer.get_customer_type_display }}</span>
                </div>
                {% if invoice.customer.phone %}
                <div class="info-row">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">{{ invoice.customer.phone }}</span>
                </div>
                {% endif %}
                {% if invoice.customer.address %}
                <div class="info-row">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ invoice.customer.address }}</span>
                </div>
                {% endif %}
                {% if invoice.customer.tax_number %}
                <div class="info-row">
                    <span class="info-label">الرقم الضريبي:</span>
                    <span class="info-value">{{ invoice.customer.tax_number }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- جدول العناصر -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 35%;">المنتج</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 10%;">الوحدة</th>
                    <th style="width: 15%;">سعر الوحدة</th>
                    <th style="width: 10%;">الخصم %</th>
                    <th style="width: 15%;">الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>
                        <div class="product-name">{{ item.product.name }}</div>
                        <div class="product-code">كود: {{ item.product.code }}</div>
                    </td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.product.unit }}</td>
                    <td>{{ item.unit_price }} ج.م</td>
                    <td>{{ item.discount_percentage }}%</td>
                    <td>{{ item.total_price }} ج.م</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" style="text-align: center; color: #666;">لا توجد عناصر في هذه الفاتورة</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- إجمالي الفاتورة -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="label">المجموع الفرعي:</td>
                    <td class="value">{{ invoice.subtotal }} ج.م</td>
                </tr>
                <tr>
                    <td class="label">الخصم ({{ invoice.discount_percentage }}%):</td>
                    <td class="value">{{ invoice.calculated_discount_amount }} ج.م</td>
                </tr>
                <tr>
                    <td class="label">الضريبة ({{ invoice.tax_percentage }}%):</td>
                    <td class="value">{{ invoice.tax_amount }} ج.م</td>
                </tr>
                <tr class="total-final">
                    <td class="label">الإجمالي النهائي:</td>
                    <td class="value">{{ invoice.total_amount }} ج.م</td>
                </tr>
            </table>
        </div>

        <!-- الملاحظات -->
        {% if invoice.notes %}
        <div class="notes-section">
            <div class="notes-title">ملاحظات:</div>
            <div>{{ invoice.notes }}</div>
        </div>
        {% endif %}

        <!-- تذييل الفاتورة -->
        <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام أوساريك للمبيعات</p>
            <p>تاريخ الطباعة: {% now "Y-m-d H:i" %}</p>
        </div>
    </div>
</body>
</html>
