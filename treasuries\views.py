from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.http import JsonResponse
from django.core.paginator import Paginator
from datetime import datetime, timedelta
from .models import Treasury, TreasuryTransaction, TreasuryTransfer
from .forms import (TreasuryForm, TreasuryTransactionForm, TreasuryTransferForm,
                   TreasurySearchForm, TransactionSearchForm)

@login_required
def treasuries_dashboard(request):
    """لوحة تحكم الخزائن"""
    # إحصائيات عامة
    total_treasuries = Treasury.objects.filter(is_active=True).count()
    total_transactions = TreasuryTransaction.objects.count()
    total_transfers = TreasuryTransfer.objects.count()

    # إحصائيات مالية
    total_balance = Treasury.objects.filter(is_active=True).aggregate(
        total=Sum('current_balance'))['total'] or 0

    # المعاملات هذا الشهر
    current_month = datetime.now().month
    current_year = datetime.now().year
    monthly_transactions = TreasuryTransaction.objects.filter(
        transaction_date__month=current_month,
        transaction_date__year=current_year
    ).count()

    # المعاملات المعلقة
    pending_transactions = TreasuryTransaction.objects.filter(status='pending').count()
    pending_transfers = TreasuryTransfer.objects.filter(status='pending').count()

    # الخزائن ذات الرصيد المنخفض
    from django.db import models as django_models
    low_balance_treasuries = Treasury.objects.filter(
        is_active=True,
        current_balance__lt=django_models.F('min_limit')
    ).count()

    # الخزائن ذات الرصيد المرتفع
    high_balance_treasuries = Treasury.objects.filter(
        is_active=True,
        current_balance__gt=django_models.F('max_limit'),
        max_limit__gt=0
    ).count()

    # أحدث المعاملات
    recent_transactions = TreasuryTransaction.objects.select_related('treasury').order_by('-created_at')[:10]

    # أحدث التحويلات
    recent_transfers = TreasuryTransfer.objects.select_related('from_treasury', 'to_treasury').order_by('-created_at')[:5]

    context = {
        'total_treasuries': total_treasuries,
        'total_transactions': total_transactions,
        'total_transfers': total_transfers,
        'total_balance': total_balance,
        'monthly_transactions': monthly_transactions,
        'pending_transactions': pending_transactions,
        'pending_transfers': pending_transfers,
        'low_balance_treasuries': low_balance_treasuries,
        'high_balance_treasuries': high_balance_treasuries,
        'recent_transactions': recent_transactions,
        'recent_transfers': recent_transfers,
    }
    return render(request, 'treasuries/dashboard.html', context)

# ========== إدارة الخزائن ==========
@login_required
def treasury_list(request):
    """قائمة الخزائن"""
    form = TreasurySearchForm(request.GET)
    treasuries = Treasury.objects.select_related('responsible_person')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        responsible_person = form.cleaned_data.get('responsible_person')
        is_active = form.cleaned_data.get('is_active')

        if search:
            treasuries = treasuries.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search)
            )

        if responsible_person:
            treasuries = treasuries.filter(responsible_person=responsible_person)

        if is_active:
            treasuries = treasuries.filter(is_active=is_active == 'True')

    paginator = Paginator(treasuries, 20)
    page_number = request.GET.get('page')
    treasuries = paginator.get_page(page_number)

    context = {
        'treasuries': treasuries,
        'form': form,
    }
    return render(request, 'treasuries/treasury_list.html', context)

@login_required
def treasury_create(request):
    """إضافة خزينة جديدة"""
    if request.method == 'POST':
        form = TreasuryForm(request.POST)
        if form.is_valid():
            treasury = form.save(commit=False)
            treasury.current_balance = treasury.opening_balance
            treasury.save()

            # إنشاء معاملة الرصيد الافتتاحي
            if treasury.opening_balance > 0:
                TreasuryTransaction.objects.create(
                    treasury=treasury,
                    transaction_date=datetime.now().date(),
                    transaction_type='opening_balance',
                    amount=treasury.opening_balance,
                    description='الرصيد الافتتاحي للخزينة',
                    status='completed',
                    balance_before=0,
                    balance_after=treasury.opening_balance,
                    created_by=request.user
                )

            messages.success(request, 'تم إضافة الخزينة بنجاح')
            return redirect('treasuries:treasury_list')
    else:
        form = TreasuryForm()

    context = {'form': form, 'title': 'إضافة خزينة جديدة'}
    return render(request, 'treasuries/treasury_form.html', context)

@login_required
def treasury_detail(request, pk):
    """تفاصيل الخزينة"""
    treasury = get_object_or_404(Treasury, pk=pk)
    transactions = treasury.transactions.order_by('-transaction_date', '-created_at')[:20]

    context = {
        'treasury': treasury,
        'transactions': transactions,
    }
    return render(request, 'treasuries/treasury_detail.html', context)

@login_required
def treasury_edit(request, pk):
    """تعديل خزينة"""
    treasury = get_object_or_404(Treasury, pk=pk)
    if request.method == 'POST':
        form = TreasuryForm(request.POST, instance=treasury)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات الخزينة بنجاح')
            return redirect('treasuries:treasury_detail', pk=treasury.pk)
    else:
        form = TreasuryForm(instance=treasury)

    context = {'form': form, 'title': 'تعديل الخزينة', 'treasury': treasury}
    return render(request, 'treasuries/treasury_form.html', context)

@login_required
def treasury_delete(request, pk):
    """حذف خزينة"""
    treasury = get_object_or_404(Treasury, pk=pk)
    if request.method == 'POST':
        treasury.delete()
        messages.success(request, 'تم حذف الخزينة بنجاح')
        return redirect('treasuries:treasury_list')

    context = {'treasury': treasury}
    return render(request, 'treasuries/treasury_confirm_delete.html', context)

# ========== إدارة المعاملات ==========
@login_required
def transaction_list(request):
    """قائمة المعاملات"""
    form = TransactionSearchForm(request.GET)
    transactions = TreasuryTransaction.objects.select_related('treasury', 'created_by')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        treasury = form.cleaned_data.get('treasury')
        transaction_type = form.cleaned_data.get('transaction_type')
        status = form.cleaned_data.get('status')
        date_from = form.cleaned_data.get('date_from')
        date_to = form.cleaned_data.get('date_to')

        if search:
            transactions = transactions.filter(
                Q(description__icontains=search) |
                Q(reference_number__icontains=search)
            )

        if treasury:
            transactions = transactions.filter(treasury=treasury)

        if transaction_type:
            transactions = transactions.filter(transaction_type=transaction_type)

        if status:
            transactions = transactions.filter(status=status)

        if date_from:
            transactions = transactions.filter(transaction_date__gte=date_from)

        if date_to:
            transactions = transactions.filter(transaction_date__lte=date_to)

    paginator = Paginator(transactions, 20)
    page_number = request.GET.get('page')
    transactions = paginator.get_page(page_number)

    context = {
        'transactions': transactions,
        'form': form,
    }
    return render(request, 'treasuries/transaction_list.html', context)

@login_required
def transaction_create(request):
    """إضافة معاملة جديدة"""
    if request.method == 'POST':
        form = TreasuryTransactionForm(request.POST)
        if form.is_valid():
            transaction = form.save(commit=False)
            transaction.created_by = request.user
            transaction.save()
            messages.success(request, 'تم إضافة المعاملة بنجاح')
            return redirect('treasuries:transaction_list')
    else:
        form = TreasuryTransactionForm()

    context = {'form': form, 'title': 'إضافة معاملة جديدة'}
    return render(request, 'treasuries/transaction_form.html', context)
