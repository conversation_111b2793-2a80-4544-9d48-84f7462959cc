from .models import SystemSettings
from .utils import NotificationManager


def system_settings(request):
    """
    Context processor لإتاحة إعدادات النظام في جميع القوالب
    """
    try:
        settings = SystemSettings.get_settings()
        
        return {
            'system_settings': settings,
            'theme_vars': getattr(request, 'theme_vars', {
                'primary_color': '#667eea',
                'secondary_color': '#764ba2',
                'theme_mode': 'light',
                'enable_animations': True,
                'compact_mode': False,
            }),
            'currency_symbol': settings.currency_symbol if settings else 'ج.م',
            'currency_code': settings.currency_code if settings else 'EGP',
            'company_name': settings.company_name if settings else 'شركة أوساريك',
            'system_language': settings.system_language if settings else 'ar',
            'date_format': getattr(settings, 'date_format', 'd/m/Y') if settings else 'd/m/Y',
            'time_format': getattr(settings, 'time_format', '24') if settings else '24',
            'decimal_places': getattr(settings, 'decimal_places', 2) if settings else 2,
            'thousand_separator': getattr(settings, 'thousand_separator', ',') if settings else ',',
            'decimal_separator': getattr(settings, 'decimal_separator', '.') if settings else '.',
            'enable_animations': getattr(settings, 'enable_animations', True) if settings else True,
            'compact_mode': getattr(settings, 'compact_mode', False) if settings else False,
            'show_breadcrumbs': getattr(settings, 'show_breadcrumbs', True) if settings else True,
            'notifications_enabled': settings.notifications_enabled if settings else True,
            'items_per_page': settings.items_per_page if settings else 20,
        }
    
    except Exception as e:
        # في حالة حدوث خطأ، إرجاع قيم افتراضية
        return {
            'system_settings': None,
            'theme_vars': {
                'primary_color': '#667eea',
                'secondary_color': '#764ba2',
                'theme_mode': 'light',
                'enable_animations': True,
                'compact_mode': False,
            },
            'currency_symbol': 'ج.م',
            'currency_code': 'EGP',
            'company_name': 'شركة أوساريك',
            'system_language': 'ar',
            'date_format': 'd/m/Y',
            'time_format': '24',
            'decimal_places': 2,
            'thousand_separator': ',',
            'decimal_separator': '.',
            'enable_animations': True,
            'compact_mode': False,
            'show_breadcrumbs': True,
            'notifications_enabled': True,
            'items_per_page': 20,
        }


def notifications_context(request):
    """إضافة الإشعارات إلى السياق العام"""
    context = {
        'notifications': [],
        'unread_notifications_count': 0,
        'has_notifications': False,
    }

    if request.user.is_authenticated:
        try:
            # الحصول على الإشعارات الحديثة
            notifications = NotificationManager.get_user_notifications(
                user=request.user,
                limit=10
            )

            # عدد الإشعارات غير المقروءة
            unread_count = NotificationManager.get_unread_count(request.user)

            context.update({
                'notifications': notifications,
                'unread_notifications_count': unread_count,
                'has_notifications': len(notifications) > 0,
            })

        except Exception as e:
            print(f"خطأ في جلب الإشعارات: {e}")

    return context
