{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة مخزون{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .receive-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .receive-form {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .form-control, .form-select {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        outline: none;
    }

    .btn-receive {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-receive:hover {
        background: linear-gradient(135deg, #218838, #1e7e34);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-info {
        background: linear-gradient(135deg, #17a2b8, #138496);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-info:hover {
        background: linear-gradient(135deg, #138496, #117a8b);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .reason-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .reason-option {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        font-size: 0.9rem;
    }

    .reason-option:hover {
        background: #e9ecef;
        border-color: #28a745;
    }

    .reason-option.selected {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }

    .cost-info {
        background: #e8f5e8;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .cost-info h6 {
        color: #155724;
        margin-bottom: 0.5rem;
    }

    .cost-info p {
        color: #155724;
        margin-bottom: 0;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="receive-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1><i class="bi bi-box-arrow-in-right me-2"></i>إضافة مخزون</h1>
        <p class="mb-0">إضافة كمية جديدة للمخزون</p>
    </div>

    <!-- Receive Form -->
    <div class="receive-form">
        <h5 class="mb-4"><i class="bi bi-clipboard-plus me-2"></i>بيانات الإضافة</h5>
        
        <form method="post" action="{% url 'warehouses:stock_receive' %}">
            {% csrf_token %}
            
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label required-field">المخزن</label>
                    <select name="warehouse" class="form-select" required>
                        <option value="">اختر المخزن</option>
                        {% for warehouse in warehouses %}
                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-6">
                    <label class="form-label required-field">المنتج</label>
                    <select name="product" class="form-select" required>
                        <option value="">اختر المنتج</option>
                        {% for product in products %}
                        <option value="{{ product.id }}">{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-6">
                    <label class="form-label required-field">الكمية المضافة</label>
                    <input type="number" name="received_quantity" class="form-control" 
                           step="0.001" min="0.001" placeholder="أدخل الكمية" required>
                </div>

                <div class="col-md-6">
                    <label class="form-label">سعر الوحدة (ج.م)</label>
                    <input type="number" name="unit_cost" class="form-control" 
                           step="0.01" min="0" placeholder="سعر التكلفة للوحدة">
                </div>

                <div class="col-md-6">
                    <label class="form-label">تاريخ الإضافة</label>
                    <input type="datetime-local" name="receive_date" class="form-control" 
                           value="{{ current_datetime }}">
                </div>

                <div class="col-md-6">
                    <label class="form-label required-field">سبب الإضافة</label>
                    <input type="text" name="reason" id="reasonInput" class="form-control" 
                           placeholder="أدخل سبب الإضافة" required>
                </div>

                <div class="col-12">
                    <div class="reason-options">
                        <div class="reason-option" onclick="selectReason('شراء جديد')">شراء جديد</div>
                        <div class="reason-option" onclick="selectReason('إنتاج')">إنتاج</div>
                        <div class="reason-option" onclick="selectReason('إرجاع من العملاء')">إرجاع من العملاء</div>
                        <div class="reason-option" onclick="selectReason('تحويل من مخزن آخر')">تحويل من مخزن آخر</div>
                        <div class="reason-option" onclick="selectReason('تسوية زيادة')">تسوية زيادة</div>
                        <div class="reason-option" onclick="selectReason('رصيد افتتاحي')">رصيد افتتاحي</div>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label">ملاحظات إضافية</label>
                    <textarea name="notes" class="form-control" rows="3" 
                              placeholder="ملاحظات حول عملية الإضافة (اختياري)"></textarea>
                </div>
            </div>

            <div class="cost-info">
                <h6><i class="bi bi-info-circle me-2"></i>معلومات التكلفة</h6>
                <p>• إذا لم تدخل سعر الوحدة، سيتم استخدام سعر التكلفة المحفوظ للمنتج</p>
                <p>• سيتم تحديث التكلفة المتوسطة للمنتج تلقائياً بناءً على الكمية الجديدة</p>
                <p>• القيمة الإجمالية للمخزون ستحدث تلقائياً</p>
            </div>

            <div class="text-center mt-4">
                <input type="submit" class="btn btn-receive me-3" value="تأكيد الإضافة">
                <a href="{% url 'warehouses:receive_history' %}" class="btn btn-info me-3">
                    <i class="bi bi-clock-history me-1"></i>سجل الإضافات
                </a>
                <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-1"></i>عودة
                </a>
            </div>
        </form>
    </div>
</div>

<script>
function selectReason(reason) {
    // إزالة التحديد من جميع الخيارات
    document.querySelectorAll('.reason-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // تحديد الخيار المختار
    event.target.classList.add('selected');
    
    // وضع السبب في الحقل
    document.getElementById('reasonInput').value = reason;
}
</script>
{% endblock %}
