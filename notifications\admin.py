from django.contrib import admin
from .models import Notification, NotificationSettings, NotificationTemplate


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ['title', 'recipient', 'notification_type', 'priority', 'is_read', 'is_seen', 'created_at']
    list_filter = ['notification_type', 'priority', 'is_read', 'is_seen', 'created_at']
    search_fields = ['title', 'message', 'recipient__username', 'recipient__email']
    readonly_fields = ['created_at', 'read_at']
    
    fieldsets = (
        ('معلومات الإشعار', {
            'fields': ('recipient', 'title', 'message', 'notification_type', 'priority')
        }),
        ('الحالة', {
            'fields': ('is_read', 'is_seen', 'created_at', 'read_at')
        }),
        ('إعدادات إضافية', {
            'fields': ('icon', 'action_url', 'sender'),
            'classes': ('collapse',)
        })
    )
    
    def mark_as_read(self, request, queryset):
        """تعيين الإشعارات المحددة كمقروءة"""
        updated = queryset.update(is_read=True)
        self.message_user(request, f'تم تعيين {updated} إشعار كمقروء')
    mark_as_read.short_description = "تعيين كمقروء"
    
    def mark_as_unread(self, request, queryset):
        """تعيين الإشعارات المحددة كغير مقروءة"""
        updated = queryset.update(is_read=False, read_at=None)
        self.message_user(request, f'تم تعيين {updated} إشعار كغير مقروء')
    mark_as_unread.short_description = "تعيين كغير مقروء"
    
    actions = ['mark_as_read', 'mark_as_unread']


@admin.register(NotificationSettings)
class NotificationSettingsAdmin(admin.ModelAdmin):
    list_display = ['user', 'email_notifications', 'browser_notifications', 'sound_notifications']
    list_filter = ['email_notifications', 'browser_notifications', 'sound_notifications']
    search_fields = ['user__username', 'user__email']


@admin.register(NotificationTemplate)
class NotificationTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'notification_type', 'priority', 'is_active']
    list_filter = ['notification_type', 'priority', 'is_active']
    search_fields = ['name', 'title_template', 'message_template']
