# Generated by Django 5.2.4 on 2025-07-25 23:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0015_finishedproductmodel_productionstage_and_more'),
        ('purchases', '0002_warehouse_purchaseinvoiceitem_applied_at_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='stockmovement',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='stockmovement',
            name='product',
        ),
        migrations.RemoveField(
            model_name='stockmovement',
            name='warehouse',
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceitem',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='المنتج'),
        ),
        migrations.AlterField(
            model_name='purchaseinvoiceitem',
            name='warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن المستهدف'),
        ),
        migrations.AlterField(
            model_name='purchaseorderitem',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='المنتج'),
        ),
        migrations.AlterField(
            model_name='purchaseorderitem',
            name='warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن المستهدف'),
        ),
        migrations.DeleteModel(
            name='Stock',
        ),
        migrations.DeleteModel(
            name='StockMovement',
        ),
    ]
