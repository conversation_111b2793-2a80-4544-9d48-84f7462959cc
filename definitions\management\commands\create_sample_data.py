from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from definitions.models import (
    WarehouseDefinition, ProductCategory, ProductDefinition,
    BankDefinition, CurrencyDefinition, CashBoxDefinition,
    AssetGroup, PersonDefinition, ExpenseType, ExpenseName,
    RevenueType, RevenueName, ProfitCenter, PrinterDefinition
)

class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للتعريفات'

    def handle(self, *args, **options):
        # إنشاء مستخدم تجريبي إذا لم يكن موجوداً
        user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            user.set_password('admin123')
            user.save()
            self.stdout.write(self.style.SUCCESS('تم إنشاء المستخدم admin'))

        # إنشاء العملات
        currencies = [
            {'code': 'EGP', 'name': 'جنيه مصري', 'symbol': 'ج.م', 'is_base_currency': True},
            {'code': 'USD', 'name': 'دولار أمريكي', 'symbol': '$', 'exchange_rate': 30.90},
            {'code': 'EUR', 'name': 'يورو', 'symbol': '€', 'exchange_rate': 33.50},
        ]
        
        for currency_data in currencies:
            currency, created = CurrencyDefinition.objects.get_or_create(
                code=currency_data['code'],
                defaults={**currency_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء العملة: {currency.name}')

        # إنشاء المخازن
        warehouses = [
            {'code': 'WH001', 'name': 'المخزن الرئيسي', 'warehouse_type': 'main'},
            {'code': 'WH002', 'name': 'مخزن المواد الخام', 'warehouse_type': 'raw_materials'},
            {'code': 'WH003', 'name': 'مخزن المنتجات التامة', 'warehouse_type': 'finished_goods'},
            {'code': 'WH004', 'name': 'مخزن التالف', 'warehouse_type': 'damaged'},
        ]
        
        for warehouse_data in warehouses:
            warehouse, created = WarehouseDefinition.objects.get_or_create(
                code=warehouse_data['code'],
                defaults={**warehouse_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء المخزن: {warehouse.name}')

        # إنشاء فئات الأصناف
        categories = [
            {'code': 'CAT001', 'name': 'إلكترونيات'},
            {'code': 'CAT002', 'name': 'ملابس'},
            {'code': 'CAT003', 'name': 'أغذية'},
            {'code': 'CAT004', 'name': 'مواد خام'},
            {'code': 'CAT005', 'name': 'قطع غيار'},
        ]
        
        for category_data in categories:
            category, created = ProductCategory.objects.get_or_create(
                code=category_data['code'],
                defaults={**category_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء فئة الأصناف: {category.name}')

        # إنشاء الأصناف
        electronics_category = ProductCategory.objects.get(code='CAT001')
        clothes_category = ProductCategory.objects.get(code='CAT002')
        
        products = [
            {
                'code': 'PRD001', 'name': 'لابتوب ديل', 'category': electronics_category,
                'product_type': 'product', 'main_unit': 'piece', 'cost_price': 2500,
                'selling_price': 3000, 'minimum_stock': 5, 'barcode': '1234567890001'
            },
            {
                'code': 'PRD002', 'name': 'قميص قطني', 'category': clothes_category,
                'product_type': 'product', 'main_unit': 'piece', 'cost_price': 50,
                'selling_price': 75, 'minimum_stock': 20, 'barcode': '1234567890002'
            },
            {
                'code': 'PRD003', 'name': 'ماوس لاسلكي', 'category': electronics_category,
                'product_type': 'product', 'main_unit': 'piece', 'cost_price': 25,
                'selling_price': 40, 'minimum_stock': 50, 'barcode': '*************'
            },
        ]
        
        for product_data in products:
            product, created = ProductDefinition.objects.get_or_create(
                code=product_data['code'],
                defaults={**product_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء الصنف: {product.name}')

        # إنشاء البنوك
        banks = [
            {'code': 'BNK001', 'name': 'البنك الأهلي المصري', 'swift_code': 'NBEGEGCX'},
            {'code': 'BNK002', 'name': 'بنك مصر', 'swift_code': 'BMISEGCX'},
            {'code': 'BNK003', 'name': 'البنك التجاري الدولي', 'swift_code': 'CIBKEGCX'},
        ]
        
        for bank_data in banks:
            bank, created = BankDefinition.objects.get_or_create(
                code=bank_data['code'],
                defaults={**bank_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء البنك: {bank.name}')

        # إنشاء الخزائن
        egp_currency = CurrencyDefinition.objects.get(code='EGP')
        usd_currency = CurrencyDefinition.objects.get(code='USD')

        cashboxes = [
            {'code': 'CB001', 'name': 'خزينة الفرع الرئيسي', 'currency': egp_currency, 'opening_balance': 50000},
            {'code': 'CB002', 'name': 'خزينة المبيعات', 'currency': egp_currency, 'opening_balance': 25000},
            {'code': 'CB003', 'name': 'خزينة العملة الأجنبية', 'currency': usd_currency, 'opening_balance': 1000},
        ]
        
        for cashbox_data in cashboxes:
            cashbox, created = CashBoxDefinition.objects.get_or_create(
                code=cashbox_data['code'],
                defaults={**cashbox_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء الخزينة: {cashbox.name}')

        # إنشاء مجموعات الأصول
        asset_groups = [
            {'code': 'AG001', 'name': 'أجهزة كمبيوتر', 'asset_type': 'fixed', 'depreciation_rate': 20},
            {'code': 'AG002', 'name': 'أثاث مكتبي', 'asset_type': 'fixed', 'depreciation_rate': 10},
            {'code': 'AG003', 'name': 'سيارات', 'asset_type': 'fixed', 'depreciation_rate': 25},
        ]
        
        for asset_group_data in asset_groups:
            asset_group, created = AssetGroup.objects.get_or_create(
                code=asset_group_data['code'],
                defaults={**asset_group_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء مجموعة الأصول: {asset_group.name}')

        # إنشاء الأشخاص
        persons = [
            {'code': 'PER001', 'name': 'شركة التقنية المتقدمة', 'person_type': 'supplier'},
            {'code': 'PER002', 'name': 'أحمد محمد علي', 'person_type': 'customer'},
            {'code': 'PER003', 'name': 'شركة النسيج الحديث', 'person_type': 'both'},
        ]
        
        for person_data in persons:
            person, created = PersonDefinition.objects.get_or_create(
                code=person_data['code'],
                defaults={**person_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء الشخص: {person.name}')

        # إنشاء أنواع المصروفات
        expense_types = [
            {'code': 'EXT001', 'name': 'مصروفات إدارية'},
            {'code': 'EXT002', 'name': 'مصروفات تشغيلية'},
            {'code': 'EXT003', 'name': 'مصروفات تسويقية'},
        ]
        
        for expense_type_data in expense_types:
            expense_type, created = ExpenseType.objects.get_or_create(
                code=expense_type_data['code'],
                defaults={**expense_type_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء نوع المصروف: {expense_type.name}')

        # إنشاء أسماء المصروفات
        admin_expense_type = ExpenseType.objects.get(code='EXT001')
        operational_expense_type = ExpenseType.objects.get(code='EXT002')
        
        expense_names = [
            {'code': 'EXN001', 'name': 'رواتب الموظفين', 'expense_type': admin_expense_type, 'default_amount': 15000},
            {'code': 'EXN002', 'name': 'فواتير الكهرباء', 'expense_type': operational_expense_type, 'default_amount': 800},
            {'code': 'EXN003', 'name': 'صيانة المعدات', 'expense_type': operational_expense_type, 'default_amount': 500},
        ]
        
        for expense_name_data in expense_names:
            expense_name, created = ExpenseName.objects.get_or_create(
                code=expense_name_data['code'],
                defaults={**expense_name_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء اسم المصروف: {expense_name.name}')

        # إنشاء أنواع الإيرادات
        revenue_types = [
            {'code': 'RVT001', 'name': 'إيرادات المبيعات'},
            {'code': 'RVT002', 'name': 'إيرادات الخدمات'},
            {'code': 'RVT003', 'name': 'إيرادات أخرى'},
        ]
        
        for revenue_type_data in revenue_types:
            revenue_type, created = RevenueType.objects.get_or_create(
                code=revenue_type_data['code'],
                defaults={**revenue_type_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء نوع الإيراد: {revenue_type.name}')

        # إنشاء مراكز الربحية
        profit_centers = [
            {'code': 'PC001', 'name': 'قسم الإلكترونيات', 'target_revenue': 100000, 'target_profit': 25000},
            {'code': 'PC002', 'name': 'قسم الملابس', 'target_revenue': 50000, 'target_profit': 15000},
        ]
        
        for profit_center_data in profit_centers:
            profit_center, created = ProfitCenter.objects.get_or_create(
                code=profit_center_data['code'],
                defaults={**profit_center_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء مركز الربحية: {profit_center.name}')

        # إنشاء الطابعات
        printers = [
            {'code': 'PRT001', 'name': 'طابعة الإيصالات الرئيسية', 'printer_type': 'thermal', 'connection_type': 'usb', 'default_for_receipts': True},
            {'code': 'PRT002', 'name': 'طابعة التقارير', 'printer_type': 'laser', 'connection_type': 'network', 'default_for_reports': True},
        ]
        
        for printer_data in printers:
            printer, created = PrinterDefinition.objects.get_or_create(
                code=printer_data['code'],
                defaults={**printer_data, 'created_by': user}
            )
            if created:
                self.stdout.write(f'تم إنشاء الطابعة: {printer.name}')

        self.stdout.write(self.style.SUCCESS('تم إنشاء جميع البيانات التجريبية بنجاح!'))
