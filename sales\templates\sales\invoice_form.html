<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if invoice %}تعديل الفاتورة{% else %}إنشاء فاتورة جديدة{% endif %} - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .form-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #43e97b;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #43e97b;
            box-shadow: 0 0 0 0.2rem rgba(67, 233, 123, 0.25);
        }
        
        .btn-save {
            background: linear-gradient(45deg, #43e97b, #38f9d7);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            min-width: 150px;
        }
        
        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(67, 233, 123, 0.3);
            color: white;
        }
        
        .btn-add-item {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-add-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .items-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .table th {
            background: linear-gradient(45deg, #43e97b, #38f9d7);
            color: white;
            border: none;
            padding: 15px;
            font-weight: 600;
        }
        
        .table td {
            padding: 12px 15px;
            vertical-align: middle;
            border-color: #e9ecef;
        }
        
        .item-row {
            transition: all 0.3s ease;
        }
        
        .item-row:hover {
            background-color: #f8f9fa;
        }
        
        .total-section {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .total-row:last-child {
            border-bottom: none;
            font-size: 1.3rem;
            font-weight: 700;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .required-field {
            color: #dc3545;
        }
        
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .customer-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-top: 10px;
        }
        
        .btn-remove-item {
            background: #dc3545;
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            transition: all 0.3s;
        }
        
        .btn-remove-item:hover {
            background: #c82333;
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-receipt"></i>
                        {% if invoice %}تعديل الفاتورة: {{ invoice.invoice_number }}{% else %}إنشاء فاتورة جديدة{% endif %}
                    </h1>
                    <p class="mb-0">{% if invoice %}تعديل بيانات الفاتورة الموجودة{% else %}إنشاء فاتورة بيع جديدة{% endif %}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'sales:invoice_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- نموذج الفاتورة -->
        <div class="form-container">
            <form method="post" id="invoiceForm">
                {% csrf_token %}
                
                <!-- معلومات الفاتورة الأساسية -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        معلومات الفاتورة
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="invoice_number" class="form-label">
                                    رقم الفاتورة <span class="required-field">*</span>
                                </label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                                       value="{{ form.invoice_number.value|default:'' }}" required readonly>
                                <div class="help-text">يتم إنشاؤه تلقائياً</div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="invoice_date" class="form-label">
                                    تاريخ الفاتورة <span class="required-field">*</span>
                                </label>
                                <input type="date" class="form-control" id="invoice_date" name="invoice_date" 
                                       value="{{ form.invoice_date.value|default:'' }}" required>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" id="due_date" name="due_date" 
                                       value="{{ form.due_date.value|default:'' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="status" class="form-label">حالة الفاتورة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="draft" {% if form.status.value == 'draft' %}selected{% endif %}>مسودة</option>
                                    <option value="confirmed" {% if form.status.value == 'confirmed' %}selected{% endif %}>مؤكدة</option>
                                    <option value="paid" {% if form.status.value == 'paid' %}selected{% endif %}>مدفوعة</option>
                                    <option value="cancelled" {% if form.status.value == 'cancelled' %}selected{% endif %}>ملغية</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات العميل -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-person"></i>
                        معلومات العميل
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer" class="form-label">
                                    العميل <span class="required-field">*</span>
                                </label>
                                <select class="form-select" id="customer" name="customer" required>
                                    <option value="">اختر العميل</option>
                                    {% for customer in customers %}
                                        <option value="{{ customer.id }}" 
                                                data-type="{{ customer.customer_type }}"
                                                data-phone="{{ customer.phone }}"
                                                data-address="{{ customer.address }}"
                                                data-credit-limit="{{ customer.credit_limit }}"
                                                {% if form.customer.value == customer.id %}selected{% endif %}>
                                            {{ customer.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div id="customerInfo" class="customer-info" style="display: none;">
                                <h6><i class="bi bi-info-circle"></i> معلومات العميل</h6>
                                <div id="customerDetails"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="representative" class="form-label">المندوب</label>
                                <select class="form-select" id="representative" name="representative">
                                    <option value="">اختر المندوب</option>
                                    {% for rep in representatives %}
                                        <option value="{{ rep.id }}" {% if form.representative.value == rep.id %}selected{% endif %}>
                                            {{ rep.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">{{ form.notes.value|default:'' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أصناف الفاتورة -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-list-ul"></i>
                        أصناف الفاتورة
                        <button type="button" class="btn btn-add-item float-end" onclick="addItem()">
                            <i class="bi bi-plus-circle"></i>
                            إضافة صنف
                        </button>
                    </h3>
                    
                    <div class="items-table">
                        <table class="table table-hover mb-0" id="itemsTable">
                            <thead>
                                <tr>
                                    <th width="30%">المنتج</th>
                                    <th width="15%">الكمية</th>
                                    <th width="15%">السعر</th>
                                    <th width="10%">الخصم %</th>
                                    <th width="15%">الإجمالي</th>
                                    <th width="10%">الضريبة %</th>
                                    <th width="5%">حذف</th>
                                </tr>
                            </thead>
                            <tbody id="itemsTableBody">
                                <!-- سيتم إضافة الأصناف هنا بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- الإجماليات -->
                <div class="total-section">
                    <h4><i class="bi bi-calculator"></i> إجماليات الفاتورة</h4>
                    
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span id="subtotal">0.00 ج.م</span>
                    </div>
                    
                    <div class="total-row">
                        <span>إجمالي الخصم:</span>
                        <span id="totalDiscount">0.00 ج.م</span>
                    </div>
                    
                    <div class="total-row">
                        <span>إجمالي الضريبة:</span>
                        <span id="totalTax">0.00 ج.م</span>
                    </div>
                    
                    <div class="total-row">
                        <span>الإجمالي النهائي:</span>
                        <span id="grandTotal">0.00 ج.م</span>
                    </div>
                    
                    <input type="hidden" id="total_amount" name="total_amount" value="0">
                </div>

                <!-- أزرار الحفظ -->
                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-save me-3">
                        <i class="bi bi-check-circle"></i>
                        {% if invoice %}حفظ التعديلات{% else %}إنشاء الفاتورة{% endif %}
                    </button>
                    <a href="{% url 'sales:invoice_list' %}" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let itemCounter = 0;
        const products = {{ products_json|safe }};

        // تأثيرات بصرية للنموذج
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.form-section');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(30px)';
                    section.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });

            // إضافة صنف افتراضي
            addItem();

            // معلومات العميل
            document.getElementById('customer').addEventListener('change', function() {
                const option = this.options[this.selectedIndex];
                const customerInfo = document.getElementById('customerInfo');
                const customerDetails = document.getElementById('customerDetails');
                
                if (option.value) {
                    const type = option.dataset.type;
                    const phone = option.dataset.phone;
                    const address = option.dataset.address;
                    const creditLimit = option.dataset.creditLimit;
                    
                    let typeText = '';
                    if (type === 'retail') typeText = 'تجزئة';
                    else if (type === 'wholesale') typeText = 'جملة';
                    else if (type === 'credit') typeText = 'آجل';
                    
                    customerDetails.innerHTML = `
                        <div><strong>النوع:</strong> ${typeText}</div>
                        ${phone ? `<div><strong>الهاتف:</strong> ${phone}</div>` : ''}
                        ${address ? `<div><strong>العنوان:</strong> ${address}</div>` : ''}
                        ${creditLimit ? `<div><strong>حد الائتمان:</strong> ${creditLimit} ج.م</div>` : ''}
                    `;
                    customerInfo.style.display = 'block';
                } else {
                    customerInfo.style.display = 'none';
                }
            });
        });

        function addItem() {
            itemCounter++;
            const tbody = document.getElementById('itemsTableBody');
            const row = document.createElement('tr');
            row.className = 'item-row';
            row.id = `item-${itemCounter}`;
            
            row.innerHTML = `
                <td>
                    <select class="form-select" name="product_${itemCounter}" onchange="updatePrice(${itemCounter})" required>
                        <option value="">اختر المنتج</option>
                        ${products.map(p => `<option value="${p.id}" data-price="${p.unit_price_retail}">${p.name} - ${p.code}</option>`).join('')}
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control" name="quantity_${itemCounter}" min="1" value="1" onchange="calculateItemTotal(${itemCounter})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="unit_price_${itemCounter}" step="0.01" min="0" onchange="calculateItemTotal(${itemCounter})" required>
                </td>
                <td>
                    <input type="number" class="form-control" name="discount_${itemCounter}" min="0" max="100" value="0" onchange="calculateItemTotal(${itemCounter})">
                </td>
                <td>
                    <span class="item-total" id="total_${itemCounter}">0.00 ج.م</span>
                </td>
                <td>
                    <input type="number" class="form-control" name="tax_${itemCounter}" min="0" max="100" value="14" onchange="calculateItemTotal(${itemCounter})">
                </td>
                <td>
                    <button type="button" class="btn btn-remove-item" onclick="removeItem(${itemCounter})">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        }

        function removeItem(itemId) {
            const row = document.getElementById(`item-${itemId}`);
            if (row) {
                row.remove();
                calculateGrandTotal();
            }
        }

        function updatePrice(itemId) {
            const productSelect = document.querySelector(`select[name="product_${itemId}"]`);
            const priceInput = document.querySelector(`input[name="unit_price_${itemId}"]`);
            
            const selectedOption = productSelect.options[productSelect.selectedIndex];
            if (selectedOption.value) {
                priceInput.value = selectedOption.dataset.price;
                calculateItemTotal(itemId);
            }
        }

        function calculateItemTotal(itemId) {
            const quantity = parseFloat(document.querySelector(`input[name="quantity_${itemId}"]`).value) || 0;
            const unitPrice = parseFloat(document.querySelector(`input[name="unit_price_${itemId}"]`).value) || 0;
            const discount = parseFloat(document.querySelector(`input[name="discount_${itemId}"]`).value) || 0;
            
            const subtotal = quantity * unitPrice;
            const discountAmount = subtotal * (discount / 100);
            const total = subtotal - discountAmount;
            
            document.getElementById(`total_${itemId}`).textContent = total.toFixed(2) + ' ج.م';
            
            calculateGrandTotal();
        }

        function calculateGrandTotal() {
            let subtotal = 0;
            let totalDiscount = 0;
            let totalTax = 0;
            
            document.querySelectorAll('.item-row').forEach(row => {
                const itemId = row.id.split('-')[1];
                const quantity = parseFloat(document.querySelector(`input[name="quantity_${itemId}"]`).value) || 0;
                const unitPrice = parseFloat(document.querySelector(`input[name="unit_price_${itemId}"]`).value) || 0;
                const discount = parseFloat(document.querySelector(`input[name="discount_${itemId}"]`).value) || 0;
                const tax = parseFloat(document.querySelector(`input[name="tax_${itemId}"]`).value) || 0;
                
                const itemSubtotal = quantity * unitPrice;
                const itemDiscount = itemSubtotal * (discount / 100);
                const itemAfterDiscount = itemSubtotal - itemDiscount;
                const itemTax = itemAfterDiscount * (tax / 100);
                
                subtotal += itemSubtotal;
                totalDiscount += itemDiscount;
                totalTax += itemTax;
            });
            
            const grandTotal = subtotal - totalDiscount + totalTax;
            
            document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ج.م';
            document.getElementById('totalDiscount').textContent = totalDiscount.toFixed(2) + ' ج.م';
            document.getElementById('totalTax').textContent = totalTax.toFixed(2) + ' ج.م';
            document.getElementById('grandTotal').textContent = grandTotal.toFixed(2) + ' ج.م';
            document.getElementById('total_amount').value = grandTotal.toFixed(2);
        }
    </script>
</body>
</html>
