{% extends 'base.html' %}

{% block title %}{{ customer.name }} - تفاصيل العميل - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{% url 'customers:customer_list' %}">العملاء</a></li>
                <li class="breadcrumb-item active">{{ customer.name }}</li>
            </ol>
        </nav>
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">{{ customer.name }}</h1>
                <p class="page-subtitle">تفاصيل العميل ومعلومات الحساب</p>
            </div>
            <div class="btn-group">
                <a href="{% url 'customers:customer_edit' customer.pk %}" class="btn btn-primary">
                    <i class="bi bi-pencil me-2"></i>تعديل البيانات
                </a>
                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                    <i class="bi bi-trash me-2"></i>حذف العميل
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Customer Information -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                        {{ customer.name.0|upper }}
                    </div>
                    <h4 class="mb-1">{{ customer.name }}</h4>
                    <p class="text-muted mb-3">عميل #{{ customer.id }}</p>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="p-2">
                                <h5 class="mb-1">0</h5>
                                <p class="text-muted small mb-0">الفواتير</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-2">
                                <h5 class="mb-1">0 ر.س</h5>
                                <p class="text-muted small mb-0">المبيعات</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-2">
                                <h5 class="mb-1">0 ر.س</h5>
                                <p class="text-muted small mb-0">المستحقات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-lines-fill me-2"></i>معلومات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted small">رقم الهاتف</label>
                        <div>
                            {% if customer.phone %}
                                <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                    <i class="bi bi-telephone me-2"></i>{{ customer.phone }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted small">البريد الإلكتروني</label>
                        <div>
                            {% if customer.email %}
                                <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                    <i class="bi bi-envelope me-2"></i>{{ customer.email }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted small">العنوان</label>
                        <div>
                            {% if customer.address %}
                                <i class="bi bi-geo-alt me-2"></i>{{ customer.address }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div>
                        <label class="form-label text-muted small">تاريخ التسجيل</label>
                        <div>
                            <i class="bi bi-calendar me-2"></i>{{ customer.created_at|date:"Y/m/d H:i" }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Activity -->
        <div class="col-lg-8">
            <!-- Recent Invoices -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-receipt me-2"></i>الفواتير الأخيرة
                    </h5>
                    <a href="#" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-plus me-1"></i>فاتورة جديدة
                    </a>
                </div>
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="bi bi-receipt text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">لا توجد فواتير</h5>
                        <p class="text-muted">لم يتم إنشاء أي فواتير لهذا العميل بعد.</p>
                        <a href="#" class="btn btn-primary">
                            <i class="bi bi-plus me-2"></i>إنشاء أول فاتورة
                        </a>
                    </div>
                </div>
            </div>

            <!-- Customer Statistics -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-graph-up me-2"></i>إحصائيات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="border rounded p-3 text-center">
                                <i class="bi bi-receipt text-primary" style="font-size: 2rem;"></i>
                                <h4 class="mt-2 mb-1">0</h4>
                                <p class="text-muted mb-0">إجمالي الفواتير</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="border rounded p-3 text-center">
                                <i class="bi bi-currency-dollar text-success" style="font-size: 2rem;"></i>
                                <h4 class="mt-2 mb-1">0 ر.س</h4>
                                <p class="text-muted mb-0">إجمالي المبيعات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Notes -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-journal-text me-2"></i>ملاحظات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center py-3">
                        <i class="bi bi-journal text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2 mb-0">لا توجد ملاحظات لهذا العميل.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">هل أنت متأكد من حذف العميل؟</h5>
                        <p class="text-muted">
                            سيتم حذف العميل "{{ customer.name }}" نهائياً من النظام.<br>
                            هذا الإجراء لا يمكن التراجع عنه.
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <a href="{% url 'customers:customer_delete' customer.pk %}" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>حذف العميل
                    </a>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-lg {
        width: 80px;
        height: 80px;
        font-size: 2rem;
        font-weight: 700;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
</style>
{% endblock %}
