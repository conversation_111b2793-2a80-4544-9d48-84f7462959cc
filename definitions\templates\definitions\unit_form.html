{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if action == 'edit' %}
تعديل وحدة القياس - {{ unit.name }}
{% else %}
إضافة وحدة قياس جديدة
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    body {
        background: #ffffff;
        min-height: 100vh;
    }

    .form-container {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        color: #333;
    }

    .form-title {
        font-size: 2rem;
        font-weight: 800;
        color: #333;
        margin-bottom: 2rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .form-section {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-label {
        color: #333;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .required::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .form-control, .form-select {
        background: #ffffff;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 0.75rem 1rem;
        color: #333;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .form-control:focus, .form-select:focus {
        background: #ffffff;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        color: #333;
        outline: none;
    }

    .btn {
        padding: 0.75rem 2rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .btn-primary {
        background: #007bff;
        color: white;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        color: white;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e0e0e0;
    }

    .form-check {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .form-check-input {
        background: #ffffff;
        border: 1px solid #ddd;
        margin-right: 0.5rem;
    }

    .form-check-input:checked {
        background: #007bff;
        border-color: #007bff;
    }

    .form-check-label {
        color: #333;
        font-weight: 600;
    }

    .form-text {
        color: #666;
        font-size: 0.8rem;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-container">
                <h1 class="form-title">
                    <i class="bi bi-rulers"></i>
                    {% if action == 'edit' %}
                    تعديل وحدة القياس
                    {% else %}
                    إضافة وحدة قياس جديدة
                    {% endif %}
                </h1>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" id="unitForm">
                    {% csrf_token %}
                    
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            المعلومات الأساسية
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="code" class="form-label required">
                                    <i class="bi bi-hash"></i>كود وحدة القياس
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="code" 
                                       name="code" 
                                       value="{% if action == 'edit' %}{{ unit.code }}{% else %}{{ form_data.code|default:'' }}{% endif %}"
                                       placeholder="مثال: KG، M، L، PCS"
                                       maxlength="10"
                                       style="text-transform: uppercase;"
                                       required>
                                <div class="form-text">كود مختصر لوحدة القياس</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="name" class="form-label required">
                                    <i class="bi bi-rulers"></i>اسم وحدة القياس
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       value="{% if action == 'edit' %}{{ unit.name }}{% else %}{{ form_data.name|default:'' }}{% endif %}"
                                       placeholder="الاسم بالعربية"
                                       required>
                                <div class="form-text">اسم وحدة القياس بالعربية</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="name_en" class="form-label">
                                    <i class="bi bi-globe"></i>الاسم بالإنجليزية
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name_en" 
                                       name="name_en" 
                                       value="{% if action == 'edit' %}{{ unit.name_en }}{% else %}{{ form_data.name_en|default:'' }}{% endif %}"
                                       placeholder="Name in English">
                                <div class="form-text">اسم وحدة القياس بالإنجليزية (اختياري)</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="unit_type" class="form-label required">
                                    <i class="bi bi-tags"></i>نوع وحدة القياس
                                </label>
                                <select class="form-select" id="unit_type" name="unit_type" required>
                                    <option value="">-- اختر نوع وحدة القياس --</option>
                                    <option value="weight" {% if action == 'edit' and unit.unit_type == 'weight' %}selected{% elif form_data.unit_type == 'weight' %}selected{% endif %}>وزن</option>
                                    <option value="length" {% if action == 'edit' and unit.unit_type == 'length' %}selected{% elif form_data.unit_type == 'length' %}selected{% endif %}>طول</option>
                                    <option value="area" {% if action == 'edit' and unit.unit_type == 'area' %}selected{% elif form_data.unit_type == 'area' %}selected{% endif %}>مساحة</option>
                                    <option value="volume" {% if action == 'edit' and unit.unit_type == 'volume' %}selected{% elif form_data.unit_type == 'volume' %}selected{% endif %}>حجم</option>
                                    <option value="quantity" {% if action == 'edit' and unit.unit_type == 'quantity' %}selected{% elif form_data.unit_type == 'quantity' %}selected{% endif %}>كمية</option>
                                    <option value="time" {% if action == 'edit' and unit.unit_type == 'time' %}selected{% elif form_data.unit_type == 'time' %}selected{% endif %}>وقت</option>
                                    <option value="other" {% if action == 'edit' and unit.unit_type == 'other' %}selected{% elif form_data.unit_type == 'other' %}selected{% endif %}>أخرى</option>
                                </select>
                                <div class="form-text">تصنيف وحدة القياس</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="decimal_places" class="form-label">
                                    <i class="bi bi-123"></i>عدد الخانات العشرية
                                </label>
                                <select class="form-select" id="decimal_places" name="decimal_places">
                                    <option value="0" {% if action == 'edit' and unit.decimal_places == 0 %}selected{% elif form_data.decimal_places == '0' %}selected{% endif %}>0 (أعداد صحيحة)</option>
                                    <option value="1" {% if action == 'edit' and unit.decimal_places == 1 %}selected{% elif form_data.decimal_places == '1' %}selected{% endif %}>1</option>
                                    <option value="2" {% if action == 'edit' and unit.decimal_places == 2 %}selected{% elif form_data.decimal_places == '2' or not form_data.decimal_places %}selected{% endif %}>2 (افتراضي)</option>
                                    <option value="3" {% if action == 'edit' and unit.decimal_places == 3 %}selected{% elif form_data.decimal_places == '3' %}selected{% endif %}>3</option>
                                    <option value="4" {% if action == 'edit' and unit.decimal_places == 4 %}selected{% elif form_data.decimal_places == '4' %}selected{% endif %}>4</option>
                                </select>
                                <div class="form-text">دقة القياس (عدد الخانات العشرية)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-gear"></i>
                            معلومات إضافية
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="description" class="form-label">
                                    <i class="bi bi-file-text"></i>وصف وحدة القياس
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="3"
                                          placeholder="وصف تفصيلي لوحدة القياس واستخداماتها...">{% if action == 'edit' %}{{ unit.description }}{% else %}{{ form_data.description|default:'' }}{% endif %}</textarea>
                                <div class="form-text">وصف تفصيلي لوحدة القياس (اختياري)</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_base_unit" 
                                           name="is_base_unit"
                                           {% if action == 'edit' and unit.is_base_unit %}checked
                                           {% elif form_data.is_base_unit %}checked{% endif %}>
                                    <label class="form-check-label" for="is_base_unit">
                                        <i class="bi bi-star me-1"></i>وحدة أساسية
                                    </label>
                                    <div class="form-text">تحديد هذه الوحدة كوحدة أساسية لنوعها</div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active"
                                           {% if action == 'edit' and unit.is_active %}checked
                                           {% elif action != 'edit' %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        <i class="bi bi-toggle-on me-1"></i>وحدة نشطة
                                    </label>
                                    <div class="form-text">تفعيل أو إلغاء تفعيل وحدة القياس</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="notes" class="form-label">
                                    <i class="bi bi-journal-text"></i>ملاحظات
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3"
                                          placeholder="ملاحظات إضافية...">{% if action == 'edit' %}{{ unit.notes }}{% else %}{{ form_data.notes|default:'' }}{% endif %}</textarea>
                                <div class="form-text">ملاحظات إضافية (اختياري)</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <div>
                            <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary me-2">
                                <i class="bi bi-house me-2"></i>التعريفات
                            </a>
                            <a href="{% url 'definitions:unit_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-2"></i>قائمة الوحدات
                            </a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" id="submitBtn" onclick="this.disabled=true; this.innerHTML='<span class=\'spinner-border spinner-border-sm me-2\'></span>جاري الحفظ...'; this.form.submit();">
                            <i class="bi bi-check-circle me-2"></i>
                            {% if action == 'edit' %}
                            حفظ التعديلات
                            {% else %}
                            إضافة وحدة القياس
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل صفحة وحدة القياس');
    
    // تحويل الكود إلى أحرف كبيرة تلقائياً
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }
});
</script>
{% endblock %}
