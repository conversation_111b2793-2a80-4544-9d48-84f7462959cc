<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمر شراء رقم {{ order.order_number }} - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .order-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .order-title {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .order-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            display: inline-block;
        }
        
        .status-draft { background: #e2e3e5; color: #495057; }
        .status-sent { background: #cce5ff; color: #004085; }
        .status-confirmed { background: #d1ecf1; color: #0c5460; }
        .status-received { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .priority-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: 600;
            font-size: 0.8rem;
            margin-right: 10px;
        }
        
        .priority-high { background: #f8d7da; color: #721c24; }
        .priority-medium { background: #fff3cd; color: #856404; }
        .priority-low { background: #d4edda; color: #155724; }
        
        .order-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ffc107;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            font-weight: 600;
            color: #333;
        }
        
        .items-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .table th {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: white;
            border: none;
            padding: 15px;
            font-weight: 600;
        }
        
        .table td {
            padding: 15px;
            vertical-align: middle;
            border-color: #e9ecef;
        }
        
        .item-row:hover {
            background-color: #f8f9fa;
        }
        
        .totals-section {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .total-row:last-child {
            border-bottom: none;
            font-size: 1.5rem;
            font-weight: 700;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .timeline-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 15px;
        }
        
        .timeline-content h6 {
            margin: 0;
            font-weight: 600;
            color: #333;
        }
        
        .timeline-content small {
            color: #6c757d;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn-action {
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-edit {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: white;
        }
        
        .btn-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 193, 7, 0.3);
            color: white;
        }
        
        .btn-convert {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .btn-convert:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .btn-print {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            color: white;
        }
        
        .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
            color: white;
        }
        
        .btn-back {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
        }
        
        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3);
            color: white;
        }
        
        .btn-confirm {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
        }
        
        .btn-confirm:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(23, 162, 184, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-cart-plus"></i>
                        أمر شراء رقم {{ order.order_number }}
                    </h1>
                    <p class="mb-0">عرض تفصيلي لأمر الشراء ومحتوياته</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'purchases:purchase_order_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- محتوى أمر الشراء -->
        <div class="order-container">
            <!-- عنوان أمر الشراء -->
            <div class="order-title">
                <div class="order-number">أمر شراء رقم {{ order.order_number }}</div>
                <div>
                    <span class="status-badge status-{{ order.status }}">
                        {% if order.status == 'draft' %}
                            مسودة
                        {% elif order.status == 'sent' %}
                            مرسل
                        {% elif order.status == 'confirmed' %}
                            مؤكد
                        {% elif order.status == 'received' %}
                            مستلم
                        {% elif order.status == 'cancelled' %}
                            ملغي
                        {% endif %}
                    </span>
                    
                    {% if order.priority %}
                        <span class="priority-badge priority-{{ order.priority }}">
                            {% if order.priority == 'high' %}
                                أولوية عالية
                            {% elif order.priority == 'medium' %}
                                أولوية متوسطة
                            {% elif order.priority == 'low' %}
                                أولوية منخفضة
                            {% endif %}
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- معلومات أمر الشراء -->
            <div class="order-info">
                <div class="info-section">
                    <h4 class="section-title">
                        <i class="bi bi-person"></i>
                        بيانات المورد
                    </h4>
                    
                    <div class="info-row">
                        <span class="info-label">اسم المورد:</span>
                        <span class="info-value">{{ order.supplier.name }}</span>
                    </div>
                    
                    {% if order.supplier.phone %}
                        <div class="info-row">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">{{ order.supplier.phone }}</span>
                        </div>
                    {% endif %}
                    
                    {% if order.supplier.email %}
                        <div class="info-row">
                            <span class="info-label">البريد الإلكتروني:</span>
                            <span class="info-value">{{ order.supplier.email }}</span>
                        </div>
                    {% endif %}
                    
                    {% if order.supplier.address %}
                        <div class="info-row">
                            <span class="info-label">العنوان:</span>
                            <span class="info-value">{{ order.supplier.address }}</span>
                        </div>
                    {% endif %}
                </div>

                <div class="info-section">
                    <h4 class="section-title">
                        <i class="bi bi-calendar"></i>
                        تواريخ أمر الشراء
                    </h4>
                    
                    <div class="info-row">
                        <span class="info-label">تاريخ الأمر:</span>
                        <span class="info-value">{{ order.order_date|date:"Y/m/d" }}</span>
                    </div>
                    
                    {% if order.expected_delivery_date %}
                        <div class="info-row">
                            <span class="info-label">تاريخ التسليم المتوقع:</span>
                            <span class="info-value">{{ order.expected_delivery_date|date:"Y/m/d" }}</span>
                        </div>
                    {% endif %}
                    
                    <div class="info-row">
                        <span class="info-label">تاريخ الإنشاء:</span>
                        <span class="info-value">{{ order.created_at|date:"Y/m/d H:i" }}</span>
                    </div>
                    
                    {% if order.reference_number %}
                        <div class="info-row">
                            <span class="info-label">الرقم المرجعي:</span>
                            <span class="info-value">{{ order.reference_number }}</span>
                        </div>
                    {% endif %}
                </div>

                {% if order.delivery_address or order.special_instructions %}
                    <div class="info-section">
                        <h4 class="section-title">
                            <i class="bi bi-truck"></i>
                            تفاصيل التسليم
                        </h4>
                        
                        {% if order.delivery_address %}
                            <div class="info-row">
                                <span class="info-label">عنوان التسليم:</span>
                                <span class="info-value">{{ order.delivery_address }}</span>
                            </div>
                        {% endif %}
                        
                        {% if order.special_instructions %}
                            <div class="info-row">
                                <span class="info-label">تعليمات خاصة:</span>
                                <span class="info-value">{{ order.special_instructions }}</span>
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
            </div>

            <!-- أصناف أمر الشراء -->
            <div class="items-table">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="40%">المنتج</th>
                            <th width="15%">الكمية</th>
                            <th width="15%">السعر</th>
                            <th width="10%">الخصم</th>
                            <th width="15%">الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in order.items.all %}
                            <tr class="item-row">
                                <td>{{ forloop.counter }}</td>
                                <td>
                                    <strong>{{ item.product.name }}</strong><br>
                                    <small class="text-muted">كود: {{ item.product.code }}</small>
                                </td>
                                <td>{{ item.quantity }} {{ item.product.unit }}</td>
                                <td>{{ item.unit_price|floatformat:2 }} ج.م</td>
                                <td>{{ item.discount_percentage|default:0 }}%</td>
                                <td>{{ item.total_price|floatformat:2 }} ج.م</td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted py-4">
                                    لا توجد أصناف في هذا الأمر
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- الإجماليات -->
            <div class="totals-section">
                <h4 class="mb-4">
                    <i class="bi bi-calculator"></i>
                    إجماليات أمر الشراء
                </h4>
                
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>{{ order.subtotal|floatformat:2 }} ج.م</span>
                </div>
                
                <div class="total-row">
                    <span>إجمالي الخصم:</span>
                    <span>{{ order.total_discount|floatformat:2 }} ج.م</span>
                </div>
                
                <div class="total-row">
                    <span>الإجمالي النهائي:</span>
                    <span>{{ order.total_amount|floatformat:2 }} ج.م</span>
                </div>
            </div>

            <!-- تتبع حالة أمر الشراء -->
            <div class="timeline-section">
                <h4 class="section-title">
                    <i class="bi bi-clock-history"></i>
                    تتبع حالة أمر الشراء
                </h4>
                
                <div class="timeline-item">
                    <div class="timeline-icon" style="background: #28a745;">
                        <i class="bi bi-plus-circle"></i>
                    </div>
                    <div class="timeline-content">
                        <h6>تم إنشاء أمر الشراء</h6>
                        <small>{{ order.created_at|date:"Y/m/d H:i" }}</small>
                    </div>
                </div>
                
                {% if order.status != 'draft' %}
                    <div class="timeline-item">
                        <div class="timeline-icon" style="background: #17a2b8;">
                            <i class="bi bi-send"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>تم إرسال أمر الشراء</h6>
                            <small>{{ order.updated_at|date:"Y/m/d H:i" }}</small>
                        </div>
                    </div>
                {% endif %}
                
                {% if order.status == 'confirmed' or order.status == 'received' %}
                    <div class="timeline-item">
                        <div class="timeline-icon" style="background: #ffc107;">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>تم تأكيد أمر الشراء</h6>
                            <small>تم تأكيد الأمر من المورد</small>
                        </div>
                    </div>
                {% endif %}
                
                {% if order.status == 'received' %}
                    <div class="timeline-item">
                        <div class="timeline-icon" style="background: #28a745;">
                            <i class="bi bi-box-arrow-in-down"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>تم استلام البضائع</h6>
                            <small>تم استلام جميع البضائع بنجاح</small>
                        </div>
                    </div>
                {% endif %}
            </div>

            <!-- ملاحظات -->
            {% if order.notes %}
                <div class="info-section">
                    <h4 class="section-title">
                        <i class="bi bi-chat-text"></i>
                        ملاحظات
                    </h4>
                    <p class="mb-0">{{ order.notes }}</p>
                </div>
            {% endif %}
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            {% if order.status == 'draft' %}
                <a href="{% url 'purchases:purchase_order_edit' order.pk %}" class="btn-action btn-edit">
                    <i class="bi bi-pencil"></i>
                    تعديل الأمر
                </a>
                <button class="btn-action btn-confirm" onclick="confirmOrder()">
                    <i class="bi bi-send"></i>
                    إرسال الأمر
                </button>
            {% endif %}
            
            {% if order.status == 'confirmed' %}
                <a href="{% url 'purchases:purchase_invoice_create' %}?order={{ order.pk }}" class="btn-action btn-convert">
                    <i class="bi bi-receipt"></i>
                    تحويل لفاتورة
                </a>
            {% endif %}
            
            <a href="{% url 'purchases:purchase_order_print' order.pk %}" class="btn-action btn-print" target="_blank">
                <i class="bi bi-printer"></i>
                طباعة الأمر
            </a>
            
            <a href="{% url 'purchases:dashboard' %}" class="btn-action btn-back">
                <i class="bi bi-house"></i>
                لوحة التحكم
            </a>

            <a href="{% url 'purchases:purchase_order_list' %}" class="btn-action btn-back">
                <i class="bi bi-arrow-left"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.info-section, .items-table, .totals-section, .timeline-section');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(30px)';
                    section.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });

        function confirmOrder() {
            if (confirm('هل تريد إرسال أمر الشراء إلى المورد؟\n\nسيتم تغيير حالة الأمر إلى "مرسل".')) {
                // إرسال طلب تأكيد الأمر
                fetch(`/purchases/purchase-orders/{{ order.pk }}/confirm/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم إرسال أمر الشراء بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء إرسال أمر الشراء');
                    }
                });
            }
        }
    </script>
</body>
</html>
