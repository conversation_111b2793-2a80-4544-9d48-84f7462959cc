from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.http import JsonResponse
from django.core.paginator import Paginator
from datetime import datetime, timedelta
from .models import Bank, BankAccount, BankTransaction
from .forms import BankForm, BankAccountForm, BankTransactionForm, BankSearchForm

@login_required
def banks_dashboard(request):
    """لوحة تحكم البنوك"""
    # إحصائيات عامة
    total_banks = Bank.objects.filter(is_active=True).count()
    total_accounts = BankAccount.objects.filter(is_active=True).count()
    total_transactions = BankTransaction.objects.count()

    # إحصائيات مالية
    total_balance = BankAccount.objects.filter(is_active=True).aggregate(
        total=Sum('current_balance'))['total'] or 0

    # المعاملات هذا الشهر
    current_month = datetime.now().month
    current_year = datetime.now().year
    monthly_transactions = BankTransaction.objects.filter(
        transaction_date__month=current_month,
        transaction_date__year=current_year
    ).count()

    # المعاملات المعلقة
    pending_transactions = BankTransaction.objects.filter(status='pending').count()

    # الحسابات حسب النوع
    accounts_by_type = BankAccount.objects.values('account_type').annotate(count=Count('id'))

    # أحدث المعاملات
    recent_transactions = BankTransaction.objects.select_related('account', 'account__bank').order_by('-created_at')[:10]

    # الحسابات ذات الرصيد المنخفض
    from django.db import models as django_models
    low_balance_accounts = BankAccount.objects.filter(
        is_active=True,
        current_balance__lt=django_models.F('minimum_balance')
    ).count()

    context = {
        'total_banks': total_banks,
        'total_accounts': total_accounts,
        'total_transactions': total_transactions,
        'total_balance': total_balance,
        'monthly_transactions': monthly_transactions,
        'pending_transactions': pending_transactions,
        'low_balance_accounts': low_balance_accounts,
        'accounts_by_type': accounts_by_type,
        'recent_transactions': recent_transactions,
    }
    return render(request, 'banks/dashboard.html', context)

# ========== إدارة البنوك ==========
@login_required
def bank_list(request):
    """قائمة البنوك"""
    search = request.GET.get('search', '')
    banks = Bank.objects.all()

    if search:
        banks = banks.filter(
            Q(name__icontains=search) |
            Q(code__icontains=search)
        )

    paginator = Paginator(banks, 20)
    page_number = request.GET.get('page')
    banks = paginator.get_page(page_number)

    context = {
        'banks': banks,
        'search': search,
    }
    return render(request, 'banks/bank_list.html', context)

@login_required
def bank_create(request):
    """إضافة بنك جديد"""
    if request.method == 'POST':
        form = BankForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة البنك بنجاح')
            return redirect('banks:bank_list')
    else:
        form = BankForm()

    context = {'form': form, 'title': 'إضافة بنك جديد'}
    return render(request, 'banks/bank_form.html', context)

@login_required
def bank_edit(request, pk):
    """تعديل بنك"""
    bank = get_object_or_404(Bank, pk=pk)
    if request.method == 'POST':
        form = BankForm(request.POST, instance=bank)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات البنك بنجاح')
            return redirect('banks:bank_list')
    else:
        form = BankForm(instance=bank)

    context = {'form': form, 'title': 'تعديل البنك', 'bank': bank}
    return render(request, 'banks/bank_form.html', context)

@login_required
def bank_delete(request, pk):
    """حذف بنك"""
    bank = get_object_or_404(Bank, pk=pk)
    if request.method == 'POST':
        bank.delete()
        messages.success(request, 'تم حذف البنك بنجاح')
        return redirect('banks:bank_list')

    context = {'bank': bank}
    return render(request, 'banks/bank_confirm_delete.html', context)

# ========== إدارة الحسابات البنكية ==========
@login_required
def account_list(request):
    """قائمة الحسابات البنكية"""
    form = BankSearchForm(request.GET)
    accounts = BankAccount.objects.select_related('bank')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        bank = form.cleaned_data.get('bank')
        account_type = form.cleaned_data.get('account_type')
        currency = form.cleaned_data.get('currency')

        if search:
            accounts = accounts.filter(
                Q(account_name__icontains=search) |
                Q(account_number__icontains=search) |
                Q(iban__icontains=search)
            )

        if bank:
            accounts = accounts.filter(bank=bank)

        if account_type:
            accounts = accounts.filter(account_type=account_type)

        if currency:
            accounts = accounts.filter(currency=currency)

    paginator = Paginator(accounts, 20)
    page_number = request.GET.get('page')
    accounts = paginator.get_page(page_number)

    context = {
        'accounts': accounts,
        'form': form,
    }
    return render(request, 'banks/account_list.html', context)

@login_required
def account_create(request):
    """إضافة حساب بنكي جديد"""
    if request.method == 'POST':
        form = BankAccountForm(request.POST)
        if form.is_valid():
            account = form.save(commit=False)
            account.current_balance = account.opening_balance
            account.save()
            messages.success(request, 'تم إضافة الحساب البنكي بنجاح')
            return redirect('banks:account_list')
    else:
        form = BankAccountForm()

    context = {'form': form, 'title': 'إضافة حساب بنكي جديد'}
    return render(request, 'banks/account_form.html', context)

@login_required
def account_detail(request, pk):
    """تفاصيل الحساب البنكي"""
    account = get_object_or_404(BankAccount, pk=pk)
    transactions = account.transactions.order_by('-transaction_date', '-created_at')[:20]

    context = {
        'account': account,
        'transactions': transactions,
    }
    return render(request, 'banks/account_detail.html', context)

@login_required
def account_edit(request, pk):
    """تعديل حساب بنكي"""
    account = get_object_or_404(BankAccount, pk=pk)
    if request.method == 'POST':
        form = BankAccountForm(request.POST, instance=account)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات الحساب بنجاح')
            return redirect('banks:account_detail', pk=account.pk)
    else:
        form = BankAccountForm(instance=account)

    context = {'form': form, 'title': 'تعديل الحساب البنكي', 'account': account}
    return render(request, 'banks/account_form.html', context)

@login_required
def account_delete(request, pk):
    """حذف حساب بنكي"""
    account = get_object_or_404(BankAccount, pk=pk)
    if request.method == 'POST':
        account.delete()
        messages.success(request, 'تم حذف الحساب البنكي بنجاح')
        return redirect('banks:account_list')

    context = {'account': account}
    return render(request, 'banks/account_confirm_delete.html', context)
