from functools import wraps
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.db import connection
from django.shortcuts import redirect
from django.contrib import messages


def permission_required(permission_code, raise_exception=True):
    """
    Decorator لفحص الصلاحيات
    
    Args:
        permission_code: كود الصلاحية المطلوبة
        raise_exception: إذا كان True يرفع استثناء، وإلا يعيد توجيه
    
    Usage:
        @permission_required('users_view')
        def my_view(request):
            ...
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            user = request.user
            
            # المدير العام لديه جميع الصلاحيات
            if user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # فحص الصلاحية
            if has_user_permission(user, permission_code):
                return view_func(request, *args, **kwargs)
            
            # المستخدم ليس لديه الصلاحية
            if raise_exception:
                raise PermissionDenied(f"ليس لديك صلاحية للوصول إلى هذه الصفحة. الصلاحية المطلوبة: {permission_code}")
            else:
                messages.error(request, f"ليس لديك صلاحية للوصول إلى هذه الصفحة.")
                return redirect('system_settings:dashboard')
        
        return _wrapped_view
    return decorator


def has_user_permission(user, permission_code):
    """فحص ما إذا كان المستخدم لديه صلاحية معينة"""
    if not user or not user.is_authenticated:
        return False
    
    # المدير العام لديه جميع الصلاحيات
    if user.is_superuser:
        return True
    
    # فحص الصلاحيات من خلال الأدوار
    cursor = connection.cursor()
    cursor.execute("""
        SELECT COUNT(*) 
        FROM system_settings_userrole ur
        JOIN system_settings_role r ON ur.role_id = r.id
        JOIN system_settings_role_permissions rp ON r.id = rp.role_id
        JOIN system_settings_permission p ON rp.permission_id = p.id
        WHERE ur.user_id = %s 
        AND ur.is_active = 1 
        AND r.is_active = 1 
        AND p.is_active = 1 
        AND p.code = %s
    """, [user.id, permission_code])
    
    result = cursor.fetchone()
    return result[0] > 0 if result else False


def any_permission_required(*permission_codes, raise_exception=True):
    """
    Decorator لفحص أي من الصلاحيات المتعددة
    
    Args:
        permission_codes: قائمة بأكواد الصلاحيات
        raise_exception: إذا كان True يرفع استثناء، وإلا يعيد توجيه
    
    Usage:
        @any_permission_required('users_view', 'users_edit')
        def my_view(request):
            ...
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            user = request.user
            
            # المدير العام لديه جميع الصلاحيات
            if user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # فحص أي من الصلاحيات
            for permission_code in permission_codes:
                if has_user_permission(user, permission_code):
                    return view_func(request, *args, **kwargs)
            
            # المستخدم ليس لديه أي من الصلاحيات
            if raise_exception:
                raise PermissionDenied(f"ليس لديك صلاحية للوصول إلى هذه الصفحة.")
            else:
                messages.error(request, f"ليس لديك صلاحية للوصول إلى هذه الصفحة.")
                return redirect('system_settings:dashboard')
        
        return _wrapped_view
    return decorator


def all_permissions_required(*permission_codes, raise_exception=True):
    """
    Decorator لفحص جميع الصلاحيات المطلوبة
    
    Args:
        permission_codes: قائمة بأكواد الصلاحيات
        raise_exception: إذا كان True يرفع استثناء، وإلا يعيد توجيه
    
    Usage:
        @all_permissions_required('users_view', 'users_edit')
        def my_view(request):
            ...
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            user = request.user
            
            # المدير العام لديه جميع الصلاحيات
            if user.is_superuser:
                return view_func(request, *args, **kwargs)
            
            # فحص جميع الصلاحيات
            for permission_code in permission_codes:
                if not has_user_permission(user, permission_code):
                    if raise_exception:
                        raise PermissionDenied(f"ليس لديك صلاحية للوصول إلى هذه الصفحة. الصلاحية المطلوبة: {permission_code}")
                    else:
                        messages.error(request, f"ليس لديك صلاحية للوصول إلى هذه الصفحة.")
                        return redirect('system_settings:dashboard')
            
            return view_func(request, *args, **kwargs)
        
        return _wrapped_view
    return decorator
