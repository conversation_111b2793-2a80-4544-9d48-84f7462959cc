import sqlite3
import os

# الاتصال بقاعدة البيانات
db_path = os.path.join(os.getcwd(), 'db.sqlite3')

if not os.path.exists(db_path):
    print("❌ ملف قاعدة البيانات غير موجود!")
    exit(1)

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print("🔍 فحص قاعدة البيانات...")

try:
    # فحص وجود جدول system_settings_systemsettings
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_settings_systemsettings';")
    result = cursor.fetchone()
    
    if result:
        print("✅ جدول system_settings_systemsettings موجود")
        
        # فحص البيانات
        cursor.execute("SELECT COUNT(*) FROM system_settings_systemsettings;")
        count = cursor.fetchone()[0]
        print(f"📊 عدد السجلات: {count}")
        
        if count == 0:
            print("⚠️ الجدول فارغ، سيتم إدراج بيانات افتراضية...")
            cursor.execute("""
                INSERT INTO system_settings_systemsettings 
                (id, company_name, created_at, updated_at) 
                VALUES (1, 'شركة أوساريك', datetime('now'), datetime('now'))
            """)
            conn.commit()
            print("✅ تم إدراج البيانات الافتراضية")
    else:
        print("❌ جدول system_settings_systemsettings غير موجود!")
        print("🔧 إنشاء الجدول...")
        
        cursor.execute("""
            CREATE TABLE system_settings_systemsettings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_name VARCHAR(200) DEFAULT 'شركة أوساريك',
                company_logo VARCHAR(100),
                company_address TEXT,
                company_phone VARCHAR(20),
                company_email VARCHAR(254),
                company_website VARCHAR(200),
                company_tax_number VARCHAR(50),
                system_language VARCHAR(10) DEFAULT 'ar',
                system_timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
                currency_code VARCHAR(3) DEFAULT 'SAR',
                currency_symbol VARCHAR(10) DEFAULT 'ريال',
                session_timeout INTEGER DEFAULT 30,
                password_min_length INTEGER DEFAULT 8,
                require_strong_password BOOLEAN DEFAULT 1,
                max_login_attempts INTEGER DEFAULT 5,
                email_host VARCHAR(100),
                email_port INTEGER DEFAULT 587,
                email_use_tls BOOLEAN DEFAULT 1,
                backup_enabled BOOLEAN DEFAULT 1,
                backup_frequency VARCHAR(20) DEFAULT 'daily',
                theme_color VARCHAR(7) DEFAULT '#667eea',
                sidebar_collapsed BOOLEAN DEFAULT 0,
                items_per_page INTEGER DEFAULT 20,
                notifications_enabled BOOLEAN DEFAULT 1,
                email_notifications BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_by_id INTEGER
            )
        """)
        
        cursor.execute("""
            INSERT INTO system_settings_systemsettings 
            (id, company_name, created_at, updated_at) 
            VALUES (1, 'شركة أوساريك', datetime('now'), datetime('now'))
        """)
        
        conn.commit()
        print("✅ تم إنشاء الجدول وإدراج البيانات الافتراضية")
    
    # فحص جدول UserProfile
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_settings_userprofile';")
    result = cursor.fetchone()
    
    if result:
        print("✅ جدول system_settings_userprofile موجود")
    else:
        print("⚠️ جدول system_settings_userprofile غير موجود، سيتم إنشاؤه...")
        cursor.execute("""
            CREATE TABLE system_settings_userprofile (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone VARCHAR(20),
                mobile VARCHAR(20),
                address TEXT,
                birth_date DATE,
                national_id VARCHAR(20),
                employee_id VARCHAR(20) UNIQUE,
                department VARCHAR(20),
                position VARCHAR(20),
                hire_date DATE,
                avatar VARCHAR(100),
                theme_preference VARCHAR(20) DEFAULT 'light',
                is_active BOOLEAN DEFAULT 1,
                last_login_ip VARCHAR(39),
                failed_login_attempts INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                manager_id INTEGER,
                user_id INTEGER UNIQUE NOT NULL
            )
        """)
        conn.commit()
        print("✅ تم إنشاء جدول system_settings_userprofile")
    
    # فحص django_migrations
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='django_migrations';")
    result = cursor.fetchone()
    
    if result:
        cursor.execute("SELECT * FROM django_migrations WHERE app='system_settings';")
        migrations = cursor.fetchall()
        if migrations:
            print(f"✅ تم العثور على {len(migrations)} migration(s) لتطبيق system_settings")
        else:
            print("⚠️ لا توجد migrations مسجلة لتطبيق system_settings")
            cursor.execute("""
                INSERT INTO django_migrations 
                (app, name, applied) 
                VALUES ('system_settings', '0001_initial', datetime('now'))
            """)
            conn.commit()
            print("✅ تم تسجيل migration في django_migrations")
    
    print("\n🎉 فحص قاعدة البيانات مكتمل!")
    print("✅ جميع الجداول المطلوبة موجودة ومُعدة بشكل صحيح")
    
except Exception as e:
    print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
    
finally:
    conn.close()

print("\n📋 يمكنك الآن الوصول إلى:")
print("🔗 http://127.0.0.1:8000/settings/")
print("🔗 http://127.0.0.1:8000/settings/system/")
