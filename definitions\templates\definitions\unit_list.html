{% extends 'base.html' %}
{% load static %}

{% block title %}وحدات القياس - أوساريك{% endblock %}

{% block extra_css %}
<style>
    body {
        background: #ffffff;
        min-height: 100vh;
    }

    .page-header {
        background: #ffffff;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-bottom: 1px solid #e0e0e0;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 800;
        color: #333;
        margin: 0;
    }

    .page-subtitle {
        color: #666;
        margin: 0.5rem 0 0 0;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: #007bff;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }

    .table-container {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .table {
        margin: 0;
        font-size: 0.875rem;
    }

    .table th {
        background: #f8f9fa;
        border-bottom: 2px solid #e0e0e0;
        color: #333;
        font-weight: 600;
        padding: 1rem 0.75rem;
    }

    .table td {
        padding: 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #f0f0f0;
    }

    .table tbody tr:hover {
        background: #f8f9fa;
    }

    .btn {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.8rem;
    }

    .btn-primary {
        background: #007bff;
        color: white;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-danger {
        background: transparent;
        border: 1px solid #dc3545;
        color: #dc3545;
    }

    .btn-outline-danger:hover {
        background: #dc3545;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-outline-info {
        background: transparent;
        border: 1px solid #17a2b8;
        color: #17a2b8;
    }

    .btn-outline-info:hover {
        background: #17a2b8;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-outline-primary {
        background: transparent;
        border: 1px solid #007bff;
        color: #007bff;
    }

    .btn-outline-primary:hover {
        background: #007bff;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-outline-secondary {
        background: transparent;
        border: 1px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .badge {
        padding: 0.4rem 0.8rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .badge.active {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .badge.inactive {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .badge.base-unit {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .badge.weight { background: #e3f2fd; color: #1976d2; }
    .badge.length { background: #e8f5e8; color: #388e3c; }
    .badge.area { background: #fff3e0; color: #f57c00; }
    .badge.volume { background: #f3e5f5; color: #7b1fa2; }
    .badge.quantity { background: #fce4ec; color: #c2185b; }
    .badge.time { background: #e0f2f1; color: #00695c; }
    .badge.other { background: #f5f5f5; color: #616161; }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #666;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #ccc;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- CSRF Token for JavaScript -->
    {% csrf_token %}
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="bi bi-rulers me-2"></i>وحدات القياس
                </h1>
                <p class="page-subtitle">إدارة وحدات القياس المستخدمة في النظام</p>
            </div>
            <div>
                <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-right me-2"></i>عودة للتعريفات
                </a>
                <a href="{% url 'definitions:unit_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة وحدة قياس
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-number">{{ total_units }}</div>
            <div class="stat-label">إجمالي الوحدات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ active_units }}</div>
            <div class="stat-label">الوحدات النشطة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ base_units }}</div>
            <div class="stat-label">الوحدات الأساسية</div>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Units Table -->
    <div class="table-container">
        {% if units %}
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الكود</th>
                        <th>الاسم</th>
                        <th>النوع</th>
                        <th>الخانات العشرية</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for unit in units %}
                        <tr>
                            <td>
                                <strong>{{ unit.code }}</strong>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ unit.name }}</strong>
                                    {% if unit.name_en %}
                                        <br><small class="text-muted">{{ unit.name_en }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge {{ unit.unit_type }}">
                                    {{ unit.get_unit_type_display }}
                                </span>
                                {% if unit.is_base_unit %}
                                    <br><span class="badge base-unit mt-1">
                                        <i class="bi bi-star me-1"></i>وحدة أساسية
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                {{ unit.decimal_places }}
                            </td>
                            <td>
                                <span class="badge {% if unit.is_active %}active{% else %}inactive{% endif %}">
                                    {% if unit.is_active %}
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    {% else %}
                                        <i class="bi bi-x-circle me-1"></i>غير نشط
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    <a href="{% url 'definitions:unit_detail' unit.id %}"
                                       class="btn btn-sm btn-outline-info"
                                       title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'definitions:unit_edit' unit.id %}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-sm btn-outline-danger"
                                            title="حذف"
                                            onclick="deleteUnit({{ unit.id }}, '{{ unit.name|escapejs }}')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="empty-state">
                <i class="bi bi-rulers"></i>
                <h3>لا توجد وحدات قياس</h3>
                <p>لم يتم العثور على وحدات قياس مطابقة للبحث.</p>
                <a href="{% url 'definitions:unit_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة أول وحدة قياس
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
function deleteUnit(unitId, unitName) {
    if (confirm('هل أنت متأكد من حذف وحدة القياس "' + unitName + '"؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        // إنشاء نموذج مخفي وإرساله
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/definitions/units/' + unitId + '/quick-delete/';

        // إضافة CSRF token
        var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        var csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // إضافة النموذج للصفحة وإرساله
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
