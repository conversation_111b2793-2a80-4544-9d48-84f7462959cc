<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .search-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #f093fb;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .product-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .product-code {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }
        
        .product-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .price-display {
            font-size: 1.2rem;
            font-weight: 700;
            color: #f093fb;
        }
        
        .stock-display {
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .stock-normal { color: #28a745; }
        .stock-low { color: #ffc107; }
        .stock-out { color: #dc3545; }
        
        .btn-create {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(240, 147, 251, 0.3);
            color: white;
        }
        
        .category-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-bottom: 10px;
            display: inline-block;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-box-seam"></i>
                        إدارة المنتجات
                    </h1>
                    <p class="mb-0">إدارة كتالوج المنتجات والمخزون</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'sales:product_create' %}" class="btn btn-create">
                        <i class="bi bi-plus-circle"></i>
                        إضافة منتج جديد
                    </a>
                    <a href="{% url 'sales:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>

        <!-- قسم البحث -->
        <div class="search-section">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="اسم المنتج أو الكود" value="{{ search }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-select">
                        <option value="">جميع الفئات</option>
                        {% for cat in categories %}
                            <option value="{{ cat }}" {% if category == cat %}selected{% endif %}>
                                {{ cat }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">حالة المخزون</label>
                    <select name="stock_status" class="form-select">
                        <option value="">جميع المنتجات</option>
                        <option value="low" {% if stock_status == 'low' %}selected{% endif %}>مخزون منخفض</option>
                        <option value="out" {% if stock_status == 'out' %}selected{% endif %}>نفد المخزون</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- قائمة المنتجات -->
        {% if products %}
            <div class="row">
                {% for product in products %}
                    <div class="col-lg-4 col-md-6">
                        <div class="product-card">
                            {% if product.category %}
                                <div class="category-badge">{{ product.category }}</div>
                            {% endif %}
                            
                            <div class="product-name">{{ product.name }}</div>
                            <div class="product-code">كود: {{ product.code }}</div>
                            
                            <div class="product-details">
                                <div>
                                    <div class="price-display">{{ product.unit_price_retail }} ج.م</div>
                                    <small class="text-muted">سعر التجزئة</small>
                                </div>
                                <div class="text-end">
                                    <div class="stock-display {% if product.stock_quantity == 0 %}stock-out{% elif product.stock_quantity <= product.min_stock_level %}stock-low{% else %}stock-normal{% endif %}">
                                        {{ product.stock_quantity }} {{ product.unit }}
                                    </div>
                                    <small class="text-muted">المخزون المتاح</small>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <small class="text-muted">الحد الأدنى: {{ product.min_stock_level }}</small>
                                </div>
                                <div>
                                    <a href="{% url 'sales:product_detail' product.pk %}" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-eye"></i>
                                        عرض
                                    </a>
                                    <a href="{% url 'sales:product_edit' product.pk %}" class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-pencil"></i>
                                        تعديل
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- الترقيم -->
            {% if products.has_other_pages %}
                <nav aria-label="ترقيم الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if products.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ products.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if stock_status %}&stock_status={{ stock_status }}{% endif %}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}

                        {% for num in products.paginator.page_range %}
                            {% if products.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > products.number|add:'-3' and num < products.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if stock_status %}&stock_status={{ stock_status }}{% endif %}">
                                        {{ num }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if products.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ products.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if category %}&category={{ category }}{% endif %}{% if stock_status %}&stock_status={{ stock_status }}{% endif %}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <i class="bi bi-box-seam"></i>
                <h3>لا توجد منتجات</h3>
                <p>لم يتم العثور على منتجات تطابق معايير البحث</p>
                <a href="{% url 'sales:product_create' %}" class="btn btn-create">
                    <i class="bi bi-plus-circle"></i>
                    إضافة أول منتج
                </a>
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.product-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 50);
            });
        });
    </script>
</body>
</html>
