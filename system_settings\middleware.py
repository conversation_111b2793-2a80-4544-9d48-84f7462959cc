from django.utils import translation
from django.utils.deprecation import MiddlewareMixin
from .models import SystemSettings


class LanguageMiddleware(MiddlewareMixin):
    """
    Middleware لتطبيق لغة النظام تلقائياً
    """
    
    def process_request(self, request):
        """تطبيق لغة النظام على كل طلب"""
        try:
            # الحصول على إعدادات النظام
            settings = SystemSettings.get_settings()
            
            # تطبيق اللغة المحددة في الإعدادات
            if settings and settings.system_language:
                # التحقق من وجود لغة محددة في الجلسة
                session_language = request.session.get('django_language')
                
                if session_language:
                    # استخدام لغة الجلسة إذا كانت موجودة
                    translation.activate(session_language)
                else:
                    # استخدام لغة النظام الافتراضية
                    translation.activate(settings.system_language)
                    request.session['django_language'] = settings.system_language
                
                # تعيين اللغة للطلب الحالي
                request.LANGUAGE_CODE = translation.get_language()
        
        except Exception as e:
            # في حالة حدوث خطأ، استخدام اللغة الافتراضية
            translation.activate('ar')
            request.LANGUAGE_CODE = 'ar'


class SystemSettingsMiddleware(MiddlewareMixin):
    """
    Middleware لتطبيق إعدادات النظام على جميع الطلبات
    """
    
    def process_request(self, request):
        """إضافة إعدادات النظام إلى السياق"""
        try:
            # الحصول على إعدادات النظام
            settings = SystemSettings.get_settings()
            
            # إضافة الإعدادات إلى الطلب
            request.system_settings = settings
            
            # تطبيق إعدادات الجلسة
            if settings:
                # تطبيق مهلة انتهاء الجلسة
                if settings.session_timeout:
                    request.session.set_expiry(settings.session_timeout * 60)
                
                # تطبيق إعدادات الأمان
                if hasattr(request, 'user') and request.user.is_authenticated:
                    # فحص انتهاء كلمة المرور (يمكن تطبيقه لاحقاً)
                    pass
        
        except Exception as e:
            # في حالة حدوث خطأ، تعيين إعدادات افتراضية
            request.system_settings = None


class ThemeMiddleware(MiddlewareMixin):
    """
    Middleware لتطبيق إعدادات الواجهة والألوان
    """
    
    def process_request(self, request):
        """تطبيق إعدادات الواجهة"""
        try:
            # الحصول على إعدادات النظام
            settings = SystemSettings.get_settings()
            
            if settings:
                # إضافة متغيرات CSS للألوان
                request.theme_vars = {
                    'primary_color': settings.theme_color or '#667eea',
                    'secondary_color': getattr(settings, 'secondary_color', '#764ba2'),
                    'theme_mode': getattr(settings, 'theme_mode', 'light'),
                    'enable_animations': getattr(settings, 'enable_animations', True),
                    'compact_mode': getattr(settings, 'compact_mode', False),
                }
            else:
                # قيم افتراضية
                request.theme_vars = {
                    'primary_color': '#667eea',
                    'secondary_color': '#764ba2',
                    'theme_mode': 'light',
                    'enable_animations': True,
                    'compact_mode': False,
                }
        
        except Exception as e:
            # قيم افتراضية في حالة الخطأ
            request.theme_vars = {
                'primary_color': '#667eea',
                'secondary_color': '#764ba2',
                'theme_mode': 'light',
                'enable_animations': True,
                'compact_mode': False,
            }


class SecurityMiddleware(MiddlewareMixin):
    """
    Middleware لتطبيق إعدادات الأمان
    """
    
    def process_request(self, request):
        """تطبيق إعدادات الأمان"""
        try:
            # الحصول على إعدادات النظام
            settings = SystemSettings.get_settings()
            
            if settings and hasattr(request, 'user') and request.user.is_authenticated:
                # فحص محاولات تسجيل الدخول الفاشلة
                max_attempts = getattr(settings, 'max_login_attempts', 5)
                lockout_duration = getattr(settings, 'lockout_duration', 15)
                
                # يمكن إضافة منطق فحص محاولات تسجيل الدخول هنا
                
                # فحص تفعيل المصادقة الثنائية
                require_2fa = getattr(settings, 'require_2fa', False)
                if require_2fa:
                    # يمكن إضافة منطق المصادقة الثنائية هنا
                    pass
                
                # فحص تسجيل الخروج التلقائي
                auto_logout = getattr(settings, 'auto_logout_inactive', True)
                if auto_logout:
                    # تحديث وقت آخر نشاط
                    request.session['last_activity'] = request.session.get('last_activity')
        
        except Exception as e:
            # تجاهل الأخطاء في middleware الأمان
            pass


class UserActivityMiddleware(MiddlewareMixin):
    """Middleware لتتبع نشاط المستخدمين وحالة الاتصال"""

    def process_request(self, request):
        """تحديث نشاط المستخدم"""
        if hasattr(request, 'user') and request.user.is_authenticated:
            self.update_user_activity(request.user)

    def update_user_activity(self, user):
        """تحديث آخر نشاط للمستخدم"""
        try:
            from django.utils import timezone
            from .models import UserProfile

            profile, created = UserProfile.objects.get_or_create(user=user)
            profile.last_activity = timezone.now()
            profile.is_online = True
            profile.save(update_fields=['last_activity', 'is_online'])
        except Exception:
            # تجاهل الأخطاء لتجنب تعطيل الموقع
            pass


class UserOnlineStatusMiddleware(MiddlewareMixin):
    """Middleware لتحديث حالة الاتصال للمستخدمين"""

    def process_response(self, request, response):
        """تحديث حالة المستخدمين غير المتصلين"""
        # تحديث حالة المستخدمين غير المتصلين كل 10 طلبات لتوفير الأداء
        import random
        if random.randint(1, 10) == 1:
            self.update_offline_users()

        return response

    def update_offline_users(self):
        """تحديث حالة المستخدمين الذين لم يعودوا متصلين"""
        try:
            from django.utils import timezone
            from datetime import timedelta
            from .models import UserProfile

            # تحديد المستخدمين الذين لم يكونوا نشطين لأكثر من 5 دقائق
            offline_threshold = timezone.now() - timedelta(minutes=5)

            UserProfile.objects.filter(
                is_online=True,
                last_activity__lt=offline_threshold
            ).update(is_online=False)

        except Exception:
            # تجاهل الأخطاء
            pass
