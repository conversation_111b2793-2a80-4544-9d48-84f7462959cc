{% extends 'base.html' %}
{% load static %}

{% block title %}سجل عمليات تحويل المخزون{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .history-container {
        max-width: 1400px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .filters-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .history-table {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .form-control, .form-select {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #218838, #1e7e34);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 15px;
        padding: 1.5rem;
        color: white;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.1);
        transform: scale(1.01);
        transition: all 0.2s ease;
    }

    .transfer-badge {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .warehouse-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .warehouse-from {
        color: #dc3545;
        font-weight: 600;
    }

    .warehouse-to {
        color: #28a745;
        font-weight: 600;
    }

    .transfer-arrow {
        color: #6c757d;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="history-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1><i class="bi bi-arrow-left-right me-2"></i>سجل عمليات تحويل المخزون</h1>
        <p class="mb-0">مراجعة جميع عمليات التحويل بين المخازن</p>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-value">{{ total_transfers }}</div>
            <div class="stat-label">إجمالي عمليات التحويل</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ total_quantity|floatformat:1 }}</div>
            <div class="stat-label">إجمالي الكمية المحولة</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ total_value|floatformat:1 }} ج.م</div>
            <div class="stat-label">إجمالي قيمة التحويلات</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ transfer_transactions|length }}</div>
            <div class="stat-label">النتائج المعروضة</div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <h5 class="mb-3"><i class="bi bi-funnel me-2"></i>فلاتر البحث</h5>
        <form method="get" class="row">
            <div class="col-md-2">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="ابحث في المنتج، المخزن، الرقم المرجعي..." 
                       value="{{ search_query }}">
            </div>
            
            <div class="col-md-2">
                <label class="form-label">من مخزن</label>
                <select name="from_warehouse" class="form-select">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if selected_from_warehouse == warehouse.id|stringformat:"s" %}selected{% endif %}>
                        {{ warehouse.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">إلى مخزن</label>
                <select name="to_warehouse" class="form-select">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if selected_to_warehouse == warehouse.id|stringformat:"s" %}selected{% endif %}>
                        {{ warehouse.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">المنتج</label>
                <select name="product" class="form-select">
                    <option value="">جميع المنتجات</option>
                    {% for product in products %}
                    <option value="{{ product.id }}" {% if selected_product == product.id|stringformat:"s" %}selected{% endif %}>
                        {{ product.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            
            <div class="col-12 text-center mt-3">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search me-2"></i>بحث
                </button>
                <a href="{% url 'warehouses:transfer_history' %}" class="btn btn-secondary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>إعادة تعيين
                </a>
                <a href="{% url 'warehouses:transfer_stock' %}" class="btn btn-success me-2">
                    <i class="bi bi-plus-circle me-2"></i>تحويل جديد
                </a>
                <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-info me-2">
                    <i class="bi bi-boxes me-2"></i>المخزون
                </a>
                <a href="{% url 'warehouses:dashboard' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-2"></i>لوحة تحكم المخازن
                </a>
            </div>
        </form>
    </div>

    <!-- Transfer History Table -->
    <div class="history-table">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>سجل عمليات التحويل</h5>
            <div class="text-muted">
                عرض {{ transfer_transactions|length }} من أصل {{ total_transfers }} عملية تحويل
            </div>
        </div>

        {% if transfer_transactions %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>التاريخ والوقت</th>
                        <th>رقم التحويل</th>
                        <th>المنتج</th>
                        <th>التحويل</th>
                        <th>الكمية</th>
                        <th>القيمة</th>
                        <th>المستخدم</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transfer_transactions %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ transaction.created_at|date:"Y-m-d" }}</strong>
                                <br><small class="text-muted">{{ transaction.created_at|time:"H:i" }}</small>
                            </div>
                        </td>
                        <td>
                            <span class="transfer-badge">{{ transaction.reference_number }}</span>
                        </td>
                        <td>
                            <div>
                                <strong>{{ transaction.inventory_item.product.name }}</strong>
                                <br><small class="text-muted">{{ transaction.inventory_item.product.code }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="warehouse-info">
                                <span class="warehouse-from">{{ transaction.inventory_item.warehouse.name }}</span>
                                <i class="bi bi-arrow-right transfer-arrow"></i>
                                <span class="warehouse-to">
                                    {% if transaction.notes %}
                                        {% if "إلى مخزن" in transaction.notes %}
                                            {{ transaction.notes|cut:"تحويل مخزون إلى مخزن "|cut:"." }}
                                        {% else %}
                                            {{ transaction.notes|truncatechars:20 }}
                                        {% endif %}
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </span>
                            </div>
                        </td>
                        <td>
                            <strong>{{ transaction.quantity|floatformat:1 }}</strong>
                        </td>
                        <td>
                            <strong>{{ transaction.total_cost|floatformat:1 }} ج.م</strong>
                        </td>
                        <td>
                            <div>
                                <strong>{{ transaction.created_by.get_full_name|default:transaction.created_by.username }}</strong>
                                <br><small class="text-muted">{{ transaction.created_at|date:"Y-m-d H:i" }}</small>
                            </div>
                        </td>
                        <td>
                            {% if transaction.notes %}
                                <span class="text-muted">{{ transaction.notes|truncatechars:50 }}</span>
                            {% else %}
                                <span class="text-muted">لا توجد</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-state">
            <i class="bi bi-arrow-left-right"></i>
            <h4>لا توجد عمليات تحويل</h4>
            <p>لم يتم العثور على عمليات تحويل تطابق معايير البحث المحددة.</p>
            <a href="{% url 'warehouses:transfer_stock' %}" class="btn btn-success">
                <i class="bi bi-plus-circle me-2"></i>إجراء تحويل جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
