from django.core.management.base import BaseCommand
from system_settings.models import Permission, Role


class Command(BaseCommand):
    help = 'إنشاء الصلاحيات والأدوار الأساسية'

    def handle(self, *args, **options):
        self.stdout.write('بدء إنشاء الصلاحيات والأدوار...')
        
        # إنشاء الصلاحيات
        self.create_permissions()
        
        # إنشاء الأدوار الأساسية
        self.create_basic_roles()
        
        self.stdout.write(
            self.style.SUCCESS('تم إنشاء الصلاحيات والأدوار بنجاح!')
        )

    def create_permissions(self):
        """إنشاء الصلاحيات الأساسية"""
        
        categories = [
            ('users', 'إدارة المستخدمين'),
            ('products', 'إدارة المنتجات'),
            ('sales', 'المبيعات'),
            ('purchases', 'المشتريات'),
            ('warehouses', 'إدارة المخازن'),
            ('accounting', 'المحاسبة'),
            ('reports', 'التقارير'),
            ('settings', 'الإعدادات'),
            ('hr', 'شؤون العاملين'),
            ('assets', 'الأصول الثابتة'),
            ('banks', 'البنوك'),
            ('treasuries', 'الخزائن'),
            ('branches', 'الفروع'),
            ('customers', 'العملاء'),
            ('suppliers', 'الموردين'),
        ]
        
        actions = [
            ('view', 'عرض'),
            ('add', 'إضافة'),
            ('edit', 'تعديل'),
            ('delete', 'حذف'),
            ('approve', 'اعتماد'),
            ('export', 'تصدير'),
            ('import', 'استيراد'),
            ('print', 'طباعة'),
        ]
        
        for category_code, category_name in categories:
            for action_code, action_name in actions:
                permission_code = f"{category_code}_{action_code}"
                permission_name = f"{action_name} {category_name}"
                
                permission, created = Permission.objects.get_or_create(
                    code=permission_code,
                    defaults={
                        'name': permission_name,
                        'category': category_code,
                        'action': action_code,
                        'description': f"صلاحية {action_name} في {category_name}",
                    }
                )
                
                if created:
                    self.stdout.write(f'تم إنشاء الصلاحية: {permission_name}')

    def create_basic_roles(self):
        """إنشاء الأدوار الأساسية"""
        
        # دور المدير العام
        admin_role, created = Role.objects.get_or_create(
            code='admin',
            defaults={
                'name': 'مدير عام',
                'description': 'مدير عام لديه جميع الصلاحيات',
            }
        )
        
        if created:
            # إعطاء جميع الصلاحيات للمدير العام
            admin_role.permissions.set(Permission.objects.all())
            self.stdout.write('تم إنشاء دور المدير العام')
        
        # دور مدير المبيعات
        sales_manager_role, created = Role.objects.get_or_create(
            code='sales_manager',
            defaults={
                'name': 'مدير المبيعات',
                'description': 'مدير المبيعات والعملاء',
            }
        )
        
        if created:
            # صلاحيات مدير المبيعات
            sales_permissions = Permission.objects.filter(
                category__in=['sales', 'customers', 'products', 'reports']
            )
            sales_manager_role.permissions.set(sales_permissions)
            self.stdout.write('تم إنشاء دور مدير المبيعات')
        
        # دور مدير المشتريات
        purchase_manager_role, created = Role.objects.get_or_create(
            code='purchase_manager',
            defaults={
                'name': 'مدير المشتريات',
                'description': 'مدير المشتريات والموردين',
            }
        )
        
        if created:
            # صلاحيات مدير المشتريات
            purchase_permissions = Permission.objects.filter(
                category__in=['purchases', 'suppliers', 'products', 'warehouses']
            )
            purchase_manager_role.permissions.set(purchase_permissions)
            self.stdout.write('تم إنشاء دور مدير المشتريات')
        
        # دور المحاسب
        accountant_role, created = Role.objects.get_or_create(
            code='accountant',
            defaults={
                'name': 'محاسب',
                'description': 'محاسب مالي',
            }
        )
        
        if created:
            # صلاحيات المحاسب
            accounting_permissions = Permission.objects.filter(
                category__in=['accounting', 'banks', 'treasuries', 'reports']
            )
            accountant_role.permissions.set(accounting_permissions)
            self.stdout.write('تم إنشاء دور المحاسب')
        
        # دور أمين المخزن
        warehouse_keeper_role, created = Role.objects.get_or_create(
            code='warehouse_keeper',
            defaults={
                'name': 'أمين المخزن',
                'description': 'أمين المخزن ومسؤول المخازن',
            }
        )
        
        if created:
            # صلاحيات أمين المخزن
            warehouse_permissions = Permission.objects.filter(
                category__in=['warehouses', 'products']
            ).exclude(action='delete')  # منع الحذف
            warehouse_keeper_role.permissions.set(warehouse_permissions)
            self.stdout.write('تم إنشاء دور أمين المخزن')
        
        # دور موظف المبيعات
        sales_employee_role, created = Role.objects.get_or_create(
            code='sales_employee',
            defaults={
                'name': 'موظف مبيعات',
                'description': 'موظف مبيعات',
            }
        )
        
        if created:
            # صلاحيات موظف المبيعات (عرض وإضافة فقط)
            sales_employee_permissions = Permission.objects.filter(
                category__in=['sales', 'customers', 'products'],
                action__in=['view', 'add']
            )
            sales_employee_role.permissions.set(sales_employee_permissions)
            self.stdout.write('تم إنشاء دور موظف المبيعات')
        
        # دور مدير الموارد البشرية
        hr_manager_role, created = Role.objects.get_or_create(
            code='hr_manager',
            defaults={
                'name': 'مدير الموارد البشرية',
                'description': 'مدير شؤون العاملين والموارد البشرية',
            }
        )
        
        if created:
            # صلاحيات مدير الموارد البشرية
            hr_permissions = Permission.objects.filter(
                category__in=['hr', 'users', 'reports']
            )
            hr_manager_role.permissions.set(hr_permissions)
            self.stdout.write('تم إنشاء دور مدير الموارد البشرية')
