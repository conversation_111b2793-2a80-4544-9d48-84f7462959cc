# Generated by Django 5.2.4 on 2025-07-20 22:36

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='unit_price',
        ),
        migrations.AddField(
            model_name='customer',
            name='credit_days',
            field=models.IntegerField(default=30, verbose_name='أيام الائتمان'),
        ),
        migrations.AddField(
            model_name='customer',
            name='customer_type',
            field=models.CharField(choices=[('wholesale', 'جملة'), ('retail', 'تجزئة'), ('credit', 'أجل')], default='retail', max_length=20, verbose_name='نوع العميل'),
        ),
        migrations.AddField(
            model_name='product',
            name='barcode',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='الباركود'),
        ),
        migrations.AddField(
            model_name='product',
            name='category',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='الفئة'),
        ),
        migrations.AddField(
            model_name='product',
            name='max_stock_level',
            field=models.IntegerField(default=1000, verbose_name='الحد الأقصى للمخزون'),
        ),
        migrations.AddField(
            model_name='product',
            name='min_stock_level',
            field=models.IntegerField(default=10, verbose_name='الحد الأدنى للمخزون'),
        ),
        migrations.AddField(
            model_name='product',
            name='unit_price_retail',
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10, verbose_name='سعر التجزئة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='product',
            name='unit_price_wholesale',
            field=models.DecimalField(decimal_places=2, default=0.0, max_digits=10, verbose_name='سعر الجملة'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='discount_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='مبلغ الخصم'),
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='invoice_type',
            field=models.CharField(choices=[('wholesale', 'جملة'), ('retail', 'تجزئة'), ('credit', 'أجل')], default='retail', max_length=20, verbose_name='نوع الفاتورة'),
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='is_printed',
            field=models.BooleanField(default=False, verbose_name='تم الطباعة'),
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='payment_method',
            field=models.CharField(choices=[('cash', 'نقدي'), ('credit', 'آجل'), ('card', 'بطاقة'), ('transfer', 'تحويل')], default='cash', max_length=20, verbose_name='طريقة الدفع'),
        ),
        migrations.CreateModel(
            name='DispensePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('permission_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإذن')),
                ('warehouse', models.CharField(max_length=100, verbose_name='المخزن')),
                ('dispense_date', models.DateField(verbose_name='تاريخ الصرف')),
                ('purpose', models.CharField(max_length=200, verbose_name='الغرض من الصرف')),
                ('total_value', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='القيمة الإجمالية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_dispenses', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'إذن صرف',
                'verbose_name_plural': 'أذونات الصرف',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DispensePermissionItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='تكلفة الوحدة')),
                ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.dispensepermission', verbose_name='إذن الصرف')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر إذن صرف',
                'verbose_name_plural': 'عناصر أذونات الصرف',
            },
        ),
        migrations.CreateModel(
            name='Inventory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('inventory_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الجرد')),
                ('inventory_type', models.CharField(choices=[('daily', 'يومي'), ('monthly', 'شهري'), ('annual', 'سنوي')], max_length=20, verbose_name='نوع الجرد')),
                ('inventory_date', models.DateField(verbose_name='تاريخ الجرد')),
                ('warehouse', models.CharField(blank=True, max_length=100, null=True, verbose_name='المخزن')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('total_value', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='القيمة الإجمالية')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_inventories', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'جرد',
                'verbose_name_plural': 'الجرد',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InventoryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('system_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الكمية النظامية')),
                ('actual_quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية الفعلية')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='تكلفة الوحدة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('inventory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.inventory', verbose_name='الجرد')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر جرد',
                'verbose_name_plural': 'عناصر الجرد',
            },
        ),
        migrations.CreateModel(
            name='SalesRepresentative',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='رقم الموظف')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('hire_date', models.DateField(verbose_name='تاريخ التوظيف')),
                ('commission_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة العمولة')),
                ('target_monthly', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='الهدف الشهري')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'مندوب مبيعات',
                'verbose_name_plural': 'مناديب المبيعات',
                'ordering': ['user__first_name', 'user__last_name'],
            },
        ),
        migrations.CreateModel(
            name='ProductMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('sale', 'بيع'), ('loading', 'تحميل'), ('dispense', 'صرف'), ('return', 'مرتجع'), ('adjustment', 'تسوية')], max_length=20, verbose_name='نوع الحركة')),
                ('movement_date', models.DateField(verbose_name='تاريخ الحركة')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('reference_number', models.CharField(max_length=50, verbose_name='رقم المرجع')),
                ('reference_type', models.CharField(max_length=50, verbose_name='نوع المرجع')),
                ('warehouse', models.CharField(blank=True, max_length=100, null=True, verbose_name='المخزن')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.product', verbose_name='المنتج')),
                ('representative', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesrepresentative', verbose_name='المندوب')),
            ],
            options={
                'verbose_name': 'حركة صنف',
                'verbose_name_plural': 'حركة الأصناف',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المدفوعة')),
                ('payment_type', models.CharField(choices=[('collection', 'تحصيل'), ('payment', 'دفع')], max_length=20, verbose_name='نوع المدفوعة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('card', 'بطاقة'), ('transfer', 'تحويل'), ('check', 'شيك')], max_length=20, verbose_name='طريقة الدفع')),
                ('payment_date', models.DateField(verbose_name='تاريخ المدفوعة')),
                ('reference_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='sales.customer', verbose_name='العميل')),
                ('representative', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesrepresentative', verbose_name='المندوب')),
            ],
            options={
                'verbose_name': 'مدفوعة',
                'verbose_name_plural': 'المدفوعات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='inventory',
            name='representative',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesrepresentative', verbose_name='المندوب'),
        ),
        migrations.AddField(
            model_name='dispensepermission',
            name='representative',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.salesrepresentative', verbose_name='المندوب'),
        ),
        migrations.AddField(
            model_name='customer',
            name='assigned_representative',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesrepresentative', verbose_name='المندوب المخصص'),
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='representative',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesrepresentative', verbose_name='المندوب'),
        ),
        migrations.AddField(
            model_name='salesorder',
            name='representative',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesrepresentative', verbose_name='المندوب'),
        ),
        migrations.CreateModel(
            name='SalesReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('return_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المرتجع')),
                ('return_date', models.DateField(verbose_name='تاريخ المرتجع')),
                ('reason', models.CharField(choices=[('damaged', 'تالف'), ('expired', 'منتهي الصلاحية'), ('wrong_item', 'صنف خاطئ'), ('customer_request', 'طلب العميل'), ('quality_issue', 'مشكلة في الجودة'), ('other', 'أخرى')], max_length=20, verbose_name='سبب المرتجع')),
                ('reason_details', models.TextField(blank=True, null=True, verbose_name='تفاصيل السبب')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي المبلغ')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_returns', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.customer', verbose_name='العميل')),
                ('original_invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.salesinvoice', verbose_name='الفاتورة الأصلية')),
                ('representative', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.salesrepresentative', verbose_name='المندوب')),
            ],
            options={
                'verbose_name': 'مرتجع مبيعات',
                'verbose_name_plural': 'مرتجعات المبيعات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SalesReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية المرتجعة')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('condition', models.CharField(choices=[('good', 'جيد'), ('damaged', 'تالف'), ('expired', 'منتهي الصلاحية')], default='good', max_length=20, verbose_name='حالة المنتج')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.product', verbose_name='المنتج')),
                ('return_record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.salesreturn', verbose_name='المرتجع')),
            ],
            options={
                'verbose_name': 'عنصر مرتجع',
                'verbose_name_plural': 'عناصر المرتجعات',
            },
        ),
        migrations.CreateModel(
            name='Vehicle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plate_number', models.CharField(max_length=20, unique=True, verbose_name='رقم اللوحة')),
                ('vehicle_type', models.CharField(choices=[('truck', 'شاحنة'), ('van', 'فان'), ('pickup', 'بيك أب'), ('car', 'سيارة')], max_length=20, verbose_name='نوع السيارة')),
                ('brand', models.CharField(max_length=50, verbose_name='الماركة')),
                ('model', models.CharField(max_length=50, verbose_name='الموديل')),
                ('year', models.IntegerField(verbose_name='سنة الصنع')),
                ('capacity', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='السعة (كيلو)')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('assigned_representative', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesrepresentative', verbose_name='المندوب المخصص')),
            ],
            options={
                'verbose_name': 'سيارة',
                'verbose_name_plural': 'السيارات',
                'ordering': ['plate_number'],
            },
        ),
        migrations.AddField(
            model_name='inventory',
            name='vehicle',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.vehicle', verbose_name='السيارة'),
        ),
        migrations.AddField(
            model_name='dispensepermission',
            name='vehicle',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.vehicle', verbose_name='السيارة'),
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='vehicle',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.vehicle', verbose_name='السيارة'),
        ),
        migrations.CreateModel(
            name='VehicleLoading',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('loading_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحميل')),
                ('loading_date', models.DateField(verbose_name='تاريخ التحميل')),
                ('warehouse', models.CharField(max_length=100, verbose_name='المخزن')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('total_weight', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الوزن الإجمالي')),
                ('total_value', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='القيمة الإجمالية')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('loaded', 'تم التحميل'), ('delivered', 'تم التسليم'), ('returned', 'مرتجع')], default='pending', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('representative', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.salesrepresentative', verbose_name='المندوب')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.vehicle', verbose_name='السيارة')),
            ],
            options={
                'verbose_name': 'تحميل سيارة',
                'verbose_name_plural': 'تحميل السيارات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VehicleLoadingItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('total_weight', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الوزن الإجمالي')),
                ('loading', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.vehicleloading', verbose_name='التحميل')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر تحميل',
                'verbose_name_plural': 'عناصر التحميل',
            },
        ),
        migrations.CreateModel(
            name='DailyMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_date', models.DateField(verbose_name='تاريخ الحركة')),
                ('opening_cash', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='رصيد افتتاحي')),
                ('total_sales', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي المبيعات')),
                ('total_collections', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي التحصيلات')),
                ('total_returns', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي المرتجعات')),
                ('expenses', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='المصروفات')),
                ('closing_cash', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='رصيد ختامي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_closed', models.BooleanField(default=False, verbose_name='مغلقة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('representative', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.salesrepresentative', verbose_name='المندوب')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.vehicle', verbose_name='السيارة')),
            ],
            options={
                'verbose_name': 'حركة يومية',
                'verbose_name_plural': 'الحركات اليومية',
                'ordering': ['-movement_date'],
                'unique_together': {('representative', 'movement_date')},
            },
        ),
    ]
