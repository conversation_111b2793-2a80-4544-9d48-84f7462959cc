{% extends 'base.html' %}

{% block title %}إدارة المخزون - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'warehouses:dashboard' %}">المخازن</a></li>
                    <li class="breadcrumb-item active">إدارة المخزون</li>
                </ol>
            </nav>
            <h1 class="page-title">إدارة المخزون</h1>
            <p class="page-subtitle">عرض ومتابعة مخزون المنتجات في جميع المخازن</p>
        </div>
        <div class="d-flex gap-2">
            <a href="#" class="btn btn-success">
                <i class="bi bi-plus-circle me-2"></i>إضافة مخزون
            </a>
            <a href="#" class="btn btn-info">
                <i class="bi bi-arrow-left-right me-2"></i>تحويل مخزون
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}" 
                               placeholder="البحث في المنتجات...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="warehouse" class="form-select">
                        <option value="">جميع المخازن</option>
                        {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}" {% if warehouse_filter == warehouse.id|stringformat:"s" %}selected{% endif %}>
                                {{ warehouse.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="low_stock" value="1" 
                               {% if low_stock_only %}checked{% endif %} id="lowStockCheck">
                        <label class="form-check-label" for="lowStockCheck">
                            المخزون المنخفض فقط
                        </label>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search"></i>
                        </button>
                        <a href="{% url 'warehouses:stock_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Stock Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">قائمة المخزون</h5>
            <span class="badge bg-primary">{{ page_obj.paginator.count }} منتج</span>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>المنتج</th>
                                <th>المخزن</th>
                                <th>الموقع</th>
                                <th>الكمية المتاحة</th>
                                <th>الكمية المحجوزة</th>
                                <th>الحد الأدنى</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stock in page_obj %}
                                <tr {% if stock.is_low_stock %}class="table-warning"{% elif stock.is_out_of_stock %}class="table-danger"{% endif %}>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="product-icon bg-primary text-white rounded me-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <i class="bi bi-box"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ stock.product.name }}</h6>
                                                <small class="text-muted">{{ stock.product.code }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ stock.warehouse.name }}</span>
                                    </td>
                                    <td>
                                        {% if stock.location %}
                                            <small class="text-muted">{{ stock.location.location_code }}</small>
                                        {% else %}
                                            <small class="text-muted">غير محدد</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>{{ stock.available_quantity }}</strong>
                                        <small class="text-muted">{{ stock.product.unit.symbol }}</small>
                                    </td>
                                    <td>
                                        {% if stock.reserved_quantity > 0 %}
                                            <span class="text-warning">{{ stock.reserved_quantity }}</span>
                                        {% else %}
                                            <span class="text-muted">0</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ stock.minimum_stock }}</span>
                                    </td>
                                    <td>
                                        {% if stock.is_out_of_stock %}
                                            <span class="badge bg-danger">منتهي</span>
                                        {% elif stock.is_low_stock %}
                                            <span class="badge bg-warning">منخفض</span>
                                        {% else %}
                                            <span class="badge bg-success">طبيعي</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="تعديل المخزون">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-outline-info" title="حركة المخزون">
                                                <i class="bi bi-arrow-left-right"></i>
                                            </button>
                                            <button class="btn btn-outline-success" title="تحويل">
                                                <i class="bi bi-truck"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="صفحات المخزون" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}{% if low_stock_only %}&low_stock=1{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}{% if low_stock_only %}&low_stock=1{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}{% if low_stock_only %}&low_stock=1{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}{% if low_stock_only %}&low_stock=1{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-boxes text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا يوجد مخزون</h4>
                    <p class="text-muted">لم يتم العثور على أي منتجات مطابقة لمعايير البحث.</p>
                    <a href="#" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة مخزون جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .table-responsive {
        border-radius: 0.375rem;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .product-icon {
        font-size: 1rem;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when checkbox changes
        const lowStockCheck = document.getElementById('lowStockCheck');
        if (lowStockCheck) {
            lowStockCheck.addEventListener('change', function() {
                this.form.submit();
            });
        }
        
        // Warehouse filter change
        const warehouseSelect = document.querySelector('select[name="warehouse"]');
        if (warehouseSelect) {
            warehouseSelect.addEventListener('change', function() {
                this.form.submit();
            });
        }
    });
</script>
{% endblock %}
