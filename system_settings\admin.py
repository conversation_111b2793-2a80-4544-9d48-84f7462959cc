from django.contrib import admin
from .models import SystemSettings, UserProfile


@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    list_display = ['company_name', 'system_language', 'currency_code', 'updated_at']
    fieldsets = (
        ('معلومات الشركة', {
            'fields': ('company_name', 'company_logo', 'company_address', 'company_phone', 'company_email', 'company_website', 'company_tax_number')
        }),
        ('إعدادات النظام', {
            'fields': ('system_language', 'system_timezone', 'currency_code', 'currency_symbol')
        }),
        ('إعدادات الأمان', {
            'fields': ('session_timeout', 'password_min_length', 'require_strong_password', 'max_login_attempts')
        }),
        ('إعدادات البريد', {
            'fields': ('email_host', 'email_port', 'email_use_tls')
        }),
        ('إعدادات النسخ الاحتياطي', {
            'fields': ('backup_enabled', 'backup_frequency')
        }),
        ('إعدادات الواجهة', {
            'fields': ('theme_color', 'sidebar_collapsed', 'items_per_page')
        }),
        ('إعدادات الإشعارات', {
            'fields': ('notifications_enabled', 'email_notifications')
        }),
    )


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'department', 'position', 'employee_id', 'is_active']
    list_filter = ['department', 'position', 'is_active']
    search_fields = ['user__username', 'user__first_name', 'user__last_name', 'employee_id']
    fieldsets = (
        ('معلومات المستخدم', {
            'fields': ('user',)
        }),
        ('معلومات شخصية', {
            'fields': ('phone', 'mobile', 'address', 'birth_date', 'national_id')
        }),
        ('معلومات وظيفية', {
            'fields': ('employee_id', 'department', 'position', 'hire_date', 'manager')
        }),
        ('إعدادات النظام', {
            'fields': ('avatar', 'theme_preference')
        }),
        ('حالة الحساب', {
            'fields': ('is_active', 'last_login_ip', 'failed_login_attempts')
        }),
    )
