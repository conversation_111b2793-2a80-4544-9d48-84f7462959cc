from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from manufacturing.models import ManufacturingOrder, ManufacturingInventoryTransaction
from warehouses.models import InventoryItem
from decimal import Decimal
from django.utils import timezone

class Command(BaseCommand):
    help = 'إنشاء حركات المخزون المفقودة لأوامر التصنيع المكتملة'

    def handle(self, *args, **options):
        self.stdout.write('بدء إنشاء حركات المخزون المفقودة...')
        
        # البحث عن أوامر التصنيع المكتملة
        completed_orders = ManufacturingOrder.objects.filter(status='completed')
        
        self.stdout.write(f'تم العثور على {completed_orders.count()} أمر تصنيع مكتمل')
        
        created_transactions = 0
        updated_inventory = 0
        
        for order in completed_orders:
            # التحقق من وجود حركة إنتاج للمنتج التام
            existing_transaction = ManufacturingInventoryTransaction.objects.filter(
                manufacturing_order=order,
                transaction_type='finished_goods_production'
            ).first()
            
            if not existing_transaction:
                self.stdout.write(f"إنشاء حركة مخزون مفقودة لأمر: {order.order_number}")
                
                # البحث عن أو إنشاء عنصر المخزون
                inventory_item, created = InventoryItem.objects.get_or_create(
                    warehouse=order.finished_goods_warehouse,
                    product=order.final_product,
                    defaults={
                        'quantity_on_hand': 0,
                        'quantity_reserved': 0,
                        'minimum_stock': 0,
                        'maximum_stock': 0,
                        'average_cost': order.final_product.cost_price or Decimal('0'),
                        'last_cost': order.final_product.cost_price or Decimal('0'),
                        'total_value': Decimal('0'),
                        'is_active': True
                    }
                )
                
                if created:
                    self.stdout.write(f"  تم إنشاء عنصر مخزون جديد")
                
                # حساب تكلفة الوحدة
                unit_cost = Decimal('0')
                if order.total_actual_cost > 0 and order.quantity_to_produce > 0:
                    unit_cost = order.total_actual_cost / order.quantity_to_produce
                
                # إنشاء حركة المخزون
                transaction = ManufacturingInventoryTransaction.objects.create(
                    manufacturing_order=order,
                    transaction_type='finished_goods_production',
                    product=order.final_product,
                    warehouse=order.finished_goods_warehouse,
                    quantity=order.quantity_to_produce,
                    unit_of_measure=order.unit_of_measure,
                    unit_cost=unit_cost,
                    total_cost=order.total_actual_cost,
                    reference_number=order.order_number,
                    notes=f'إنتاج منتج تام من أمر التصنيع {order.order_number}',
                    is_processed=True,
                    processed_at=order.actual_completion_date or timezone.now(),
                    processed_by=order.completed_by or User.objects.first(),
                    created_by=order.completed_by or User.objects.first()
                )
                
                created_transactions += 1
                self.stdout.write(f"  تم إنشاء حركة المخزون - تكلفة الوحدة: {unit_cost}")
                
                # تحديث المخزون
                # إضافة الكمية للمخزون (إذا لم تكن مضافة بالفعل)
                current_quantity = inventory_item.quantity_on_hand
                
                # تحديث متوسط التكلفة
                if unit_cost > 0:
                    if current_quantity > 0:
                        # حساب متوسط التكلفة الجديد
                        total_value_before = current_quantity * inventory_item.average_cost
                        new_total_value = total_value_before + order.total_actual_cost
                        new_total_quantity = current_quantity + order.quantity_to_produce
                        inventory_item.average_cost = new_total_value / new_total_quantity
                    else:
                        inventory_item.average_cost = unit_cost
                    
                    inventory_item.last_cost = unit_cost
                    inventory_item.quantity_on_hand += order.quantity_to_produce
                    inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
                    inventory_item.save()
                    
                    updated_inventory += 1
                    self.stdout.write(f"  تم تحديث المخزون:")
                    self.stdout.write(f"    الكمية الجديدة: {inventory_item.quantity_on_hand}")
                    self.stdout.write(f"    متوسط التكلفة: {inventory_item.average_cost}")
                    self.stdout.write(f"    إجمالي القيمة: {inventory_item.total_value}")
                
                self.stdout.write("-" * 50)
            else:
                self.stdout.write(f"حركة المخزون موجودة بالفعل لأمر: {order.order_number}")
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {created_transactions} حركة مخزون وتحديث {updated_inventory} عنصر مخزون!')
        )
