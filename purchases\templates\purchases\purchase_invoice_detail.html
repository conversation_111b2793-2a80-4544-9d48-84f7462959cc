{% extends 'base.html' %}

{% block title %}تفاصيل فاتورة الشراء {{ invoice.invoice_number }} - نظام أوساريك{% endblock %}

{% block extra_css %}
    <style>
        .page-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .invoice-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 10px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 150px;
        }

        /* نظام المدفوعات المتقدم */
        .payment-summary {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .payment-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            display: inline-block;
        }

        .status-paid { background: #d4edda; color: #155724; }
        .status-partial { background: #fff3cd; color: #856404; }
        .status-unpaid { background: #f8d7da; color: #721c24; }
        .status-overdue {
            background: #f5c6cb;
            color: #721c24;
            animation: pulse 2s infinite;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.05); }
            100% { opacity: 1; transform: scale(1); }
        }

        .amount-display {
            font-size: 1.3rem;
            font-weight: 700;
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .amount-total { background: rgba(255,255,255,0.2); }
        .amount-paid { background: rgba(255,255,255,0.15); }
        .amount-remaining {
            background: rgba(255,255,255,0.1);
            border-color: #ffc107;
            color: #ffc107;
        }

        .payment-item {
            background: linear-gradient(145deg, #f8f9fa, #ffffff);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .payment-item:hover {
            background: linear-gradient(145deg, #e3f2fd, #f8f9fa);
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
        }

        .payment-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 2px 0 0 2px;
        }

        .btn-payment {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-payment:hover {
            background: linear-gradient(135deg, #218838, #1e7e34);
            transform: translateY(-2px);
            color: white;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .alert-payment {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            animation: glow 3s infinite;
        }

        @keyframes glow {
            0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.3); }
            50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.6); }
            100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.3); }
        }
        
        .info-value {
            color: #333;
            flex: 1;
            text-align: left;
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
        }
        
        .status-received {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .status-paid {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }
        
        .status-overdue {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .status-cancelled {
            background: linear-gradient(45deg, #6c757d, #5a6268);
            color: white;
        }
        
        .items-table {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .table {
            border-radius: 15px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 15px;
            font-weight: 600;
        }
        
        .table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
        }
        
        .total-section {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        
        .total-row:last-child {
            margin-bottom: 0;
            font-size: 1.3rem;
            font-weight: 700;
            border-top: 2px solid rgba(255, 255, 255, 0.3);
            padding-top: 20px;
            margin-top: 20px;
        }
        
        .action-buttons {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .btn-action {
            padding: 12px 25px;
            border-radius: 25px;
            border: none;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            text-decoration: none;
        }
        
        .btn-edit {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: white;
        }
        
        .btn-edit:hover {
            color: white;
            box-shadow: 0 6px 12px rgba(255, 193, 7, 0.3);
        }
        
        .btn-print {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            color: white;
        }
        
        .btn-print:hover {
            color: white;
            box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
        }
        
        .btn-delete {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .btn-delete:hover {
            color: white;
            box-shadow: 0 6px 12px rgba(220, 53, 69, 0.3);
        }
        
        .btn-back {
            background: linear-gradient(45deg, #6c757d, #5a6268);
            color: white;
        }
        
        .btn-back:hover {
            color: white;
            box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3);
        }
        
        .supplier-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .supplier-name {
            font-size: 1.2rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 10px;
        }
        
        .supplier-details {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-receipt"></i>
                        فاتورة الشراء {{ invoice.invoice_number }}
                    </h1>
                    <p class="mb-0">تفاصيل فاتورة الشراء من {{ invoice.supplier.name }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'purchases:purchase_invoice_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- عرض الرسائل -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- ملخص المدفوعات المتقدم -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="payment-summary">
                    <h4 class="text-center mb-4">
                        <i class="bi bi-cash-stack"></i>
                        ملخص المدفوعات
                    </h4>

                    <div class="amount-display amount-total">
                        <div>إجمالي الفاتورة</div>
                        <div>{{ invoice.total_amount|floatformat:2 }} ج.م</div>
                    </div>

                    <div class="amount-display amount-paid">
                        <div>المبلغ المدفوع</div>
                        <div>{{ invoice.paid_amount|floatformat:2 }} ج.م</div>
                    </div>

                    <div class="amount-display amount-remaining">
                        <div>المبلغ المتبقي</div>
                        <div>{{ invoice.remaining_amount|floatformat:2 }} ج.م</div>
                    </div>

                    <!-- حالة الدفع -->
                    <div class="text-center mt-3">
                        {% if invoice.remaining_amount <= 0 %}
                            <span class="payment-status status-paid">
                                <i class="bi bi-check-circle"></i> مدفوعة بالكامل
                            </span>
                        {% elif invoice.paid_amount > 0 %}
                            <span class="payment-status status-partial">
                                <i class="bi bi-clock"></i> مدفوعة جزئياً
                            </span>
                        {% elif invoice.due_date and invoice.due_date < today %}
                            <span class="payment-status status-overdue">
                                <i class="bi bi-exclamation-triangle"></i> متأخرة
                            </span>
                        {% else %}
                            <span class="payment-status status-unpaid">
                                <i class="bi bi-x-circle"></i> غير مدفوعة
                            </span>
                        {% endif %}
                    </div>

                    {% if invoice.remaining_amount > 0 %}
                    <div class="text-center mt-4">
                        <button class="btn btn-payment" data-bs-toggle="modal" data-bs-target="#paymentModal">
                            <i class="bi bi-plus-circle"></i>
                            إضافة دفعة جديدة
                        </button>
                    </div>
                    {% endif %}
                </div>

                <!-- تنبيهات المدفوعات -->
                {% if invoice.due_date and invoice.due_date < today and invoice.remaining_amount > 0 %}
                <div class="alert-payment">
                    <h6><i class="bi bi-exclamation-triangle"></i> تنبيه مهم</h6>
                    <p class="mb-2">هذه الفاتورة متأخرة عن موعد الاستحقاق!</p>
                    <small>تاريخ الاستحقاق: {{ invoice.due_date|date:"d/m/Y" }}</small>
                </div>
                {% endif %}
            </div>

            <div class="col-md-8">
                <!-- سجل المدفوعات -->
                <div class="invoice-section">
                    <h3 class="section-title">
                        <i class="bi bi-clock-history"></i>
                        سجل المدفوعات
                    </h3>

                    {% if payments %}
                        {% for payment in payments %}
                        <div class="payment-item">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <strong>{{ payment.payment_date|date:"d/m/Y" }}</strong>
                                    <br><small class="text-muted">{{ payment.payment_date|date:"H:i" }}</small>
                                </div>
                                <div class="col-md-2">
                                    <span class="badge bg-success fs-6">{{ payment.total_amount|floatformat:2 }} ج.م</span>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">{{ payment.get_payment_method_display }}</small>
                                    {% if payment.reference_number %}
                                        <br><small>مرجع: {{ payment.reference_number }}</small>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    {% if payment.notes %}
                                        <small class="text-muted">{{ payment.notes }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-inbox display-4"></i>
                            <p class="mt-3">لا توجد مدفوعات بعد</p>
                            {% if invoice.remaining_amount > 0 %}
                                <button class="btn btn-payment mt-2" data-bs-toggle="modal" data-bs-target="#paymentModal">
                                    إضافة أول دفعة
                                </button>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="row">
            <!-- معلومات الفاتورة -->
            <div class="col-md-8">
                <div class="invoice-section">
                    <h3 class="section-title">معلومات الفاتورة</h3>
                    
                    <div class="info-row">
                        <span class="info-label">رقم الفاتورة:</span>
                        <span class="info-value"><strong>{{ invoice.invoice_number }}</strong></span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">تاريخ الفاتورة:</span>
                        <span class="info-value">{{ invoice.invoice_date|date:"d/m/Y" }}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">تاريخ الاستحقاق:</span>
                        <span class="info-value">
                            {% if invoice.due_date %}
                                {{ invoice.due_date|date:"d/m/Y" }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">الحالة:</span>
                        <span class="info-value">
                            {% if invoice.status == 'received' %}
                                <span class="status-badge status-received">مستلمة</span>
                            {% elif invoice.status == 'paid' %}
                                <span class="status-badge status-paid">مدفوعة</span>
                            {% elif invoice.status == 'overdue' %}
                                <span class="status-badge status-overdue">متأخرة</span>
                            {% elif invoice.status == 'cancelled' %}
                                <span class="status-badge status-cancelled">ملغية</span>
                            {% endif %}
                        </span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">تاريخ الإنشاء:</span>
                        <span class="info-value">{{ invoice.created_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    
                    <div class="info-row">
                        <span class="info-label">آخر تحديث:</span>
                        <span class="info-value">{{ invoice.updated_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    
                    {% if invoice.notes %}
                        <div class="info-row">
                            <span class="info-label">ملاحظات:</span>
                            <span class="info-value">{{ invoice.notes }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- معلومات المورد -->
            <div class="col-md-4">
                <div class="invoice-section">
                    <h3 class="section-title">معلومات المورد</h3>
                    
                    <div class="supplier-info">
                        <div class="supplier-name">{{ invoice.supplier.name }}</div>
                        <div class="supplier-details">
                            {% if invoice.supplier.phone %}
                                <div><i class="bi bi-telephone"></i> {{ invoice.supplier.phone }}</div>
                            {% endif %}
                            {% if invoice.supplier.email %}
                                <div><i class="bi bi-envelope"></i> {{ invoice.supplier.email }}</div>
                            {% endif %}
                            {% if invoice.supplier.address %}
                                <div><i class="bi bi-geo-alt"></i> {{ invoice.supplier.address }}</div>
                            {% endif %}
                            {% if invoice.supplier.payment_terms %}
                                <div><i class="bi bi-credit-card"></i> {{ invoice.supplier.payment_terms }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أصناف الفاتورة -->
        <div class="items-table">
            <h3 class="section-title">أصناف الفاتورة</h3>
            
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>المخزن</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>الخصم</th>
                            <th>الإجمالي</th>
                            <th>حالة المخزون</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in invoice.items.all %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ item.product.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ item.product.code }}</small>
                                    </div>
                                </td>
                                <td>
                                    {% if item.warehouse %}
                                        <strong>{{ item.warehouse.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ item.warehouse.code }}</small>
                                    {% else %}
                                        <span class="text-warning">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>{{ item.quantity }} {{ item.product.unit }}</td>
                                <td>{{ item.unit_price|floatformat:2 }} ج.م</td>
                                <td>{{ item.discount_percentage|floatformat:1 }}%</td>
                                <td><strong>{{ item.total_price|floatformat:2 }} ج.م</strong></td>
                                <td>
                                    {% if item.applied_to_stock %}
                                        <span class="status-badge status-paid">
                                            <i class="bi bi-check-circle"></i>
                                            تم التطبيق
                                        </span>
                                        <br>
                                        <small class="text-muted">{{ item.applied_at|date:"d/m/Y H:i" }}</small>
                                    {% else %}
                                        <span class="status-badge status-overdue">
                                            <i class="bi bi-clock"></i>
                                            في الانتظار
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="bi bi-box" style="font-size: 2rem;"></i>
                                        <p class="mt-2">لا توجد أصناف في هذه الفاتورة</p>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- إجمالي الفاتورة -->
            <div class="total-section">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>{{ invoice.subtotal|floatformat:2 }} ج.م</span>
                </div>
                <div class="total-row">
                    <span>إجمالي الخصم:</span>
                    <span>{{ invoice.total_discount|floatformat:2 }} ج.م</span>
                </div>
                <div class="total-row">
                    <span>الضريبة ({{ invoice.tax_percentage|floatformat:1 }}%):</span>
                    <span>{{ invoice.tax_amount|floatformat:2 }} ج.م</span>
                </div>
                <div class="total-row">
                    <span>الإجمالي النهائي:</span>
                    <span>{{ invoice.total_amount|floatformat:2 }} ج.م</span>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            {% if not all_items_applied %}
                <button type="button" class="btn-action" style="background: linear-gradient(45deg, #28a745, #20c997); color: white;"
                        onclick="applyToStock('{{ invoice.pk }}')">
                    <i class="bi bi-box-arrow-in-down"></i>
                    تطبيق على المخزون
                </button>
            {% endif %}

            <a href="{% url 'purchases:purchase_invoice_edit' invoice.pk %}" class="btn-action btn-edit">
                <i class="bi bi-pencil"></i>
                تعديل الفاتورة
            </a>

            <a href="{% url 'purchases:purchase_invoice_print' invoice.pk %}" class="btn-action btn-print">
                <i class="bi bi-printer"></i>
                طباعة الفاتورة
            </a>

            <button type="button" class="btn-action btn-delete"
                    onclick="confirmDelete('{{ invoice.invoice_number }}', '{% url 'purchases:purchase_invoice_delete' invoice.pk %}')">
                <i class="bi bi-trash"></i>
                حذف الفاتورة
            </button>

            <a href="{% url 'purchases:dashboard' %}" class="btn-action btn-back">
                <i class="bi bi-house"></i>
                لوحة التحكم
            </a>

            <a href="{% url 'purchases:purchase_invoice_list' %}" class="btn-action btn-back">
                <i class="bi bi-arrow-left"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        function confirmDelete(invoiceNumber, deleteUrl) {
            if (confirm(`هل أنت متأكد من حذف فاتورة الشراء "${invoiceNumber}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                window.location.href = deleteUrl;
            }
        }

        function applyToStock(invoiceId) {
            if (confirm('هل أنت متأكد من تطبيق هذه الفاتورة على المخزون؟\n\nسيتم إضافة الكميات إلى المخازن المحددة وتسجيل حركات المخزون.\n\nهذا الإجراء لا يمكن التراجع عنه.')) {
                // إظهار مؤشر التحميل
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="bi bi-hourglass-split"></i> جاري التطبيق...';
                button.disabled = true;

                // إرسال طلب AJAX
                fetch(`/purchases/purchase-invoices/${invoiceId}/apply-to-stock/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم تطبيق الفاتورة على المخزون بنجاح!');
                        location.reload();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال');
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            }
        }
    </script>

    <!-- Modal إضافة دفعة جديدة -->
    <div class="modal fade" id="paymentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle"></i>
                        إضافة دفعة جديدة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="post" action="{% url 'purchases:add_payment' invoice.pk %}">
                    {% csrf_token %}
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إجمالي الفاتورة</label>
                                    <input type="text" class="form-control" value="{{ invoice.total_amount|floatformat:2 }} ج.م" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ المدفوع سابقاً</label>
                                    <input type="text" class="form-control" value="{{ invoice.paid_amount|floatformat:2 }} ج.م" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-warning">
                                    <strong>المبلغ المتبقي: {{ invoice.remaining_amount|floatformat:2 }} ج.م</strong>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">مبلغ الدفعة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" name="amount" class="form-control" step="0.01"
                                               max="{{ invoice.remaining_amount }}" required>
                                        <span class="input-group-text">ج.م</span>
                                    </div>
                                    <small class="text-muted">الحد الأقصى: {{ invoice.remaining_amount|floatformat:2 }} ج.م</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الدفع</label>
                                    <input type="date" name="payment_date" class="form-control" value="{{ today|date:'Y-m-d' }}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                    <select name="payment_method" class="form-select" required>
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="cash">نقدي</option>
                                        <option value="bank_transfer">تحويل بنكي</option>
                                        <option value="check">شيك</option>
                                        <option value="credit_card">بطاقة ائتمان</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم المرجع</label>
                                    <input type="text" name="reference_number" class="form-control"
                                           placeholder="رقم الشيك، رقم التحويل، إلخ">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="3"
                                      placeholder="ملاحظات إضافية حول الدفعة"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i>
                            حفظ الدفعة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- إضافة CSRF token للـ AJAX -->
    {% csrf_token %}
{% endblock %}
