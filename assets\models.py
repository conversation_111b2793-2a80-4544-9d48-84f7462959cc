from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal
from datetime import date

class AssetCategory(models.Model):
    """نموذج فئات الأصول"""
    name = models.CharField(max_length=100, verbose_name="اسم الفئة")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    depreciation_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="معدل الاستهلاك السنوي (%)")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "فئة أصل"
        verbose_name_plural = "فئات الأصول"
        ordering = ['name']

    def __str__(self):
        return self.name

class Asset(models.Model):
    """نموذج الأصول الثابتة"""
    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('maintenance', 'تحت الصيانة'),
        ('disposed', 'تم التخلص منه'),
        ('sold', 'تم البيع'),
    ]

    CONDITION_CHOICES = [
        ('excellent', 'ممتاز'),
        ('good', 'جيد'),
        ('fair', 'مقبول'),
        ('poor', 'ضعيف'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم الأصل")
    asset_code = models.CharField(max_length=50, unique=True, verbose_name="كود الأصل")
    category = models.ForeignKey(AssetCategory, on_delete=models.CASCADE, verbose_name="الفئة")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")

    # معلومات الشراء
    purchase_date = models.DateField(verbose_name="تاريخ الشراء")
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="سعر الشراء")
    supplier = models.CharField(max_length=200, blank=True, null=True, verbose_name="المورد")

    # معلومات الاستهلاك
    useful_life_years = models.IntegerField(default=5, verbose_name="العمر الافتراضي (سنوات)")
    salvage_value = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="القيمة المتبقية")

    # الحالة والموقع
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    condition = models.CharField(max_length=20, choices=CONDITION_CHOICES, default='excellent', verbose_name="حالة الأصل")
    location = models.CharField(max_length=200, blank=True, null=True, verbose_name="الموقع")

    # معلومات إضافية
    serial_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="الرقم التسلسلي")
    model = models.CharField(max_length=100, blank=True, null=True, verbose_name="الموديل")
    manufacturer = models.CharField(max_length=100, blank=True, null=True, verbose_name="الشركة المصنعة")

    # المسؤول
    responsible_person = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المسؤول")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "أصل ثابت"
        verbose_name_plural = "الأصول الثابتة"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.asset_code})"

    @property
    def current_book_value(self):
        """حساب القيمة الدفترية الحالية"""
        years_passed = (date.today() - self.purchase_date).days / 365.25
        if years_passed >= self.useful_life_years:
            return self.salvage_value

        annual_depreciation = (self.purchase_price - self.salvage_value) / self.useful_life_years
        total_depreciation = annual_depreciation * years_passed
        return max(self.purchase_price - total_depreciation, self.salvage_value)

    @property
    def accumulated_depreciation(self):
        """حساب الاستهلاك المتراكم"""
        return self.purchase_price - self.current_book_value

    @property
    def annual_depreciation(self):
        """حساب الاستهلاك السنوي"""
        return (self.purchase_price - self.salvage_value) / self.useful_life_years

class AssetMaintenance(models.Model):
    """نموذج صيانة الأصول"""
    MAINTENANCE_TYPE_CHOICES = [
        ('preventive', 'صيانة وقائية'),
        ('corrective', 'صيانة إصلاحية'),
        ('emergency', 'صيانة طارئة'),
    ]

    asset = models.ForeignKey(Asset, related_name='maintenances', on_delete=models.CASCADE, verbose_name="الأصل")
    maintenance_type = models.CharField(max_length=20, choices=MAINTENANCE_TYPE_CHOICES, verbose_name="نوع الصيانة")
    description = models.TextField(verbose_name="وصف الصيانة")
    maintenance_date = models.DateField(verbose_name="تاريخ الصيانة")
    cost = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="تكلفة الصيانة")
    vendor = models.CharField(max_length=200, blank=True, null=True, verbose_name="مقدم الخدمة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="تم بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "صيانة أصل"
        verbose_name_plural = "صيانة الأصول"
        ordering = ['-maintenance_date']

    def __str__(self):
        return f"صيانة {self.asset.name} - {self.maintenance_date}"

class AssetTransfer(models.Model):
    """نموذج تحويل الأصول"""
    asset = models.ForeignKey(Asset, related_name='transfers', on_delete=models.CASCADE, verbose_name="الأصل")
    from_location = models.CharField(max_length=200, verbose_name="من الموقع")
    to_location = models.CharField(max_length=200, verbose_name="إلى الموقع")
    from_person = models.ForeignKey(User, related_name='asset_transfers_from', on_delete=models.SET_NULL, null=True, blank=True, verbose_name="من المسؤول")
    to_person = models.ForeignKey(User, related_name='asset_transfers_to', on_delete=models.SET_NULL, null=True, blank=True, verbose_name="إلى المسؤول")
    transfer_date = models.DateField(verbose_name="تاريخ التحويل")
    reason = models.TextField(verbose_name="سبب التحويل")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    approved_by = models.ForeignKey(User, related_name='asset_transfers_approved', on_delete=models.CASCADE, verbose_name="تمت الموافقة بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "تحويل أصل"
        verbose_name_plural = "تحويلات الأصول"
        ordering = ['-transfer_date']

    def __str__(self):
        return f"تحويل {self.asset.name} - {self.transfer_date}"

class AssetDisposal(models.Model):
    """نموذج التخلص من الأصول"""
    DISPOSAL_TYPE_CHOICES = [
        ('sale', 'بيع'),
        ('scrap', 'خردة'),
        ('donation', 'تبرع'),
        ('trade', 'مقايضة'),
    ]

    asset = models.OneToOneField(Asset, on_delete=models.CASCADE, verbose_name="الأصل")
    disposal_type = models.CharField(max_length=20, choices=DISPOSAL_TYPE_CHOICES, verbose_name="نوع التخلص")
    disposal_date = models.DateField(verbose_name="تاريخ التخلص")
    disposal_value = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="قيمة التخلص")
    buyer_details = models.TextField(blank=True, null=True, verbose_name="تفاصيل المشتري")
    reason = models.TextField(verbose_name="سبب التخلص")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    approved_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="تمت الموافقة بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "التخلص من أصل"
        verbose_name_plural = "التخلص من الأصول"
        ordering = ['-disposal_date']

    def __str__(self):
        return f"التخلص من {self.asset.name} - {self.disposal_date}"

    @property
    def gain_loss(self):
        """حساب الربح أو الخسارة من التخلص"""
        return self.disposal_value - self.asset.current_book_value
