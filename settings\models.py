from django.db import models
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.core.exceptions import ValidationError
import json


class SystemSettings(models.Model):
    """إعدادات النظام الشاملة"""
    
    # معلومات الشركة
    company_name = models.CharField(
        max_length=200,
        default='شركة أوساريك',
        verbose_name="اسم الشركة"
    )
    
    company_logo = models.ImageField(
        upload_to='company/',
        null=True,
        blank=True,
        verbose_name="شعار الشركة"
    )
    
    company_address = models.TextField(
        blank=True,
        verbose_name="عنوان الشركة"
    )
    
    company_phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="هاتف الشركة"
    )
    
    company_email = models.EmailField(
        blank=True,
        verbose_name="بريد الشركة"
    )
    
    company_website = models.URLField(
        blank=True,
        verbose_name="موقع الشركة"
    )
    
    company_tax_number = models.CharField(
        max_length=50,
        blank=True,
        verbose_name="الرقم الضريبي"
    )
    
    # إعدادات النظام العامة
    system_language = models.CharField(
        max_length=10,
        choices=[
            ('ar', 'العربية'),
            ('en', 'English'),
        ],
        default='ar',
        verbose_name="لغة النظام"
    )
    
    system_timezone = models.CharField(
        max_length=50,
        default='Asia/Riyadh',
        verbose_name="المنطقة الزمنية"
    )
    
    currency_code = models.CharField(
        max_length=3,
        default='SAR',
        verbose_name="رمز العملة"
    )
    
    currency_symbol = models.CharField(
        max_length=10,
        default='ريال',
        verbose_name="رمز العملة"
    )
    
    # إعدادات الأمان
    session_timeout = models.IntegerField(
        default=30,
        validators=[MinValueValidator(5), MaxValueValidator(480)],
        verbose_name="انتهاء الجلسة (دقيقة)"
    )
    
    password_min_length = models.IntegerField(
        default=8,
        validators=[MinValueValidator(6), MaxValueValidator(20)],
        verbose_name="الحد الأدنى لطول كلمة المرور"
    )
    
    require_strong_password = models.BooleanField(
        default=True,
        verbose_name="كلمة مرور قوية مطلوبة"
    )
    
    max_login_attempts = models.IntegerField(
        default=5,
        validators=[MinValueValidator(3), MaxValueValidator(10)],
        verbose_name="عدد محاولات تسجيل الدخول"
    )
    
    # إعدادات البريد الإلكتروني
    email_host = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="خادم البريد"
    )
    
    email_port = models.IntegerField(
        default=587,
        verbose_name="منفذ البريد"
    )
    
    email_use_tls = models.BooleanField(
        default=True,
        verbose_name="استخدام TLS"
    )
    
    # إعدادات النسخ الاحتياطي
    backup_enabled = models.BooleanField(
        default=True,
        verbose_name="تفعيل النسخ الاحتياطي"
    )
    
    backup_frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري'),
        ],
        default='daily',
        verbose_name="تكرار النسخ الاحتياطي"
    )
    
    # إعدادات الواجهة
    theme_color = models.CharField(
        max_length=7,
        default='#667eea',
        verbose_name="لون الواجهة الأساسي"
    )
    
    sidebar_collapsed = models.BooleanField(
        default=False,
        verbose_name="الشريط الجانبي مطوي افتراضياً"
    )
    
    items_per_page = models.IntegerField(
        default=20,
        validators=[MinValueValidator(10), MaxValueValidator(100)],
        verbose_name="عدد العناصر في الصفحة"
    )
    
    # إعدادات الإشعارات
    notifications_enabled = models.BooleanField(
        default=True,
        verbose_name="تفعيل الإشعارات"
    )
    
    email_notifications = models.BooleanField(
        default=True,
        verbose_name="إشعارات البريد الإلكتروني"
    )
    
    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="محدث بواسطة"
    )

    class Meta:
        verbose_name = "إعدادات النظام"
        verbose_name_plural = "إعدادات النظام"

    def __str__(self):
        return f"إعدادات النظام - {self.company_name}"

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات النظام"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings


class UserProfile(models.Model):
    """ملف المستخدم الشخصي المتقدم"""
    
    DEPARTMENTS = [
        ('admin', 'الإدارة'),
        ('sales', 'المبيعات'),
        ('purchases', 'المشتريات'),
        ('warehouse', 'المخازن'),
        ('accounting', 'المحاسبة'),
        ('hr', 'الموارد البشرية'),
        ('it', 'تقنية المعلومات'),
        ('customer_service', 'خدمة العملاء'),
    ]
    
    POSITIONS = [
        ('ceo', 'المدير التنفيذي'),
        ('manager', 'مدير'),
        ('supervisor', 'مشرف'),
        ('senior_employee', 'موظف أول'),
        ('employee', 'موظف'),
        ('intern', 'متدرب'),
    ]
    
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        verbose_name="المستخدم"
    )
    
    # معلومات شخصية
    phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="رقم الهاتف"
    )
    
    mobile = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="رقم الجوال"
    )
    
    address = models.TextField(
        blank=True,
        verbose_name="العنوان"
    )
    
    birth_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ الميلاد"
    )
    
    national_id = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="رقم الهوية"
    )
    
    # معلومات وظيفية
    employee_id = models.CharField(
        max_length=20,
        unique=True,
        blank=True,
        verbose_name="رقم الموظف"
    )
    
    department = models.CharField(
        max_length=20,
        choices=DEPARTMENTS,
        blank=True,
        verbose_name="القسم"
    )
    
    position = models.CharField(
        max_length=20,
        choices=POSITIONS,
        blank=True,
        verbose_name="المنصب"
    )
    
    hire_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ التوظيف"
    )
    
    manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='subordinates',
        verbose_name="المدير المباشر"
    )
    
    # إعدادات النظام
    avatar = models.ImageField(
        upload_to='avatars/',
        null=True,
        blank=True,
        verbose_name="الصورة الشخصية"
    )
    
    theme_preference = models.CharField(
        max_length=20,
        choices=[
            ('light', 'فاتح'),
            ('dark', 'داكن'),
            ('auto', 'تلقائي'),
        ],
        default='light',
        verbose_name="تفضيل الواجهة"
    )
    
    # حالة الحساب
    is_active = models.BooleanField(
        default=True,
        verbose_name="نشط"
    )
    
    last_login_ip = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="آخر IP تسجيل دخول"
    )
    
    failed_login_attempts = models.IntegerField(
        default=0,
        verbose_name="محاولات تسجيل الدخول الفاشلة"
    )
    
    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "ملف المستخدم"
        verbose_name_plural = "ملفات المستخدمين"

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username}"


class CustomRole(models.Model):
    """الأدوار المخصصة للمستخدمين"""

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="اسم الدور"
    )

    description = models.TextField(
        blank=True,
        verbose_name="وصف الدور"
    )

    permissions = models.ManyToManyField(
        Permission,
        blank=True,
        verbose_name="الصلاحيات"
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name="نشط"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        verbose_name="منشئ بواسطة"
    )

    class Meta:
        verbose_name = "دور مخصص"
        verbose_name_plural = "الأدوار المخصصة"

    def __str__(self):
        return self.name


class UserRole(models.Model):
    """ربط المستخدمين بالأدوار"""

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name="المستخدم"
    )

    role = models.ForeignKey(
        CustomRole,
        on_delete=models.CASCADE,
        verbose_name="الدور"
    )

    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='assigned_roles',
        verbose_name="مُعين بواسطة"
    )

    assigned_at = models.DateTimeField(auto_now_add=True)

    is_active = models.BooleanField(
        default=True,
        verbose_name="نشط"
    )

    class Meta:
        verbose_name = "دور المستخدم"
        verbose_name_plural = "أدوار المستخدمين"
        unique_together = ['user', 'role']

    def __str__(self):
        return f"{self.user.username} - {self.role.name}"


class SystemLog(models.Model):
    """سجلات النظام"""

    LEVEL_CHOICES = [
        ('debug', 'تصحيح'),
        ('info', 'معلومات'),
        ('warning', 'تحذير'),
        ('error', 'خطأ'),
        ('critical', 'حرج'),
    ]

    ACTION_CHOICES = [
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('view', 'عرض'),
        ('export', 'تصدير'),
        ('import', 'استيراد'),
        ('backup', 'نسخ احتياطي'),
        ('restore', 'استعادة'),
        ('settings_change', 'تغيير إعدادات'),
        ('permission_change', 'تغيير صلاحيات'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="المستخدم"
    )

    level = models.CharField(
        max_length=20,
        choices=LEVEL_CHOICES,
        default='info',
        verbose_name="المستوى"
    )

    action = models.CharField(
        max_length=30,
        choices=ACTION_CHOICES,
        verbose_name="الإجراء"
    )

    message = models.TextField(verbose_name="الرسالة")

    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="عنوان IP"
    )

    user_agent = models.TextField(
        blank=True,
        verbose_name="معلومات المتصفح"
    )

    extra_data = models.JSONField(
        default=dict,
        blank=True,
        verbose_name="بيانات إضافية"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "سجل النظام"
        verbose_name_plural = "سجلات النظام"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_level_display()} - {self.message[:50]}"


class ApplicationModule(models.Model):
    """وحدات التطبيق"""

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="اسم الوحدة"
    )

    display_name = models.CharField(
        max_length=100,
        verbose_name="الاسم المعروض"
    )

    description = models.TextField(
        blank=True,
        verbose_name="الوصف"
    )

    icon = models.CharField(
        max_length=50,
        default='bi-app',
        verbose_name="الأيقونة"
    )

    url_pattern = models.CharField(
        max_length=200,
        blank=True,
        verbose_name="نمط الرابط"
    )

    is_enabled = models.BooleanField(
        default=True,
        verbose_name="مفعل"
    )

    order = models.IntegerField(
        default=0,
        verbose_name="الترتيب"
    )

    required_permissions = models.ManyToManyField(
        Permission,
        blank=True,
        verbose_name="الصلاحيات المطلوبة"
    )

    class Meta:
        verbose_name = "وحدة التطبيق"
        verbose_name_plural = "وحدات التطبيق"
        ordering = ['order', 'name']

    def __str__(self):
        return self.display_name
