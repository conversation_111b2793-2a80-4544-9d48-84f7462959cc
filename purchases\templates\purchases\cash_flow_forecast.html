{% extends 'base.html' %}
{% load static %}

{% block title %}توقعات التدفق النقدي - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .forecast-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .forecast-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .forecast-card.positive {
        border-left: 5px solid #28a745;
        background: linear-gradient(45deg, #f0fff4, #ffffff);
    }

    .forecast-card.negative {
        border-left: 5px solid #dc3545;
        background: linear-gradient(45deg, #fff5f5, #ffffff);
    }

    .forecast-card.neutral {
        border-left: 5px solid #6c757d;
        background: linear-gradient(45deg, #f8f9fa, #ffffff);
    }

    .forecast-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .forecast-period {
        font-weight: bold;
        color: #2c3e50;
        font-size: 1.2rem;
    }

    .forecast-status {
        padding: 6px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .status-positive {
        background: #d4edda;
        color: #155724;
    }

    .status-negative {
        background: #f8d7da;
        color: #721c24;
    }

    .status-neutral {
        background: #e2e3e5;
        color: #383d41;
    }

    .forecast-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .metric-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .metric-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 8px;
    }

    .metric-value {
        font-size: 1.3rem;
        font-weight: bold;
    }

    .metric-value.positive {
        color: #28a745;
    }

    .metric-value.negative {
        color: #dc3545;
    }

    .metric-value.neutral {
        color: #17a2b8;
    }

    .confidence-bar {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
        overflow: hidden;
        margin-top: 10px;
    }

    .confidence-fill {
        height: 100%;
        background: linear-gradient(45deg, #17a2b8, #138496);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .form-section {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .section-title {
        color: #17a2b8;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }

    .btn-generate {
        background: linear-gradient(45deg, #17a2b8, #138496);
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-generate:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(23, 162, 184, 0.3);
        color: white;
    }

    .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .summary-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-top: 4px solid #17a2b8;
    }

    .summary-card.purchases {
        border-top-color: #dc3545;
    }

    .summary-card.payments {
        border-top-color: #28a745;
    }

    .summary-card.outstanding {
        border-top-color: #ffc107;
    }

    .summary-card.impact {
        border-top-color: #6f42c1;
    }

    .summary-number {
        font-size: 2.2rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .summary-number.purchases {
        color: #dc3545;
    }

    .summary-number.payments {
        color: #28a745;
    }

    .summary-number.outstanding {
        color: #ffc107;
    }

    .summary-number.impact {
        color: #6f42c1;
    }

    .summary-label {
        color: #6c757d;
        font-weight: 500;
    }

    .chart-container {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .alert-info {
        background: linear-gradient(45deg, #d1ecf1, #bee5eb);
        border: none;
        border-radius: 10px;
        color: #0c5460;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-graph-up-arrow"></i>
                    توقعات التدفق النقدي
                </h1>
                <p class="mb-0">تحليل وتوقع التدفقات النقدية للمشتريات والمدفوعات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'purchases:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house"></i>
                        لوحة التحكم
                    </a>
                    <a href="{% url 'purchases:reports' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-right"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إنشاء توقع جديد -->
    <div class="form-section">
        <h3 class="section-title">
            <i class="bi bi-plus-circle"></i>
            إنشاء توقع جديد
        </h3>
        
        <form method="post">
            {% csrf_token %}
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">نوع الفترة</label>
                    <select name="period_type" class="form-select" required>
                        <option value="weekly">أسبوعي</option>
                        <option value="monthly" selected>شهري</option>
                        <option value="quarterly">ربع سنوي</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">تاريخ البداية</label>
                    <input type="date" name="start_date" class="form-control" value="{{ today }}" required>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">تاريخ النهاية</label>
                    <input type="date" name="end_date" class="form-control" required>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-generate w-100">
                        <i class="bi bi-calculator"></i>
                        إنشاء التوقع
                    </button>
                </div>
            </div>
        </form>
        
        <div class="alert alert-info mt-3">
            <i class="bi bi-info-circle"></i>
            <strong>ملاحظة:</strong> يتم حساب التوقعات بناءً على البيانات التاريخية والفواتير المستحقة.
        </div>
    </div>

    <!-- التوقعات الحالية -->
    {% if forecasts %}
        <h3 class="section-title">
            <i class="bi bi-list-ul"></i>
            التوقعات الحديثة
        </h3>
        
        {% for forecast in forecasts %}
            <div class="forecast-card {{ forecast.flow_status }}">
                <div class="forecast-header">
                    <div class="forecast-period">
                        {{ forecast.get_period_type_display }} 
                        ({{ forecast.start_date }} - {{ forecast.end_date }})
                    </div>
                    <div class="forecast-status status-{{ forecast.flow_status }}">
                        {% if forecast.flow_status == 'positive' %}
                            تدفق إيجابي
                        {% elif forecast.flow_status == 'negative' %}
                            تدفق سلبي
                        {% else %}
                            تدفق متوازن
                        {% endif %}
                    </div>
                </div>
                
                <div class="forecast-metrics">
                    <div class="metric-item">
                        <div class="metric-label">المشتريات المتوقعة</div>
                        <div class="metric-value negative">{{ forecast.expected_purchases|floatformat:0 }} ج.م</div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-label">المدفوعات المتوقعة</div>
                        <div class="metric-value positive">{{ forecast.expected_payments|floatformat:0 }} ج.م</div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-label">الفواتير المعلقة</div>
                        <div class="metric-value neutral">{{ forecast.outstanding_invoices|floatformat:0 }} ج.م</div>
                    </div>
                    
                    <div class="metric-item">
                        <div class="metric-label">صافي التدفق</div>
                        <div class="metric-value {{ forecast.flow_status }}">
                            {% if forecast.cash_flow_impact >= 0 %}+{% endif %}{{ forecast.cash_flow_impact|floatformat:0 }} ج.م
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="bi bi-calendar"></i>
                            تم الإنشاء: {{ forecast.created_at|date:"Y-m-d H:i" }}
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            مستوى الثقة: {{ forecast.confidence_level }}%
                        </small>
                        <div class="confidence-bar">
                            <div class="confidence-fill" style="width: {{ forecast.confidence_level }}%;"></div>
                        </div>
                    </div>
                </div>
                
                {% if forecast.notes %}
                    <div class="mt-3">
                        <strong>ملاحظات:</strong> {{ forecast.notes }}
                    </div>
                {% endif %}
            </div>
        {% endfor %}
        
        <!-- ملخص إجمالي -->
        <div class="summary-grid">
            {% with first_forecast=forecasts|first %}
                <div class="summary-card purchases">
                    <div class="summary-number purchases">
                        {{ first_forecast.expected_purchases|floatformat:0 }}
                    </div>
                    <div class="summary-label">متوسط المشتريات المتوقعة</div>
                </div>

                <div class="summary-card payments">
                    <div class="summary-number payments">
                        {{ first_forecast.expected_payments|floatformat:0 }}
                    </div>
                    <div class="summary-label">متوسط المدفوعات المتوقعة</div>
                </div>

                <div class="summary-card outstanding">
                    <div class="summary-number outstanding">
                        {{ first_forecast.outstanding_invoices|floatformat:0 }}
                    </div>
                    <div class="summary-label">إجمالي الفواتير المعلقة</div>
                </div>

                <div class="summary-card impact">
                    <div class="summary-number impact">
                        {% if first_forecast.cash_flow_impact >= 0 %}+{% endif %}{{ first_forecast.cash_flow_impact|floatformat:0 }}
                    </div>
                    <div class="summary-label">صافي التأثير المتوقع</div>
                </div>
            {% endwith %}
        </div>
        
    {% else %}
        <div class="empty-state">
            <i class="bi bi-graph-up"></i>
            <h4>لا توجد توقعات حالياً</h4>
            <p>قم بإنشاء توقع جديد لتحليل التدفق النقدي</p>
        </div>
    {% endif %}

    <!-- رسم بياني للتوقعات -->
    {% if forecasts %}
        <div class="chart-container">
            <h3 class="section-title">
                <i class="bi bi-bar-chart"></i>
                الرسم البياني للتوقعات
            </h3>
            
            <canvas id="cashFlowChart" width="400" height="200"></canvas>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
{% if forecasts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // بيانات الرسم البياني
    const ctx = document.getElementById('cashFlowChart').getContext('2d');
    const chartData = {
        labels: [
            {% for forecast in forecasts %}
                '{{ forecast.start_date|date:"M Y" }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [
            {
                label: 'المشتريات المتوقعة',
                data: [
                    {% for forecast in forecasts %}
                        {{ forecast.expected_purchases }}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                backgroundColor: 'rgba(220, 53, 69, 0.2)',
                borderColor: 'rgba(220, 53, 69, 1)',
                borderWidth: 2
            },
            {
                label: 'المدفوعات المتوقعة',
                data: [
                    {% for forecast in forecasts %}
                        {{ forecast.expected_payments }}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                backgroundColor: 'rgba(40, 167, 69, 0.2)',
                borderColor: 'rgba(40, 167, 69, 1)',
                borderWidth: 2
            },
            {
                label: 'صافي التدفق',
                data: [
                    {% for forecast in forecasts %}
                        {{ forecast.cash_flow_impact }}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                backgroundColor: 'rgba(23, 162, 184, 0.2)',
                borderColor: 'rgba(23, 162, 184, 1)',
                borderWidth: 2,
                type: 'line'
            }
        ]
    };

    const config = {
        type: 'bar',
        data: chartData,
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'توقعات التدفق النقدي'
                },
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ج.م';
                        }
                    }
                }
            }
        }
    };

    new Chart(ctx, config);
</script>
{% endif %}

<script>
    // تحديث تاريخ النهاية تلقائياً عند تغيير نوع الفترة
    document.querySelector('select[name="period_type"]').addEventListener('change', function() {
        const startDate = document.querySelector('input[name="start_date"]').value;
        if (startDate) {
            const start = new Date(startDate);
            let end = new Date(start);
            
            switch(this.value) {
                case 'weekly':
                    end.setDate(start.getDate() + 7);
                    break;
                case 'monthly':
                    end.setMonth(start.getMonth() + 1);
                    break;
                case 'quarterly':
                    end.setMonth(start.getMonth() + 3);
                    break;
            }
            
            document.querySelector('input[name="end_date"]').value = end.toISOString().split('T')[0];
        }
    });
</script>
{% endblock %}
