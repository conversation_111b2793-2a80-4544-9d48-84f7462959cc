{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .loading-form {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin: 20px 0;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding: 25px;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        background: #f8f9fa;
    }
    
    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #007bff;
    }
    
    .items-table {
        margin-top: 20px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .btn-add-item {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-add-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .summary-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 10px;
        margin-top: 20px;
    }
    
    .vehicle-info {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-top: 10px;
    }
    
    .representative-info {
        background: #f3e5f5;
        padding: 15px;
        border-radius: 8px;
        margin-top: 10px;
    }
    
    .item-row {
        transition: all 0.3s;
    }
    
    .item-row:hover {
        background: #f8f9fa;
    }
    
    .delete-btn {
        background: #dc3545;
        border: none;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 0.8rem;
    }
    
    .total-display {
        font-size: 1.1rem;
        font-weight: 600;
        color: #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="loading-form">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="text-primary">
                        <i class="bi bi-truck-flatbed"></i>
                        {{ title }}
                    </h2>
                    <a href="{% url 'sales:vehicle_loading_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>

                <form method="post" id="loading-form">
                    {% csrf_token %}
                    
                    <!-- معلومات التحميل الأساسية -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            معلومات التحميل
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">السيارة</label>
                                    {{ form.vehicle }}
                                    <div id="vehicle-info" class="vehicle-info" style="display: none;">
                                        <small class="text-muted">معلومات السيارة ستظهر هنا</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المندوب</label>
                                    {{ form.representative }}
                                    <div id="representative-info" class="representative-info" style="display: none;">
                                        <small class="text-muted">معلومات المندوب ستظهر هنا</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ التحميل</label>
                                    {{ form.loading_date }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المخزن</label>
                                    {{ form.warehouse }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">حالة التحميل</label>
                                    {{ form.status }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    {{ form.notes }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- عناصر التحميل -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-list-ul"></i>
                            عناصر التحميل
                        </h4>
                        
                        {{ formset.management_form }}
                        
                        <div class="table-responsive items-table">
                            <table class="table table-bordered mb-0" id="items-table">
                                <thead class="table-primary">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>سعر الوحدة</th>
                                        <th>الوزن الإجمالي (كيلو)</th>
                                        <th>القيمة الإجمالية</th>
                                        <th>حذف</th>
                                    </tr>
                                </thead>
                                <tbody id="items-tbody">
                                    {% for form in formset %}
                                        <tr class="item-row">
                                            <td>
                                                {{ form.product }}
                                                {{ form.id }}
                                            </td>
                                            <td>{{ form.quantity }}</td>
                                            <td>{{ form.unit_price }}</td>
                                            <td>{{ form.total_weight }}</td>
                                            <td class="item-total total-display">0.00</td>
                                            <td>
                                                {% if not forloop.first %}
                                                    {{ form.DELETE }}
                                                    <label for="{{ form.DELETE.id_for_label }}" class="delete-btn">
                                                        <i class="bi bi-trash"></i>
                                                    </label>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-add-item" id="add-item">
                                <i class="bi bi-plus-circle"></i>
                                إضافة عنصر جديد
                            </button>
                        </div>
                    </div>

                    <!-- ملخص التحميل -->
                    <div class="summary-section">
                        <div class="row">
                            <div class="col-md-8">
                                <h5>
                                    <i class="bi bi-clipboard-data"></i>
                                    ملخص التحميل
                                </h5>
                                <p class="mb-0">تأكد من صحة جميع البيانات قبل الحفظ</p>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>عدد العناصر:</span>
                                    <span id="total-items">0</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الوزن الإجمالي:</span>
                                    <span id="total-weight">0.00 كيلو</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>القيمة الإجمالية:</strong>
                                    <strong id="total-value">0.00 ج.م</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-save"></i>
                            حفظ التحميل
                        </button>
                        <a href="{% url 'sales:vehicle_loading_list' %}" class="btn btn-secondary btn-lg ms-2">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث معلومات السيارة عند التغيير
    $('#id_vehicle').change(function() {
        var vehicleId = $(this).val();
        if (vehicleId) {
            var vehicleText = $(this).find('option:selected').text();
            var info = `<strong>السيارة المختارة:</strong> ${vehicleText}`;
            $('#vehicle-info').html(info).show();
        } else {
            $('#vehicle-info').hide();
        }
    });

    // تحديث معلومات المندوب عند التغيير
    $('#id_representative').change(function() {
        var repId = $(this).val();
        if (repId) {
            var repText = $(this).find('option:selected').text();
            var info = `<strong>المندوب المختار:</strong> ${repText}`;
            $('#representative-info').html(info).show();
        } else {
            $('#representative-info').hide();
        }
    });

    // تحديث سعر المنتج عند التغيير
    $(document).on('change', '[id*="product"]', function() {
        var productId = $(this).val();
        var row = $(this).closest('tr');
        
        if (productId) {
            $.get('/sales/api/product/' + productId + '/', function(data) {
                row.find('[id*="unit_price"]').val(data.unit_price_wholesale);
                calculateRowTotal(row);
            });
        }
    });

    // حساب إجمالي الصف
    function calculateRowTotal(row) {
        var quantity = parseFloat(row.find('[id*="quantity"]').val()) || 0;
        var unitPrice = parseFloat(row.find('[id*="unit_price"]').val()) || 0;
        
        var total = quantity * unitPrice;
        row.find('.item-total').text(total.toFixed(2));
        calculateLoadingSummary();
    }

    // حساب ملخص التحميل
    function calculateLoadingSummary() {
        var totalItems = 0;
        var totalWeight = 0;
        var totalValue = 0;
        
        $('.item-row').each(function() {
            var quantity = parseFloat($(this).find('[id*="quantity"]').val()) || 0;
            var weight = parseFloat($(this).find('[id*="total_weight"]').val()) || 0;
            var value = parseFloat($(this).find('.item-total').text()) || 0;
            
            if (quantity > 0) {
                totalItems++;
                totalWeight += weight;
                totalValue += value;
            }
        });
        
        $('#total-items').text(totalItems);
        $('#total-weight').text(totalWeight.toFixed(2) + ' كيلو');
        $('#total-value').text(totalValue.toFixed(2) + ' ج.م');
    }

    // تحديث الحسابات عند تغيير القيم
    $(document).on('input', '[id*="quantity"], [id*="unit_price"], [id*="total_weight"]', function() {
        calculateRowTotal($(this).closest('tr'));
    });

    // إضافة صف جديد
    $('#add-item').click(function() {
        var totalForms = $('#id_form-TOTAL_FORMS');
        var formNum = parseInt(totalForms.val());
        
        var newRow = $('.item-row:first').clone();
        newRow.find('input, select').each(function() {
            var name = $(this).attr('name');
            if (name) {
                name = name.replace('-0-', '-' + formNum + '-');
                $(this).attr('name', name);
                $(this).attr('id', 'id_' + name);
                $(this).val('');
            }
        });
        
        newRow.find('.item-total').text('0.00');
        $('#items-tbody').append(newRow);
        
        totalForms.val(formNum + 1);
    });

    // حذف صف
    $(document).on('change', '[id*="DELETE"]', function() {
        if ($(this).is(':checked')) {
            $(this).closest('tr').hide();
            calculateLoadingSummary();
        }
    });

    // حساب أولي
    calculateLoadingSummary();
});
</script>
{% endblock %}
