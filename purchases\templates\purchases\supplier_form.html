<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if supplier %}تعديل المورد{% else %}إضافة مورد جديد{% endif %} - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .form-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #28a745;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-left: 15px;
            color: #28a745;
            font-size: 1.5rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select, .form-check-input {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        
        .form-check-input:checked {
            background-color: #28a745;
            border-color: #28a745;
        }
        
        .btn-save {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            min-width: 150px;
        }
        
        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .btn-cancel {
            background: #6c757d;
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            min-width: 150px;
            text-decoration: none;
        }
        
        .btn-cancel:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: white;
        }
        
        .required-field {
            color: #dc3545;
        }
        
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .input-group-icon {
            position: relative;
        }
        
        .input-group-icon i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 10;
        }
        
        .input-group-icon .form-control {
            padding-right: 45px;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .payment-terms-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .payment-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .payment-card:hover {
            border-color: #28a745;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(40, 167, 69, 0.2);
        }
        
        .payment-card.selected {
            border-color: #28a745;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        .payment-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .payment-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .payment-description {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .supplier-type-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .type-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .type-card:hover {
            border-color: #28a745;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(40, 167, 69, 0.2);
        }
        
        .type-card.selected {
            border-color: #28a745;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }
        
        .type-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .type-title {
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .type-description {
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-person-plus"></i>
                        {% if supplier %}تعديل المورد: {{ supplier.name }}{% else %}إضافة مورد جديد{% endif %}
                    </h1>
                    <p class="mb-0">{% if supplier %}تعديل بيانات المورد الموجود{% else %}إضافة مورد جديد إلى قاعدة البيانات{% endif %}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'purchases:supplier_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- نموذج المورد -->
        <div class="form-container">
            <form method="post" id="supplierForm">
                {% csrf_token %}
                
                <!-- المعلومات الأساسية -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        المعلومات الأساسية
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    اسم المورد <span class="required-field">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ form.name.value|default:'' }}" required>
                                <div class="help-text">أدخل الاسم الكامل للمورد أو الشركة</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="code" class="form-label">كود المورد</label>
                                <input type="text" class="form-control" id="code" name="code" 
                                       value="{{ form.code.value|default:'' }}">
                                <div class="help-text">كود فريد للمورد (اختياري)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                       value="{{ form.tax_number.value|default:'' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="commercial_register" class="form-label">السجل التجاري</label>
                                <input type="text" class="form-control" id="commercial_register" name="commercial_register" 
                                       value="{{ form.commercial_register.value|default:'' }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نوع المورد -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-tags"></i>
                        نوع المورد
                    </h3>
                    
                    <div class="supplier-type-cards">
                        <div class="type-card" data-type="manufacturer" onclick="selectSupplierType('manufacturer')">
                            <div class="type-icon">
                                <i class="bi bi-gear"></i>
                            </div>
                            <div class="type-title">مصنع</div>
                            <div class="type-description">مورد ينتج البضائع</div>
                        </div>
                        
                        <div class="type-card" data-type="distributor" onclick="selectSupplierType('distributor')">
                            <div class="type-icon">
                                <i class="bi bi-truck"></i>
                            </div>
                            <div class="type-title">موزع</div>
                            <div class="type-description">مورد يوزع المنتجات</div>
                        </div>
                        
                        <div class="type-card" data-type="wholesaler" onclick="selectSupplierType('wholesaler')">
                            <div class="type-icon">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="type-title">تاجر جملة</div>
                            <div class="type-description">بيع بالجملة</div>
                        </div>
                        
                        <div class="type-card" data-type="service" onclick="selectSupplierType('service')">
                            <div class="type-icon">
                                <i class="bi bi-tools"></i>
                            </div>
                            <div class="type-title">مقدم خدمة</div>
                            <div class="type-description">يقدم خدمات وليس منتجات</div>
                        </div>
                    </div>
                    
                    <input type="hidden" id="supplier_type" name="supplier_type" value="{{ form.supplier_type.value|default:'distributor' }}">
                </div>

                <!-- معلومات الاتصال -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-telephone"></i>
                        معلومات الاتصال
                    </h3>
                    
                    <div class="contact-grid">
                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <div class="input-group-icon">
                                <i class="bi bi-telephone"></i>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ form.phone.value|default:'' }}">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="mobile" class="form-label">رقم المحمول</label>
                            <div class="input-group-icon">
                                <i class="bi bi-phone"></i>
                                <input type="tel" class="form-control" id="mobile" name="mobile" 
                                       value="{{ form.mobile.value|default:'' }}">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group-icon">
                                <i class="bi bi-envelope"></i>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ form.email.value|default:'' }}">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="website" class="form-label">الموقع الإلكتروني</label>
                            <div class="input-group-icon">
                                <i class="bi bi-globe"></i>
                                <input type="url" class="form-control" id="website" name="website" 
                                       value="{{ form.website.value|default:'' }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- العنوان -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-geo-alt"></i>
                        العنوان
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان التفصيلي</label>
                                <textarea class="form-control" id="address" name="address" rows="3">{{ form.address.value|default:'' }}</textarea>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="city" class="form-label">المدينة</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="{{ form.city.value|default:'' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="state" class="form-label">المحافظة</label>
                                <input type="text" class="form-control" id="state" name="state" 
                                       value="{{ form.state.value|default:'' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="postal_code" class="form-label">الرمز البريدي</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                       value="{{ form.postal_code.value|default:'' }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- شروط الدفع -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-credit-card"></i>
                        شروط الدفع والتعامل
                    </h3>
                    
                    <div class="payment-terms-cards">
                        <div class="payment-card" data-terms="cash" onclick="selectPaymentTerms('cash')">
                            <div class="payment-icon">
                                <i class="bi bi-cash"></i>
                            </div>
                            <div class="payment-title">نقدي</div>
                            <div class="payment-description">دفع فوري</div>
                        </div>
                        
                        <div class="payment-card" data-terms="15_days" onclick="selectPaymentTerms('15_days')">
                            <div class="payment-icon">
                                <i class="bi bi-calendar2-week"></i>
                            </div>
                            <div class="payment-title">15 يوم</div>
                            <div class="payment-description">آجل 15 يوم</div>
                        </div>
                        
                        <div class="payment-card" data-terms="30_days" onclick="selectPaymentTerms('30_days')">
                            <div class="payment-icon">
                                <i class="bi bi-calendar2-month"></i>
                            </div>
                            <div class="payment-title">30 يوم</div>
                            <div class="payment-description">آجل شهر</div>
                        </div>
                        
                        <div class="payment-card" data-terms="60_days" onclick="selectPaymentTerms('60_days')">
                            <div class="payment-icon">
                                <i class="bi bi-calendar2-range"></i>
                            </div>
                            <div class="payment-title">60 يوم</div>
                            <div class="payment-description">آجل شهرين</div>
                        </div>
                    </div>
                    
                    <input type="hidden" id="payment_terms" name="payment_terms" value="{{ form.payment_terms.value|default:'30_days' }}">
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="credit_limit" class="form-label">حد الائتمان (ج.م)</label>
                                <input type="number" class="form-control" id="credit_limit" name="credit_limit" 
                                       step="0.01" min="0" value="{{ form.credit_limit.value|default:'' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="discount_percentage" class="form-label">نسبة الخصم (%)</label>
                                <input type="number" class="form-control" id="discount_percentage" name="discount_percentage" 
                                       step="0.01" min="0" max="100" value="{{ form.discount_percentage.value|default:'' }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات إضافية -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-chat-text"></i>
                        ملاحظات إضافية
                    </h3>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4">{{ form.notes.value|default:'' }}</textarea>
                        <div class="help-text">أي ملاحظات أو معلومات إضافية عن المورد</div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                               {% if form.is_active.value %}checked{% endif %}>
                        <label class="form-check-label" for="is_active">
                            مورد نشط
                        </label>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="text-center">
                    <button type="submit" class="btn btn-save me-3">
                        <i class="bi bi-check-circle"></i>
                        {% if supplier %}حفظ التعديلات{% else %}إضافة المورد{% endif %}
                    </button>
                    <a href="{% url 'purchases:supplier_list' %}" class="btn btn-cancel">
                        <i class="bi bi-x-circle"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للنموذج
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.form-section');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(30px)';
                    section.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });

            // تحديد نوع المورد الحالي
            const currentType = document.getElementById('supplier_type').value;
            selectSupplierType(currentType);

            // تحديد شروط الدفع الحالية
            const currentTerms = document.getElementById('payment_terms').value;
            selectPaymentTerms(currentTerms);
        });

        function selectSupplierType(type) {
            // إزالة التحديد من جميع البطاقات
            document.querySelectorAll('.type-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // تحديد البطاقة المختارة
            document.querySelector(`[data-type="${type}"]`).classList.add('selected');
            
            // تحديث القيمة المخفية
            document.getElementById('supplier_type').value = type;
        }

        function selectPaymentTerms(terms) {
            // إزالة التحديد من جميع البطاقات
            document.querySelectorAll('.payment-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // تحديد البطاقة المختارة
            document.querySelector(`[data-terms="${terms}"]`).classList.add('selected');
            
            // تحديث القيمة المخفية
            document.getElementById('payment_terms').value = terms;
        }
    </script>
</body>
</html>
