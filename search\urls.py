from django.urls import path
from . import views

app_name = 'search'

urlpatterns = [
    # API endpoints
    path('api/', views.search_api, name='search_api'),
    path('api/suggestions/', views.search_suggestions, name='search_suggestions'),
    path('api/advanced/', views.advanced_search_api, name='advanced_search_api'),
    path('api/history/', views.search_history, name='search_history'),
    path('api/popular/', views.popular_searches, name='popular_searches'),
    path('api/analytics/', views.search_analytics, name='search_analytics'),
    
    # Actions
    path('clear-history/', views.clear_search_history, name='clear_history'),
    
    # Pages
    path('', views.search_page, name='search_page'),
]
