# Generated by Django 5.2.4 on 2025-07-14 19:02

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('system_settings', '0003_auto_20250714_1709'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='systemsettings',
            name='auto_generate_reports',
            field=models.BooleanField(default=False, verbose_name='إنشاء التقارير تلقائياً'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='auto_logout_inactive',
            field=models.BooleanField(default=True, verbose_name='تسجيل خروج تلقائي عند عدم النشاط'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='backup_compress',
            field=models.BooleanField(default=True, verbose_name='ضغط النسخ الاحتياطي'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='backup_encrypt',
            field=models.BooleanField(default=False, verbose_name='تشفير النسخ الاحتياطي'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='backup_location',
            field=models.CharField(choices=[('local', 'محلي'), ('cloud', 'سحابي'), ('ftp', 'FTP')], default='local', max_length=200, verbose_name='موقع النسخ الاحتياطي'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='backup_retention_days',
            field=models.IntegerField(default=30, validators=[django.core.validators.MinValueValidator(7), django.core.validators.MaxValueValidator(365)], verbose_name='مدة الاحتفاظ بالنسخ (يوم)'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='cache_timeout',
            field=models.IntegerField(default=300, validators=[django.core.validators.MinValueValidator(60), django.core.validators.MaxValueValidator(3600)], verbose_name='مهلة التخزين المؤقت (ثانية)'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='compact_mode',
            field=models.BooleanField(default=False, verbose_name='الوضع المضغوط'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='country',
            field=models.CharField(default='Egypt', max_length=50, verbose_name='الدولة'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='custom_css',
            field=models.TextField(blank=True, verbose_name='CSS مخصص'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='custom_js',
            field=models.TextField(blank=True, verbose_name='JavaScript مخصص'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='date_format',
            field=models.CharField(choices=[('d/m/Y', 'DD/MM/YYYY'), ('m/d/Y', 'MM/DD/YYYY'), ('Y-m-d', 'YYYY-MM-DD'), ('d-m-Y', 'DD-MM-YYYY')], default='d/m/Y', max_length=20, verbose_name='تنسيق التاريخ'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='decimal_places',
            field=models.IntegerField(default=2, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(6)], verbose_name='عدد الخانات العشرية'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='decimal_separator',
            field=models.CharField(default='.', max_length=5, verbose_name='فاصل العشرية'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='default_report_format',
            field=models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV'), ('html', 'HTML')], default='pdf', max_length=10, verbose_name='تنسيق التقرير الافتراضي'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='email_from_name',
            field=models.CharField(default='نظام أوساريك', max_length=100, verbose_name='اسم المرسل'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='email_host_password',
            field=models.CharField(blank=True, max_length=100, verbose_name='كلمة مرور البريد'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='email_host_user',
            field=models.CharField(blank=True, max_length=100, verbose_name='اسم مستخدم البريد'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='email_signature',
            field=models.TextField(blank=True, verbose_name='توقيع البريد الإلكتروني'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='email_use_ssl',
            field=models.BooleanField(default=False, verbose_name='استخدام SSL'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='enable_animations',
            field=models.BooleanField(default=True, verbose_name='تفعيل الرسوم المتحركة'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='enable_caching',
            field=models.BooleanField(default=True, verbose_name='تفعيل التخزين المؤقت'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='enable_compression',
            field=models.BooleanField(default=True, verbose_name='تفعيل الضغط'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='lockout_duration',
            field=models.IntegerField(default=15, validators=[django.core.validators.MinValueValidator(5), django.core.validators.MaxValueValidator(120)], verbose_name='مدة الحظر (دقيقة)'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='max_file_upload_size',
            field=models.IntegerField(default=10, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(100)], verbose_name='الحد الأقصى لحجم الملف (MB)'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='notification_frequency',
            field=models.CharField(choices=[('instant', 'فوري'), ('hourly', 'كل ساعة'), ('daily', 'يومي'), ('weekly', 'أسبوعي')], default='instant', max_length=20, verbose_name='تكرار الإشعارات'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='notification_sound',
            field=models.BooleanField(default=True, verbose_name='صوت الإشعارات'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='password_expiry_days',
            field=models.IntegerField(default=90, validators=[django.core.validators.MinValueValidator(30), django.core.validators.MaxValueValidator(365)], verbose_name='انتهاء كلمة المرور (يوم)'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='push_notifications',
            field=models.BooleanField(default=True, verbose_name='الإشعارات الفورية'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='report_retention_days',
            field=models.IntegerField(default=90, validators=[django.core.validators.MinValueValidator(30), django.core.validators.MaxValueValidator(365)], verbose_name='مدة الاحتفاظ بالتقارير (يوم)'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='require_2fa',
            field=models.BooleanField(default=False, verbose_name='المصادقة الثنائية مطلوبة'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='secondary_color',
            field=models.CharField(default='#764ba2', max_length=7, verbose_name='اللون الثانوي'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='show_breadcrumbs',
            field=models.BooleanField(default=True, verbose_name='إظهار مسار التنقل'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='sms_notifications',
            field=models.BooleanField(default=False, verbose_name='إشعارات الرسائل النصية'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='theme_mode',
            field=models.CharField(choices=[('light', 'فاتح'), ('dark', 'داكن'), ('auto', 'تلقائي')], default='light', max_length=10, verbose_name='وضع الواجهة'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='thousand_separator',
            field=models.CharField(default=',', max_length=5, verbose_name='فاصل الآلاف'),
        ),
        migrations.AddField(
            model_name='systemsettings',
            name='time_format',
            field=models.CharField(choices=[('24', '24 ساعة'), ('12', '12 ساعة')], default='24', max_length=10, verbose_name='تنسيق الوقت'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='backup_frequency',
            field=models.CharField(choices=[('hourly', 'كل ساعة'), ('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري')], default='daily', max_length=20, verbose_name='تكرار النسخ الاحتياطي'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='currency_code',
            field=models.CharField(default='EGP', max_length=3, verbose_name='رمز العملة'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='currency_symbol',
            field=models.CharField(default='ج.م', max_length=10, verbose_name='رمز العملة'),
        ),
        migrations.AlterField(
            model_name='systemsettings',
            name='system_timezone',
            field=models.CharField(default='Africa/Cairo', max_length=50, verbose_name='المنطقة الزمنية'),
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الإشعار')),
                ('message', models.TextField(verbose_name='رسالة الإشعار')),
                ('notification_type', models.CharField(choices=[('info', 'معلومات'), ('success', 'نجاح'), ('warning', 'تحذير'), ('error', 'خطأ'), ('welcome', 'ترحيب'), ('system', 'نظام')], default='info', max_length=20, verbose_name='نوع الإشعار')),
                ('is_read', models.BooleanField(default=False, verbose_name='تم القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('action_url', models.URLField(blank=True, verbose_name='رابط الإجراء')),
                ('action_text', models.CharField(blank=True, max_length=100, verbose_name='نص الإجراء')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='system_notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الصلاحية')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود الصلاحية')),
                ('category', models.CharField(choices=[('users', 'إدارة المستخدمين'), ('products', 'إدارة المنتجات'), ('sales', 'المبيعات'), ('purchases', 'المشتريات'), ('warehouses', 'إدارة المخازن'), ('accounting', 'المحاسبة'), ('reports', 'التقارير'), ('settings', 'الإعدادات'), ('hr', 'شؤون العاملين'), ('assets', 'الأصول الثابتة'), ('banks', 'البنوك'), ('treasuries', 'الخزائن'), ('branches', 'الفروع'), ('customers', 'العملاء'), ('suppliers', 'الموردين')], max_length=20, verbose_name='الفئة')),
                ('action', models.CharField(choices=[('view', 'عرض'), ('add', 'إضافة'), ('edit', 'تعديل'), ('delete', 'حذف'), ('approve', 'اعتماد'), ('export', 'تصدير'), ('import', 'استيراد'), ('print', 'طباعة')], max_length=20, verbose_name='الإجراء')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'صلاحية',
                'verbose_name_plural': 'الصلاحيات',
                'ordering': ['category', 'action'],
                'unique_together': {('category', 'action')},
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الدور')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود الدور')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('permissions', models.ManyToManyField(blank=True, to='system_settings.permission', verbose_name='الصلاحيات')),
            ],
            options={
                'verbose_name': 'دور',
                'verbose_name_plural': 'الأدوار',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعيين')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('assigned_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_roles', to=settings.AUTH_USER_MODEL, verbose_name='تم التعيين بواسطة')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_users', to='system_settings.role', verbose_name='الدور')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'دور المستخدم',
                'verbose_name_plural': 'أدوار المستخدمين',
                'unique_together': {('user', 'role')},
            },
        ),
    ]
