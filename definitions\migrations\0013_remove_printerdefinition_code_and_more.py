# Generated by Django 5.2.4 on 2025-07-14 01:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0012_remove_profitcenter_fiscal_year_end_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='printerdefinition',
            name='code',
        ),
        migrations.AddField(
            model_name='printerdefinition',
            name='description',
            field=models.TextField(blank=True, verbose_name='وصف الطابعة'),
        ),
        migrations.AddField(
            model_name='printerdefinition',
            name='is_default',
            field=models.BooleanField(default=False, verbose_name='طابعة افتراضية'),
        ),
        migrations.AddField(
            model_name='printerdefinition',
            name='model',
            field=models.CharField(blank=True, max_length=100, verbose_name='موديل الطابعة'),
        ),
        migrations.AddField(
            model_name='printerdefinition',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='printerdefinition',
            name='connection_type',
            field=models.CharField(choices=[('usb', 'USB'), ('network', 'شبكة'), ('bluetooth', 'بلوتوث'), ('serial', 'منفذ تسلسلي'), ('parallel', 'منفذ متوازي')], default='usb', max_length=20, verbose_name='نوع الاتصال'),
        ),
        migrations.AlterField(
            model_name='printerdefinition',
            name='printer_type',
            field=models.CharField(choices=[('thermal', 'طابعة حرارية'), ('inkjet', 'طابعة نفث حبر'), ('laser', 'طابعة ليزر'), ('dot_matrix', 'طابعة نقطية'), ('label', 'طابعة ملصقات')], default='thermal', max_length=20, verbose_name='نوع الطابعة'),
        ),
    ]
