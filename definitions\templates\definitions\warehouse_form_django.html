{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء مخزن جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-building me-2"></i>
                        إنشاء مخزن جديد
                    </h4>
                </div>
                
                <div class="card-body p-4">
                    <!-- عرض رسائل الخطأ أو النجاح -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- النموذج باستخدام Django Forms -->
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- كود المخزن -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">{{ form.code.label }} *</label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                    <div class="text-danger small">{{ form.code.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- اسم المخزن -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }} *</label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small">{{ form.name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- نوع المخزن -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse_type.id_for_label }}" class="form-label">{{ form.warehouse_type.label }} *</label>
                                {{ form.warehouse_type }}
                                {% if form.warehouse_type.errors %}
                                    <div class="text-danger small">{{ form.warehouse_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- مدير المخزن -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.manager_name.id_for_label }}" class="form-label">{{ form.manager_name.label }}</label>
                                {{ form.manager_name }}
                                {% if form.manager_name.errors %}
                                    <div class="text-danger small">{{ form.manager_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- العنوان -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }}</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="text-danger small">{{ form.address.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- رقم الهاتف -->
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">{{ form.phone.label }}</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger small">{{ form.phone.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- الخيارات -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-check mb-2">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                </div>
                                
                                <div class="form-check">
                                    {{ form.allow_negative_stock }}
                                    <label class="form-check-label" for="{{ form.allow_negative_stock.id_for_label }}">
                                        {{ form.allow_negative_stock.label }}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ والإلغاء -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-success btn-lg me-3">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ المخزن
                            </button>
                            <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-secondary btn-lg">
                                <i class="bi bi-x-circle me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    padding: 1.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.btn {
    border-radius: 8px;
    padding: 12px 30px;
    font-weight: 600;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.text-danger {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
</style>
{% endblock %}
