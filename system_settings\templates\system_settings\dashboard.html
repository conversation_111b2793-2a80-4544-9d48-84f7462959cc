{% extends 'base.html' %}
{% load static %}

{% block title %}إعدادات النظام المتقدمة{% endblock %}

{% block extra_css %}
<style>
    .settings-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
    }

    .stats-card {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }

    .quick-action-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
        border: none;
        margin-bottom: 1rem;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Settings Header -->
<div class="settings-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-gear me-2"></i>
                    إعدادات النظام المتقدمة
                </h1>
                <p class="mb-0 opacity-75">إدارة شاملة لإعدادات النظام والمستخدمين والصلاحيات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <i class="bi bi-calendar me-2"></i>
                    {{ "now"|date:"Y/m/d" }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container-fluid">
    <div class="quick-actions">
        <a href="{% url 'system_settings:system_settings' %}" class="quick-action-btn">
            <i class="bi bi-sliders"></i>
            إعدادات النظام
        </a>
        <a href="{% url 'system_settings:users_management' %}" class="quick-action-btn">
            <i class="bi bi-people"></i>
            إدارة المستخدمين
        </a>
        <a href="{% url 'system_settings:permissions_management' %}" class="quick-action-btn" style="background: linear-gradient(135deg, #10b981, #059669);">
            <i class="bi bi-shield-check"></i>
            إدارة الصلاحيات
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-shield-lock"></i>
            إعدادات الأمان
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-hdd"></i>
            النسخ الاحتياطية
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(102, 126, 234, 0.1); color: #667eea;">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stats-number" style="color: #667eea;">{{ stats.total_users }}</div>
                <p class="stats-label">إجمالي المستخدمين</p>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(40, 167, 69, 0.1); color: #28a745;">
                    <i class="bi bi-person-check"></i>
                </div>
                <div class="stats-number text-success">{{ stats.active_users }}</div>
                <p class="stats-label">المستخدمين النشطين</p>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-person-x"></i>
                </div>
                <div class="stats-number text-danger">{{ stats.inactive_users }}</div>
                <p class="stats-label">المستخدمين غير النشطين</p>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(16, 185, 129, 0.1); color: #10b981;">
                    <i class="bi bi-shield-check"></i>
                </div>
                <div class="stats-number" style="color: #10b981;">{{ stats.total_permissions }}</div>
                <p class="stats-label">إجمالي الصلاحيات</p>
            </div>
        </div>
    </div>

    <!-- New Permissions System Banner -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card" style="background: linear-gradient(135deg, #10b981, #059669); color: white; border: none;">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-2">
                                <i class="bi bi-star me-2"></i>
                                نظام إدارة الصلاحيات المتطور
                            </h4>
                            <p class="mb-0 opacity-90">
                                نظام متطور وسهل الاستخدام لإدارة صلاحيات المستخدمين مع قوالب جاهزة وواجهات احترافية
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{% url 'system_settings:permissions_management' %}" class="btn btn-light btn-lg">
                                <i class="bi bi-arrow-left me-2"></i>
                                انتقل لإدارة الصلاحيات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Users -->
    {% if active_users %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-people me-2"></i>
                        المستخدمين النشطين مؤخراً
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for user in active_users %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="avatar me-3">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        {{ user.first_name.0|default:user.username.0|upper }}
                                    </div>
                                </div>
                                <div>
                                    <h6 class="mb-1">{{ user.get_full_name|default:user.username }}</h6>
                                    <small class="text-muted">
                                        {% if user.last_login %}
                                            آخر دخول: {{ user.last_login|timesince }} مضت
                                        {% else %}
                                            لم يسجل دخول من قبل
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ تم تحميل لوحة تحكم إعدادات النظام المتقدمة');
    
    // تأثيرات تفاعلية للبطاقات
    document.querySelectorAll('.stats-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
