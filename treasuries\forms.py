from django import forms
from django.contrib.auth.models import User
from .models import Treasury, TreasuryTransaction, TreasuryTransfer

class TreasuryForm(forms.ModelForm):
    """نموذج إضافة/تعديل الخزائن"""
    class Meta:
        model = Treasury
        fields = ['name', 'code', 'description', 'location', 'responsible_person', 
                 'opening_balance', 'max_limit', 'min_limit', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الخزينة'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود الخزينة'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف الخزينة'}),
            'location': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'موقع الخزينة'}),
            'responsible_person': forms.Select(attrs={'class': 'form-select'}),
            'opening_balance': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'max_limit': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'min_limit': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['responsible_person'].queryset = User.objects.filter(is_active=True)
        self.fields['responsible_person'].empty_label = "اختر المسؤول"

class TreasuryTransactionForm(forms.ModelForm):
    """نموذج إضافة/تعديل معاملات الخزينة"""
    class Meta:
        model = TreasuryTransaction
        fields = ['treasury', 'transaction_type', 'amount', 'description', 
                 'reference_number', 'transaction_date', 'status']
        widgets = {
            'treasury': forms.Select(attrs={'class': 'form-select'}),
            'transaction_type': forms.Select(attrs={'class': 'form-select'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف المعاملة'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم المرجع'}),
            'transaction_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['treasury'].queryset = Treasury.objects.filter(is_active=True)

class TreasuryTransferForm(forms.ModelForm):
    """نموذج التحويل بين الخزائن"""
    class Meta:
        model = TreasuryTransfer
        fields = ['from_treasury', 'to_treasury', 'amount', 'transfer_date', 
                 'description', 'reference_number']
        widgets = {
            'from_treasury': forms.Select(attrs={'class': 'form-select'}),
            'to_treasury': forms.Select(attrs={'class': 'form-select'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'transfer_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف التحويل'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم المرجع'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['from_treasury'].queryset = Treasury.objects.filter(is_active=True)
        self.fields['to_treasury'].queryset = Treasury.objects.filter(is_active=True)

    def clean(self):
        cleaned_data = super().clean()
        from_treasury = cleaned_data.get('from_treasury')
        to_treasury = cleaned_data.get('to_treasury')
        amount = cleaned_data.get('amount')

        if from_treasury and to_treasury:
            if from_treasury == to_treasury:
                raise forms.ValidationError("لا يمكن التحويل من الخزينة إلى نفسها")
            
            if amount and from_treasury.current_balance < amount:
                raise forms.ValidationError(f"الرصيد المتاح في {from_treasury.name} غير كافي للتحويل")

        return cleaned_data

class TreasurySearchForm(forms.Form):
    """نموذج البحث في الخزائن"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم أو الكود...',
            'data-placeholder-en': 'Search by name or code...'
        })
    )
    responsible_person = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        required=False,
        empty_label="جميع المسؤولين",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    is_active = forms.ChoiceField(
        choices=[('', 'جميع الحالات'), ('True', 'نشط'), ('False', 'غير نشط')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

class TransactionSearchForm(forms.Form):
    """نموذج البحث في المعاملات"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالوصف أو رقم المرجع...',
            'data-placeholder-en': 'Search by description or reference number...'
        })
    )
    treasury = forms.ModelChoiceField(
        queryset=Treasury.objects.filter(is_active=True),
        required=False,
        empty_label="جميع الخزائن",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    transaction_type = forms.ChoiceField(
        choices=[('', 'جميع أنواع المعاملات')] + TreasuryTransaction.TRANSACTION_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + TreasuryTransaction.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
