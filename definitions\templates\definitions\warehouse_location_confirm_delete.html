{% extends 'base.html' %}
{% load static %}

{% block title %}حذف مكان الصنف - {{ object.name }}{% endblock %}

{% block extra_css %}
<style>
    /* Delete Confirmation Styles */
    :root {
        --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Hero Section */
    .delete-hero {
        background: var(--danger-gradient);
        padding: 2rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        border-radius: 0 0 2rem 2rem;
    }

    .delete-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        text-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .hero-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    /* Confirmation Container */
    .delete-container {
        background: white;
        border-radius: 20px;
        padding: 2.5rem;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        border: 1px solid #e5e7eb;
        position: relative;
        overflow: hidden;
        max-width: 600px;
        margin: 0 auto;
    }

    .delete-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--danger-gradient);
    }

    /* Warning Icon */
    .warning-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--warning-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        font-size: 2rem;
        color: white;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    /* Object Info */
    .object-info {
        background: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
        border: 1px solid #e5e7eb;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e5e7eb;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #6b7280;
    }

    .info-value {
        color: #374151;
        font-weight: 500;
    }

    /* Warning Message */
    .warning-message {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border: 2px solid #f59e0b;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: center;
    }

    .warning-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #92400e;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .warning-text {
        color: #92400e;
        margin-bottom: 0;
        line-height: 1.5;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn-delete {
        background: var(--danger-gradient);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        color: white;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(252, 70, 107, 0.3);
        color: white;
    }

    .btn-cancel {
        background: #6b7280;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        color: white;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cancel:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
        background: #4b5563;
        color: white;
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .delete-container {
            padding: 1.5rem;
            margin: 1rem;
        }
        
        .hero-title {
            font-size: 1.5rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-delete, .btn-cancel {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="delete-hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">
                <i class="bi bi-exclamation-triangle me-2"></i>
                تأكيد حذف المكان
            </h1>
            <p class="hero-subtitle">
                هذا الإجراء لا يمكن التراجع عنه
            </p>
        </div>
    </div>
</div>

<div class="container">
    <div class="delete-container">
        <!-- Warning Icon -->
        <div class="warning-icon">
            <i class="bi bi-exclamation-triangle"></i>
        </div>

        <!-- Confirmation Message -->
        <div class="text-center mb-4">
            <h3 class="text-danger mb-3">هل أنت متأكد من حذف هذا المكان؟</h3>
            <p class="text-muted">
                سيتم حذف جميع البيانات المرتبطة بهذا المكان نهائياً ولن يمكن استردادها.
            </p>
        </div>

        <!-- Object Information -->
        <div class="object-info">
            <div class="info-item">
                <span class="info-label">
                    <i class="bi bi-hash me-1"></i>
                    كود المكان
                </span>
                <span class="info-value">{{ object.code }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">
                    <i class="bi bi-tag me-1"></i>
                    اسم المكان
                </span>
                <span class="info-value">{{ object.name }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">
                    <i class="bi bi-building me-1"></i>
                    المخزن
                </span>
                <span class="info-value">{{ object.warehouse.name }}</span>
            </div>
            
            {% if object.current_capacity > 0 %}
            <div class="info-item">
                <span class="info-label">
                    <i class="bi bi-box me-1"></i>
                    السعة الحالية
                </span>
                <span class="info-value text-warning">{{ object.current_capacity|floatformat:1 }} وحدة</span>
            </div>
            {% endif %}
            
            {% if object.current_items > 0 %}
            <div class="info-item">
                <span class="info-label">
                    <i class="bi bi-collection me-1"></i>
                    عدد الأصناف
                </span>
                <span class="info-value text-warning">{{ object.current_items }} صنف</span>
            </div>
            {% endif %}
        </div>

        <!-- Warning Message -->
        {% if object.current_capacity > 0 or object.current_items > 0 %}
        <div class="warning-message">
            <div class="warning-title">
                <i class="bi bi-exclamation-triangle"></i>
                تحذير: المكان يحتوي على أصناف
            </div>
            <p class="warning-text">
                هذا المكان يحتوي حالياً على أصناف مخزنة. 
                تأكد من نقل جميع الأصناف إلى مكان آخر قبل الحذف لتجنب فقدان البيانات.
            </p>
        </div>
        {% endif %}

        <!-- Confirmation Form -->
        <form method="post">
            {% csrf_token %}
            <div class="action-buttons">
                <button type="submit" class="btn btn-delete">
                    <i class="bi bi-trash"></i>
                    نعم، احذف المكان
                </button>
                <a href="{% url 'definitions:warehouse_location_detail' object.id %}" class="btn btn-cancel">
                    <i class="bi bi-x-circle"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
