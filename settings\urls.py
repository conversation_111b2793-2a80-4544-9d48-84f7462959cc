from django.urls import path
from . import views, advanced_views

app_name = 'settings'

urlpatterns = [
    # لوحة التحكم الأساسية
    path('', views.settings_dashboard, name='dashboard'),

    # الإعدادات المتقدمة
    path('advanced/', advanced_views.advanced_settings_dashboard, name='advanced_dashboard'),
    path('advanced/system/', advanced_views.advanced_system_settings, name='advanced_system_settings'),
    path('advanced/users/', advanced_views.users_management_advanced, name='users_management_advanced'),
    path('advanced/users/<int:user_id>/', advanced_views.user_detail_advanced, name='user_detail_advanced'),

    # إدارة إعدادات النظام الأساسية
    path('system/', views.system_settings_list, name='system_settings_list'),
    path('system/create/', views.system_setting_create, name='system_setting_create'),
    path('system/<int:pk>/edit/', views.system_setting_edit, name='system_setting_edit'),
    path('system/<int:pk>/delete/', views.system_setting_delete, name='system_setting_delete'),

    # إدارة الأدوار والصلاحيات
    path('roles/', views.roles_management, name='roles_management'),
    path('roles/new/', views.role_detail, name='role_create'),
    path('roles/<int:role_id>/', views.role_detail, name='role_detail'),

    # سجلات النظام
    path('logs/', views.system_logs, name='system_logs'),
    path('logs/api/', views.system_logs_api, name='system_logs_api'),

    # الخدمات
    path('services/', views.services_management, name='services_management'),
    path('services/<int:service_id>/', views.service_detail, name='service_detail'),

    # النسخ الاحتياطي
    path('backup/', views.backup_management, name='backup_management'),
    path('backup/create/', views.create_backup, name='create_backup'),
    path('backup/restore/', views.restore_backup, name='restore_backup'),

    # API endpoints
    path('api/test-email/', views.test_email_settings, name='test_email'),
    path('api/test-service/<int:service_id>/', views.test_service, name='test_service'),
    path('api/user-permissions/<int:user_id>/', views.get_user_permissions, name='user_permissions'),
]
