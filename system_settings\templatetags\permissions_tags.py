from django import template
from django.db import connection

register = template.Library()


@register.filter
def has_permission(user, permission_code):
    """فحص ما إذا كان المستخدم لديه صلاحية معينة"""
    if not user or not user.is_authenticated:
        return False
    
    # المدير العام لديه جميع الصلاحيات
    if user.is_superuser:
        return True
    
    # فحص الصلاحيات من خلال الأدوار
    cursor = connection.cursor()
    cursor.execute("""
        SELECT COUNT(*) 
        FROM system_settings_userrole ur
        JOIN system_settings_role r ON ur.role_id = r.id
        JOIN system_settings_role_permissions rp ON r.id = rp.role_id
        JOIN system_settings_permission p ON rp.permission_id = p.id
        WHERE ur.user_id = %s 
        AND ur.is_active = 1 
        AND r.is_active = 1 
        AND p.is_active = 1 
        AND p.code = %s
    """, [user.id, permission_code])
    
    result = cursor.fetchone()
    return result[0] > 0 if result else False


@register.filter
def has_category_permission(user, category_action):
    """فحص صلاحية فئة معينة وإجراء معين
    الاستخدام: user|has_category_permission:"sales_view"
    """
    if not user or not user.is_authenticated:
        return False
    
    return has_permission(user, category_action)


@register.simple_tag
def user_permissions(user):
    """الحصول على جميع صلاحيات المستخدم"""
    if not user or not user.is_authenticated:
        return []
    
    if user.is_superuser:
        cursor = connection.cursor()
        cursor.execute("""
            SELECT code, name, category, action 
            FROM system_settings_permission 
            WHERE is_active = 1
            ORDER BY category, action
        """)
        return [{'code': row[0], 'name': row[1], 'category': row[2], 'action': row[3]} 
                for row in cursor.fetchall()]
    
    cursor = connection.cursor()
    cursor.execute("""
        SELECT DISTINCT p.code, p.name, p.category, p.action
        FROM system_settings_userrole ur
        JOIN system_settings_role r ON ur.role_id = r.id
        JOIN system_settings_role_permissions rp ON r.id = rp.role_id
        JOIN system_settings_permission p ON rp.permission_id = p.id
        WHERE ur.user_id = %s 
        AND ur.is_active = 1 
        AND r.is_active = 1 
        AND p.is_active = 1
        ORDER BY p.category, p.action
    """, [user.id])
    
    return [{'code': row[0], 'name': row[1], 'category': row[2], 'action': row[3]} 
            for row in cursor.fetchall()]


@register.simple_tag
def user_roles(user):
    """الحصول على أدوار المستخدم"""
    if not user or not user.is_authenticated:
        return []
    
    cursor = connection.cursor()
    cursor.execute("""
        SELECT r.id, r.name, r.code, r.description
        FROM system_settings_userrole ur
        JOIN system_settings_role r ON ur.role_id = r.id
        WHERE ur.user_id = %s 
        AND ur.is_active = 1 
        AND r.is_active = 1
        ORDER BY r.name
    """, [user.id])
    
    return [{'id': row[0], 'name': row[1], 'code': row[2], 'description': row[3]} 
            for row in cursor.fetchall()]


@register.inclusion_tag('system_settings/permission_check.html')
def permission_required(user, permission_code):
    """عرض محتوى فقط إذا كان المستخدم لديه الصلاحية"""
    return {
        'has_permission': has_permission(user, permission_code),
        'user': user,
        'permission_code': permission_code
    }
