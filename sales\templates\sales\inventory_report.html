<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المخزون - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>


    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    .report-header {
        background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .filter-section {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border-left: 5px solid;
        text-align: center;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }
    
    .stats-card.total { border-left-color: #007bff; }
    .stats-card.value { border-left-color: #28a745; }
    .stats-card.low { border-left-color: #ffc107; }
    .stats-card.out { border-left-color: #dc3545; }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin: 0 auto 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .icon-total { background: linear-gradient(45deg, #007bff, #0056b3); color: white; }
    .icon-value { background: linear-gradient(45deg, #28a745, #1e7e34); color: white; }
    .icon-low { background: linear-gradient(45deg, #ffc107, #e0a800); color: #333; }
    .icon-out { background: linear-gradient(45deg, #dc3545, #c82333); color: white; }
    
    .stats-number {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 10px;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .product-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border-left: 4px solid #fd7e14;
    }
    
    .product-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }
    
    .product-card.low-stock {
        border-left-color: #ffc107;
        background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
    }
    
    .product-card.out-of-stock {
        border-left-color: #dc3545;
        background: linear-gradient(135deg, #f8d7da 0%, #ffffff 100%);
    }
    
    .product-name {
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
    }
    
    .product-code {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 15px;
    }
    
    .product-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .stock-level {
        font-size: 1.5rem;
        font-weight: 700;
    }
    
    .stock-normal { color: #28a745; }
    .stock-low { color: #ffc107; }
    .stock-out { color: #dc3545; }
    
    .product-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: #fd7e14;
    }
    
    .stock-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-normal { background: #d4edda; color: #155724; }
    .status-low { background: #fff3cd; color: #856404; }
    .status-out { background: #f8d7da; color: #721c24; }
    
    .btn-filter {
        background: linear-gradient(45deg, #fd7e14, #e55a00);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(253, 126, 20, 0.3);
        color: white;
    }
    
    .section-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 25px;
        color: #333;
        border-bottom: 3px solid #fd7e14;
        padding-bottom: 10px;
    }
    
    .alert-section {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 25px;
        border-radius: 20px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .alert-icon {
        font-size: 3rem;
        margin-bottom: 15px;
    }
    
    .category-filter {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .category-badge {
        background: #fd7e14;
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
        margin-bottom: 10px;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس التقرير -->
    <div class="report-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-boxes"></i>
                    تقرير المخزون التفصيلي
                </h1>
                <p class="mb-0">تحليل شامل لحالة المخزون والمنتجات والقيم الإجمالية</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <h5>{{ "now"|date:"l, j F Y" }}</h5>
                    <p class="mb-0">آخر تحديث: {{ "now"|date:"H:i" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم الفلترة -->
    <div class="filter-section">
        <h4 class="section-title">فلترة المنتجات</h4>
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="اسم المنتج أو الكود" value="{{ search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">الفئة</label>
                <select name="category" class="form-select">
                    <option value="">جميع الفئات</option>
                    {% for cat in categories %}
                        <option value="{{ cat }}" {% if category == cat %}selected{% endif %}>
                            {{ cat }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">حالة المخزون</label>
                <select name="low_stock" class="form-select">
                    <option value="">جميع المنتجات</option>
                    <option value="1" {% if low_stock %}selected{% endif %}>منخفض المخزون فقط</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-filter w-100">
                    <i class="bi bi-funnel"></i>
                    تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card total">
                <div class="stats-icon icon-total">
                    <i class="bi bi-box-seam"></i>
                </div>
                <div class="stats-number text-primary">{{ total_products }}</div>
                <div class="stats-label">إجمالي المنتجات</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card value">
                <div class="stats-icon icon-value">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="stats-number text-success">{{ total_value|floatformat:0 }}</div>
                <div class="stats-label">قيمة المخزون (ج.م)</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card low">
                <div class="stats-icon icon-low">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <div class="stats-number text-warning">{{ low_stock_count }}</div>
                <div class="stats-label">منتجات منخفضة المخزون</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card out">
                <div class="stats-icon icon-out">
                    <i class="bi bi-x-circle"></i>
                </div>
                <div class="stats-number text-danger">{{ out_of_stock_count }}</div>
                <div class="stats-label">منتجات نفدت من المخزون</div>
            </div>
        </div>
    </div>

    <!-- تنبيه المنتجات منخفضة المخزون -->
    {% if low_stock_count > 0 %}
        <div class="alert-section">
            <div class="alert-icon">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <h4>تنبيه: منتجات تحتاج إعادة تموين</h4>
            <p class="mb-0">يوجد {{ low_stock_count }} منتج منخفض المخزون يحتاج إعادة تموين فوري</p>
        </div>
    {% endif %}

    <!-- قائمة المنتجات -->
    <div class="row">
        <div class="col-12">
            <h4 class="section-title">تفاصيل المنتجات</h4>
        </div>
    </div>

    <div class="row">
        {% for product in products %}
            <div class="col-lg-4 col-md-6">
                <div class="product-card {% if product.stock_quantity == 0 %}out-of-stock{% elif product.stock_quantity <= product.min_stock_level %}low-stock{% endif %}">
                    {% if product.category %}
                        <div class="category-badge">{{ product.category }}</div>
                    {% endif %}
                    
                    <div class="product-name">{{ product.name }}</div>
                    <div class="product-code">كود: {{ product.code }}</div>
                    
                    <div class="product-details">
                        <div>
                            <div class="stock-level {% if product.stock_quantity == 0 %}stock-out{% elif product.stock_quantity <= product.min_stock_level %}stock-low{% else %}stock-normal{% endif %}">
                                {{ product.stock_quantity }} {{ product.unit }}
                            </div>
                            <small class="text-muted">الكمية المتاحة</small>
                        </div>
                        <div class="text-end">
                            <div class="product-value">
                                {{ product.stock_quantity|floatformat:0 }} × {{ product.cost_price }} = 
                                {{ product.stock_quantity|mul:product.cost_price|floatformat:0 }} ج.م
                            </div>
                            <small class="text-muted">القيمة الإجمالية</small>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">الحد الأدنى: {{ product.min_stock_level }}</small>
                        </div>
                        <div>
                            {% if product.stock_quantity == 0 %}
                                <span class="stock-status status-out">نفد المخزون</span>
                            {% elif product.stock_quantity <= product.min_stock_level %}
                                <span class="stock-status status-low">مخزون منخفض</span>
                            {% else %}
                                <span class="stock-status status-normal">متوفر</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="col-12">
                <div class="text-center text-muted py-5">
                    <i class="bi bi-inbox" style="font-size: 4rem; opacity: 0.5;"></i>
                    <h4>لا توجد منتجات</h4>
                    <p>لم يتم العثور على منتجات تطابق معايير البحث</p>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- أزرار التصدير -->
    <div class="text-center mt-4">
        <button class="btn btn-outline-primary" onclick="exportToPDF()">
            <i class="bi bi-file-earmark-pdf"></i>
            تصدير PDF
        </button>
        <button class="btn btn-outline-success" onclick="exportToExcel()">
            <i class="bi bi-file-earmark-excel"></i>
            تصدير Excel
        </button>
        <button class="btn btn-outline-info" onclick="window.print()">
            <i class="bi bi-printer"></i>
            طباعة
        </button>
    </div>
</div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .inventory-table, .alert-section');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });

            // رسم بياني لحالة المخزون
            const stockCtx = document.getElementById('stockStatusChart').getContext('2d');
            const stockChart = new Chart(stockCtx, {
                type: 'doughnut',
                data: {
                    labels: ['متوفر', 'مخزون منخفض', 'نفد المخزون'],
                    datasets: [{
                        data: [
                            {{ total_products }} - {{ low_stock_products }} - {{ out_of_stock_products }},
                            {{ low_stock_products }},
                            {{ out_of_stock_products }}
                        ],
                        backgroundColor: [
                            '#28a745',
                            '#ffc107',
                            '#dc3545'
                        ],
                        borderWidth: 3,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });

            // رسم بياني لأعلى المنتجات قيمة
            const topProductsCtx = document.getElementById('topProductsChart').getContext('2d');
            const topProductsChart = new Chart(topProductsCtx, {
                type: 'bar',
                data: {
                    labels: {{ top_products_names|safe }},
                    datasets: [{
                        label: 'قيمة المخزون (ج.م)',
                        data: {{ top_products_values|safe }},
                        backgroundColor: 'rgba(168, 237, 234, 0.8)',
                        borderColor: 'rgba(168, 237, 234, 1)',
                        borderWidth: 2,
                        borderRadius: 10
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ج.م';
                                }
                            }
                        }
                    }
                }
            });
        });

        function exportReport() {
            // تصدير التقرير إلى PDF أو Excel
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'pdf');
            window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
        }
    </script>
</body>
</html>
