from django.db import models
from django.contrib.auth.models import User
from branches.models import Branch

class Department(models.Model):
    """الأقسام"""
    name = models.CharField(max_length=100, verbose_name="اسم القسم")
    code = models.Char<PERSON>ield(max_length=20, unique=True, verbose_name="رمز القسم")
    description = models.TextField(blank=True, verbose_name="الوصف")
    manager = models.ForeignKey('Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_departments', verbose_name="مدير القسم")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "قسم"
        verbose_name_plural = "الأقسام"

    def __str__(self):
        return self.name

class Position(models.Model):
    """المناصب الوظيفية"""
    name = models.CharField(max_length=100, verbose_name="اسم المنصب")
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز المنصب")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name="القسم")
    basic_salary = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الراتب الأساسي")
    description = models.TextField(blank=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "منصب وظيفي"
        verbose_name_plural = "المناصب الوظيفية"

    def __str__(self):
        return f"{self.name} - {self.department.name}"

class Employee(models.Model):
    """الموظفين"""
    GENDER_CHOICES = [
        ('male', 'ذكر'),
        ('female', 'أنثى'),
    ]

    MARITAL_STATUS = [
        ('single', 'أعزب'),
        ('married', 'متزوج'),
        ('divorced', 'مطلق'),
        ('widowed', 'أرمل'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('terminated', 'منتهي الخدمة'),
        ('suspended', 'موقوف'),
    ]

    # Personal Information
    employee_id = models.CharField(max_length=20, unique=True, verbose_name="رقم الموظف")
    user = models.OneToOneField(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name="حساب المستخدم")
    first_name = models.CharField(max_length=50, verbose_name="الاسم الأول")
    last_name = models.CharField(max_length=50, verbose_name="اسم العائلة")
    national_id = models.CharField(max_length=20, unique=True, verbose_name="رقم الهوية")
    birth_date = models.DateField(verbose_name="تاريخ الميلاد")
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, verbose_name="الجنس")
    marital_status = models.CharField(max_length=10, choices=MARITAL_STATUS, verbose_name="الحالة الاجتماعية")

    # Contact Information
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    email = models.EmailField(verbose_name="البريد الإلكتروني")
    address = models.TextField(verbose_name="العنوان")

    # Work Information
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, verbose_name="الفرع")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name="القسم")
    position = models.ForeignKey(Position, on_delete=models.CASCADE, verbose_name="المنصب")
    hire_date = models.DateField(verbose_name="تاريخ التوظيف")
    salary = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الراتب")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")

    # Additional Information
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "موظف"
        verbose_name_plural = "الموظفين"
        ordering = ['employee_id']

    def __str__(self):
        return f"{self.employee_id} - {self.first_name} {self.last_name}"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self):
        """حساب العمر"""
        from datetime import date
        today = date.today()
        return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))

    @property
    def years_of_service(self):
        """حساب سنوات الخدمة"""
        from datetime import date
        today = date.today()
        return today.year - self.hire_date.year - ((today.month, today.day) < (self.hire_date.month, self.hire_date.day))

class Attendance(models.Model):
    """نموذج الحضور والانصراف"""
    employee = models.ForeignKey(Employee, related_name='attendances', on_delete=models.CASCADE, verbose_name="الموظف")
    date = models.DateField(verbose_name="التاريخ")
    check_in = models.TimeField(null=True, blank=True, verbose_name="وقت الحضور")
    check_out = models.TimeField(null=True, blank=True, verbose_name="وقت الانصراف")
    break_start = models.TimeField(null=True, blank=True, verbose_name="بداية الاستراحة")
    break_end = models.TimeField(null=True, blank=True, verbose_name="نهاية الاستراحة")
    total_hours = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="إجمالي الساعات")
    overtime_hours = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="ساعات إضافية")
    late_minutes = models.PositiveIntegerField(default=0, verbose_name="دقائق التأخير")
    early_leave_minutes = models.PositiveIntegerField(default=0, verbose_name="دقائق المغادرة المبكرة")
    is_absent = models.BooleanField(default=False, verbose_name="غائب")
    absence_reason = models.CharField(max_length=200, blank=True, null=True, verbose_name="سبب الغياب")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "حضور وانصراف"
        verbose_name_plural = "الحضور والانصراف"
        unique_together = ['employee', 'date']
        ordering = ['-date', 'employee']

    def __str__(self):
        return f"{self.employee.full_name} - {self.date}"

    def calculate_hours(self):
        """حساب ساعات العمل"""
        if self.check_in and self.check_out:
            from datetime import datetime, timedelta

            # تحويل الأوقات إلى datetime
            check_in_dt = datetime.combine(self.date, self.check_in)
            check_out_dt = datetime.combine(self.date, self.check_out)

            # حساب إجمالي الوقت
            total_time = check_out_dt - check_in_dt

            # طرح وقت الاستراحة إذا كان موجوداً
            if self.break_start and self.break_end:
                break_start_dt = datetime.combine(self.date, self.break_start)
                break_end_dt = datetime.combine(self.date, self.break_end)
                break_time = break_end_dt - break_start_dt
                total_time -= break_time

            # تحويل إلى ساعات
            self.total_hours = total_time.total_seconds() / 3600

            # حساب الساعات الإضافية (أكثر من 8 ساعات)
            if self.total_hours > 8:
                self.overtime_hours = self.total_hours - 8

            self.save()

class Leave(models.Model):
    """نموذج الإجازات"""
    LEAVE_TYPE_CHOICES = [
        ('annual', 'إجازة سنوية'),
        ('sick', 'إجازة مرضية'),
        ('emergency', 'إجازة طارئة'),
        ('maternity', 'إجازة أمومة'),
        ('paternity', 'إجازة أبوة'),
        ('unpaid', 'إجازة بدون راتب'),
        ('hajj', 'إجازة حج'),
        ('study', 'إجازة دراسية'),
    ]

    STATUS_CHOICES = [
        ('pending', 'معلق'),
        ('approved', 'موافق عليه'),
        ('rejected', 'مرفوض'),
        ('cancelled', 'ملغي'),
    ]

    employee = models.ForeignKey(Employee, related_name='leaves', on_delete=models.CASCADE, verbose_name="الموظف")
    leave_type = models.CharField(max_length=20, choices=LEAVE_TYPE_CHOICES, verbose_name="نوع الإجازة")
    start_date = models.DateField(verbose_name="تاريخ البداية")
    end_date = models.DateField(verbose_name="تاريخ النهاية")
    days_count = models.PositiveIntegerField(verbose_name="عدد الأيام")
    reason = models.TextField(verbose_name="السبب")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="الحالة")
    approved_by = models.ForeignKey(Employee, related_name='approved_leaves', on_delete=models.SET_NULL,
                                   null=True, blank=True, verbose_name="تمت الموافقة بواسطة")
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الموافقة")
    rejection_reason = models.TextField(blank=True, null=True, verbose_name="سبب الرفض")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الطلب")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "إجازة"
        verbose_name_plural = "الإجازات"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_leave_type_display()} ({self.start_date} - {self.end_date})"

    def save(self, *args, **kwargs):
        """حساب عدد الأيام تلقائياً"""
        if self.start_date and self.end_date:
            self.days_count = (self.end_date - self.start_date).days + 1
        super().save(*args, **kwargs)

class Payroll(models.Model):
    """نموذج كشوف الرواتب"""
    employee = models.ForeignKey(Employee, related_name='payrolls', on_delete=models.CASCADE, verbose_name="الموظف")
    month = models.PositiveIntegerField(verbose_name="الشهر")
    year = models.PositiveIntegerField(verbose_name="السنة")
    basic_salary = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الراتب الأساسي")
    allowances = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="البدلات")
    overtime_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="مبلغ الساعات الإضافية")
    deductions = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الخصومات")
    insurance_deduction = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="خصم التأمين")
    tax_deduction = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="خصم الضريبة")
    net_salary = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="صافي الراتب")
    is_paid = models.BooleanField(default=False, verbose_name="تم الدفع")
    payment_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الدفع")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "كشف راتب"
        verbose_name_plural = "كشوف الرواتب"
        unique_together = ['employee', 'month', 'year']
        ordering = ['-year', '-month', 'employee']

    def __str__(self):
        return f"{self.employee.full_name} - {self.month}/{self.year}"

    def calculate_net_salary(self):
        """حساب صافي الراتب"""
        gross_salary = self.basic_salary + self.allowances + self.overtime_amount
        total_deductions = self.deductions + self.insurance_deduction + self.tax_deduction
        self.net_salary = gross_salary - total_deductions
        self.save()
