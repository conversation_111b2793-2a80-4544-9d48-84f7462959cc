#!/usr/bin/env python
"""
اختبار نظام تنبيهات المشتريات
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from purchases.models import AlertManager, PaymentAlert, PurchaseInvoice, Supplier
from django.contrib.auth.models import User
from datetime import date, timedelta

def test_purchase_alerts():
    """اختبار إنشاء تنبيهات المشتريات"""
    print("🔄 بدء اختبار نظام تنبيهات المشتريات...")
    
    try:
        # إنشاء تنبيهات المستحقات القريبة
        print("\n1️⃣ إنشاء تنبيهات المستحقات القريبة...")
        AlertManager.create_due_soon_alerts()
        due_soon_alerts = PaymentAlert.objects.filter(alert_type='due_soon')
        print(f"✅ تم إنشاء {due_soon_alerts.count()} تنبيه للمستحقات القريبة")
        
        # إنشاء تنبيهات المتأخرات
        print("\n2️⃣ إنشاء تنبيهات المتأخرات...")
        AlertManager.create_overdue_alerts()
        overdue_alerts = PaymentAlert.objects.filter(alert_type='overdue')
        print(f"✅ تم إنشاء {overdue_alerts.count()} تنبيه للمتأخرات")
        
        # فحص حدود الائتمان
        print("\n3️⃣ فحص حدود الائتمان...")
        AlertManager.check_credit_limits()
        credit_alerts = PaymentAlert.objects.filter(alert_type='credit_limit')
        print(f"✅ تم إنشاء {credit_alerts.count()} تنبيه لحدود الائتمان")
        
        # اختبار الحصول على التنبيهات للإشعارات
        print("\n4️⃣ اختبار الحصول على التنبيهات للإشعارات...")
        notifications = AlertManager.get_alerts_for_notifications()
        print(f"✅ تم الحصول على {len(notifications)} إشعار")
        
        # عرض تفاصيل بعض التنبيهات
        if notifications:
            print("\n📋 تفاصيل أول 3 تنبيهات:")
            for i, notification in enumerate(notifications[:3]):
                print(f"   {i+1}. {notification['title']}")
                print(f"      النوع: {notification['type']}")
                print(f"      الأولوية: {notification['priority']}")
                if notification.get('amount'):
                    print(f"      المبلغ: {notification['amount']} ج.م")
                if notification.get('supplier'):
                    print(f"      المورد: {notification['supplier']}")
                print()
        
        print("✅ تم اختبار نظام تنبيهات المشتريات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام التنبيهات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """اختبار API endpoints"""
    print("\n🔄 اختبار API endpoints...")
    
    try:
        from django.test import Client
        from django.contrib.auth.models import User
        
        # إنشاء مستخدم للاختبار
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        client = Client()
        client.force_login(user)
        
        # اختبار API التنبيهات
        response = client.get('/purchases/api/alerts/')
        print(f"✅ API التنبيهات: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   عدد التنبيهات: {data.get('count', 0)}")
            print(f"   غير المقروءة: {data.get('unread_count', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار API: {e}")
        return False

if __name__ == '__main__':
    print("🚀 بدء اختبار نظام ربط تنبيهات المشتريات بالإشعارات")
    print("=" * 60)
    
    # اختبار النظام
    alerts_test = test_purchase_alerts()
    api_test = test_api_endpoints()
    
    print("\n" + "=" * 60)
    if alerts_test and api_test:
        print("🎉 تم اختبار النظام بنجاح! يمكنك الآن رؤية تنبيهات المشتريات في زر الإشعارات")
    else:
        print("⚠️ هناك مشاكل في النظام تحتاج إلى إصلاح")
