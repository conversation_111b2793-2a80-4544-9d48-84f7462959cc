{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    
    .manufacturing-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border-radius: 25px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        position: relative;
        overflow: hidden;
    }
    
    .manufacturing-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }
    
    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }
    
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .section-title {
        color: #2c3e50;
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .section-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }
    
    .order-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin: 2rem 0;
    }
    
    .info-item {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid #667eea;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .info-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
    }
    
    .status-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-in-progress {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin: 3rem 0;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        border: none;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .btn-complete {
        background-color: #28a745;
        color: white;
        min-width: 150px;
    }
    
    .btn-complete:hover {
        background-color: #218838;
        color: white;
    }
    
    .btn-cancel {
        background-color: #6c757d;
        color: white;
    }
    
    .btn-cancel:hover {
        background-color: #5a6268;
        color: white;
    }
    
    .completion-info {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        color: #0d47a1;
    }
    
    .completion-info h5 {
        color: #1565c0;
        font-weight: 600;
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="manufacturing-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-check-circle me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'manufacturing:order_detail' order.id %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة لتفاصيل الأمر
                </a>
            </div>
        </div>
    </div>

    <!-- ملخص الأمر -->
    <div class="content-card">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-info-circle"></i>
            </div>
            ملخص أمر التصنيع
        </h2>
        
        <div class="order-info-grid">
            <div class="info-item">
                <div class="info-label">رقم الأمر</div>
                <div class="info-value">{{ order.order_number }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">المنتج النهائي</div>
                <div class="info-value">{{ order.final_product.name }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">الكمية المنتجة</div>
                <div class="info-value">{{ order.quantity_to_produce }} {{ order.unit_of_measure.name }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">الحالة الحالية</div>
                <div class="info-value">
                    <span class="status-badge status-in-progress">
                        <i class="bi bi-gear me-1"></i>قيد التنفيذ
                    </span>
                </div>
            </div>
            
            <div class="info-item">
                <div class="info-label">تاريخ بدء الإنتاج</div>
                <div class="info-value">{{ order.started_at|date:"d/m/Y H:i" }}</div>
            </div>
            
            <div class="info-item">
                <div class="info-label">مخزن المنتجات التامة</div>
                <div class="info-value">{{ order.finished_goods_warehouse.name }}</div>
            </div>
        </div>
    </div>

    <!-- معلومات الإكمال -->
    <div class="completion-info">
        <h5><i class="bi bi-info-circle me-2"></i>معلومات إكمال الإنتاج</h5>
        <p>عند إكمال الإنتاج سيتم:</p>
        <ul>
            <li>إضافة {{ order.quantity_to_produce }} {{ order.unit_of_measure.name }} من {{ order.final_product.name }} إلى مخزن {{ order.finished_goods_warehouse.name }}</li>
            <li>تغيير حالة الأمر إلى "مكتمل"</li>
            <li>تسجيل تاريخ ووقت الإكمال</li>
        </ul>
        <p class="mb-0"><strong>تأكد من اكتمال جميع مراحل الإنتاج قبل المتابعة.</strong></p>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="action-buttons">
        <form method="post" style="display: inline;">
            {% csrf_token %}
            <input type="submit" value="إكمال الإنتاج وإضافة للمخزون" class="btn-action btn-complete">
        </form>

        <a href="{% url 'manufacturing:order_detail' order.id %}" class="btn-action btn-cancel">
            العودة
        </a>
    </div>
</div>
{% endblock %}
