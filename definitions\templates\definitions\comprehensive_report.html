{% extends 'base.html' %}
{% load static %}

{% block title %}التقرير الشامل للتعريفات - أوساريك{% endblock %}

{% block breadcrumb %}التعريفات <i class="bi bi-chevron-left mx-2"></i> التقرير الشامل{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .report-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
        color: #333;
    }

    .report-header {
        text-align: center;
        margin-bottom: 3rem;
        padding-bottom: 2rem;
        border-bottom: 3px solid rgba(102, 126, 234, 0.3);
    }

    .report-title {
        font-size: 3rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
    }

    .report-subtitle {
        color: #6c757d;
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }

    .report-date {
        color: #495057;
        font-size: 1rem;
        font-weight: 600;
    }

    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .summary-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
        transition: all 0.3s ease;
    }

    .summary-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(31, 38, 135, 0.5);
    }

    .summary-number {
        font-size: 3rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
    }

    .summary-label {
        color: #6c757d;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .section {
        background: rgba(102, 126, 234, 0.1);
        backdrop-filter: blur(5px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .stat-item {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 15px;
        padding: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-3px);
    }

    .stat-label {
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #667eea;
    }

    .percentage {
        font-size: 0.9rem;
        color: #28a745;
        margin-right: 0.5rem;
    }

    .btn {
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 700;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 1rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid rgba(102, 126, 234, 0.2);
    }

    @media (max-width: 768px) {
        .report-container {
            padding: 2rem;
        }
        
        .report-title {
            font-size: 2rem;
        }
        
        .summary-cards {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            <div class="report-container">
                <div class="report-header">
                    <h1 class="report-title">
                        <i class="bi bi-graph-up me-3"></i>
                        التقرير الشامل للتعريفات
                    </h1>
                    <p class="report-subtitle">تقرير شامل لجميع التعريفات والإعدادات في النظام</p>
                    <p class="report-date">
                        <i class="bi bi-calendar me-2"></i>
                        تاريخ التقرير: {{ "now"|date:"d/m/Y H:i" }}
                    </p>
                </div>

                <!-- Summary Cards -->
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="summary-number">{{ total_definitions }}</div>
                        <div class="summary-label">إجمالي التعريفات</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number">{{ total_active }}</div>
                        <div class="summary-label">التعريفات النشطة</div>
                    </div>
                    <div class="summary-card">
                        <div class="summary-number">
                            {% if total_definitions > 0 %}
                                {{ total_active|floatformat:0 }}/{{ total_definitions|floatformat:0 }}
                            {% else %}
                                0/0
                            {% endif %}
                        </div>
                        <div class="summary-label">نسبة التفعيل</div>
                    </div>
                </div>

                <!-- المخازن والأصناف -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="bi bi-building"></i>
                        المخازن والأصناف
                    </h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-building"></i>المخازن
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_warehouses %}
                                    <span class="percentage">{{ percentages.active_warehouses }}%</span>
                                {% endif %}
                                {{ stats.active_warehouses }}/{{ stats.warehouses }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-box"></i>الأصناف
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_products %}
                                    <span class="percentage">{{ percentages.active_products }}%</span>
                                {% endif %}
                                {{ stats.active_products }}/{{ stats.products }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-tags"></i>فئات الأصناف
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_categories %}
                                    <span class="percentage">{{ percentages.active_categories }}%</span>
                                {% endif %}
                                {{ stats.active_categories }}/{{ stats.categories }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- العملات والبنوك -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="bi bi-currency-exchange"></i>
                        العملات والبنوك
                    </h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-currency-dollar"></i>العملات
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_currencies %}
                                    <span class="percentage">{{ percentages.active_currencies }}%</span>
                                {% endif %}
                                {{ stats.active_currencies }}/{{ stats.currencies }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-bank"></i>البنوك
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_banks %}
                                    <span class="percentage">{{ percentages.active_banks }}%</span>
                                {% endif %}
                                {{ stats.active_banks }}/{{ stats.banks }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-safe"></i>الخزائن
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_cash_boxes %}
                                    <span class="percentage">{{ percentages.active_cash_boxes }}%</span>
                                {% endif %}
                                {{ stats.active_cash_boxes }}/{{ stats.cash_boxes }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الأشخاص -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="bi bi-people"></i>
                        الأشخاص
                    </h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-people"></i>إجمالي الأشخاص
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_persons %}
                                    <span class="percentage">{{ percentages.active_persons }}%</span>
                                {% endif %}
                                {{ stats.active_persons }}/{{ stats.persons }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-person-check"></i>العملاء
                            </div>
                            <div class="stat-value">{{ stats.customers }}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-truck"></i>الموردين
                            </div>
                            <div class="stat-value">{{ stats.suppliers }}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-person-badge"></i>الموظفين
                            </div>
                            <div class="stat-value">{{ stats.employees }}</div>
                        </div>
                    </div>
                </div>

                <!-- الوحدات والأصول -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="bi bi-rulers"></i>
                        الوحدات والأصول
                    </h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-rulers"></i>وحدات القياس
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_units %}
                                    <span class="percentage">{{ percentages.active_units }}%</span>
                                {% endif %}
                                {{ stats.active_units }}/{{ stats.units }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-collection"></i>مجموعات الأصول
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_asset_groups %}
                                    <span class="percentage">{{ percentages.active_asset_groups }}%</span>
                                {% endif %}
                                {{ stats.active_asset_groups }}/{{ stats.asset_groups }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-award"></i>علامات الأصول
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_asset_brands %}
                                    <span class="percentage">{{ percentages.active_asset_brands }}%</span>
                                {% endif %}
                                {{ stats.active_asset_brands }}/{{ stats.asset_brands }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المصروفات والإيرادات -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="bi bi-graph-up-arrow"></i>
                        المصروفات والإيرادات
                    </h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-arrow-down-circle"></i>أنواع المصروفات
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_expense_types %}
                                    <span class="percentage">{{ percentages.active_expense_types }}%</span>
                                {% endif %}
                                {{ stats.active_expense_types }}/{{ stats.expense_types }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-receipt"></i>أسماء المصروفات
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_expense_names %}
                                    <span class="percentage">{{ percentages.active_expense_names }}%</span>
                                {% endif %}
                                {{ stats.active_expense_names }}/{{ stats.expense_names }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-arrow-up-circle"></i>أنواع الإيرادات
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_revenue_types %}
                                    <span class="percentage">{{ percentages.active_revenue_types }}%</span>
                                {% endif %}
                                {{ stats.active_revenue_types }}/{{ stats.revenue_types }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-cash"></i>أسماء الإيرادات
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_revenue_names %}
                                    <span class="percentage">{{ percentages.active_revenue_names }}%</span>
                                {% endif %}
                                {{ stats.active_revenue_names }}/{{ stats.revenue_names }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- مراكز الربحية والطابعات -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="bi bi-gear"></i>
                        مراكز الربحية والطابعات
                    </h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-bullseye"></i>مراكز الربحية
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_profit_centers %}
                                    <span class="percentage">{{ percentages.active_profit_centers }}%</span>
                                {% endif %}
                                {{ stats.active_profit_centers }}/{{ stats.profit_centers }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-house"></i>المراكز الرئيسية
                            </div>
                            <div class="stat-value">{{ stats.main_profit_centers }}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-diagram-3"></i>المراكز الفرعية
                            </div>
                            <div class="stat-value">{{ stats.sub_profit_centers }}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-printer"></i>الطابعات
                            </div>
                            <div class="stat-value">
                                {% if percentages.active_printers %}
                                    <span class="percentage">{{ percentages.active_printers }}%</span>
                                {% endif %}
                                {{ stats.active_printers }}/{{ stats.printers }}
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">
                                <i class="bi bi-star"></i>الطابعات الافتراضية
                            </div>
                            <div class="stat-value">{{ stats.default_printers }}</div>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-right me-2"></i>عودة للتعريفات
                    </a>
                    <button onclick="window.print()" class="btn btn-primary">
                        <i class="bi bi-printer me-2"></i>طباعة التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل التقرير الشامل');
    
    // إضافة تأثيرات بصرية للإحصائيات
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            item.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, 100);
        }, index * 50);
    });
});
</script>
{% endblock %}
