from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login
from django.contrib.auth.forms import AuthenticationForm
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user
from django.contrib import messages
from django.views.decorators.csrf import csrf_protect
from .models import UserSettings
from definitions.models import (
    PersonDefinition, ProductDefinition, WarehouseDefinition,
    CurrencyDefinition, BankDefinition
)

@csrf_protect
def login_view(request):
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)

                # إضافة رسالة ترحيب للجلسة
                user_name = user.first_name or user.username
                messages.success(request, f'مرحباً بك {user_name}! تم تسجيل دخولك بنجاح.')

                # تعيين علامة لتشغيل صوت الترحيب
                request.session['show_welcome_notification'] = True
                request.session['user_just_logged_in'] = True

                return redirect('dashboard_home')
    else:
        form = AuthenticationForm()
    return render(request, 'registration/login.html', {'form': form})

@login_required
def dashboard_home(request):
    # إحصائيات من تطبيق التعريفات
    customers_count = PersonDefinition.objects.filter(person_type__in=['customer', 'both']).count()
    suppliers_count = PersonDefinition.objects.filter(person_type__in=['supplier', 'both']).count()
    products_count = ProductDefinition.objects.count()
    warehouses_count = WarehouseDefinition.objects.count()
    currencies_count = CurrencyDefinition.objects.count()
    banks_count = BankDefinition.objects.count()

    # منتجات تحت الحد الأدنى
    low_stock_count = ProductDefinition.objects.filter(
        minimum_stock__gt=0,
        is_active=True,
        track_inventory=True
    ).count()

    user = get_user(request)

    # فحص إذا كان المستخدم سجل دخول للتو
    show_welcome = request.session.pop('show_welcome_notification', False)
    user_just_logged_in = request.session.pop('user_just_logged_in', False)

    return render(request, 'dashboard/home.html', {
        'customers_count': customers_count,
        'suppliers_count': suppliers_count,
        'products_count': products_count,
        'warehouses_count': warehouses_count,
        'currencies_count': currencies_count,
        'banks_count': banks_count,
        'low_stock_count': low_stock_count,
        'user': user,
        'show_welcome_notification': show_welcome,
        'user_just_logged_in': user_just_logged_in,
    })

@login_required
def profile_view(request):
    """عرض وتحديث الملف الشخصي للمستخدم"""
    user = request.user

    if request.method == 'POST':
        # تحديث بيانات المستخدم
        first_name = request.POST.get('first_name', '').strip()
        last_name = request.POST.get('last_name', '').strip()
        email = request.POST.get('email', '').strip()

        # التحقق من صحة البيانات
        if not first_name:
            messages.error(request, 'الاسم الأول مطلوب')
        elif not last_name:
            messages.error(request, 'الاسم الأخير مطلوب')
        elif not email:
            messages.error(request, 'البريد الإلكتروني مطلوب')
        else:
            # تحديث البيانات
            user.first_name = first_name
            user.last_name = last_name
            user.email = email
            user.save()

            messages.success(request, 'تم تحديث الملف الشخصي بنجاح')
            return redirect('profile')

    return render(request, 'dashboard/profile.html', {
        'user': user,
    })

@login_required
def settings_view(request):
    """عرض وإدارة إعدادات النظام - حقيقي وفعال"""
    user = request.user

    # الحصول على إعدادات المستخدم أو إنشاؤها
    user_settings = UserSettings.get_or_create_for_user(user)

    if request.method == 'POST':
        setting_type = request.POST.get('setting_type')
        print(f"POST request received! Setting type: {setting_type}")
        print(f"POST data: {dict(request.POST)}")

        if setting_type == 'notifications':
            # حفظ إعدادات الإشعارات في قاعدة البيانات
            user_settings.email_notifications = 'email_notifications' in request.POST
            user_settings.daily_reports = 'daily_reports' in request.POST
            user_settings.push_notifications = 'push_notifications' in request.POST
            user_settings.sound_notifications = 'sound_notifications' in request.POST
            user_settings.save()

            messages.success(request, 'تم حفظ إعدادات الإشعارات بنجاح!')

        elif setting_type == 'appearance':
            # حفظ إعدادات المظهر في قاعدة البيانات
            dark_mode = 'dark_mode' in request.POST
            user_settings.dark_mode = dark_mode
            user_settings.font_size = request.POST.get('font_size', 'medium')
            user_settings.language = request.POST.get('language', 'ar')
            user_settings.timezone = request.POST.get('timezone', 'Africa/Cairo')
            user_settings.save()

            # رسالة نجاح بناءً على اللغة المختارة
            if user_settings.language == 'en':
                messages.success(request, 'Appearance settings saved successfully!')
            else:
                messages.success(request, 'تم حفظ إعدادات المظهر بنجاح!')

        elif setting_type == 'security':
            # حفظ إعدادات الأمان في قاعدة البيانات
            user_settings.two_factor = 'two_factor' in request.POST
            user_settings.login_alerts = 'login_alerts' in request.POST
            user_settings.session_timeout = 'session_timeout' in request.POST
            user_settings.save()

            messages.success(request, 'تم حفظ إعدادات الأمان بنجاح!')

        return redirect('settings')

    return render(request, 'dashboard/settings.html', {
        'user': user,
        'settings': user_settings,
    })
