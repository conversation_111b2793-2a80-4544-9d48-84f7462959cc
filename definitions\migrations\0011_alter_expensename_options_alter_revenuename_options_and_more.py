# Generated by Django 5.2.4 on 2025-07-14 00:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0010_alter_expensetype_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='expensename',
            options={'ordering': ['name'], 'verbose_name': 'اسم مصروف', 'verbose_name_plural': 'أسماء المصروفات'},
        ),
        migrations.AlterModelOptions(
            name='revenuename',
            options={'ordering': ['name'], 'verbose_name': 'اسم إيراد', 'verbose_name_plural': 'أسماء الإيرادات'},
        ),
        migrations.AlterModelOptions(
            name='revenuetype',
            options={'ordering': ['code'], 'verbose_name': 'نوع إيراد', 'verbose_name_plural': 'أنواع الإيرادات'},
        ),
        migrations.RemoveField(
            model_name='expensename',
            name='default_amount',
        ),
        migrations.RemoveField(
            model_name='revenuename',
            name='default_amount',
        ),
        migrations.RemoveField(
            model_name='revenuetype',
            name='is_taxable',
        ),
        migrations.RemoveField(
            model_name='revenuetype',
            name='tax_rate',
        ),
        migrations.AddField(
            model_name='expensename',
            name='name_en',
            field=models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية'),
        ),
        migrations.AddField(
            model_name='expensename',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AddField(
            model_name='expensename',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AddField(
            model_name='revenuename',
            name='name_en',
            field=models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية'),
        ),
        migrations.AddField(
            model_name='revenuename',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AddField(
            model_name='revenuename',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AddField(
            model_name='revenuetype',
            name='category',
            field=models.CharField(choices=[('operational', 'تشغيلي'), ('investment', 'استثماري')], default='operational', max_length=20, verbose_name='التصنيف'),
        ),
        migrations.AddField(
            model_name='revenuetype',
            name='name_en',
            field=models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية'),
        ),
        migrations.AddField(
            model_name='revenuetype',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AddField(
            model_name='revenuetype',
            name='revenue_type',
            field=models.CharField(choices=[('recurring', 'متكرر'), ('one-time', 'لمرة واحدة')], default='recurring', max_length=20, verbose_name='نوع الإيراد'),
        ),
        migrations.AddField(
            model_name='revenuetype',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='expensename',
            name='expense_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.expensetype', verbose_name='نوع المصروف'),
        ),
        migrations.AlterField(
            model_name='revenuename',
            name='revenue_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.revenuetype', verbose_name='نوع الإيراد'),
        ),
    ]
