<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - أوساريك</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', '<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .signup-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
            display: flex;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .signup-left {
            flex: 1;
            background: linear-gradient(135deg, #0d6efd 0%, #0dcaf0 100%);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .signup-right {
            flex: 1.2;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .brand-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .brand-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .brand-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .signup-title {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .signup-subtitle {
            color: #6c757d;
            margin-bottom: 2rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .btn-signup {
            background: linear-gradient(135deg, #0d6efd 0%, #0dcaf0 100%);
            border: none;
            border-radius: 10px;
            padding: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .btn-signup:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(13, 110, 253, 0.3);
            color: white;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            z-index: 10;
        }

        .login-link {
            text-align: center;
            color: #6c757d;
        }

        .login-link a {
            color: #0d6efd;
            text-decoration: none;
            font-weight: 600;
        }

        .login-link a:hover {
            text-decoration: underline;
        }

        .alert {
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }

        .password-requirements {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }

        .password-requirements ul {
            margin: 0;
            padding-right: 1rem;
        }

        @media (max-width: 768px) {
            .signup-container {
                flex-direction: column;
                margin: 10px;
            }

            .signup-left {
                padding: 2rem;
            }

            .signup-right {
                padding: 2rem;
            }

            .brand-title {
                font-size: 2rem;
            }

            .signup-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <!-- Left Side - Branding -->
        <div class="signup-left">
            <div class="brand-logo">
                <i class="bi bi-building"></i>
            </div>
            <h1 class="brand-title">أوساريك</h1>
            <p class="brand-subtitle">
                انضم إلى نظام أوساريك المتكامل<br>
                لإدارة الحسابات والمخزون بكفاءة عالية
            </p>
        </div>

        <!-- Right Side - Signup Form -->
        <div class="signup-right">
            <h2 class="signup-title">إنشاء حساب جديد</h2>
            <p class="signup-subtitle">املأ البيانات التالية لإنشاء حسابك</p>

            {% if form.non_field_errors %}
                <div class="alert alert-danger">
                    {% for error in form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}

            <form method="post">
                {% csrf_token %}

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control {% if form.first_name.errors %}is-invalid{% endif %}"
                                   id="id_first_name"
                                   name="first_name"
                                   placeholder="الاسم الأول"
                                   value="{{ form.first_name.value|default:'' }}"
                                   required>
                            <label for="id_first_name">
                                <i class="bi bi-person me-2"></i>الاسم الأول
                            </label>
                            {% if form.first_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.first_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text"
                                   class="form-control {% if form.last_name.errors %}is-invalid{% endif %}"
                                   id="id_last_name"
                                   name="last_name"
                                   placeholder="الاسم الأخير"
                                   value="{{ form.last_name.value|default:'' }}"
                                   required>
                            <label for="id_last_name">
                                <i class="bi bi-person me-2"></i>الاسم الأخير
                            </label>
                            {% if form.last_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.last_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-floating">
                    <input type="text"
                           class="form-control {% if form.username.errors %}is-invalid{% endif %}"
                           id="id_username"
                           name="username"
                           placeholder="اسم المستخدم"
                           value="{{ form.username.value|default:'' }}"
                           required>
                    <label for="id_username">
                        <i class="bi bi-at me-2"></i>اسم المستخدم
                    </label>
                    {% if form.username.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.username.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-floating">
                    <input type="email"
                           class="form-control {% if form.email.errors %}is-invalid{% endif %}"
                           id="id_email"
                           name="email"
                           placeholder="البريد الإلكتروني"
                           value="{{ form.email.value|default:'' }}"
                           required>
                    <label for="id_email">
                        <i class="bi bi-envelope me-2"></i>البريد الإلكتروني
                    </label>
                    {% if form.email.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.email.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="form-floating position-relative">
                    <input type="password"
                           class="form-control {% if form.password1.errors %}is-invalid{% endif %}"
                           id="id_password1"
                           name="password1"
                           placeholder="كلمة المرور"
                           required>
                    <label for="id_password1">
                        <i class="bi bi-lock me-2"></i>كلمة المرور
                    </label>
                    <span class="password-toggle" onclick="togglePassword('id_password1', 'toggleIcon1')">
                        <i class="bi bi-eye" id="toggleIcon1"></i>
                    </span>
                    {% if form.password1.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.password1.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    <div class="password-requirements">
                        <ul>
                            <li>يجب أن تحتوي على 8 أحرف على الأقل</li>
                            <li>يجب أن تحتوي على أحرف وأرقام</li>
                        </ul>
                    </div>
                </div>

                <div class="form-floating position-relative">
                    <input type="password"
                           class="form-control {% if form.password2.errors %}is-invalid{% endif %}"
                           id="id_password2"
                           name="password2"
                           placeholder="تأكيد كلمة المرور"
                           required>
                    <label for="id_password2">
                        <i class="bi bi-lock-fill me-2"></i>تأكيد كلمة المرور
                    </label>
                    <span class="password-toggle" onclick="togglePassword('id_password2', 'toggleIcon2')">
                        <i class="bi bi-eye" id="toggleIcon2"></i>
                    </span>
                    {% if form.password2.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.password2.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <button type="submit" class="btn btn-signup">
                    <i class="bi bi-person-plus me-2"></i>
                    إنشاء الحساب
                </button>
            </form>

            <div class="login-link">
                لديك حساب بالفعل؟
                <a href="{% url 'login' %}">تسجيل الدخول</a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function togglePassword(inputId, iconId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(iconId);

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        // Add loading state to signup button
        document.querySelector('form').addEventListener('submit', function() {
            const button = document.querySelector('.btn-signup');
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري إنشاء الحساب...';
            button.disabled = true;
        });

        // Auto-focus on first name field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('id_first_name').focus();
        });

        // Password strength indicator
        document.getElementById('id_password1').addEventListener('input', function() {
            const password = this.value;
            const requirements = document.querySelectorAll('.password-requirements li');

            // Check length
            if (password.length >= 8) {
                requirements[0].style.color = '#198754';
            } else {
                requirements[0].style.color = '#6c757d';
            }

            // Check for letters and numbers
            if (/(?=.*[a-zA-Z])(?=.*[0-9])/.test(password)) {
                requirements[1].style.color = '#198754';
            } else {
                requirements[1].style.color = '#6c757d';
            }
        });

        // Password confirmation validation
        document.getElementById('id_password2').addEventListener('input', function() {
            const password1 = document.getElementById('id_password1').value;
            const password2 = this.value;

            if (password1 && password2) {
                if (password1 === password2) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            }
        });
    </script>
</body>
</html>
