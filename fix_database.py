import sqlite3
import os

# الاتصال بقاعدة البيانات
db_path = os.path.join(os.getcwd(), 'db.sqlite3')
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print("🔧 إصلاح قاعدة البيانات...")

try:
    # إنشاء جدول إعدادات النظام
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS system_settings_systemsettings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_name VARCHAR(200) DEFAULT 'شركة أوساريك',
            company_logo VARCHAR(100),
            company_address TEXT,
            company_phone VARCHAR(20),
            company_email VARCHAR(254),
            company_website VARCHAR(200),
            company_tax_number VARCHAR(50),
            system_language VARCHAR(10) DEFAULT 'ar',
            system_timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
            currency_code VARCHAR(3) DEFAULT 'SAR',
            currency_symbol VARCHAR(10) DEFAULT 'ريال',
            session_timeout INTEGER DEFAULT 30,
            password_min_length INTEGER DEFAULT 8,
            require_strong_password BOOLEAN DEFAULT 1,
            max_login_attempts INTEGER DEFAULT 5,
            email_host VARCHAR(100),
            email_port INTEGER DEFAULT 587,
            email_use_tls BOOLEAN DEFAULT 1,
            backup_enabled BOOLEAN DEFAULT 1,
            backup_frequency VARCHAR(20) DEFAULT 'daily',
            theme_color VARCHAR(7) DEFAULT '#667eea',
            sidebar_collapsed BOOLEAN DEFAULT 0,
            items_per_page INTEGER DEFAULT 20,
            notifications_enabled BOOLEAN DEFAULT 1,
            email_notifications BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_by_id INTEGER
        )
    """)
    print("✅ تم إنشاء جدول system_settings_systemsettings")
    
    # إنشاء جدول ملفات المستخدمين
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS system_settings_userprofile (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            phone VARCHAR(20),
            mobile VARCHAR(20),
            address TEXT,
            birth_date DATE,
            national_id VARCHAR(20),
            employee_id VARCHAR(20) UNIQUE,
            department VARCHAR(20),
            position VARCHAR(20),
            hire_date DATE,
            avatar VARCHAR(100),
            theme_preference VARCHAR(20) DEFAULT 'light',
            is_active BOOLEAN DEFAULT 1,
            last_login_ip VARCHAR(39),
            failed_login_attempts INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            manager_id INTEGER,
            user_id INTEGER UNIQUE NOT NULL
        )
    """)
    print("✅ تم إنشاء جدول system_settings_userprofile")
    
    # إدراج إعدادات افتراضية
    cursor.execute("""
        INSERT OR IGNORE INTO system_settings_systemsettings 
        (id, company_name, created_at, updated_at) 
        VALUES (1, 'شركة أوساريك', datetime('now'), datetime('now'))
    """)
    print("✅ تم إدراج الإعدادات الافتراضية")
    
    # إدراج سجل في django_migrations
    cursor.execute("""
        INSERT OR IGNORE INTO django_migrations 
        (app, name, applied) 
        VALUES ('system_settings', '0001_initial', datetime('now'))
    """)
    print("✅ تم تسجيل migration في django_migrations")
    
    # حفظ التغييرات
    conn.commit()
    print("\n🎉 تم إصلاح قاعدة البيانات بنجاح!")
    print("يمكنك الآن الوصول إلى إعدادات النظام")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    conn.rollback()
    
finally:
    conn.close()

print("\n📋 الخطوات التالية:")
print("1. تأكد من أن الخادم يعمل: python manage.py runserver")
print("2. اذهب إلى: http://127.0.0.1:8000/settings/")
print("3. أو اذهب إلى: http://127.0.0.1:8000/settings/system/")
