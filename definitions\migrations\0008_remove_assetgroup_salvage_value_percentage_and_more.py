# Generated by Django 5.2.4 on 2025-07-13 23:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0007_unitdefinition'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='assetgroup',
            name='salvage_value_percentage',
        ),
        migrations.AddField(
            model_name='assetgroup',
            name='asset_type',
            field=models.CharField(choices=[('fixed', 'أصول ثابتة'), ('current', 'أصول متداولة'), ('intangible', 'أصول غير ملموسة'), ('investment', 'استثمارات')], default='fixed', max_length=20, verbose_name='نوع الأصل'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='assetgroup',
            name='name_en',
            field=models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية'),
        ),
        migrations.AddField(
            model_name='assetgroup',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AddField(
            model_name='assetgroup',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.assetgroup', verbose_name='المجموعة الأب'),
        ),
        migrations.AddField(
            model_name='assetgroup',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='assetgroup',
            name='depreciation_method',
            field=models.CharField(choices=[('straight_line', 'القسط الثابت'), ('declining_balance', 'الرصيد المتناقص'), ('sum_of_years', 'مجموع سنوات الخدمة')], default='straight_line', max_length=20, verbose_name='طريقة الإهلاك'),
        ),
        migrations.AlterField(
            model_name='assetgroup',
            name='depreciation_rate',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الإهلاك السنوي %'),
        ),
    ]
