{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة مرتجعات المبيعات{% endblock %}

{% block extra_css %}
<style>
    .return-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .return-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .page-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
    }
    
    .search-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    .return-header {
        background: linear-gradient(45deg, #dc3545, #c82333);
        color: white;
        padding: 20px;
    }
    
    .return-number {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .return-date {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .return-body {
        padding: 20px;
    }
    
    .return-details {
        margin-bottom: 15px;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 5px 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .detail-value {
        font-weight: 600;
        color: #333;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-pending {
        background: #ffc107;
        color: #212529;
    }
    
    .status-approved {
        background: #28a745;
        color: white;
    }
    
    .reason-badge {
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 500;
        margin-bottom: 10px;
        display: inline-block;
    }
    
    .reason-damaged { background: #f8d7da; color: #721c24; }
    .reason-expired { background: #fff3cd; color: #856404; }
    .reason-wrong_item { background: #d1ecf1; color: #0c5460; }
    .reason-customer_request { background: #d4edda; color: #155724; }
    .reason-quality_issue { background: #e2e3e5; color: #383d41; }
    .reason-other { background: #f8f9fa; color: #495057; }
    
    .amount-display {
        font-size: 1.2rem;
        font-weight: 700;
        color: #dc3545;
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-top: 15px;
    }
    
    .btn-create {
        background: linear-gradient(45deg, #dc3545, #c82333);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(220, 53, 69, 0.3);
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-arrow-return-left"></i>
                    إدارة مرتجعات المبيعات
                </h1>
                <p class="mb-0">إدارة ومتابعة مرتجعات العملاء وأسباب الإرجاع</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'sales:sales_return_create' %}" class="btn btn-create">
                    <i class="bi bi-plus-circle"></i>
                    مرتجع جديد
                </a>
            </div>
        </div>
    </div>

    <!-- قسم البحث والفلترة -->
    <div class="search-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="رقم المرتجع، العميل، أو رقم الفاتورة" 
                       value="{{ search }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>في الانتظار</option>
                    <option value="approved" {% if status == 'approved' %}selected{% endif %}>معتمد</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">السبب</label>
                <select name="reason" class="form-select">
                    <option value="">جميع الأسباب</option>
                    {% for value, label in reason_choices %}
                        <option value="{{ value }}" {% if reason == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search"></i>
                </button>
                <a href="{% url 'sales:sales_return_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- قائمة المرتجعات -->
    {% if returns %}
        <div class="row">
            {% for return_record in returns %}
                <div class="col-lg-6 col-xl-4">
                    <div class="return-card">
                        <div class="return-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="return-number">{{ return_record.return_number }}</div>
                                    <div class="return-date">{{ return_record.return_date }}</div>
                                </div>
                                <span class="status-badge {% if return_record.is_approved %}status-approved{% else %}status-pending{% endif %}">
                                    {% if return_record.is_approved %}معتمد{% else %}في الانتظار{% endif %}
                                </span>
                            </div>
                        </div>
                        
                        <div class="return-body">
                            <div class="reason-badge reason-{{ return_record.reason }}">
                                {{ return_record.get_reason_display }}
                            </div>
                            
                            <div class="return-details">
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-person text-primary"></i>
                                        العميل:
                                    </span>
                                    <span class="detail-value">{{ return_record.customer.name }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-receipt text-info"></i>
                                        الفاتورة الأصلية:
                                    </span>
                                    <span class="detail-value">{{ return_record.original_invoice.invoice_number }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-person-badge text-success"></i>
                                        المندوب:
                                    </span>
                                    <span class="detail-value">{{ return_record.representative.full_name }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-list-ol text-warning"></i>
                                        عدد العناصر:
                                    </span>
                                    <span class="detail-value">{{ return_record.items.count }}</span>
                                </div>
                                {% if return_record.reason_details %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-chat-text text-muted"></i>
                                            التفاصيل:
                                        </span>
                                        <span class="detail-value">{{ return_record.reason_details|truncatechars:30 }}</span>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="amount-display">
                                {{ return_record.total_amount }} ج.م
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="{% url 'sales:sales_return_detail' return_record.pk %}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    عرض التفاصيل
                                </a>
                                {% if not return_record.is_approved %}
                                    <a href="{% url 'sales:sales_return_edit' return_record.pk %}" 
                                       class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-pencil"></i>
                                        تعديل
                                    </a>
                                    <a href="{% url 'sales:sales_return_approve' return_record.pk %}" 
                                       class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-check-circle"></i>
                                        اعتماد
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- الترقيم -->
        {% if returns.has_other_pages %}
            <nav aria-label="ترقيم الصفحات" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if returns.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ returns.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if reason %}&reason={{ reason }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                السابق
                            </a>
                        </li>
                    {% endif %}

                    {% for num in returns.paginator.page_range %}
                        {% if returns.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > returns.number|add:'-3' and num < returns.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if reason %}&reason={{ reason }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                    {{ num }}
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if returns.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ returns.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if reason %}&reason={{ reason }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                التالي
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="empty-state">
            <i class="bi bi-arrow-return-left"></i>
            <h3>لا توجد مرتجعات</h3>
            <p>لم يتم العثور على أي مرتجعات تطابق معايير البحث</p>
            <a href="{% url 'sales:sales_return_create' %}" class="btn btn-create">
                <i class="bi bi-plus-circle"></i>
                إنشاء أول مرتجع
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تأثيرات بصرية للبطاقات
    $('.return-card').hover(
        function() {
            $(this).find('.return-number').addClass('text-warning');
        },
        function() {
            $(this).find('.return-number').removeClass('text-warning');
        }
    );
});
</script>
{% endblock %}
