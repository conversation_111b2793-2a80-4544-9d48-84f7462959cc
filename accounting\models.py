from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from django.utils import timezone
from decimal import Decimal

class AccountType(models.Model):
    """نموذج أنواع الحسابات"""
    TYPE_CHOICES = [
        ('asset', 'أصول'),
        ('liability', 'خصوم'),
        ('equity', 'حقوق الملكية'),
        ('revenue', 'إيرادات'),
        ('expense', 'مصروفات'),
    ]

    name = models.CharField(max_length=100, verbose_name="اسم النوع")
    type_category = models.CharField(max_length=20, choices=TYPE_CHOICES, verbose_name="فئة النوع")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "نوع حساب"
        verbose_name_plural = "أنواع الحسابات"
        ordering = ['name']

    def __str__(self):
        return self.name

class Account(models.Model):
    """نموذج دليل الحسابات"""
    account_code = models.CharField(max_length=20, unique=True, verbose_name="كود الحساب")
    account_name = models.CharField(max_length=200, verbose_name="اسم الحساب")
    account_type = models.ForeignKey(AccountType, on_delete=models.CASCADE, verbose_name="نوع الحساب")
    parent_account = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                                      related_name='sub_accounts', verbose_name="الحساب الأب")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    opening_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد الافتتاحي")
    current_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد الحالي")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_main_account = models.BooleanField(default=False, verbose_name="حساب رئيسي")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "حساب"
        verbose_name_plural = "دليل الحسابات"
        ordering = ['account_code']

    def __str__(self):
        return f"{self.account_code} - {self.account_name}"

    @property
    def level(self):
        """مستوى الحساب في الهيكل الشجري"""
        level = 0
        parent = self.parent_account
        while parent:
            level += 1
            parent = parent.parent_account
        return level

    @property
    def full_code(self):
        """الكود الكامل للحساب مع الحسابات الأب"""
        codes = [self.account_code]
        parent = self.parent_account
        while parent:
            codes.insert(0, parent.account_code)
            parent = parent.parent_account
        return '.'.join(codes)

class JournalEntry(models.Model):
    """نموذج القيود المحاسبية"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('posted', 'مرحل'),
        ('cancelled', 'ملغي'),
    ]

    entry_number = models.CharField(max_length=50, unique=True, verbose_name="رقم القيد")
    entry_date = models.DateField(verbose_name="تاريخ القيد")
    description = models.TextField(verbose_name="وصف القيد")
    reference_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="رقم المرجع")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    total_debit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي المدين")
    total_credit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="إجمالي الدائن")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    posted_by = models.ForeignKey(User, related_name='posted_entries', on_delete=models.SET_NULL,
                                 null=True, blank=True, verbose_name="رحل بواسطة")
    posted_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الترحيل")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "قيد محاسبي"
        verbose_name_plural = "القيود المحاسبية"
        ordering = ['-entry_date', '-created_at']

    def __str__(self):
        return f"قيد {self.entry_number} - {self.entry_date}"

    @property
    def is_balanced(self):
        """هل القيد متوازن"""
        return self.total_debit == self.total_credit

    def calculate_totals(self):
        """حساب إجماليات القيد"""
        self.total_debit = sum(line.debit_amount for line in self.lines.all())
        self.total_credit = sum(line.credit_amount for line in self.lines.all())
        self.save()

    def post_entry(self, posted_by_user):
        """ترحيل القيد"""
        if self.status == 'draft' and self.is_balanced:
            # تحديث أرصدة الحسابات
            for line in self.lines.all():
                account = line.account
                if line.debit_amount > 0:
                    if account.account_type.type_category in ['asset', 'expense']:
                        account.current_balance += line.debit_amount
                    else:
                        account.current_balance -= line.debit_amount
                elif line.credit_amount > 0:
                    if account.account_type.type_category in ['liability', 'equity', 'revenue']:
                        account.current_balance += line.credit_amount
                    else:
                        account.current_balance -= line.credit_amount
                account.save()

            self.status = 'posted'
            self.posted_by = posted_by_user
            self.posted_at = timezone.now()
            self.save()

class JournalEntryLine(models.Model):
    """نموذج تفاصيل القيد المحاسبي"""
    journal_entry = models.ForeignKey(JournalEntry, related_name='lines', on_delete=models.CASCADE, verbose_name="القيد المحاسبي")
    account = models.ForeignKey(Account, on_delete=models.CASCADE, verbose_name="الحساب")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    debit_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المبلغ المدين")
    credit_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="المبلغ الدائن")
    line_order = models.PositiveIntegerField(default=1, verbose_name="ترتيب السطر")

    class Meta:
        verbose_name = "سطر قيد محاسبي"
        verbose_name_plural = "أسطر القيود المحاسبية"
        ordering = ['line_order']

    def __str__(self):
        return f"{self.account.account_name} - {self.debit_amount or self.credit_amount}"

    def clean(self):
        """التحقق من صحة البيانات"""
        from django.core.exceptions import ValidationError
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError("لا يمكن أن يكون السطر مدين ودائن في نفس الوقت")
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError("يجب أن يحتوي السطر على مبلغ مدين أو دائن")
