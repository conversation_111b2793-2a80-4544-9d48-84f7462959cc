{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم الأصول الثابتة{% endblock %}

{% block extra_css %}
<style>
    .assets-header {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
    }

    .stats-card {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-bottom: 2rem;
    }

    .quick-action-btn {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(111,66,193,0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Assets Header -->
<div class="assets-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-building me-2"></i>
                    لوحة تحكم الأصول الثابتة
                </h1>
                <p class="mb-0 opacity-75">إدارة شاملة للأصول الثابتة والمعدات والممتلكات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <i class="bi bi-calendar me-2"></i>
                    {{ "now"|date:"Y/m/d" }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container-fluid">
    <div class="quick-actions">
        <a href="{% url 'assets:asset_create' %}" class="quick-action-btn">
            <i class="bi bi-plus-circle"></i>
            إضافة أصل جديد
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-arrow-repeat"></i>
            إهلاك الأصول
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-tools"></i>
            صيانة الأصول
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-file-earmark-text"></i>
            تقارير الأصول
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(111, 66, 193, 0.1); color: #6f42c1;">
                    <i class="bi bi-building"></i>
                </div>
                <div class="stats-number" style="color: #6f42c1;">{{ total_assets }}</div>
                <p class="stats-label">إجمالي الأصول</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(40, 167, 69, 0.1); color: #28a745;">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="stats-number text-success">{{ active_assets }}</div>
                <p class="stats-label">أصول نشطة</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-tools"></i>
                </div>
                <div class="stats-number text-warning">{{ maintenance_assets }}</div>
                <p class="stats-label">أصول تحت الصيانة</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-x-circle"></i>
                </div>
                <div class="stats-number text-danger">{{ disposed_assets }}</div>
                <p class="stats-label">أصول مستبعدة</p>
            </div>
        </div>
    </div>

    <!-- Financial Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(32, 201, 151, 0.1); color: #20c997;">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="stats-number text-info">{{ total_value|floatformat:2 }}</div>
                <p class="stats-label">إجمالي القيمة (ريال)</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(0, 123, 255, 0.1); color: #007bff;">
                    <i class="bi bi-graph-down"></i>
                </div>
                <div class="stats-number text-primary">{{ total_depreciation|floatformat:2 }}</div>
                <p class="stats-label">إجمالي الإهلاك (ريال)</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(111, 66, 193, 0.1); color: #6f42c1;">
                    <i class="bi bi-calculator"></i>
                </div>
                <div class="stats-number" style="color: #6f42c1;">{{ net_book_value|floatformat:2 }}</div>
                <p class="stats-label">صافي القيمة الدفترية (ريال)</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-calendar-month"></i>
                </div>
                <div class="stats-number text-warning">{{ monthly_depreciation|floatformat:2 }}</div>
                <p class="stats-label">إهلاك هذا الشهر (ريال)</p>
            </div>
        </div>
    </div>

    <!-- Assets by Category and Recent Assets -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث الأصول</h5>
                    <a href="{% url 'assets:asset_list' %}" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_assets %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الأصل</th>
                                    <th>الفئة</th>
                                    <th>تاريخ الشراء</th>
                                    <th>القيمة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for asset in recent_assets %}
                                <tr>
                                    <td>{{ asset.name }}</td>
                                    <td>{{ asset.category.name }}</td>
                                    <td>{{ asset.purchase_date }}</td>
                                    <td>{{ asset.purchase_price|floatformat:2 }} ريال</td>
                                    <td>
                                        {% if asset.status == 'active' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% elif asset.status == 'maintenance' %}
                                            <span class="badge bg-warning">صيانة</span>
                                        {% elif asset.status == 'disposed' %}
                                            <span class="badge bg-danger">مستبعد</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ asset.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-building text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد أصول حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">الأصول حسب الفئة</h5>
                </div>
                <div class="card-body">
                    {% if assets_by_category %}
                    <div class="list-group list-group-flush">
                        {% for category in assets_by_category %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-0">{{ category.category__name|default:"غير محدد" }}</h6>
                                </div>
                                <div>
                                    <span class="badge bg-primary">{{ category.count }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-pie-chart text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد بيانات إحصائية</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
