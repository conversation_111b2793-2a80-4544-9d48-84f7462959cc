{% extends 'base.html' %}
{% load static %}

{% block title %}الموافقات المعلقة - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .approval-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-left: 5px solid #2196f3;
        transition: all 0.3s ease;
    }

    .approval-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .approval-card.pending {
        border-left-color: #ff9800;
        background: linear-gradient(45deg, #fff8e1, #ffffff);
    }

    .approval-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .approval-title {
        font-weight: bold;
        color: #2c3e50;
        font-size: 1.2rem;
        margin: 0;
    }

    .approval-amount {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2196f3;
    }

    .approval-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }

    .meta-item {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        text-align: center;
    }

    .meta-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .meta-value {
        font-weight: bold;
        color: #2c3e50;
    }

    .justification {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
        border-left: 4px solid #2196f3;
    }

    .btn-approve {
        background: linear-gradient(45deg, #4caf50, #45a049);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin-right: 10px;
    }

    .btn-approve:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(76, 175, 80, 0.3);
        color: white;
    }

    .btn-reject {
        background: linear-gradient(45deg, #f44336, #d32f2f);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-reject:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(244, 67, 54, 0.3);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-top: 4px solid #2196f3;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        color: #2196f3;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 500;
    }

    .section-title {
        color: #2c3e50;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }

    .days-pending {
        background: #fff3cd;
        color: #856404;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .priority-high {
        background: #f8d7da;
        color: #721c24;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .priority-medium {
        background: #fff3cd;
        color: #856404;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-clipboard-check"></i>
                    الموافقات المعلقة
                </h1>
                <p class="mb-0">مراجعة والموافقة على المدفوعات الكبيرة والطلبات المعلقة</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'purchases:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house"></i>
                        لوحة التحكم
                    </a>
                    <a href="{% url 'purchases:reports' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-right"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ pending_approvals.count }}</div>
            <div class="stat-label">موافقات معلقة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ approved_approvals.count }}</div>
            <div class="stat-label">موافقات حديثة</div>
        </div>
    </div>

    <!-- الموافقات المعلقة -->
    <div class="row">
        <div class="col-12">
            <h3 class="section-title">
                <i class="bi bi-hourglass-split"></i>
                الموافقات المعلقة
            </h3>
            
            {% if pending_approvals %}
                {% for approval in pending_approvals %}
                    <div class="approval-card pending">
                        <div class="approval-header">
                            <div class="approval-title">
                                موافقة دفعة للمورد {{ approval.payment.supplier.name }}
                            </div>
                            <div class="approval-amount">{{ approval.payment.total_amount|floatformat:2 }} ج.م</div>
                        </div>
                        
                        <div class="approval-meta">
                            <div class="meta-item">
                                <div class="meta-label">طلب بواسطة</div>
                                <div class="meta-value">{{ approval.requested_by.get_full_name|default:approval.requested_by.username }}</div>
                            </div>
                            <div class="meta-item">
                                <div class="meta-label">تاريخ الطلب</div>
                                <div class="meta-value">{{ approval.requested_at|date:"Y-m-d H:i" }}</div>
                            </div>
                            <div class="meta-item">
                                <div class="meta-label">أيام الانتظار</div>
                                <div class="meta-value">
                                    <span class="days-pending">{{ approval.days_pending }} يوم</span>
                                </div>
                            </div>
                            <div class="meta-item">
                                <div class="meta-label">الأولوية</div>
                                <div class="meta-value">
                                    <span class="priority-{{ approval.priority }}">{{ approval.get_priority_display }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="justification">
                            <strong>مبرر الدفعة:</strong><br>
                            {{ approval.justification }}
                        </div>
                        
                        <div class="text-center">
                            <a href="{% url 'purchases:approve_payment' approval.id %}" class="btn btn-approve">
                                <i class="bi bi-check-circle"></i>
                                مراجعة ومعالجة
                            </a>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="bi bi-check-circle text-success"></i>
                    <h4>لا توجد موافقات معلقة</h4>
                    <p>جميع الطلبات تمت مراجعتها</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- الموافقات الحديثة -->
    {% if approved_approvals %}
        <div class="row mt-4">
            <div class="col-12">
                <h3 class="section-title">
                    <i class="bi bi-check-circle"></i>
                    الموافقات الحديثة
                </h3>
                
                {% for approval in approved_approvals %}
                    <div class="approval-card">
                        <div class="approval-header">
                            <div class="approval-title">
                                {{ approval.payment.supplier.name }} - {{ approval.get_status_display }}
                            </div>
                            <div class="approval-amount">{{ approval.payment.total_amount|floatformat:2 }} ج.م</div>
                        </div>
                        
                        <div class="approval-meta">
                            <div class="meta-item">
                                <div class="meta-label">{{ approval.get_status_display }} بواسطة</div>
                                <div class="meta-value">{{ approval.approved_by.get_full_name|default:approval.approved_by.username }}</div>
                            </div>
                            <div class="meta-item">
                                <div class="meta-label">تاريخ {{ approval.get_status_display }}</div>
                                <div class="meta-value">{{ approval.approved_at|date:"Y-m-d H:i" }}</div>
                            </div>
                        </div>
                        
                        {% if approval.rejection_reason %}
                            <div class="justification">
                                <strong>سبب الرفض:</strong><br>
                                {{ approval.rejection_reason }}
                            </div>
                        {% endif %}
                        
                        {% if approval.notes %}
                            <div class="justification">
                                <strong>ملاحظات:</strong><br>
                                {{ approval.notes }}
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث تلقائي كل 2 دقيقة للموافقات المعلقة
    setInterval(function() {
        if ({{ pending_approvals.count }} > 0) {
            location.reload();
        }
    }, 120000);
</script>
{% endblock %}
