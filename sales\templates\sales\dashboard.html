<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المبيعات المتكامل - أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
<style>
    .sales-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
    }

    .stats-card {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-bottom: 2rem;
    }

    .quick-action-btn {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Sales Header -->
<div class="sales-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-cart-plus me-2"></i>
                    {% if user_language == 'en' %}Sales Dashboard{% else %}لوحة تحكم المبيعات{% endif %}
                </h1>
                <p class="mb-0 opacity-75">{% if user_language == 'en' %}Comprehensive management of all sales operations, customers and products{% else %}إدارة شاملة لجميع عمليات البيع والعملاء والمنتجات{% endif %}</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <i class="bi bi-calendar me-2"></i>
                    {{ "now"|date:"Y/m/d" }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container-fluid">
    <div class="quick-actions">
        <a href="{% url 'sales:customer_create' %}" class="quick-action-btn">
            <i class="bi bi-person-plus"></i>
            {% if user_language == 'en' %}Add New Customer{% else %}إضافة عميل جديد{% endif %}
        </a>
        <a href="{% url 'sales:product_create' %}" class="quick-action-btn">
            <i class="bi bi-box-seam"></i>
            {% if user_language == 'en' %}Add New Product{% else %}إضافة منتج جديد{% endif %}
        </a>
        <a href="{% url 'sales:order_create' %}" class="quick-action-btn">
            <i class="bi bi-cart-plus"></i>
            {% if user_language == 'en' %}Create Sales Order{% else %}إنشاء أمر بيع{% endif %}
        </a>
        <a href="{% url 'sales:invoice_create' %}" class="quick-action-btn">
            <i class="bi bi-receipt"></i>
            {% if user_language == 'en' %}Create Invoice{% else %}إنشاء فاتورة{% endif %}
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(40, 167, 69, 0.1); color: #28a745;">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stats-number text-success">{{ total_customers }}</div>
                <p class="stats-label">إجمالي العملاء</p>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(0, 123, 255, 0.1); color: #007bff;">
                    <i class="bi bi-box-seam"></i>
                </div>
                <div class="stats-number text-primary">{{ total_products }}</div>
                <p class="stats-label">إجمالي المنتجات</p>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-cart-check"></i>
                </div>
                <div class="stats-number text-warning">{{ total_orders }}</div>
                <p class="stats-label">أوامر البيع</p>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-receipt"></i>
                </div>
                <div class="stats-number text-danger">{{ total_invoices }}</div>
                <p class="stats-label">الفواتير</p>
            </div>
        </div>
    </div>

    <!-- Financial Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(32, 201, 151, 0.1); color: #20c997;">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="stats-number text-info">{{ total_sales|floatformat:2 }}</div>
                <p class="stats-label">إجمالي المبيعات (ريال)</p>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-clock"></i>
                </div>
                <div class="stats-number text-warning">{{ pending_invoices }}</div>
                <p class="stats-label">فواتير معلقة</p>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <div class="stats-number text-danger">{{ overdue_invoices }}</div>
                <p class="stats-label">فواتير متأخرة</p>
            </div>
        </div>
    </div>

    <!-- Recent Orders and Invoices -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث أوامر البيع</h5>
                    <a href="{% url 'sales:order_list' %}" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_orders %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in recent_orders %}
                                <tr>
                                    <td>{{ order.order_number }}</td>
                                    <td>{{ order.customer.name }}</td>
                                    <td>{{ order.order_date }}</td>
                                    <td>
                                        {% if order.status == 'draft' %}
                                            <span class="badge bg-secondary">مسودة</span>
                                        {% elif order.status == 'confirmed' %}
                                            <span class="badge bg-primary">مؤكد</span>
                                        {% elif order.status == 'shipped' %}
                                            <span class="badge bg-warning">تم الشحن</span>
                                        {% elif order.status == 'delivered' %}
                                            <span class="badge bg-success">تم التسليم</span>
                                        {% else %}
                                            <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-cart-x text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد أوامر بيع حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث الفواتير</h5>
                    <a href="{% url 'sales:invoice_list' %}" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices %}
                                <tr>
                                    <td>{{ invoice.invoice_number }}</td>
                                    <td>{{ invoice.customer.name }}</td>
                                    <td>{{ invoice.invoice_date }}</td>
                                    <td>
                                        {% if invoice.status == 'draft' %}
                                            <span class="badge bg-secondary">مسودة</span>
                                        {% elif invoice.status == 'sent' %}
                                            <span class="badge bg-primary">مرسلة</span>
                                        {% elif invoice.status == 'paid' %}
                                            <span class="badge bg-success">مدفوعة</span>
                                        {% elif invoice.status == 'overdue' %}
                                            <span class="badge bg-danger">متأخرة</span>
                                        {% else %}
                                            <span class="badge bg-dark">ملغية</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-receipt text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد فواتير حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Information -->
    <div class="row g-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>أحدث أوامر البيع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="bi bi-cart-check text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">لا توجد أوامر بيع</h5>
                        <p class="text-muted">لم يتم إنشاء أي أوامر بيع بعد.</p>
                        <a href="#" class="btn btn-primary">
                            <i class="bi bi-plus me-2"></i>إنشاء أول أمر بيع
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-pie-chart me-2"></i>إحصائيات المبيعات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span>أوامر البيع</span>
                            <span class="badge bg-primary">{{ total_orders }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span>الفواتير</span>
                            <span class="badge bg-success">{{ total_invoices }}</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span>فواتير مدفوعة</span>
                            <span class="badge bg-info">0</span>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <span>فواتير معلقة</span>
                            <span class="badge bg-warning">{{ pending_invoices }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Top Products -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-star me-2"></i>أفضل المنتجات مبيعاً
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center py-3">
                        <i class="bi bi-box text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2 mb-0">لا توجد بيانات مبيعات</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
