{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3><i class="fas fa-users-cog me-2"></i>{{ title }}</h3>
                </div>
                <div class="card-body">
                    {% if groups %}
                        {% for group in groups %}
                        <div class="alert alert-info">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5><i class="fas fa-users me-2"></i>{{ group.name }}</h5>
                                    <p class="mb-0">
                                        <span class="badge bg-secondary me-2">{{ group.user_set.count }} مستخدم</span>
                                        <span class="badge bg-success">{{ group.permissions.count }} صلاحية</span>
                                    </p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <a href="{% url 'system_settings:edit_group_permissions' group.id %}" 
                                       class="btn btn-primary btn-sm me-2">
                                        <i class="fas fa-edit me-1"></i>تعديل الصلاحيات
                                    </a>
                                    <a href="{% url 'system_settings:group_users' group.id %}" 
                                       class="btn btn-secondary btn-sm">
                                        <i class="fas fa-users me-1"></i>إدارة المستخدمين
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                            <h4>لا توجد أدوار</h4>
                            <p>لم يتم إنشاء أي أدوار في النظام بعد</p>
                            <a href="{% url 'system_settings:permissions_management' %}" class="btn btn-primary">
                                <i class="fas fa-shield-alt me-2"></i>إعداد الصلاحيات أولاً
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
