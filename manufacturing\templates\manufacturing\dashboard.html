{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .manufacturing-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border-radius: 25px;
        padding: 3rem 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        position: relative;
        overflow: hidden;
    }
    
    .manufacturing-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }
    
    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .manufacturing-title {
        color: white;
        font-size: 3rem;
        font-weight: 900;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }
    
    .manufacturing-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.2rem;
        margin-top: 0.5rem;
        position: relative;
        z-index: 2;
    }
    
    .stats-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient);
    }
    
    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }
    
    .stat-card.primary::before { --gradient: linear-gradient(135deg, #667eea, #764ba2); }
    .stat-card.success::before { --gradient: linear-gradient(135deg, #56ab2f, #a8e6cf); }
    .stat-card.warning::before { --gradient: linear-gradient(135deg, #f093fb, #f5576c); }
    .stat-card.danger::before { --gradient: linear-gradient(135deg, #ff6b6b, #ee5a24); }
    .stat-card.info::before { --gradient: linear-gradient(135deg, #74b9ff, #0984e3); }
    
    .stat-icon {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        color: white;
        margin-bottom: 1.5rem;
        background: var(--gradient);
    }
    
    .stat-number {
        font-size: 3rem;
        font-weight: 900;
        color: #2d3436;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 1.1rem;
        color: #636e72;
        font-weight: 600;
    }
    
    .content-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 2.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .section-title {
        font-size: 1.8rem;
        font-weight: 800;
        color: #2d3436;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .section-icon {
        width: 50px;
        height: 50px;
        border-radius: 15px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }
    
    .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 1.5rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }
    
    .action-btn:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .action-btn.success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        box-shadow: 0 10px 25px rgba(86, 171, 47, 0.3);
    }
    
    .action-btn.success:hover {
        box-shadow: 0 15px 35px rgba(86, 171, 47, 0.4);
    }
    
    .action-btn.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        box-shadow: 0 10px 25px rgba(240, 147, 251, 0.3);
    }
    
    .action-btn.warning:hover {
        box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
    }
    
    .order-list {
        display: grid;
        gap: 1rem;
    }
    
    .order-item {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 15px;
        padding: 1.5rem;
        border-left: 4px solid #667eea;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .order-item:hover {
        transform: translateX(10px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    .order-item.urgent {
        border-left-color: #ff6b6b;
        background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
    }
    
    .order-item.overdue {
        border-left-color: #ee5a24;
        background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
    }
    
    .order-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .order-number {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2d3436;
    }
    
    .order-status {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }
    
    .status-draft { background: #ddd6fe; color: #7c3aed; }
    .status-approved { background: #dbeafe; color: #2563eb; }
    .status-in-progress { background: #fef3c7; color: #d97706; }
    .status-completed { background: #d1fae5; color: #059669; }
    .status-overdue { background: #fee2e2; color: #dc2626; }
    
    .order-details {
        color: #636e72;
        font-size: 0.95rem;
        line-height: 1.6;
    }
    
    .cost-summary {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
    }
    
    .cost-item {
        margin-bottom: 1rem;
    }
    
    .cost-label {
        font-size: 1.1rem;
        color: #546e7a;
        font-weight: 600;
    }
    
    .cost-value {
        font-size: 2rem;
        font-weight: 900;
        color: #1565c0;
    }
    
    @media (max-width: 768px) {
        .manufacturing-title {
            font-size: 2rem;
        }
        
        .stats-container {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="manufacturing-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="manufacturing-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    نظام التصنيع المتكامل
                </h1>
                <p class="manufacturing-subtitle">
                    إدارة شاملة لعمليات التصنيع من المواد الخام إلى المنتج النهائي
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'manufacturing:order_create' %}" class="action-btn success">
                    <i class="bi bi-plus-circle"></i>
                    أمر تصنيع جديد
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-container">
        <div class="stat-card primary">
            <div class="stat-icon primary">
                <i class="bi bi-clipboard-data"></i>
            </div>
            <div class="stat-number">{{ stats.total_orders }}</div>
            <div class="stat-label">إجمالي أوامر التصنيع</div>
        </div>
        
        <div class="stat-card info">
            <div class="stat-icon info">
                <i class="bi bi-play-circle"></i>
            </div>
            <div class="stat-number">{{ stats.active_orders }}</div>
            <div class="stat-label">أوامر نشطة</div>
        </div>
        
        <div class="stat-card success">
            <div class="stat-icon success">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stat-number">{{ stats.completed_orders }}</div>
            <div class="stat-label">أوامر مكتملة</div>
        </div>
        
        <div class="stat-card warning">
            <div class="stat-icon warning">
                <i class="bi bi-clock"></i>
            </div>
            <div class="stat-number">{{ stats.pending_orders }}</div>
            <div class="stat-label">في الانتظار</div>
        </div>
        
        <div class="stat-card danger">
            <div class="stat-icon danger">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <div class="stat-number">{{ stats.overdue_orders }}</div>
            <div class="stat-label">أوامر متأخرة</div>
        </div>
    </div>

    <!-- Cost Statistics Cards -->
    {% if cost_stats %}
    <div class="stats-container" style="margin-top: 2rem;">
        <div class="stat-card" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
            <div class="stat-icon" style="background: linear-gradient(135deg, #1976d2, #1565c0);">
                <i class="bi bi-calculator"></i>
            </div>
            <div class="stat-number" style="color: #1565c0;">{{ cost_stats.estimated_raw_material|floatformat:0|default:0 }}</div>
            <div class="stat-label">إجمالي المواد الخام المقدرة</div>
            <small style="color: #666;">جنيه</small>
        </div>

        <div class="stat-card" style="background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);">
            <div class="stat-icon" style="background: linear-gradient(135deg, #7b1fa2, #6a1b9a);">
                <i class="bi bi-people"></i>
            </div>
            <div class="stat-number" style="color: #7b1fa2;">{{ cost_stats.estimated_labor|floatformat:0|default:0 }}</div>
            <div class="stat-label">إجمالي تكلفة العمالة المقدرة</div>
            <small style="color: #666;">جنيه</small>
        </div>

        <div class="stat-card" style="background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);">
            <div class="stat-icon" style="background: linear-gradient(135deg, #f57c00, #ef6c00);">
                <i class="bi bi-gear"></i>
            </div>
            <div class="stat-number" style="color: #f57c00;">{{ cost_stats.estimated_overhead|floatformat:0|default:0 }}</div>
            <div class="stat-label">إجمالي التكاليف الإضافية المقدرة</div>
            <small style="color: #666;">جنيه</small>
        </div>

        <div class="stat-card" style="background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);">
            <div class="stat-icon" style="background: linear-gradient(135deg, #388e3c, #2e7d32);">
                <i class="bi bi-currency-dollar"></i>
            </div>
            <div class="stat-number" style="color: #2e7d32;">{{ cost_stats.total_estimated_cost|floatformat:0|default:0 }}</div>
            <div class="stat-label">إجمالي التكلفة المقدرة</div>
            <small style="color: #666;">جنيه</small>
        </div>

        <div class="stat-card" style="background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);">
            <div class="stat-icon" style="background: linear-gradient(135deg, #d32f2f, #c62828);">
                <i class="bi bi-cash-stack"></i>
            </div>
            <div class="stat-number" style="color: #d32f2f;">{{ cost_stats.total_actual_cost|floatformat:0|default:0 }}</div>
            <div class="stat-label">إجمالي التكلفة الفعلية</div>
            <small style="color: #666;">جنيه</small>
        </div>

        <div class="stat-card" style="background: linear-gradient(135deg, {% if cost_stats.cost_variance > 0 %}#ffebee 0%, #ffcdd2{% elif cost_stats.cost_variance < 0 %}#e8f5e8 0%, #c8e6c9{% else %}#f5f5f5 0%, #eeeeee{% endif %} 100%);">
            <div class="stat-icon" style="background: linear-gradient(135deg, {% if cost_stats.cost_variance > 0 %}#d32f2f, #c62828{% elif cost_stats.cost_variance < 0 %}#388e3c, #2e7d32{% else %}#757575, #616161{% endif %});">
                <i class="bi bi-{% if cost_stats.cost_variance > 0 %}arrow-up{% elif cost_stats.cost_variance < 0 %}arrow-down{% else %}dash{% endif %}"></i>
            </div>
            <div class="stat-number" style="color: {% if cost_stats.cost_variance > 0 %}#d32f2f{% elif cost_stats.cost_variance < 0 %}#388e3c{% else %}#757575{% endif %};">
                {% if cost_stats.cost_variance > 0 %}+{% endif %}{{ cost_stats.cost_variance|floatformat:0|default:0 }}
            </div>
            <div class="stat-label">انحراف التكلفة</div>
            <small style="color: {% if cost_stats.cost_variance > 0 %}#d32f2f{% elif cost_stats.cost_variance < 0 %}#388e3c{% else %}#757575{% endif %};">
                ({{ cost_stats.cost_variance_percentage|floatformat:1 }}%)
            </small>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="content-section">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="bi bi-lightning"></i>
            </div>
            إجراءات سريعة
        </h2>
        
        <div class="action-buttons">
            <a href="{% url 'manufacturing:order_list' %}" class="action-btn">
                <i class="bi bi-list-ul"></i>
                عرض جميع الأوامر
            </a>
            
            <a href="{% url 'manufacturing:order_create' %}" class="action-btn success">
                <i class="bi bi-plus-circle"></i>
                إنشاء أمر تصنيع
            </a>
            
            <a href="#" class="action-btn warning">
                <i class="bi bi-graph-up"></i>
                تقارير الإنتاج
            </a>
            
            <a href="#" class="action-btn">
                <i class="bi bi-gear"></i>
                إعدادات التصنيع
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Recent Orders -->
        <div class="col-lg-8">
            <div class="content-section">
                <h2 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    أحدث أوامر التصنيع
                </h2>
                
                <div class="order-list">
                    {% for order in recent_orders %}
                    <div class="order-item {% if order.priority == 'urgent' %}urgent{% elif order.is_overdue %}overdue{% endif %}" 
                         onclick="window.location.href='{% url 'manufacturing:order_detail' order.id %}'">
                        <div class="order-header">
                            <div class="order-number">{{ order.order_number }}</div>
                            <div class="order-status status-{{ order.status }}">
                                {{ order.get_status_display }}
                            </div>
                        </div>
                        <div class="order-details">
                            <strong>{{ order.final_product.name }}</strong><br>
                            الكمية: {{ order.quantity_to_produce }} {{ order.unit_of_measure.name }}<br>
                            التاريخ المتوقع: {{ order.expected_completion_date|date:"d/m/Y" }}<br>
                            أنشئ بواسطة: {{ order.created_by.get_full_name|default:order.created_by.username }}
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="bi bi-inbox" style="font-size: 3rem; color: #6c757d;"></i>
                        <p class="mt-2 text-muted">لا توجد أوامر تصنيع حديثة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Cost Summary & Alerts -->
        <div class="col-lg-4">
            <!-- Cost Summary -->
            {% if cost_stats %}
            <div class="content-section">
                <h2 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    ملخص التكاليف التفصيلي
                </h2>

                <div class="cost-summary">
                    <!-- التكاليف المقدرة -->
                    <h5 style="color: #1565c0; margin-bottom: 15px; font-weight: bold;">التكاليف المقدرة</h5>
                    <div class="cost-item" style="font-size: 0.9rem; margin-bottom: 8px;">
                        <div class="cost-label">مواد خام مقدرة</div>
                        <div class="cost-value" style="font-size: 1.3rem;">{{ cost_stats.estimated_raw_material|floatformat:0|default:0 }}</div>
                        <small style="color: #666;">جنيه</small>
                    </div>
                    <div class="cost-item" style="font-size: 0.9rem; margin-bottom: 8px;">
                        <div class="cost-label">عمالة مقدرة</div>
                        <div class="cost-value" style="font-size: 1.3rem;">{{ cost_stats.estimated_labor|floatformat:0|default:0 }}</div>
                        <small style="color: #666;">جنيه</small>
                    </div>
                    <div class="cost-item" style="font-size: 0.9rem; margin-bottom: 15px;">
                        <div class="cost-label">تكاليف إضافية مقدرة</div>
                        <div class="cost-value" style="font-size: 1.3rem;">{{ cost_stats.estimated_overhead|floatformat:0|default:0 }}</div>
                        <small style="color: #666;">جنيه</small>
                    </div>
                    <div class="cost-item" style="border-top: 2px solid #1565c0; padding-top: 10px; margin-bottom: 20px;">
                        <div class="cost-label" style="font-weight: bold;">إجمالي التكلفة المقدرة</div>
                        <div class="cost-value" style="color: #1565c0;">{{ cost_stats.total_estimated_cost|floatformat:0|default:0 }}</div>
                        <small style="color: #666;">جنيه</small>
                    </div>

                    <!-- التكاليف الفعلية -->
                    <h5 style="color: #2e7d32; margin-bottom: 15px; font-weight: bold;">التكاليف الفعلية</h5>
                    <div class="cost-item" style="font-size: 0.9rem; margin-bottom: 8px;">
                        <div class="cost-label">مواد خام فعلية</div>
                        <div class="cost-value" style="font-size: 1.3rem; color: #2e7d32;">{{ cost_stats.actual_raw_material|floatformat:0|default:0 }}</div>
                        <small style="color: #666;">جنيه</small>
                    </div>
                    <div class="cost-item" style="font-size: 0.9rem; margin-bottom: 8px;">
                        <div class="cost-label">عمالة فعلية</div>
                        <div class="cost-value" style="font-size: 1.3rem; color: #2e7d32;">{{ cost_stats.actual_labor|floatformat:0|default:0 }}</div>
                        <small style="color: #666;">جنيه</small>
                    </div>
                    <div class="cost-item" style="font-size: 0.9rem; margin-bottom: 15px;">
                        <div class="cost-label">تكاليف إضافية فعلية</div>
                        <div class="cost-value" style="font-size: 1.3rem; color: #2e7d32;">{{ cost_stats.actual_overhead|floatformat:0|default:0 }}</div>
                        <small style="color: #666;">جنيه</small>
                    </div>
                    <div class="cost-item" style="border-top: 2px solid #2e7d32; padding-top: 10px; margin-bottom: 20px;">
                        <div class="cost-label" style="font-weight: bold;">إجمالي التكلفة الفعلية</div>
                        <div class="cost-value" style="color: #2e7d32;">{{ cost_stats.total_actual_cost|floatformat:0|default:0 }}</div>
                        <small style="color: #666;">جنيه</small>
                    </div>

                    <!-- انحراف التكلفة -->
                    {% if cost_stats.total_estimated_cost > 0 %}
                    <div class="cost-item" style="border-top: 2px solid #ff5722; padding-top: 10px;">
                        <div class="cost-label" style="font-weight: bold;">انحراف التكلفة</div>
                        <div class="cost-value" style="color: {% if cost_stats.cost_variance > 0 %}#d32f2f{% elif cost_stats.cost_variance < 0 %}#388e3c{% else %}#666{% endif %};">
                            {% if cost_stats.cost_variance > 0 %}+{% endif %}{{ cost_stats.cost_variance|floatformat:0|default:0 }}
                        </div>
                        <small style="color: {% if cost_stats.cost_variance > 0 %}#d32f2f{% elif cost_stats.cost_variance < 0 %}#388e3c{% else %}#666{% endif %};">
                            {% if cost_stats.cost_variance > 0 %}زيادة في التكلفة{% elif cost_stats.cost_variance < 0 %}توفير في التكلفة{% else %}لا يوجد انحراف{% endif %}
                            ({{ cost_stats.cost_variance_percentage|floatformat:1 }}%)
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- High Priority Orders -->
            {% if high_priority_orders %}
            <div class="content-section">
                <h2 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    أوامر عالية الأولوية
                </h2>
                
                <div class="order-list">
                    {% for order in high_priority_orders %}
                    <div class="order-item urgent" onclick="window.location.href='{% url 'manufacturing:order_detail' order.id %}'">
                        <div class="order-header">
                            <div class="order-number">{{ order.order_number }}</div>
                        </div>
                        <div class="order-details">
                            <strong>{{ order.final_product.name }}</strong><br>
                            {{ order.expected_completion_date|date:"d/m/Y" }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Overdue Orders -->
            {% if overdue_orders %}
            <div class="content-section">
                <h2 class="section-title">
                    <div class="section-icon">
                        <i class="bi bi-clock"></i>
                    </div>
                    أوامر متأخرة
                </h2>
                
                <div class="order-list">
                    {% for order in overdue_orders %}
                    <div class="order-item overdue" onclick="window.location.href='{% url 'manufacturing:order_detail' order.id %}'">
                        <div class="order-header">
                            <div class="order-number">{{ order.order_number }}</div>
                        </div>
                        <div class="order-details">
                            <strong>{{ order.final_product.name }}</strong><br>
                            متأخر منذ: {{ order.expected_completion_date|date:"d/m/Y" }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
