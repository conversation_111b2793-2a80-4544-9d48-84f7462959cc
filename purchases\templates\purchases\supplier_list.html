{% extends 'base.html' %}

{% block title %}قائمة الموردين - نظام أوساريك{% endblock %}

{% block extra_css %}
    <style>
        
        .page-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .search-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .suppliers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .supplier-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #28a745;
            position: relative;
            overflow: hidden;
        }
        
        .supplier-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.05));
            border-radius: 50%;
            transform: translate(20px, -20px);
        }
        
        .supplier-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .supplier-card.inactive {
            opacity: 0.7;
            border-left-color: #6c757d;
        }
        
        .supplier-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .supplier-name {
            font-size: 1.4rem;
            font-weight: 700;
            color: #333;
            margin: 0;
        }
        
        .supplier-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: 600;
            font-size: 0.8rem;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .supplier-info {
            margin-bottom: 20px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: #6c757d;
        }
        
        .info-item i {
            width: 20px;
            margin-left: 10px;
            color: #28a745;
        }
        
        .supplier-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        
        .btn-action {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            border: none;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .btn-view {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
        }
        
        .btn-view:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
            color: white;
        }
        
        .btn-edit {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: white;
        }
        
        .btn-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
            color: white;
        }
        
        .btn-delete {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .btn-delete:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
            color: white;
        }
        
        .btn-add {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn-add:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .btn-print {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .stats-summary {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            text-align: center;
        }
        
        .stat-item {
            flex: 1;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #28a745;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-people"></i>
                        قائمة الموردين
                    </h1>
                    <p class="mb-0">إدارة وعرض جميع الموردين في النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 justify-content-end">
                        <button class="btn btn-print" onclick="window.print()">
                            <i class="bi bi-printer"></i>
                            طباعة
                        </button>
                        <a href="{% url 'purchases:supplier_create' %}" class="btn-add">
                            <i class="bi bi-plus-circle"></i>
                            إضافة مورد جديد
                        </a>
                        <a href="{% url 'purchases:dashboard' %}" class="btn btn-outline-light">
                            <i class="bi bi-arrow-right"></i>
                            العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-summary">
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-number">{{ total_suppliers }}</div>
                    <div class="stat-label">إجمالي الموردين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ active_suppliers }}</div>
                    <div class="stat-label">موردين نشطين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ inactive_suppliers }}</div>
                    <div class="stat-label">موردين غير نشطين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ suppliers_with_email }}</div>
                    <div class="stat-label">لديهم بريد إلكتروني</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ suppliers_with_phone }}</div>
                    <div class="stat-label">لديهم رقم هاتف</div>
                </div>
            </div>
        </div>

        <!-- قسم البحث والفلترة -->
        <div class="search-section">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" name="search" class="form-control" 
                               placeholder="اسم المورد أو رقم الهاتف" value="{{ search }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="active" {% if status == 'active' %}selected{% endif %}>نشط</option>
                        <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">ترتيب حسب</label>
                    <select name="sort" class="form-select">
                        <option value="name" {% if sort == 'name' %}selected{% endif %}>الاسم</option>
                        <option value="-created_at" {% if sort == '-created_at' %}selected{% endif %}>الأحدث</option>
                        <option value="created_at" {% if sort == 'created_at' %}selected{% endif %}>الأقدم</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-funnel"></i>
                        تطبيق
                    </button>
                </div>
            </form>
        </div>

        <!-- قائمة الموردين -->
        {% if suppliers %}
            <div class="suppliers-grid">
                {% for supplier in suppliers %}
                    <div class="supplier-card {% if not supplier.is_active %}inactive{% endif %}">
                        <div class="supplier-header">
                            <h5 class="supplier-name">{{ supplier.name }}</h5>
                            <span class="supplier-status {% if supplier.is_active %}status-active{% else %}status-inactive{% endif %}">
                                {% if supplier.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </div>
                        
                        <div class="supplier-info">
                            {% if supplier.phone %}
                                <div class="info-item">
                                    <i class="bi bi-telephone"></i>
                                    <span>{{ supplier.phone }}</span>
                                </div>
                            {% endif %}
                            
                            {% if supplier.email %}
                                <div class="info-item">
                                    <i class="bi bi-envelope"></i>
                                    <span>{{ supplier.email }}</span>
                                </div>
                            {% endif %}
                            
                            {% if supplier.address %}
                                <div class="info-item">
                                    <i class="bi bi-geo-alt"></i>
                                    <span>{{ supplier.address|truncatechars:50 }}</span>
                                </div>
                            {% endif %}
                            
                            {% if supplier.payment_terms %}
                                <div class="info-item">
                                    <i class="bi bi-credit-card"></i>
                                    <span>{{ supplier.payment_terms }}</span>
                                </div>
                            {% endif %}
                            
                            <div class="info-item">
                                <i class="bi bi-calendar"></i>
                                <span>انضم في {{ supplier.created_at|date:"Y/m/d" }}</span>
                            </div>
                        </div>
                        
                        <div class="supplier-actions">
                            <a href="{% url 'purchases:supplier_detail' supplier.pk %}" class="btn-action btn-view">
                                <i class="bi bi-eye"></i>
                                عرض
                            </a>
                            <a href="{% url 'purchases:supplier_edit' supplier.pk %}" class="btn-action btn-edit">
                                <i class="bi bi-pencil"></i>
                                تعديل
                            </a>
                            <button class="btn-action btn-delete" onclick="deleteSupplier('{{ supplier.name }}', '{% url 'purchases:supplier_delete' supplier.pk %}')">
                                <i class="bi bi-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- الترقيم -->
            {% if suppliers.has_other_pages %}
                <nav aria-label="ترقيم الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if suppliers.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ suppliers.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}

                        {% for num in suppliers.paginator.page_range %}
                            {% if suppliers.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > suppliers.number|add:'-3' and num < suppliers.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}">
                                        {{ num }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if suppliers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ suppliers.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if sort %}&sort={{ sort }}{% endif %}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <i class="bi bi-people"></i>
                <h3>لا توجد موردين</h3>
                <p>لم يتم العثور على موردين يطابقون معايير البحث</p>
                <a href="{% url 'purchases:supplier_create' %}" class="btn-add mt-3">
                    <i class="bi bi-plus-circle"></i>
                    إضافة أول مورد
                </a>
            </div>
        {% endif %}
    </div>

{% endblock %}

{% block extra_js %}
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.supplier-card, .search-section, .stats-summary');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 50);
            });
        });

        function deleteSupplier(supplierName, deleteUrl) {
            if (confirm(`هل تريد حذف المورد "${supplierName}"؟`)) {
                window.location.href = deleteUrl;
            }
        }
    </script>
{% endblock %}
