# نظام الصلاحيات المتقدم

## ⚠️ تحديث مهم - يوليو 2025

**تم نقل إدارة المستخدمين إلى النظام الجديد في `system_settings`**

- ✅ **النظام الجديد**: `system_settings/users_management` - تصميم حديث وميزات متطورة
- ❌ **النظام القديم**: `permissions/users` - تم إلغاؤه وحذف وظائفه

**يرجى استخدام النظام الجديد لجميع عمليات إدارة المستخدمين.**

---

## نظرة عامة

تم إنشاء نظام صلاحيات متقدم ومفصل يوفر تحكماً دقيقاً في صلاحيات المستخدمين حسب مناصبهم ووظائفهم.

**ملاحظة**: يركز هذا النظام الآن على إدارة الصلاحيات فقط، بينما تتم إدارة المستخدمين من خلال `system_settings`.

## المكونات الأساسية

### 1. النماذج (Models)

#### Permission (الصلاحيات)
- **الغرض**: تعريف الصلاحيات المختلفة في النظام
- **الحقول**:
  - `name`: اسم الصلاحية
  - `code`: كود فريد للصلاحية
  - `category`: فئة الصلاحية (مستخدمين، مبيعات، مشتريات، إلخ)
  - `action`: نوع الإجراء (عرض، إضافة، تعديل، حذف، إلخ)
  - `description`: وصف الصلاحية
  - `is_active`: حالة الصلاحية

#### Role (الأدوار)
- **الغرض**: تجميع الصلاحيات في أدوار وظيفية
- **الحقول**:
  - `name`: اسم الدور
  - `code`: كود فريد للدور
  - `description`: وصف الدور
  - `permissions`: الصلاحيات المرتبطة بالدور
  - `is_active`: حالة الدور

#### UserRole (أدوار المستخدمين)
- **الغرض**: ربط المستخدمين بالأدوار
- **الحقول**:
  - `user`: المستخدم
  - `role`: الدور
  - `assigned_by`: من قام بالتعيين
  - `assigned_at`: تاريخ التعيين
  - `is_active`: حالة التعيين

### 2. الفئات والإجراءات المتاحة

#### الفئات:
- `users`: إدارة المستخدمين
- `products`: إدارة المنتجات
- `sales`: المبيعات
- `purchases`: المشتريات
- `warehouses`: إدارة المخازن
- `accounting`: المحاسبة
- `reports`: التقارير
- `settings`: الإعدادات
- `hr`: شؤون العاملين
- `assets`: الأصول الثابتة
- `banks`: البنوك
- `treasuries`: الخزائن
- `branches`: الفروع
- `customers`: العملاء
- `suppliers`: الموردين

#### الإجراءات:
- `view`: عرض
- `add`: إضافة
- `edit`: تعديل
- `delete`: حذف
- `approve`: اعتماد
- `export`: تصدير
- `import`: استيراد
- `print`: طباعة

### 3. الأدوار الأساسية المُعرَّفة مسبقاً

1. **مدير عام** (`admin`): جميع الصلاحيات
2. **مدير المبيعات** (`sales_manager`): صلاحيات المبيعات والعملاء
3. **مدير المشتريات** (`purchase_manager`): صلاحيات المشتريات والموردين
4. **أمين المخزن** (`warehouse_keeper`): صلاحيات المخازن والمنتجات
5. **موظف مبيعات** (`sales_employee`): صلاحيات محدودة للمبيعات
6. **محاسب** (`accountant`): صلاحيات المحاسبة والتقارير المالية
7. **مدير الموارد البشرية** (`hr_manager`): صلاحيات شؤون العاملين

## الاستخدام

### 1. في Views

#### استخدام Decorators:

```python
from system_settings.decorators import permission_required

@permission_required('users_view')
def users_list(request):
    # عرض قائمة المستخدمين
    pass

@permission_required('sales_add')
def create_sale(request):
    # إنشاء عملية بيع جديدة
    pass
```

#### فحص الصلاحيات يدوياً:

```python
from system_settings.decorators import has_user_permission

def my_view(request):
    if has_user_permission(request.user, 'users_edit'):
        # المستخدم لديه صلاحية التعديل
        pass
```

### 2. في Templates

#### تحميل Template Tags:

```html
{% load permissions_tags %}
```

#### فحص الصلاحيات:

```html
{% if user|has_permission:"users_view" %}
    <a href="{% url 'users_list' %}">عرض المستخدمين</a>
{% endif %}

{% if user|has_category_permission:"sales_add" %}
    <button>إضافة عملية بيع</button>
{% endif %}
```

#### عرض صلاحيات المستخدم:

```html
{% user_permissions user as permissions %}
{% for permission in permissions %}
    <span class="badge">{{ permission.name }}</span>
{% endfor %}

{% user_roles user as roles %}
{% for role in roles %}
    <span class="role-badge">{{ role.name }}</span>
{% endfor %}
```

### 3. إدارة الصلاحيات

#### الصفحات المتاحة:
- `/settings/permissions/`: إدارة الصلاحيات
- `/settings/roles/`: إدارة الأدوار
- `/settings/users/{id}/permissions/`: تعيين صلاحيات مستخدم
- `/settings/permissions/summary/`: ملخص نظام الصلاحيات

#### الوصول السريع:
- من القائمة العلوية: زر الإعدادات السريعة
- من قائمة المستخدمين: زر "الصلاحيات" لكل مستخدم

## المميزات

### 1. نظام مرن ومتدرج
- صلاحيات مفصلة لكل فئة وإجراء
- أدوار قابلة للتخصيص
- إمكانية تعيين أدوار متعددة لمستخدم واحد

### 2. أمان متقدم
- فحص الصلاحيات على مستوى View
- فحص الصلاحيات على مستوى Template
- حماية من الوصول غير المصرح به

### 3. سهولة الاستخدام
- واجهات بصرية جميلة وسهلة
- بحث وفلترة في الصلاحيات والأدوار
- إحصائيات مفصلة

### 4. قابلية التوسع
- إمكانية إضافة فئات وإجراءات جديدة
- إمكانية إنشاء أدوار مخصصة
- نظام قاعدة بيانات محسن

## الصيانة والتطوير

### إضافة صلاحيات جديدة:
```python
# في Django shell أو management command
from system_settings.models import Permission

Permission.objects.create(
    name="عرض التقارير المالية",
    code="financial_reports_view",
    category="reports",
    action="view",
    description="صلاحية عرض التقارير المالية"
)
```

### إنشاء دور جديد:
```python
from system_settings.models import Role, Permission

role = Role.objects.create(
    name="مدير فرع",
    code="branch_manager",
    description="مدير فرع مع صلاحيات محدودة"
)

# إضافة صلاحيات للدور
permissions = Permission.objects.filter(
    category__in=['sales', 'customers', 'reports']
)
role.permissions.set(permissions)
```

## الأمان والاعتبارات

1. **المدير العام**: لديه جميع الصلاحيات تلقائياً
2. **فحص الصلاحيات**: يتم على مستوى قاعدة البيانات
3. **الأداء**: استعلامات محسنة مع فهرسة مناسبة
4. **التدقيق**: تسجيل من قام بتعيين الأدوار ومتى

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى مراجعة:
- ملفات التوثيق في المشروع
- صفحة ملخص الصلاحيات في النظام
- سجلات النظام للأخطاء
