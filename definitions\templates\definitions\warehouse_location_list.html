{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة أماكن الأصناف في المخازن{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Warehouse Locations Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --shadow-heavy: 0 15px 35px rgba(31, 38, 135, 0.2);
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Hero Section */
    .locations-hero {
        background: var(--primary-gradient);
        padding: 3rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        border-radius: 0 0 2rem 2rem;
    }

    .locations-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        text-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .hero-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    /* Filter Section */
    .filter-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e5e7eb;
    }

    .filter-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #374151;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Advanced Table */
    .locations-table {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: none;
    }

    .table-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem 2rem;
        border-radius: 20px 20px 0 0;
    }

    .table-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .table thead th {
        background: #f8fafc;
        border: none;
        font-weight: 700;
        color: #374151;
        padding: 1.5rem 1rem;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .table tbody td {
        border: none;
        padding: 1.25rem 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f5f9;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: linear-gradient(90deg, #f8fafc 0%, #e0e7ff 100%);
        transform: scale(1.01);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    /* Status Badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        white-space: nowrap;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .status-badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .status-active {
        background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
        color: white;
    }

    .status-inactive {
        background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
        color: white;
    }

    /* Capacity Progress */
    .capacity-progress {
        width: 100%;
        height: 8px;
        background: #e5e7eb;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
    }

    .capacity-bar {
        height: 100%;
        border-radius: 10px;
        transition: width 0.8s ease;
        position: relative;
    }

    .capacity-bar.low {
        background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
    }

    .capacity-bar.medium {
        background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
    }

    .capacity-bar.high {
        background: linear-gradient(90deg, #ef4444 0%, #f87171 100%);
    }

    .capacity-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* Action Buttons */
    .action-btn {
        padding: 0.5rem 1rem;
        border-radius: 8px;
        border: none;
        font-size: 0.8rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.3rem;
        transition: all 0.3s ease;
        margin: 0.2rem;
    }

    .btn-view {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
    }

    .btn-edit {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
    }

    .btn-delete {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }

    /* Add Button */
    .add-location-btn {
        background: var(--success-gradient);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .add-location-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(17, 153, 142, 0.3);
        color: white;
        text-decoration: none;
    }

    /* Back Button */
    .btn-outline-light {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(8px);
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
    }

    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
        color: white;
        text-decoration: none;
    }

    /* Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }
        
        .filter-section {
            padding: 1rem;
        }
        
        .table-responsive {
            border-radius: 16px;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    /* Location Icon */
    .location-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
    }

    /* Capacity Info */
    .capacity-info {
        min-width: 120px;
    }

    .capacity-info .fw-bold {
        font-size: 0.95rem;
    }

    /* Enhanced Table Layout */
    .table {
        font-size: 0.9rem;
    }

    .table thead th {
        text-align: center;
        padding: 1rem 0.75rem;
        font-size: 0.85rem;
        white-space: nowrap;
        border-bottom: 2px solid #e5e7eb;
    }

    .table tbody td {
        text-align: center;
        padding: 1rem 0.75rem;
    }

    .table tbody tr:hover {
        transform: scale(1.005);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* Column Widths */
    .table th:nth-child(1), .table td:nth-child(1) { width: 12%; }
    .table th:nth-child(2), .table td:nth-child(2) { width: 20%; }
    .table th:nth-child(3), .table td:nth-child(3) { width: 18%; }
    .table th:nth-child(4), .table td:nth-child(4) { width: 15%; }
    .table th:nth-child(5), .table td:nth-child(5) { width: 15%; }
    .table th:nth-child(6), .table td:nth-child(6) { width: 10%; }
    .table th:nth-child(7), .table td:nth-child(7) { width: 10%; }

    /* Compact Action Buttons */
    .action-buttons {
        display: flex;
        gap: 0.25rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .action-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
        border-radius: 6px;
        min-width: auto;
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="locations-hero">
    <div class="container-fluid">
        <div class="hero-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">
                        <i class="bi bi-geo-alt me-3"></i>
                        إدارة أماكن الأصناف في المخازن
                    </h1>
                    <p class="hero-subtitle">
                        تحديد وإدارة مواقع الأصناف داخل المخازن مع تتبع السعة والحدود
                    </p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="d-flex gap-3 justify-content-end">
                        <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left me-2"></i>
                            عودة للتعريفات
                        </a>
                        <a href="{% url 'definitions:warehouse_location_create' %}" class="add-location-btn">
                            <i class="bi bi-plus-circle"></i>
                            إضافة مكان جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon" style="background: var(--primary-gradient);">
                <i class="bi bi-geo-alt"></i>
            </div>
            <div class="stat-number text-primary">{{ total_locations }}</div>
            <div class="stat-label">إجمالي الأماكن</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon" style="background: var(--success-gradient);">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stat-number text-success">{{ active_locations }}</div>
            <div class="stat-label">أماكن نشطة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon" style="background: var(--warning-gradient);">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <div class="stat-number text-warning">{{ full_locations }}</div>
            <div class="stat-label">أماكن ممتلئة</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon" style="background: var(--info-gradient);">
                <i class="bi bi-building"></i>
            </div>
            <div class="stat-number text-info">{{ total_warehouses }}</div>
            <div class="stat-label">المخازن المرتبطة</div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <div class="filter-title">
            <i class="bi bi-funnel"></i>
            تصفية وبحث الأماكن
        </div>
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">المخزن</label>
                <select name="warehouse" class="form-select">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if request.GET.warehouse == warehouse.id|stringformat:"s" %}selected{% endif %}>
                        {{ warehouse.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="active" {% if request.GET.status == "active" %}selected{% endif %}>نشط</option>
                    <option value="inactive" {% if request.GET.status == "inactive" %}selected{% endif %}>غير نشط</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" placeholder="اسم المكان أو الكود" value="{{ request.GET.search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i> بحث
                    </button>
                    <a href="{% url 'definitions:warehouse_location_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Locations Table -->
    <div class="locations-table">
        <div class="table-header">
            <h3 class="table-title">
                <i class="bi bi-table"></i>
                قائمة أماكن الأصناف
            </h3>
        </div>

        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th><i class="bi bi-hash me-1"></i>كود المكان</th>
                        <th><i class="bi bi-geo-alt me-1"></i>اسم المكان</th>
                        <th><i class="bi bi-building me-1"></i>المخزن</th>
                        <th><i class="bi bi-box me-1"></i>السعة</th>
                        <th><i class="bi bi-speedometer2 me-1"></i>نسبة الاستخدام</th>
                        <th><i class="bi bi-toggle-on me-1"></i>الحالة</th>
                        <th><i class="bi bi-gear me-1"></i>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for location in locations %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="location-icon me-2">
                                    <i class="bi bi-geo-alt-fill text-primary"></i>
                                </div>
                                <strong class="text-primary">{{ location.code }}</strong>
                            </div>
                        </td>
                        <td>
                            <div class="text-start">
                                <div class="fw-bold text-dark">{{ location.name }}</div>
                                {% if location.description %}
                                <small class="text-muted d-block mt-1">{{ location.description|truncatechars:40 }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="text-center">
                                <div class="fw-bold text-info">{{ location.warehouse.name }}</div>
                                <small class="text-muted">{{ location.warehouse.code }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="capacity-info">
                                {% if location.max_capacity %}
                                <div class="d-flex align-items-center justify-content-between mb-1">
                                    <span class="fw-bold text-primary">{{ location.current_capacity|floatformat:1 }}</span>
                                    <span class="text-muted">/</span>
                                    <span class="fw-bold text-success">{{ location.max_capacity|floatformat:1 }}</span>
                                </div>
                                <small class="text-muted">
                                    <i class="bi bi-box-seam me-1"></i>متاح: {{ location.available_capacity|floatformat:1 }}
                                </small>
                                {% else %}
                                <div class="text-center">
                                    <i class="bi bi-infinity text-muted"></i>
                                    <br><small class="text-muted">غير محدود</small>
                                </div>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            {% if location.max_capacity %}
                            <div class="text-center">
                                <div class="capacity-progress mb-1">
                                    {% with percentage=location.capacity_percentage %}
                                    <div class="capacity-bar {% if percentage < 50 %}low{% elif percentage < 80 %}medium{% else %}high{% endif %}"
                                         style="width: {{ percentage }}%"></div>
                                    {% endwith %}
                                </div>
                                <small class="fw-bold">{{ location.capacity_percentage|floatformat:1 }}%</small>
                            </div>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex justify-content-center">
                                {% if location.is_active %}
                                <span class="status-badge status-active">
                                    <i class="bi bi-check-circle me-1"></i>
                                    <span>نشط</span>
                                </span>
                                {% else %}
                                <span class="status-badge status-inactive">
                                    <i class="bi bi-x-circle me-1"></i>
                                    <span>غير نشط</span>
                                </span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'definitions:warehouse_location_detail' location.id %}"
                                   class="action-btn btn-view" title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'definitions:warehouse_location_edit' location.id %}"
                                   class="action-btn btn-edit" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <form method="post" action="{% url 'definitions:warehouse_location_delete' location.id %}" style="display: inline;" class="delete-form">
                                    {% csrf_token %}
                                    <button type="button"
                                            class="action-btn btn-delete delete-location-btn"
                                            data-location-name="{{ location.name }}"
                                            data-location-id="{{ location.id }}"
                                            title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <div class="text-muted">
                                <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">لا توجد أماكن مسجلة</h5>
                                <p>ابدأ بإضافة أماكن جديدة للأصناف في المخازن</p>
                                <a href="{% url 'definitions:warehouse_location_create' %}" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-2"></i>إضافة مكان جديد
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="d-flex justify-content-center py-3">
            <nav aria-label="تنقل الصفحات">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">السابقة</a>
                    </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                    </li>

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأخيرة</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-location-btn');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const locationName = this.getAttribute('data-location-name');
            const locationId = this.getAttribute('data-location-id');
            const form = this.closest('form');

            console.log('Delete button clicked for:', locationName);

            if (confirm(`هل أنت متأكد من حذف المكان: ${locationName}؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
                console.log('User confirmed deletion');

                // تغيير نص الزر
                this.innerHTML = '<span class="spinner-border spinner-border-sm"></span>';
                this.disabled = true;

                console.log('Submitting form...');

                // إرسال النموذج
                form.submit();
            } else {
                console.log('User cancelled deletion');
            }
        });
    });
});
</script>
{% endblock %}
