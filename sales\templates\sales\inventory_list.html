{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة الجرد{% endblock %}

{% block extra_css %}
<style>
    .inventory-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .inventory-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .page-header {
        background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
    }
    
    .search-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    .inventory-header {
        background: linear-gradient(45deg, #fd7e14, #e55a00);
        color: white;
        padding: 20px;
    }
    
    .inventory-number {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .inventory-date {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .inventory-body {
        padding: 20px;
    }
    
    .inventory-details {
        margin-bottom: 15px;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 5px 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .detail-value {
        font-weight: 600;
        color: #333;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-pending {
        background: #ffc107;
        color: #212529;
    }
    
    .status-approved {
        background: #28a745;
        color: white;
    }
    
    .type-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        margin-bottom: 10px;
        display: inline-block;
    }
    
    .type-daily { background: #e3f2fd; color: #1976d2; }
    .type-monthly { background: #f3e5f5; color: #7b1fa2; }
    .type-annual { background: #fff3e0; color: #f57c00; }
    
    .value-display {
        font-size: 1.2rem;
        font-weight: 700;
        color: #fd7e14;
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-top: 15px;
    }
    
    .btn-create {
        background: linear-gradient(45deg, #fd7e14, #e55a00);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(253, 126, 20, 0.3);
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    .warehouse-info {
        background: #e8f5e8;
        color: #2e7d32;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 0.85rem;
        margin-bottom: 10px;
        border-left: 4px solid #4caf50;
    }
    
    .vehicle-info {
        background: #e3f2fd;
        color: #1976d2;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 0.85rem;
        margin-bottom: 10px;
        border-left: 4px solid #2196f3;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-clipboard-check"></i>
                    إدارة الجرد
                </h1>
                <p class="mb-0">إدارة ومتابعة عمليات الجرد اليومي والشهري والسنوي</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'sales:inventory_create' %}" class="btn btn-create">
                    <i class="bi bi-plus-circle"></i>
                    جرد جديد
                </a>
            </div>
        </div>
    </div>

    <!-- قسم البحث والفلترة -->
    <div class="search-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="رقم الجرد، المخزن، أو المندوب" 
                       value="{{ search }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">نوع الجرد</label>
                <select name="inventory_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in type_choices %}
                        <option value="{{ value }}" {% if inventory_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>في الانتظار</option>
                    <option value="approved" {% if status == 'approved' %}selected{% endif %}>معتمد</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search"></i>
                </button>
                <a href="{% url 'sales:inventory_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- قائمة الجرد -->
    {% if inventories %}
        <div class="row">
            {% for inventory in inventories %}
                <div class="col-lg-6 col-xl-4">
                    <div class="inventory-card">
                        <div class="inventory-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="inventory-number">{{ inventory.inventory_number }}</div>
                                    <div class="inventory-date">{{ inventory.inventory_date }}</div>
                                </div>
                                <span class="status-badge {% if inventory.is_approved %}status-approved{% else %}status-pending{% endif %}">
                                    {% if inventory.is_approved %}معتمد{% else %}في الانتظار{% endif %}
                                </span>
                            </div>
                        </div>
                        
                        <div class="inventory-body">
                            <div class="type-badge type-{{ inventory.inventory_type }}">
                                {{ inventory.get_inventory_type_display }}
                            </div>
                            
                            {% if inventory.warehouse %}
                                <div class="warehouse-info">
                                    <i class="bi bi-building"></i>
                                    <strong>المخزن:</strong> {{ inventory.warehouse }}
                                </div>
                            {% endif %}
                            
                            {% if inventory.vehicle %}
                                <div class="vehicle-info">
                                    <i class="bi bi-truck"></i>
                                    <strong>السيارة:</strong> {{ inventory.vehicle.plate_number }}
                                </div>
                            {% endif %}
                            
                            <div class="inventory-details">
                                {% if inventory.representative %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-person text-primary"></i>
                                            المندوب:
                                        </span>
                                        <span class="detail-value">{{ inventory.representative.full_name }}</span>
                                    </div>
                                {% endif %}
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-list-ol text-warning"></i>
                                        عدد العناصر:
                                    </span>
                                    <span class="detail-value">{{ inventory.items.count }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-person-badge text-success"></i>
                                        أنشئ بواسطة:
                                    </span>
                                    <span class="detail-value">{{ inventory.created_by.get_full_name }}</span>
                                </div>
                                {% if inventory.is_approved and inventory.approved_by %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-check-circle text-success"></i>
                                            اعتمد بواسطة:
                                        </span>
                                        <span class="detail-value">{{ inventory.approved_by.get_full_name }}</span>
                                    </div>
                                {% endif %}
                                {% if inventory.notes %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-chat-text text-muted"></i>
                                            ملاحظات:
                                        </span>
                                        <span class="detail-value">{{ inventory.notes|truncatechars:30 }}</span>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="value-display">
                                {{ inventory.total_value }} ج.م
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="{% url 'sales:inventory_detail' inventory.pk %}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    عرض التفاصيل
                                </a>
                                {% if not inventory.is_approved %}
                                    <a href="{% url 'sales:inventory_edit' inventory.pk %}" 
                                       class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-pencil"></i>
                                        تعديل
                                    </a>
                                    <a href="{% url 'sales:inventory_approve' inventory.pk %}" 
                                       class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-check-circle"></i>
                                        اعتماد
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- الترقيم -->
        {% if inventories.has_other_pages %}
            <nav aria-label="ترقيم الصفحات" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if inventories.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ inventories.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if inventory_type %}&inventory_type={{ inventory_type }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                السابق
                            </a>
                        </li>
                    {% endif %}

                    {% for num in inventories.paginator.page_range %}
                        {% if inventories.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > inventories.number|add:'-3' and num < inventories.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if inventory_type %}&inventory_type={{ inventory_type }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                    {{ num }}
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if inventories.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ inventories.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if inventory_type %}&inventory_type={{ inventory_type }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                التالي
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="empty-state">
            <i class="bi bi-clipboard-check"></i>
            <h3>لا توجد عمليات جرد</h3>
            <p>لم يتم العثور على أي عمليات جرد تطابق معايير البحث</p>
            <a href="{% url 'sales:inventory_create' %}" class="btn btn-create">
                <i class="bi bi-plus-circle"></i>
                إنشاء أول جرد
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تأثيرات بصرية للبطاقات
    $('.inventory-card').hover(
        function() {
            $(this).find('.inventory-number').addClass('text-warning');
        },
        function() {
            $(this).find('.inventory-number').removeClass('text-warning');
        }
    );
});
</script>
{% endblock %}
