{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    .stats-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid rgba(0,0,0,0.1);
    }
    .filter-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    .badge-status {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
        border-radius: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:finished_product_model_create' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>إنشاء نموذج جديد
                </a>
                <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-primary">إجمالي النماذج</h4>
                <h2 class="text-dark">{{ total_models }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-success">النماذج النشطة</h4>
                <h2 class="text-dark">{{ active_models }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-info">النماذج المعتمدة</h4>
                <h2 class="text-dark">{{ approved_models }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-warning">قيد المراجعة</h4>
                <h2 class="text-dark">{{ total_models|add:"-"|add:approved_models }}</h2>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="content-card">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" value="{{ search_query }}" placeholder="البحث في الكود أو الاسم">
            </div>
            <div class="col-md-2">
                <label class="form-label">نوع المنتج</label>
                <select name="product_type" class="form-select">
                    <option value="">الكل</option>
                    {% for value, label in product_types %}
                        <option value="{{ value }}" {% if product_type == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">مستوى الجودة</label>
                <select name="quality_level" class="form-select">
                    <option value="">الكل</option>
                    {% for value, label in quality_levels %}
                        <option value="{{ value }}" {% if quality_level == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="is_active" class="form-select">
                    <option value="">الكل</option>
                    <option value="true" {% if is_active == 'true' %}selected{% endif %}>نشط</option>
                    <option value="false" {% if is_active == 'false' %}selected{% endif %}>غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الاعتماد</label>
                <select name="is_approved" class="form-select">
                    <option value="">الكل</option>
                    <option value="true" {% if is_approved == 'true' %}selected{% endif %}>معتمد</option>
                    <option value="false" {% if is_approved == 'false' %}selected{% endif %}>غير معتمد</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary d-block w-100">
                    <i class="bi bi-search"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- قائمة النماذج -->
    <div class="content-card">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الكود</th>
                        <th>اسم النموذج</th>
                        <th>المنتج النهائي</th>
                        <th>نوع المنتج</th>
                        <th>مستوى الجودة</th>
                        <th>التكلفة المقدرة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for model in models %}
                    <tr>
                        <td><strong>{{ model.code }}</strong></td>
                        <td>
                            <a href="{% url 'definitions:finished_product_model_detail' model.id %}" class="text-decoration-none">
                                {{ model.name }}
                            </a>
                        </td>
                        <td>{{ model.final_product.name }}</td>
                        <td>
                            <span class="badge bg-info">{{ model.get_product_type_display }}</span>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ model.get_quality_level_display }}</span>
                        </td>
                        <td>{{ model.total_estimated_cost|floatformat:2 }} جنيه</td>
                        <td>
                            {% if model.is_approved %}
                                <span class="badge bg-success">معتمد</span>
                            {% elif model.is_active %}
                                <span class="badge bg-warning">نشط</span>
                            {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'definitions:finished_product_model_detail' model.id %}" class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'definitions:finished_product_model_edit' model.id %}" class="btn btn-outline-warning" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'definitions:finished_product_model_delete' model.id %}" class="btn btn-outline-danger" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="bi bi-gear-wide-connected" style="font-size: 3rem; color: #6c757d;"></i>
                            <p class="mt-2 text-muted">لا توجد نماذج إنتاج متاحة</p>
                            <a href="{% url 'definitions:finished_product_model_create' %}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إنشاء نموذج جديد
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
