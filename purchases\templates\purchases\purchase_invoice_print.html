{% extends 'base.html' %}

{% block title %}طباعة فاتورة الشراء {{ invoice.invoice_number }} - نظام أوساريك{% endblock %}

{% block extra_css %}
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                background: white !important;
            }
            
            .container-fluid {
                padding: 0 !important;
            }
        }
        
        .print-invoice {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .invoice-header {
            border-bottom: 3px solid #dc3545;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .company-name {
            font-size: 2rem;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 10px;
        }
        
        .invoice-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin: 20px 0;
        }
        
        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .detail-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .detail-title {
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        
        .detail-value {
            color: #333;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .items-table th {
            background: #dc3545;
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 600;
        }
        
        .items-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
        }
        
        .items-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .totals-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .total-row.final {
            border-top: 2px solid #dc3545;
            padding-top: 15px;
            margin-top: 15px;
            font-weight: 700;
            font-size: 1.3rem;
            color: #dc3545;
        }
        
        .invoice-footer {
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
        }
        
        .print-button {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            margin: 20px;
            transition: all 0.3s ease;
        }
        
        .print-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid py-4">
        <!-- زر الطباعة -->
        <div class="text-center no-print">
            <button onclick="window.print()" class="print-button">
                <i class="bi bi-printer"></i>
                طباعة الفاتورة
            </button>
            <a href="{% url 'purchases:purchase_invoice_detail' invoice.pk %}" class="btn btn-secondary ms-3">
                <i class="bi bi-arrow-left"></i>
                العودة
            </a>
        </div>

        <!-- فاتورة الطباعة -->
        <div class="print-invoice">
            <!-- رأس الفاتورة -->
            <div class="invoice-header">
                <div class="company-info">
                    <div class="company-name">نظام أوساريك</div>
                    <div class="text-muted">نظام إدارة المشتريات والمبيعات</div>
                </div>
                
                <div class="invoice-title">فاتورة شراء</div>
            </div>

            <!-- تفاصيل الفاتورة -->
            <div class="invoice-details">
                <!-- معلومات الفاتورة -->
                <div class="detail-section">
                    <div class="detail-title">معلومات الفاتورة</div>
                    
                    <div class="detail-row">
                        <span class="detail-label">رقم الفاتورة:</span>
                        <span class="detail-value">{{ invoice.invoice_number }}</span>
                    </div>
                    
                    <div class="detail-row">
                        <span class="detail-label">تاريخ الفاتورة:</span>
                        <span class="detail-value">{{ invoice.invoice_date|date:"d/m/Y" }}</span>
                    </div>
                    
                    {% if invoice.due_date %}
                        <div class="detail-row">
                            <span class="detail-label">تاريخ الاستحقاق:</span>
                            <span class="detail-value">{{ invoice.due_date|date:"d/m/Y" }}</span>
                        </div>
                    {% endif %}
                    
                    <div class="detail-row">
                        <span class="detail-label">الحالة:</span>
                        <span class="detail-value">
                            {% if invoice.status == 'received' %}مستلمة
                            {% elif invoice.status == 'paid' %}مدفوعة
                            {% elif invoice.status == 'overdue' %}متأخرة
                            {% elif invoice.status == 'cancelled' %}ملغية
                            {% endif %}
                        </span>
                    </div>
                </div>

                <!-- معلومات المورد -->
                <div class="detail-section">
                    <div class="detail-title">معلومات المورد</div>
                    
                    <div class="detail-row">
                        <span class="detail-label">اسم المورد:</span>
                        <span class="detail-value">{{ invoice.supplier.name }}</span>
                    </div>
                    
                    {% if invoice.supplier.phone %}
                        <div class="detail-row">
                            <span class="detail-label">الهاتف:</span>
                            <span class="detail-value">{{ invoice.supplier.phone }}</span>
                        </div>
                    {% endif %}
                    
                    {% if invoice.supplier.email %}
                        <div class="detail-row">
                            <span class="detail-label">البريد الإلكتروني:</span>
                            <span class="detail-value">{{ invoice.supplier.email }}</span>
                        </div>
                    {% endif %}
                    
                    {% if invoice.supplier.address %}
                        <div class="detail-row">
                            <span class="detail-label">العنوان:</span>
                            <span class="detail-value">{{ invoice.supplier.address }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- جدول الأصناف -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>سعر الوحدة</th>
                        <th>الخصم</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items.all %}
                        <tr>
                            <td>
                                <strong>{{ item.product.name }}</strong><br>
                                <small>{{ item.product.code }}</small>
                            </td>
                            <td>{{ item.quantity }} {{ item.product.unit }}</td>
                            <td>{{ item.unit_price|floatformat:2 }} ج.م</td>
                            <td>{{ item.discount_percentage|floatformat:1 }}%</td>
                            <td><strong>{{ item.total_price|floatformat:2 }} ج.م</strong></td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="5" class="text-center">لا توجد أصناف في هذه الفاتورة</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- الإجماليات -->
            <div class="totals-section">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>{{ invoice.subtotal|floatformat:2 }} ج.م</span>
                </div>
                
                <div class="total-row">
                    <span>إجمالي الخصم:</span>
                    <span>{{ invoice.total_discount|floatformat:2 }} ج.م</span>
                </div>
                
                <div class="total-row">
                    <span>الضريبة ({{ invoice.tax_percentage|floatformat:1 }}%):</span>
                    <span>{{ invoice.tax_amount|floatformat:2 }} ج.م</span>
                </div>
                
                <div class="total-row final">
                    <span>الإجمالي النهائي:</span>
                    <span>{{ invoice.total_amount|floatformat:2 }} ج.م</span>
                </div>
            </div>

            {% if invoice.notes %}
                <div class="detail-section">
                    <div class="detail-title">ملاحظات</div>
                    <p>{{ invoice.notes }}</p>
                </div>
            {% endif %}

            <!-- تذييل الفاتورة -->
            <div class="invoice-footer">
                <p>تم إنشاء هذه الفاتورة بواسطة نظام أوساريك</p>
                <p>تاريخ الطباعة: {{ today|date:"d/m/Y H:i" }}</p>
            </div>
        </div>
    </div>
{% endblock %}
