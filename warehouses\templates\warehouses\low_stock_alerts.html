{% extends 'base.html' %}
{% load static %}
{% load warehouse_extras %}

{% block title %}تنبيهات المخزون{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%);
        min-height: 100vh;
    }

    .alerts-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        box-shadow: 0 15px 50px rgba(253, 126, 20, 0.37);
    }

    .filter-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 50px rgba(253, 126, 20, 0.37);
        margin-bottom: 2rem;
    }

    .alerts-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 3rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 50px rgba(253, 126, 20, 0.37);
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.8rem;
        font-weight: 800;
        background: linear-gradient(135deg, #fd7e14 0%, #dc3545 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .alert-tabs {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .alert-tab {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 2px solid #fd7e14;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        text-decoration: none;
        color: #fd7e14;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;
    }

    .alert-tab:hover {
        background: linear-gradient(135deg, #fd7e14, #dc3545);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(253, 126, 20, 0.3);
    }

    .alert-tab.active {
        background: linear-gradient(135deg, #fd7e14, #dc3545);
        color: white;
        box-shadow: 0 8px 25px rgba(253, 126, 20, 0.3);
    }

    .alert-badge {
        background: #dc3545;
        color: white;
        border-radius: 50%;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 700;
        position: absolute;
        top: -8px;
        right: -8px;
        min-width: 20px;
        text-align: center;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        border: 2px solid #fd7e14;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(253, 126, 20, 0.2);
    }

    .stat-card.danger {
        border-color: #dc3545;
        background: linear-gradient(135deg, #fff5f5, #fed7d7);
    }

    .stat-card.warning {
        border-color: #ffc107;
        background: linear-gradient(135deg, #fffbf0, #fef3c7);
    }

    .stat-card.info {
        border-color: #17a2b8;
        background: linear-gradient(135deg, #f0f9ff, #cffafe);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        background: linear-gradient(135deg, #fd7e14, #dc3545);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin: 0 auto 1rem;
    }

    .stat-icon.danger {
        background: linear-gradient(135deg, #dc3545, #b91c1c);
    }

    .stat-icon.warning {
        background: linear-gradient(135deg, #ffc107, #f59e0b);
    }

    .stat-icon.info {
        background: linear-gradient(135deg, #17a2b8, #0891b2);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: #fd7e14;
        margin-bottom: 0.5rem;
    }

    .stat-number.danger { color: #dc3545; }
    .stat-number.warning { color: #ffc107; }
    .stat-number.info { color: #17a2b8; }

    .stat-label {
        color: #6c757d;
        font-weight: 600;
    }

    .alert-item {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-left: 5px solid #fd7e14;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .alert-item:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    }

    .alert-item.danger {
        border-left-color: #dc3545;
        background: linear-gradient(135deg, #fff5f5, #fed7d7);
    }

    .alert-item.warning {
        border-left-color: #ffc107;
        background: linear-gradient(135deg, #fffbf0, #fef3c7);
    }

    .alert-item.info {
        border-left-color: #17a2b8;
        background: linear-gradient(135deg, #f0f9ff, #cffafe);
    }

    .alert-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
        margin-bottom: 1rem;
    }

    .alert-title {
        font-size: 1.1rem;
        font-weight: 700;
        color: #495057;
        margin-bottom: 0.25rem;
    }

    .alert-subtitle {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .alert-badge-status {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .alert-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .alert-detail {
        text-align: center;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 10px;
    }

    .alert-detail-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #495057;
    }

    .alert-detail-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .form-control, .form-select {
        border-radius: 15px;
        border: 2px solid rgba(253, 126, 20, 0.2);
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #fd7e14;
        box-shadow: 0 0 20px rgba(253, 126, 20, 0.3);
        outline: none;
    }

    .btn-filter {
        background: linear-gradient(135deg, #fd7e14, #dc3545);
        border: none;
        border-radius: 15px;
        padding: 0.75rem 1.5rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(253, 126, 20, 0.3);
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(253, 126, 20, 0.5);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<!-- Alerts Header -->
<div class="alerts-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-bell me-2"></i>تنبيهات المخزون
                </h1>
                <p class="mb-0 opacity-75">مراقبة المخزون المنخفض والنافد والزائد</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group">
                    <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-light">
                        <i class="bi bi-list-ul me-1"></i>المخزون
                    </a>
                    <a href="{% url 'warehouses:dashboard' %}" class="btn btn-light">
                        <i class="bi bi-arrow-left me-1"></i>لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Filter Section -->
    <div class="filter-section">
        <h5 class="mb-3"><i class="bi bi-funnel me-2"></i>فلاتر التنبيهات</h5>
        
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">نوع التنبيه</label>
                <select name="type" class="form-select">
                    <option value="all" {% if alert_type == 'all' %}selected{% endif %}>جميع التنبيهات</option>
                    <option value="out_of_stock" {% if alert_type == 'out_of_stock' %}selected{% endif %}>مخزون نافد</option>
                    <option value="low_stock" {% if alert_type == 'low_stock' %}selected{% endif %}>مخزون منخفض</option>
                    <option value="overstock" {% if alert_type == 'overstock' %}selected{% endif %}>مخزون زائد</option>
                </select>
            </div>
            
            <div class="col-md-4">
                <label class="form-label">المخزن</label>
                <select name="warehouse" class="form-select">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if selected_warehouse and selected_warehouse.id == warehouse.id %}selected{% endif %}>
                        {{ warehouse.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-filter w-100">
                    <i class="bi bi-search me-1"></i>تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>

    <!-- Alert Tabs -->
    <div class="alert-tabs">
        <a href="?type=all{% if selected_warehouse %}&warehouse={{ selected_warehouse.id }}{% endif %}" 
           class="alert-tab {% if alert_type == 'all' %}active{% endif %}">
            <i class="bi bi-bell"></i>
            جميع التنبيهات
            {% if total_alerts > 0 %}
                <span class="alert-badge">{{ total_alerts }}</span>
            {% endif %}
        </a>
        
        <a href="?type=out_of_stock{% if selected_warehouse %}&warehouse={{ selected_warehouse.id }}{% endif %}" 
           class="alert-tab {% if alert_type == 'out_of_stock' %}active{% endif %}">
            <i class="bi bi-x-circle"></i>
            مخزون نافد
            {% if out_of_stock_count > 0 %}
                <span class="alert-badge">{{ out_of_stock_count }}</span>
            {% endif %}
        </a>
        
        <a href="?type=low_stock{% if selected_warehouse %}&warehouse={{ selected_warehouse.id }}{% endif %}" 
           class="alert-tab {% if alert_type == 'low_stock' %}active{% endif %}">
            <i class="bi bi-exclamation-triangle"></i>
            مخزون منخفض
            {% if low_stock_count > 0 %}
                <span class="alert-badge">{{ low_stock_count }}</span>
            {% endif %}
        </a>
        
        <a href="?type=overstock{% if selected_warehouse %}&warehouse={{ selected_warehouse.id }}{% endif %}" 
           class="alert-tab {% if alert_type == 'overstock' %}active{% endif %}">
            <i class="bi bi-arrow-up-circle"></i>
            مخزون زائد
            {% if overstock_count > 0 %}
                <span class="alert-badge">{{ overstock_count }}</span>
            {% endif %}
        </a>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card danger">
            <div class="stat-icon danger">
                <i class="bi bi-x-circle"></i>
            </div>
            <div class="stat-number danger">{{ out_of_stock_count }}</div>
            <div class="stat-label">مخزون نافد</div>
        </div>
        
        <div class="stat-card warning">
            <div class="stat-icon warning">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <div class="stat-number warning">{{ low_stock_count }}</div>
            <div class="stat-label">مخزون منخفض</div>
        </div>
        
        <div class="stat-card info">
            <div class="stat-icon info">
                <i class="bi bi-arrow-up-circle"></i>
            </div>
            <div class="stat-number info">{{ overstock_count }}</div>
            <div class="stat-label">مخزون زائد</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="bi bi-currency-dollar"></i>
            </div>
            <div class="stat-number">{{ out_of_stock_value|add:low_stock_value|number_format }}</div>
            <div class="stat-label">قيمة النقص (ج.م)</div>
        </div>
    </div>

    <!-- Alerts List -->
    <div class="alerts-section">
        <h2 class="section-title">
            <i class="bi bi-{{ alert_icon }}"></i>
            {{ alert_title }}
        </h2>

        {% if alert_items %}
            {% for item in alert_items %}
            <div class="alert-item {% if item.quantity_on_hand == 0 %}danger{% elif item.quantity_on_hand <= item.minimum_stock %}warning{% else %}info{% endif %}">
                <div class="alert-header">
                    <div>
                        <div class="alert-title">{{ item.product.name }}</div>
                        <div class="alert-subtitle">{{ item.product.code }} - {{ item.warehouse.name }}</div>
                    </div>
                    <div>
                        {% if item.quantity_on_hand == 0 %}
                            <span class="alert-badge-status bg-danger text-white">نافد</span>
                        {% elif item.quantity_on_hand <= item.minimum_stock and item.minimum_stock > 0 %}
                            <span class="alert-badge-status bg-warning text-dark">منخفض</span>
                        {% elif item.quantity_on_hand >= item.maximum_stock and item.maximum_stock > 0 %}
                            <span class="alert-badge-status bg-info text-white">زائد</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="alert-details">
                    <div class="alert-detail">
                        <div class="alert-detail-value">{{ item.quantity_on_hand }}</div>
                        <div class="alert-detail-label">الكمية الحالية</div>
                    </div>
                    
                    {% if item.minimum_stock > 0 %}
                    <div class="alert-detail">
                        <div class="alert-detail-value">{{ item.minimum_stock }}</div>
                        <div class="alert-detail-label">الحد الأدنى</div>
                    </div>
                    {% endif %}
                    
                    {% if item.maximum_stock > 0 %}
                    <div class="alert-detail">
                        <div class="alert-detail-value">{{ item.maximum_stock }}</div>
                        <div class="alert-detail-label">الحد الأقصى</div>
                    </div>
                    {% endif %}
                    
                    <div class="alert-detail">
                        <div class="alert-detail-value">{{ item.total_value|floatformat:2 }}</div>
                        <div class="alert-detail-label">القيمة (ج.م)</div>
                    </div>
                    
                    {% if item.quantity_on_hand == 0 and item.minimum_stock > 0 %}
                    <div class="alert-detail">
                        {% widthratio item.minimum_stock 1 item.product.cost_price as shortage_value %}
                        <div class="alert-detail-value text-danger">{{ shortage_value|floatformat:2 }}</div>
                        <div class="alert-detail-label">قيمة النقص الحقيقية (ج.م)</div>
                    </div>
                    {% elif item.quantity_on_hand <= item.minimum_stock and item.minimum_stock > 0 %}
                    <div class="alert-detail">
                        {% widthratio item.minimum_stock 1 item.product.cost_price as min_value %}
                        {% widthratio item.quantity_on_hand 1 item.product.cost_price as current_value %}
                        <div class="alert-detail-value text-warning">{{ min_value|floatformat:2 }}</div>
                        <div class="alert-detail-label">قيمة الحد الأدنى المطلوب (ج.م)</div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <i class="bi bi-check-circle"></i>
                <h4>لا توجد تنبيهات</h4>
                <p>جميع عناصر المخزون في حالة جيدة</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
