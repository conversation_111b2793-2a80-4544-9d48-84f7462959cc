{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة تحميل السيارات{% endblock %}

{% block extra_css %}
<style>
    .loading-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        margin-bottom: 20px;
    }
    
    .loading-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
    }
    
    .search-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }
    
    .status-pending { background: #ffc107; color: #212529; }
    .status-loaded { background: #17a2b8; color: white; }
    .status-delivered { background: #28a745; color: white; }
    .status-returned { background: #dc3545; color: white; }
    
    .loading-info {
        padding: 20px;
    }
    
    .loading-number {
        font-size: 1.2rem;
        font-weight: 700;
        color: #007bff;
        margin-bottom: 10px;
    }
    
    .loading-details {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .loading-stats {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 0 0 15px 15px;
        border-top: 1px solid #dee2e6;
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: #007bff;
    }
    
    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
    }
    
    .btn-create {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-truck"></i>
                    إدارة تحميل السيارات
                </h1>
                <p class="mb-0">إدارة وتتبع عمليات تحميل البضائع على السيارات</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'sales:vehicle_loading_create' %}" class="btn btn-create">
                    <i class="bi bi-plus-circle"></i>
                    تحميل جديد
                </a>
            </div>
        </div>
    </div>

    <!-- قسم البحث والفلترة -->
    <div class="search-section">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="رقم التحميل، رقم السيارة، أو اسم المندوب" 
                       value="{{ search }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search"></i>
                    بحث
                </button>
                <a href="{% url 'sales:vehicle_loading_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i>
                    إعادة تعيين
                </a>
            </div>
        </form>
    </div>

    <!-- قائمة التحميل -->
    {% if loadings %}
        <div class="row">
            {% for loading in loadings %}
                <div class="col-lg-6 col-xl-4">
                    <div class="loading-card">
                        <div class="loading-info">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="loading-number">{{ loading.loading_number }}</div>
                                <span class="status-badge status-{{ loading.status }}">
                                    {{ loading.get_status_display }}
                                </span>
                            </div>
                            
                            <div class="loading-details">
                                <div class="mb-2">
                                    <i class="bi bi-truck text-primary"></i>
                                    <strong>السيارة:</strong> {{ loading.vehicle.plate_number }}
                                </div>
                                <div class="mb-2">
                                    <i class="bi bi-person text-success"></i>
                                    <strong>المندوب:</strong> {{ loading.representative.full_name }}
                                </div>
                                <div class="mb-2">
                                    <i class="bi bi-building text-info"></i>
                                    <strong>المخزن:</strong> {{ loading.warehouse }}
                                </div>
                                <div class="mb-2">
                                    <i class="bi bi-calendar text-warning"></i>
                                    <strong>التاريخ:</strong> {{ loading.loading_date }}
                                </div>
                                {% if loading.notes %}
                                    <div class="mb-2">
                                        <i class="bi bi-chat-text text-muted"></i>
                                        <small>{{ loading.notes|truncatechars:50 }}</small>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="{% url 'sales:vehicle_loading_detail' loading.pk %}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    عرض التفاصيل
                                </a>
                                {% if loading.status == 'pending' %}
                                    <a href="{% url 'sales:vehicle_loading_edit' loading.pk %}" 
                                       class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-pencil"></i>
                                        تعديل
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="loading-stats">
                            <div class="row">
                                <div class="col-4 stat-item">
                                    <div class="stat-value">{{ loading.items.count }}</div>
                                    <div class="stat-label">عنصر</div>
                                </div>
                                <div class="col-4 stat-item">
                                    <div class="stat-value">{{ loading.total_weight|floatformat:1 }}</div>
                                    <div class="stat-label">كيلو</div>
                                </div>
                                <div class="col-4 stat-item">
                                    <div class="stat-value">{{ loading.total_value|floatformat:0 }}</div>
                                    <div class="stat-label">ج.م</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- الترقيم -->
        {% if loadings.has_other_pages %}
            <nav aria-label="ترقيم الصفحات" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if loadings.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ loadings.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                                السابق
                            </a>
                        </li>
                    {% endif %}

                    {% for num in loadings.paginator.page_range %}
                        {% if loadings.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > loadings.number|add:'-3' and num < loadings.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                                    {{ num }}
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if loadings.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ loadings.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}">
                                التالي
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="empty-state">
            <i class="bi bi-truck"></i>
            <h3>لا توجد عمليات تحميل</h3>
            <p>لم يتم العثور على أي عمليات تحميل تطابق معايير البحث</p>
            <a href="{% url 'sales:vehicle_loading_create' %}" class="btn btn-create">
                <i class="bi bi-plus-circle"></i>
                إنشاء أول تحميل
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث تلقائي للصفحة كل 30 ثانية
    setTimeout(function() {
        location.reload();
    }, 30000);
    
    // تأثيرات بصرية للبطاقات
    $('.loading-card').hover(
        function() {
            $(this).find('.loading-number').addClass('text-primary');
        },
        function() {
            $(this).find('.loading-number').removeClass('text-primary');
        }
    );
});
</script>
{% endblock %}
