#!/usr/bin/env python
"""
سكريبت لتطبيق migrations للتطبيق الجديد system_settings
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from django.core.management import call_command
from django.db import connection

def main():
    print("🔧 تطبيق migrations لتطبيق system_settings...")
    
    try:
        # تطبيق migrations
        call_command('migrate', 'system_settings', verbosity=2)
        print("✅ تم تطبيق migrations بنجاح!")
        
        # إنشاء إعدادات افتراضية
        from system_settings.models import SystemSettings
        settings, created = SystemSettings.objects.get_or_create(pk=1)
        if created:
            print("✅ تم إنشاء إعدادات النظام الافتراضية")
        else:
            print("ℹ️ إعدادات النظام موجودة بالفعل")
            
        print("\n🎉 تم إصلاح المشكلة بنجاح!")
        print("يمكنك الآن الوصول إلى:")
        print("- الصفحة الرئيسية: http://127.0.0.1:8000/")
        print("- إعدادات النظام: http://127.0.0.1:8000/settings/")
        print("- إعدادات النظام المتقدمة: http://127.0.0.1:8000/settings/system/")
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق migrations: {e}")
        
        # محاولة إنشاء الجداول يدوياً
        print("🔄 محاولة إنشاء الجداول يدوياً...")
        try:
            with connection.cursor() as cursor:
                # إنشاء جدول SystemSettings
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_settings_systemsettings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        company_name VARCHAR(200) DEFAULT 'شركة أوساريك',
                        company_logo VARCHAR(100),
                        company_address TEXT,
                        company_phone VARCHAR(20),
                        company_email VARCHAR(254),
                        company_website VARCHAR(200),
                        company_tax_number VARCHAR(50),
                        system_language VARCHAR(10) DEFAULT 'ar',
                        system_timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
                        currency_code VARCHAR(3) DEFAULT 'SAR',
                        currency_symbol VARCHAR(10) DEFAULT 'ريال',
                        session_timeout INTEGER DEFAULT 30,
                        password_min_length INTEGER DEFAULT 8,
                        require_strong_password BOOLEAN DEFAULT 1,
                        max_login_attempts INTEGER DEFAULT 5,
                        email_host VARCHAR(100),
                        email_port INTEGER DEFAULT 587,
                        email_use_tls BOOLEAN DEFAULT 1,
                        backup_enabled BOOLEAN DEFAULT 1,
                        backup_frequency VARCHAR(20) DEFAULT 'daily',
                        theme_color VARCHAR(7) DEFAULT '#667eea',
                        sidebar_collapsed BOOLEAN DEFAULT 0,
                        items_per_page INTEGER DEFAULT 20,
                        notifications_enabled BOOLEAN DEFAULT 1,
                        email_notifications BOOLEAN DEFAULT 1,
                        created_at DATETIME,
                        updated_at DATETIME,
                        updated_by_id INTEGER
                    )
                """)
                
                # إنشاء جدول UserProfile
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_settings_userprofile (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        phone VARCHAR(20),
                        mobile VARCHAR(20),
                        address TEXT,
                        birth_date DATE,
                        national_id VARCHAR(20),
                        employee_id VARCHAR(20) UNIQUE,
                        department VARCHAR(20),
                        position VARCHAR(20),
                        hire_date DATE,
                        avatar VARCHAR(100),
                        theme_preference VARCHAR(20) DEFAULT 'light',
                        is_active BOOLEAN DEFAULT 1,
                        last_login_ip VARCHAR(39),
                        failed_login_attempts INTEGER DEFAULT 0,
                        created_at DATETIME,
                        updated_at DATETIME,
                        manager_id INTEGER,
                        user_id INTEGER UNIQUE NOT NULL
                    )
                """)
                
                print("✅ تم إنشاء الجداول يدوياً!")
                
                # إدراج إعدادات افتراضية
                cursor.execute("""
                    INSERT OR IGNORE INTO system_settings_systemsettings 
                    (id, company_name, created_at, updated_at) 
                    VALUES (1, 'شركة أوساريك', datetime('now'), datetime('now'))
                """)
                
                print("✅ تم إدراج الإعدادات الافتراضية!")
                
        except Exception as e2:
            print(f"❌ خطأ في إنشاء الجداول يدوياً: {e2}")

if __name__ == "__main__":
    main()
