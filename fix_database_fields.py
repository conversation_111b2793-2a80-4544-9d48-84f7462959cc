import sqlite3
import os

# الاتصال بقاعدة البيانات
db_path = os.path.join(os.getcwd(), 'db.sqlite3')

if not os.path.exists(db_path):
    print("❌ ملف قاعدة البيانات غير موجود!")
    exit(1)

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print("🔧 إصلاح الحقول المفقودة في قاعدة البيانات...")

try:
    # فحص الحقول الموجودة
    cursor.execute("PRAGMA table_info(system_settings_systemsettings)")
    existing_columns = [column[1] for column in cursor.fetchall()]
    
    # الحقول المطلوب إضافتها مع قيم افتراضية صحيحة
    fields_to_add = [
        ("date_format", "VARCHAR(20)", "'d/m/Y'"),
        ("thousand_separator", "VARCHAR(5)", "','"),
        ("decimal_separator", "VARCHAR(5)", "'.'"),
        ("email_host_user", "VARCHAR(100)", "''"),
        ("email_host_password", "VARCHAR(100)", "''"),
        ("email_from_name", "VARCHAR(100)", "'نظام أوساريك'"),
        ("secondary_color", "VARCHAR(7)", "'#764ba2'"),
    ]
    
    # إضافة الحقول المفقودة
    for field_name, field_type, default_value in fields_to_add:
        if field_name not in existing_columns:
            try:
                cursor.execute(f"""
                    ALTER TABLE system_settings_systemsettings 
                    ADD COLUMN {field_name} {field_type} DEFAULT {default_value}
                """)
                print(f"✅ تم إضافة الحقل: {field_name}")
            except Exception as e:
                print(f"⚠️ خطأ في إضافة الحقل {field_name}: {e}")
        else:
            print(f"⏭️ الحقل {field_name} موجود بالفعل")
    
    # تحديث القيم الافتراضية
    cursor.execute("SELECT COUNT(*) FROM system_settings_systemsettings")
    count = cursor.fetchone()[0]
    
    if count > 0:
        print("🔄 تحديث القيم الافتراضية...")
        
        # تحديث الإعدادات الأساسية
        update_queries = [
            "UPDATE system_settings_systemsettings SET country = 'Egypt' WHERE id = 1",
            "UPDATE system_settings_systemsettings SET currency_code = 'EGP' WHERE id = 1",
            "UPDATE system_settings_systemsettings SET currency_symbol = 'ج.م' WHERE id = 1",
            "UPDATE system_settings_systemsettings SET system_timezone = 'Africa/Cairo' WHERE id = 1",
            "UPDATE system_settings_systemsettings SET date_format = 'd/m/Y' WHERE id = 1 AND date_format IS NULL",
            "UPDATE system_settings_systemsettings SET thousand_separator = ',' WHERE id = 1 AND thousand_separator IS NULL",
            "UPDATE system_settings_systemsettings SET decimal_separator = '.' WHERE id = 1 AND decimal_separator IS NULL",
            "UPDATE system_settings_systemsettings SET secondary_color = '#764ba2' WHERE id = 1 AND secondary_color IS NULL",
        ]
        
        for query in update_queries:
            try:
                cursor.execute(query)
                print(f"✅ تم تنفيذ: {query.split('SET')[1].split('WHERE')[0].strip()}")
            except Exception as e:
                print(f"⚠️ خطأ في التحديث: {e}")
    
    conn.commit()
    
    # عرض الإعدادات الحالية
    cursor.execute("""
        SELECT company_name, country, currency_code, currency_symbol, 
               system_timezone, date_format, theme_color, secondary_color 
        FROM system_settings_systemsettings WHERE id = 1
    """)
    settings = cursor.fetchone()
    
    if settings:
        print(f"\n📋 الإعدادات الحالية:")
        print(f"🏢 اسم الشركة: {settings[0] or 'غير محدد'}")
        print(f"🌍 الدولة: {settings[1] or 'غير محدد'}")
        print(f"💰 العملة: {settings[2] or 'غير محدد'} ({settings[3] or 'غير محدد'})")
        print(f"🕐 المنطقة الزمنية: {settings[4] or 'غير محدد'}")
        print(f"📅 تنسيق التاريخ: {settings[5] or 'غير محدد'}")
        print(f"🎨 اللون الأساسي: {settings[6] or 'غير محدد'}")
        print(f"🎨 اللون الثانوي: {settings[7] or 'غير محدد'}")
    
    print(f"\n🎉 تم إصلاح قاعدة البيانات بنجاح!")
    
except Exception as e:
    print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
    conn.rollback()
    
finally:
    conn.close()

print("\n📋 يمكنك الآن الوصول إلى:")
print("🔗 http://127.0.0.1:8000/settings/system/")
print("🔗 جميع الإعدادات متاحة ومحدثة!")
