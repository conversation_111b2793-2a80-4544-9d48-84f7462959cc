<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .settings-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .settings-nav {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .nav-pills .nav-link {
            border-radius: 25px;
            padding: 12px 25px;
            margin: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .nav-pills .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-color: #667eea;
            color: white;
        }
        
        .settings-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #667eea;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-left: 15px;
            color: #667eea;
            font-size: 1.8rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .btn-save {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            min-width: 150px;
        }
        
        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-reset {
            background: #6c757d;
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            min-width: 150px;
        }
        
        .btn-reset:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: white;
        }
        
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .alert-info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            color: #0c5460;
        }
        
        .alert-warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .feature-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-weight: 700;
            color: #333;
            margin: 0;
        }
        
        .feature-toggle {
            transform: scale(1.2);
        }
        
        .feature-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .backup-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .backup-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .backup-title i {
            margin-left: 15px;
            font-size: 1.8rem;
        }
        
        .backup-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .btn-backup {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-backup:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            color: white;
        }
        
        .logo-upload {
            border: 2px dashed #667eea;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .logo-upload:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: #764ba2;
        }
        
        .logo-preview {
            max-width: 200px;
            max-height: 100px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        
        .currency-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .currency-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .currency-option:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .currency-option.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .currency-symbol {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .currency-name {
            font-size: 0.9rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-gear"></i>
                        إعدادات النظام
                    </h1>
                    <p class="mb-0">إدارة وتخصيص إعدادات النظام العامة</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'sales:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>

        <!-- محتوى الإعدادات -->
        <div class="settings-container">
            <!-- تبويبات الإعدادات -->
            <div class="settings-nav">
                <ul class="nav nav-pills justify-content-center" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="pill" data-bs-target="#general" type="button" role="tab">
                            <i class="bi bi-gear"></i>
                            إعدادات عامة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="company-tab" data-bs-toggle="pill" data-bs-target="#company" type="button" role="tab">
                            <i class="bi bi-building"></i>
                            بيانات الشركة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="features-tab" data-bs-toggle="pill" data-bs-target="#features" type="button" role="tab">
                            <i class="bi bi-toggles"></i>
                            الميزات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="backup-tab" data-bs-toggle="pill" data-bs-target="#backup" type="button" role="tab">
                            <i class="bi bi-shield-check"></i>
                            النسخ الاحتياطي
                        </button>
                    </li>
                </ul>
            </div>

            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="tab-content" id="settingsTabContent">
                    <!-- الإعدادات العامة -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel">
                        <div class="settings-section">
                            <h3 class="section-title">
                                <i class="bi bi-globe"></i>
                                الإعدادات الأساسية
                            </h3>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">لغة النظام</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="ar" {% if settings.language == 'ar' %}selected{% endif %}>العربية</option>
                                            <option value="en" {% if settings.language == 'en' %}selected{% endif %}>English</option>
                                        </select>
                                        <div class="help-text">اختر لغة واجهة النظام</div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="Africa/Cairo" {% if settings.timezone == 'Africa/Cairo' %}selected{% endif %}>القاهرة (GMT+2)</option>
                                            <option value="Asia/Riyadh" {% if settings.timezone == 'Asia/Riyadh' %}selected{% endif %}>الرياض (GMT+3)</option>
                                            <option value="Asia/Dubai" {% if settings.timezone == 'Asia/Dubai' %}selected{% endif %}>دبي (GMT+4)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">العملة الأساسية</label>
                                <div class="currency-selector">
                                    <div class="currency-option {% if settings.currency == 'EGP' %}selected{% endif %}" onclick="selectCurrency('EGP')">
                                        <div class="currency-symbol">ج.م</div>
                                        <div class="currency-name">جنيه مصري</div>
                                    </div>
                                    <div class="currency-option {% if settings.currency == 'SAR' %}selected{% endif %}" onclick="selectCurrency('SAR')">
                                        <div class="currency-symbol">ر.س</div>
                                        <div class="currency-name">ريال سعودي</div>
                                    </div>
                                    <div class="currency-option {% if settings.currency == 'AED' %}selected{% endif %}" onclick="selectCurrency('AED')">
                                        <div class="currency-symbol">د.إ</div>
                                        <div class="currency-name">درهم إماراتي</div>
                                    </div>
                                    <div class="currency-option {% if settings.currency == 'USD' %}selected{% endif %}" onclick="selectCurrency('USD')">
                                        <div class="currency-symbol">$</div>
                                        <div class="currency-name">دولار أمريكي</div>
                                    </div>
                                </div>
                                <input type="hidden" id="currency" name="currency" value="{{ settings.currency|default:'EGP' }}">
                            </div>
                        </div>

                        <div class="settings-section">
                            <h3 class="section-title">
                                <i class="bi bi-receipt"></i>
                                إعدادات الفواتير
                            </h3>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="invoice_prefix" class="form-label">بادئة رقم الفاتورة</label>
                                        <input type="text" class="form-control" id="invoice_prefix" name="invoice_prefix" 
                                               value="{{ settings.invoice_prefix|default:'INV' }}">
                                        <div class="help-text">مثال: INV-2024-001</div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tax_rate" class="form-label">معدل الضريبة الافتراضي (%)</label>
                                        <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                               step="0.01" min="0" max="100" value="{{ settings.tax_rate|default:14 }}">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="auto_invoice_number" name="auto_invoice_number" 
                                               {% if settings.auto_invoice_number %}checked{% endif %}>
                                        <label class="form-check-label" for="auto_invoice_number">
                                            ترقيم تلقائي للفواتير
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="require_customer" name="require_customer" 
                                               {% if settings.require_customer %}checked{% endif %}>
                                        <label class="form-check-label" for="require_customer">
                                            إجبارية اختيار العميل
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- بيانات الشركة -->
                    <div class="tab-pane fade" id="company" role="tabpanel">
                        <div class="settings-section">
                            <h3 class="section-title">
                                <i class="bi bi-building"></i>
                                معلومات الشركة
                            </h3>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="company_name" class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" 
                                               value="{{ settings.company_name|default:'شركة أوساريك للتجارة' }}">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="company_phone" class="form-label">هاتف الشركة</label>
                                        <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                               value="{{ settings.company_phone }}">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="company_email" class="form-label">بريد الشركة الإلكتروني</label>
                                        <input type="email" class="form-control" id="company_email" name="company_email" 
                                               value="{{ settings.company_email }}">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                               value="{{ settings.tax_number }}">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="company_address" class="form-label">عنوان الشركة</label>
                                <textarea class="form-control" id="company_address" name="company_address" rows="3">{{ settings.company_address }}</textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">شعار الشركة</label>
                                <div class="logo-upload" onclick="document.getElementById('logo').click()">
                                    {% if settings.company_logo %}
                                        <img src="{{ settings.company_logo.url }}" alt="شعار الشركة" class="logo-preview">
                                    {% else %}
                                        <i class="bi bi-cloud-upload" style="font-size: 3rem; color: #667eea; margin-bottom: 15px;"></i>
                                    {% endif %}
                                    <p class="mb-0">انقر لرفع شعار الشركة</p>
                                    <small class="text-muted">PNG, JPG أو SVG (الحد الأقصى: 2MB)</small>
                                </div>
                                <input type="file" id="logo" name="company_logo" class="d-none" accept="image/*">
                            </div>
                        </div>
                    </div>

                    <!-- الميزات -->
                    <div class="tab-pane fade" id="features" role="tabpanel">
                        <div class="alert alert-custom alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            يمكنك تفعيل أو إلغاء تفعيل الميزات حسب احتياجات عملك
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-header">
                                <h5 class="feature-title">إدارة المخزون</h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input feature-toggle" type="checkbox" id="inventory_management" 
                                           name="inventory_management" {% if settings.inventory_management %}checked{% endif %}>
                                </div>
                            </div>
                            <p class="feature-description">تتبع المخزون وإدارة الكميات والتنبيهات</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-header">
                                <h5 class="feature-title">نظام العملاء الآجل</h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input feature-toggle" type="checkbox" id="credit_system" 
                                           name="credit_system" {% if settings.credit_system %}checked{% endif %}>
                                </div>
                            </div>
                            <p class="feature-description">إدارة حدود الائتمان وأرصدة العملاء</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-header">
                                <h5 class="feature-title">التقارير المتقدمة</h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input feature-toggle" type="checkbox" id="advanced_reports" 
                                           name="advanced_reports" {% if settings.advanced_reports %}checked{% endif %}>
                                </div>
                            </div>
                            <p class="feature-description">تقارير مالية ومبيعات وتحليلات متقدمة</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-header">
                                <h5 class="feature-title">إشعارات البريد الإلكتروني</h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input feature-toggle" type="checkbox" id="email_notifications" 
                                           name="email_notifications" {% if settings.email_notifications %}checked{% endif %}>
                                </div>
                            </div>
                            <p class="feature-description">إرسال الفواتير والتذكيرات عبر البريد الإلكتروني</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-header">
                                <h5 class="feature-title">نظام المناديب</h5>
                                <div class="form-check form-switch">
                                    <input class="form-check-input feature-toggle" type="checkbox" id="sales_representatives" 
                                           name="sales_representatives" {% if settings.sales_representatives %}checked{% endif %}>
                                </div>
                            </div>
                            <p class="feature-description">إدارة المناديب وتتبع أدائهم</p>
                        </div>
                    </div>

                    <!-- النسخ الاحتياطي -->
                    <div class="tab-pane fade" id="backup" role="tabpanel">
                        <div class="backup-section">
                            <h3 class="backup-title">
                                <i class="bi bi-shield-check"></i>
                                النسخ الاحتياطي
                            </h3>
                            
                            <div class="backup-info">
                                <h6>آخر نسخة احتياطية:</h6>
                                <p class="mb-2">{{ last_backup_date|default:"لم يتم إنشاء نسخة احتياطية بعد" }}</p>
                                <small>حجم النسخة الاحتياطية: {{ backup_size|default:"غير متوفر" }}</small>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-backup w-100 mb-3" onclick="createBackup()">
                                        <i class="bi bi-download"></i>
                                        إنشاء نسخة احتياطية
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-backup w-100 mb-3" onclick="restoreBackup()">
                                        <i class="bi bi-upload"></i>
                                        استعادة نسخة احتياطية
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-custom alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> تأكد من إنشاء نسخ احتياطية دورية لحماية بياناتك
                        </div>
                        
                        <div class="settings-section">
                            <h3 class="section-title">
                                <i class="bi bi-clock"></i>
                                النسخ الاحتياطي التلقائي
                            </h3>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="auto_backup" name="auto_backup" 
                                               {% if settings.auto_backup %}checked{% endif %}>
                                        <label class="form-check-label" for="auto_backup">
                                            تفعيل النسخ الاحتياطي التلقائي
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="backup_frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                        <select class="form-select" id="backup_frequency" name="backup_frequency">
                                            <option value="daily" {% if settings.backup_frequency == 'daily' %}selected{% endif %}>يومي</option>
                                            <option value="weekly" {% if settings.backup_frequency == 'weekly' %}selected{% endif %}>أسبوعي</option>
                                            <option value="monthly" {% if settings.backup_frequency == 'monthly' %}selected{% endif %}>شهري</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-save me-3">
                        <i class="bi bi-check-circle"></i>
                        حفظ الإعدادات
                    </button>
                    <button type="button" class="btn btn-reset" onclick="resetSettings()">
                        <i class="bi bi-arrow-clockwise"></i>
                        إعادة تعيين
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للإعدادات
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.settings-section, .feature-card');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(30px)';
                    section.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });

        function selectCurrency(currency) {
            // إزالة التحديد من جميع العملات
            document.querySelectorAll('.currency-option').forEach(option => {
                option.classList.remove('selected');
            });
            
            // تحديد العملة المختارة
            event.target.closest('.currency-option').classList.add('selected');
            
            // تحديث القيمة المخفية
            document.getElementById('currency').value = currency;
        }

        function createBackup() {
            if (confirm('هل تريد إنشاء نسخة احتياطية من البيانات؟')) {
                // إرسال طلب إنشاء نسخة احتياطية
                fetch('/api/backup/create/', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم إنشاء النسخة الاحتياطية بنجاح');
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء إنشاء النسخة الاحتياطية');
                    }
                });
            }
        }

        function restoreBackup() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.sql,.zip';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file && confirm('هل تريد استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                    const formData = new FormData();
                    formData.append('backup_file', file);
                    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
                    
                    fetch('/api/backup/restore/', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('تم استعادة النسخة الاحتياطية بنجاح');
                            location.reload();
                        } else {
                            alert('حدث خطأ أثناء استعادة النسخة الاحتياطية');
                        }
                    });
                }
            };
            input.click();
        }

        function resetSettings() {
            if (confirm('هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                location.reload();
            }
        }
    </script>
</body>
</html>
