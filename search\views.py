from django.shortcuts import render
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_GET
from django.core.paginator import Paginator
from django.db.models import Q, Avg
from .search_engine import search_engine
from .models import SearchHistory, PopularSearch
import json


@require_GET
def search_api(request):
    """API البحث السريع"""
    query = request.GET.get('q', '').strip()
    limit = int(request.GET.get('limit', 10))
    
    if not query:
        return JsonResponse({
            'results': [],
            'suggestions': search_engine._get_search_suggestions(''),
            'total_count': 0
        })
    
    # تنفيذ البحث
    search_results = search_engine.search(
        query=query,
        user=request.user if request.user.is_authenticated else None,
        limit=limit
    )
    
    return JsonResponse({
        'results': search_results['results'],
        'suggestions': search_results['suggestions'],
        'total_count': search_results['total_count'],
        'execution_time': round(search_results['execution_time'], 3),
        'categories': search_results['categories']
    })


@require_GET
def search_suggestions(request):
    """API اقتراحات البحث"""
    query = request.GET.get('q', '').strip()
    
    suggestions = search_engine._get_search_suggestions(query)
    
    return JsonResponse({
        'suggestions': suggestions
    })


@login_required
def search_page(request):
    """صفحة البحث الكاملة"""
    query = request.GET.get('q', '').strip()
    category = request.GET.get('category', '')
    page_number = request.GET.get('page', 1)
    
    results = []
    total_count = 0
    execution_time = 0
    categories = {}
    
    if query:
        # تنفيذ البحث
        search_results = search_engine.search(
            query=query,
            user=request.user,
            limit=100
        )
        
        results = search_results['results']
        total_count = search_results['total_count']
        execution_time = search_results['execution_time']
        categories = search_results['categories']
        
        # فلترة حسب الفئة
        if category and category in categories:
            results = categories[category]
        
        # التصفح
        paginator = Paginator(results, 20)
        results = paginator.get_page(page_number)
    
    # البحثات الشائعة
    popular_searches = PopularSearch.objects.all()[:10]
    
    # تاريخ البحث للمستخدم
    recent_searches = SearchHistory.objects.filter(
        user=request.user
    ).order_by('-created_at')[:10]
    
    context = {
        'query': query,
        'results': results,
        'total_count': total_count,
        'execution_time': round(execution_time, 3),
        'categories': categories,
        'current_category': category,
        'popular_searches': popular_searches,
        'recent_searches': recent_searches,
    }
    
    return render(request, 'search/search_simple.html', context)


@require_GET
def search_history(request):
    """تاريخ البحث للمستخدم"""
    if not request.user.is_authenticated:
        return JsonResponse({
            'history': []
        })

    try:
        history = SearchHistory.objects.filter(
            user=request.user
        ).order_by('-created_at')[:20]

        history_data = []
        for item in history:
            history_data.append({
                'query': item.query,
                'results_count': item.results_count,
                'created_at': item.created_at.strftime('%Y-%m-%d %H:%M'),
                'execution_time': item.execution_time
            })

        return JsonResponse({
            'history': history_data
        })
    except Exception as e:
        return JsonResponse({
            'history': [],
            'error': str(e)
        })


@login_required
def clear_search_history(request):
    """مسح تاريخ البحث"""
    if request.method == 'POST':
        SearchHistory.objects.filter(user=request.user).delete()
        return JsonResponse({
            'success': True,
            'message': 'تم مسح تاريخ البحث بنجاح'
        })
    
    return JsonResponse({
        'success': False,
        'error': 'طريقة غير مسموحة'
    })


@require_GET
def popular_searches(request):
    """البحثات الشائعة"""
    popular = PopularSearch.objects.all()[:15]
    
    popular_data = []
    for item in popular:
        popular_data.append({
            'query': item.query,
            'count': item.search_count,
            'last_searched': item.last_searched.strftime('%Y-%m-%d')
        })
    
    return JsonResponse({
        'popular_searches': popular_data
    })


def advanced_search_api(request):
    """API البحث المتقدم"""
    query = request.GET.get('q', '').strip()
    models = request.GET.getlist('models[]')  # النماذج المحددة للبحث
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if not query:
        return JsonResponse({
            'results': [],
            'total_count': 0
        })
    
    # تخصيص البحث حسب النماذج المحددة
    if models:
        # تصفية النماذج
        filtered_models = {}
        for model, config in search_engine.search_models.items():
            if model.__name__ in models:
                filtered_models[model] = config
        
        # تعديل مؤقت لمحرك البحث
        original_models = search_engine.search_models
        search_engine.search_models = filtered_models
        
        # تنفيذ البحث
        search_results = search_engine.search(
            query=query,
            user=request.user if request.user.is_authenticated else None,
            limit=50
        )
        
        # استعادة النماذج الأصلية
        search_engine.search_models = original_models
    else:
        search_results = search_engine.search(
            query=query,
            user=request.user if request.user.is_authenticated else None,
            limit=50
        )
    
    return JsonResponse({
        'results': search_results['results'],
        'total_count': search_results['total_count'],
        'execution_time': round(search_results['execution_time'], 3),
        'categories': search_results['categories']
    })


def search_analytics(request):
    """تحليلات البحث"""
    if not request.user.is_staff:
        return JsonResponse({'error': 'غير مسموح'}, status=403)
    
    # إحصائيات عامة
    total_searches = SearchHistory.objects.count()
    unique_queries = SearchHistory.objects.values('query').distinct().count()
    avg_execution_time = SearchHistory.objects.aggregate(
        avg_time=Avg('execution_time')
    )['avg_time'] or 0
    
    # أكثر البحثات شيوعاً
    top_searches = PopularSearch.objects.order_by('-search_count')[:10]
    
    # البحثات الأخيرة
    recent_searches = SearchHistory.objects.order_by('-created_at')[:20]
    
    analytics_data = {
        'total_searches': total_searches,
        'unique_queries': unique_queries,
        'avg_execution_time': round(avg_execution_time, 3),
        'top_searches': [
            {
                'query': item.query,
                'count': item.search_count
            } for item in top_searches
        ],
        'recent_searches': [
            {
                'query': item.query,
                'user': item.user.username,
                'results_count': item.results_count,
                'created_at': item.created_at.strftime('%Y-%m-%d %H:%M')
            } for item in recent_searches
        ]
    }
    
    return JsonResponse(analytics_data)
