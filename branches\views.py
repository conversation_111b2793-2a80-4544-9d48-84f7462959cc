from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q, Avg
from django.http import JsonResponse
from django.core.paginator import Paginator
from datetime import datetime, timedelta
from .models import Branch, BranchEmployee, BranchAsset
from .forms import BranchForm, BranchEmployeeForm, BranchAssetForm, BranchSearchForm, EmployeeSearchForm

@login_required
def branches_dashboard(request):
    """لوحة تحكم الفروع"""
    # إحصائيات عامة
    total_branches = Branch.objects.filter(is_active=True).count()
    headquarters_count = Branch.objects.filter(branch_type='headquarters', is_active=True).count()
    branches_count = Branch.objects.filter(branch_type='branch', is_active=True).count()
    warehouses_count = Branch.objects.filter(branch_type='warehouse', is_active=True).count()
    offices_count = Branch.objects.filter(branch_type='office', is_active=True).count()

    # إحصائيات الموظفين
    total_employees = BranchEmployee.objects.filter(is_active=True).count()
    total_managers = BranchEmployee.objects.filter(position='manager', is_active=True).count()

    # إحصائيات الأصول
    total_assets = BranchAsset.objects.filter(is_active=True).count()
    total_assets_value = BranchAsset.objects.filter(is_active=True).aggregate(
        total=Sum('current_value'))['total'] or 0

    # الفروع حسب الحالة
    branches_by_status = Branch.objects.values('status').annotate(count=Count('id'))

    # الفروع حسب النوع
    branches_by_type = Branch.objects.values('branch_type').annotate(count=Count('id'))

    # أحدث الفروع
    recent_branches = Branch.objects.order_by('-created_at')[:5]

    # الفروع حسب المدن
    branches_by_city = Branch.objects.values('city').annotate(count=Count('id')).order_by('-count')[:10]

    context = {
        'total_branches': total_branches,
        'headquarters_count': headquarters_count,
        'branches_count': branches_count,
        'warehouses_count': warehouses_count,
        'offices_count': offices_count,
        'total_employees': total_employees,
        'total_managers': total_managers,
        'total_assets': total_assets,
        'total_assets_value': total_assets_value,
        'branches_by_status': branches_by_status,
        'branches_by_type': branches_by_type,
        'recent_branches': recent_branches,
        'branches_by_city': branches_by_city,
    }
    return render(request, 'branches/dashboard.html', context)

# ========== إدارة الفروع ==========
@login_required
def branch_list(request):
    """قائمة الفروع"""
    form = BranchSearchForm(request.GET)
    branches = Branch.objects.select_related('manager', 'parent_branch')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        branch_type = form.cleaned_data.get('branch_type')
        status = form.cleaned_data.get('status')
        city = form.cleaned_data.get('city')
        manager = form.cleaned_data.get('manager')

        if search:
            branches = branches.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search) |
                Q(city__icontains=search)
            )

        if branch_type:
            branches = branches.filter(branch_type=branch_type)

        if status:
            branches = branches.filter(status=status)

        if city:
            branches = branches.filter(city__icontains=city)

        if manager:
            branches = branches.filter(manager=manager)

    paginator = Paginator(branches, 20)
    page_number = request.GET.get('page')
    branches = paginator.get_page(page_number)

    context = {
        'branches': branches,
        'form': form,
    }
    return render(request, 'branches/branch_list.html', context)

@login_required
def branch_create(request):
    """إضافة فرع جديد"""
    if request.method == 'POST':
        form = BranchForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة الفرع بنجاح')
            return redirect('branches:branch_list')
    else:
        form = BranchForm()

    context = {'form': form, 'title': 'إضافة فرع جديد'}
    return render(request, 'branches/branch_form.html', context)

@login_required
def branch_detail(request, pk):
    """تفاصيل الفرع"""
    branch = get_object_or_404(Branch, pk=pk)
    employees = branch.employees.select_related('user').filter(is_active=True)
    assets = branch.assets.filter(is_active=True)

    context = {
        'branch': branch,
        'employees': employees,
        'assets': assets,
    }
    return render(request, 'branches/branch_detail.html', context)

@login_required
def branch_edit(request, pk):
    """تعديل فرع"""
    branch = get_object_or_404(Branch, pk=pk)
    if request.method == 'POST':
        form = BranchForm(request.POST, instance=branch)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات الفرع بنجاح')
            return redirect('branches:branch_detail', pk=branch.pk)
    else:
        form = BranchForm(instance=branch)

    context = {'form': form, 'title': 'تعديل الفرع', 'branch': branch}
    return render(request, 'branches/branch_form.html', context)

@login_required
def branch_delete(request, pk):
    """حذف فرع"""
    branch = get_object_or_404(Branch, pk=pk)
    if request.method == 'POST':
        branch.delete()
        messages.success(request, 'تم حذف الفرع بنجاح')
        return redirect('branches:branch_list')

    context = {'branch': branch}
    return render(request, 'branches/branch_confirm_delete.html', context)
