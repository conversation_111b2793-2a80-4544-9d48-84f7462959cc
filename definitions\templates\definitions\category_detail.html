{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل فئة الأصناف - {{ category.name }}{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Category Detail Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        --warning-gradient: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
        --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --glass-bg: rgba(255, 255, 255, 0.15);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
    }

    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #56ab2f 50%, #a8e6cf 75%, #4facfe 100%);
        background-attachment: fixed;
        background-size: 400% 400%;
        animation: gradientShift 20s ease infinite;
        min-height: 100vh;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Hero Section */
    .category-hero {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 800;
        color: white;
        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        margin-bottom: 0.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        color: rgba(255,255,255,0.9);
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    /* Info Cards */
    .info-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
        color: white;
    }

    .info-card h3 {
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        margin-bottom: 1rem;
    }

    /* Enhanced Buttons */
    .btn-primary {
        background: var(--primary-gradient);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 15px;
        font-weight: 600;
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
    }

    .btn-outline-light {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 15px;
        font-weight: 600;
        backdrop-filter: blur(8px);
        transition: all 0.3s ease;
    }

    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
        color: white;
    }

    /* Enhanced Badges */
    .badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .badge-success {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(86, 171, 47, 0.3);
    }

    .badge-danger {
        background: var(--danger-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(255, 65, 108, 0.3);
    }

    .badge-info {
        background: var(--info-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
    }

    .badge-warning {
        background: var(--warning-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(247, 151, 30, 0.3);
    }

    /* Stats Cards */
    .stat-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(31, 38, 135, 0.4);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .stat-label {
        color: rgba(255,255,255,0.9);
        font-weight: 600;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid" style="margin-top: 2rem;">
    <!-- Hero Section -->
    <div class="category-hero">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="hero-title">
                    <i class="bi bi-tag-fill me-3"></i>
                    {{ category.name }}
                </h1>
                <p class="hero-subtitle">
                    كود الفئة: <strong>{{ category.code }}</strong>
                    {% if category.parent %}
                    | فئة فرعية من: <strong>{{ category.parent.name }}</strong>
                    {% else %}
                    | فئة رئيسية
                    {% endif %}
                </p>
            </div>
            <div class="d-flex gap-3">
                <a href="{% url 'definitions:category_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>عودة للقائمة
                </a>
                <a href="{% url 'definitions:category_edit' category.id %}" class="btn btn-primary">
                    <i class="bi bi-pencil me-2"></i>تعديل الفئة
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-number">{{ category.children.count }}</div>
                <div class="stat-label">الفئات الفرعية</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-number">{{ category.products.count }}</div>
                <div class="stat-label">الأصناف</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-number">
                    {% if category.is_active %}
                    <span class="badge badge-success">نشط</span>
                    {% else %}
                    <span class="badge badge-danger">غير نشط</span>
                    {% endif %}
                </div>
                <div class="stat-label">الحالة</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card">
                <div class="stat-number">{{ category.created_at|date:"d/m/Y" }}</div>
                <div class="stat-label">تاريخ الإنشاء</div>
            </div>
        </div>
    </div>

    <!-- Category Information -->
    <div class="row">
        <div class="col-lg-8">
            <div class="info-card">
                <h3><i class="bi bi-info-circle me-2"></i>معلومات الفئة</h3>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>الكود:</strong> {{ category.code }}</p>
                        <p><strong>الاسم:</strong> {{ category.name }}</p>
                        {% if category.name_en %}
                        <p><strong>الاسم بالإنجليزية:</strong> {{ category.name_en }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <p><strong>الفئة الأب:</strong> 
                            {% if category.parent %}
                            <a href="{% url 'definitions:category_detail' category.parent.id %}" class="text-info">{{ category.parent.name }}</a>
                            {% else %}
                            <span class="badge badge-info">فئة رئيسية</span>
                            {% endif %}
                        </p>
                        <p><strong>تاريخ الإنشاء:</strong> {{ category.created_at|date:"d/m/Y H:i" }}</p>
                        <p><strong>آخر تحديث:</strong> {{ category.updated_at|date:"d/m/Y H:i" }}</p>
                    </div>
                </div>
                {% if category.description %}
                <div class="mt-3">
                    <p><strong>الوصف:</strong></p>
                    <p>{{ category.description }}</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="info-card">
                <h3><i class="bi bi-gear me-2"></i>إجراءات سريعة</h3>
                <div class="d-grid gap-2">
                    <a href="{% url 'definitions:category_edit' category.id %}" class="btn btn-primary">
                        <i class="bi bi-pencil me-2"></i>تعديل الفئة
                    </a>
                    <a href="{% url 'definitions:category_create' %}?parent={{ category.id }}" class="btn btn-outline-light">
                        <i class="bi bi-plus-circle me-2"></i>إضافة فئة فرعية
                    </a>
                    <a href="{% url 'definitions:product_create' %}?category={{ category.id }}" class="btn btn-outline-light">
                        <i class="bi bi-box-seam me-2"></i>إضافة صنف جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
