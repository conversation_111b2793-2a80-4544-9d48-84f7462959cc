{% extends 'base.html' %}

{% block title %}حذف البنك - {{ bank.name }}{% endblock %}

{% block extra_css %}
<style>
    .delete-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .delete-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
        border: 2px solid #dc3545;
    }

    .warning-section {
        background: #f8d7da;
        color: #721c24;
        padding: 2rem;
        text-align: center;
        border-bottom: 1px solid #f5c6cb;
    }

    .bank-info-section {
        padding: 2rem;
        background: #fff;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
    }

    .info-value {
        color: #212529;
    }

    .action-buttons {
        padding: 2rem;
        background: #f8f9fa;
        display: flex;
        gap: 1rem;
        justify-content: center;
    }

    .btn-danger-confirm {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
        font-weight: 600;
    }

    .btn-danger-confirm:hover {
        background: #c82333;
        border-color: #bd2130;
        color: white;
    }

    .btn-alternative {
        background: #17a2b8;
        border-color: #17a2b8;
        color: white;
    }

    .btn-alternative:hover {
        background: #138496;
        border-color: #117a8b;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Delete Header -->
<div class="delete-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    تأكيد حذف البنك
                </h1>
                <p class="mb-0 opacity-75">هذا الإجراء لا يمكن التراجع عنه</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:bank_detail' bank.id %}" class="btn btn-light">
                    <i class="bi bi-arrow-left me-2"></i>عودة للتفاصيل
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="delete-card">
                <!-- Warning Section -->
                <div class="warning-section">
                    <i class="bi bi-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                    <h4 class="mb-3">تحذير: حذف البنك</h4>
                    <p class="mb-0">
                        أنت على وشك حذف البنك نهائياً من النظام. 
                        هذا الإجراء لا يمكن التراجع عنه وسيؤثر على جميع البيانات المرتبطة.
                    </p>
                </div>

                <!-- Bank Information -->
                <div class="bank-info-section">
                    <h5 class="mb-3">
                        <i class="bi bi-bank me-2"></i>بيانات البنك المراد حذفه:
                    </h5>
                    
                    <div class="info-item">
                        <span class="info-label">كود البنك:</span>
                        <span class="info-value"><strong>{{ bank.code }}</strong></span>
                    </div>

                    <div class="info-item">
                        <span class="info-label">اسم البنك:</span>
                        <span class="info-value"><strong>{{ bank.name }}</strong></span>
                    </div>

                    {% if bank.name_en %}
                    <div class="info-item">
                        <span class="info-label">الاسم بالإنجليزية:</span>
                        <span class="info-value">{{ bank.name_en }}</span>
                    </div>
                    {% endif %}

                    {% if bank.swift_code %}
                    <div class="info-item">
                        <span class="info-label">SWIFT Code:</span>
                        <span class="info-value"><code>{{ bank.swift_code }}</code></span>
                    </div>
                    {% endif %}

                    <div class="info-item">
                        <span class="info-label">تاريخ الإنشاء:</span>
                        <span class="info-value">{{ bank.created_at|date:"d/m/Y H:i" }}</span>
                    </div>

                    <div class="info-item">
                        <span class="info-label">الحالة:</span>
                        <span class="info-value">
                            {% if bank.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </span>
                    </div>
                </div>

                <!-- Consequences Warning -->
                <div class="warning-section">
                    <h6 class="mb-2">
                        <i class="bi bi-info-circle me-2"></i>تأثير الحذف:
                    </h6>
                    <ul class="text-start mb-0">
                        <li>سيتم حذف جميع الحسابات المصرفية المرتبطة بهذا البنك</li>
                        <li>ستفقد جميع المعاملات المالية المرتبطة بحسابات هذا البنك</li>
                        <li>لن تتمكن من استرداد هذه البيانات بعد الحذف</li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="{% url 'definitions:bank_detail' bank.id %}" class="btn btn-secondary btn-lg">
                        <i class="bi bi-x-circle me-2"></i>إلغاء
                    </a>
                    <a href="{% url 'definitions:bank_edit' bank.id %}" class="btn btn-alternative btn-lg">
                        <i class="bi bi-pencil me-2"></i>تعديل بدلاً من الحذف
                    </a>
                    <form method="post" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" 
                                class="btn btn-danger-confirm btn-lg"
                                onclick="return confirm('هل أنت متأكد تماماً من حذف البنك {{ bank.name }}؟\n\nهذا الإجراء لا يمكن التراجع عنه!')">
                            <i class="bi bi-trash me-2"></i>تأكيد الحذف
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
