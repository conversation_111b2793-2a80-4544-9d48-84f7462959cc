from django.core.management.base import BaseCommand
from django.db import models
from manufacturing.models import ManufacturingOrder, ManufacturingInventoryTransaction
from warehouses.models import InventoryItem
from decimal import Decimal

class Command(BaseCommand):
    help = 'إصلاح تكاليف أوامر التصنيع المكتملة'

    def handle(self, *args, **options):
        self.stdout.write('بدء إصلاح تكاليف أوامر التصنيع...')
        
        # البحث عن أوامر التصنيع المكتملة
        completed_orders = ManufacturingOrder.objects.filter(status='completed')
        
        self.stdout.write(f'تم العثور على {completed_orders.count()} أمر تصنيع مكتمل')
        
        updated_orders = 0
        updated_inventory = 0
        
        for order in completed_orders:
            order_updated = False
            
            # نسخ التكاليف المقدرة إلى الفعلية إذا كانت الفعلية صفر
            if order.actual_labor_cost == 0 and order.estimated_labor_cost > 0:
                order.actual_labor_cost = order.estimated_labor_cost
                order_updated = True
                
            if order.actual_overhead_cost == 0 and order.estimated_overhead_cost > 0:
                order.actual_overhead_cost = order.estimated_overhead_cost
                order_updated = True
            
            # إذا لم تكن تكلفة المواد الخام محسوبة، احسبها من المواد الخام
            if order.actual_raw_material_cost == 0:
                raw_material_cost = order.raw_materials.aggregate(
                    total=models.Sum('total_cost')
                )['total'] or Decimal('0')
                order.actual_raw_material_cost = raw_material_cost
                order_updated = True
            
            if order_updated:
                order.save()
                updated_orders += 1
                
                self.stdout.write(f"تم تحديث أمر التصنيع: {order.order_number}")
                self.stdout.write(f"  التكلفة الفعلية الجديدة: {order.total_actual_cost}")
                
                # تحديث حركات المخزون المرتبطة
                production_transactions = ManufacturingInventoryTransaction.objects.filter(
                    manufacturing_order=order,
                    transaction_type='finished_goods_production'
                )
                
                for trans in production_transactions:
                    if order.total_actual_cost > 0 and order.quantity_to_produce > 0:
                        new_unit_cost = order.total_actual_cost / order.quantity_to_produce
                        trans.unit_cost = new_unit_cost
                        trans.total_cost = trans.quantity * new_unit_cost
                        trans.save()
                        
                        self.stdout.write(f"  تم تحديث حركة المخزون - تكلفة الوحدة: {new_unit_cost}")
                        
                        # تحديث المخزون
                        try:
                            inventory_item = InventoryItem.objects.get(
                                warehouse=trans.warehouse,
                                product=trans.product
                            )
                            
                            # إعادة حساب متوسط التكلفة
                            all_production_transactions = ManufacturingInventoryTransaction.objects.filter(
                                product=trans.product,
                                warehouse=trans.warehouse,
                                transaction_type='finished_goods_production'
                            )
                            
                            total_quantity = Decimal('0')
                            total_cost = Decimal('0')
                            
                            for t in all_production_transactions:
                                total_quantity += t.quantity
                                total_cost += t.total_cost or Decimal('0')
                            
                            if total_quantity > 0:
                                inventory_item.average_cost = total_cost / total_quantity
                                inventory_item.last_cost = new_unit_cost
                                inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
                                inventory_item.save()
                                
                                updated_inventory += 1
                                self.stdout.write(f"  تم تحديث المخزون - إجمالي القيمة: {inventory_item.total_value}")
                        
                        except InventoryItem.DoesNotExist:
                            self.stdout.write(f"  تحذير: لم يتم العثور على عنصر مخزون")
                
                self.stdout.write("-" * 50)
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إصلاح {updated_orders} أمر تصنيع و {updated_inventory} عنصر مخزون!')
        )
