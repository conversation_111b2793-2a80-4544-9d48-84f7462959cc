{% extends 'base.html' %}
{% load static %}

{% block title %}{% if user_language == 'en' %}Warehouse Definition{% else %}تعريف المخازن{% endif %}{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-bottom: 3px solid #007bff;
    }

    .page-title {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        color: #6c757d;
        margin-bottom: 0;
    }

    /* تنسيق أزرار الإدارة */
    .btn-group .btn {
        border-radius: 0;
        border-right: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .btn-group .btn:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    .btn-group .btn:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
        border-right: none;
    }

    .btn-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 2;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
        border: none;
    }

    .shadow-sm {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
        border-left: 4px solid #007bff;
        transition: transform 0.2s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #007bff;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #495057;
        font-size: 0.95rem;
        font-weight: 500;
    }

    .search-filters {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        padding: 2.5rem;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
        border: 1px solid #e9ecef;
        border-top: 4px solid #28a745;
        position: static !important;
        float: none !important;
        width: 100%;
        transition: all 0.3s ease;
    }

    .search-filters:hover {
        box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }

    .search-filters .form-control,
    .search-filters .form-select {
        height: 50px;
        font-size: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 16px;
        transition: all 0.3s ease;
        background: white;
    }

    .search-filters .form-control:focus,
    .search-filters .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        transform: translateY(-1px);
    }

    .search-filters .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        font-size: 0.95rem;
    }

    .search-filters .btn {
        height: 50px;
        font-weight: 600;
        border-radius: 8px;
        padding: 12px 20px;
        transition: all 0.3s ease;
        border: none;
        font-size: 0.95rem;
    }

    .search-filters .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .search-filters .btn-success:hover {
        background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        transform: translateY(-2px);
    }

    .search-filters .btn-outline-secondary {
        background: white;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .search-filters .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .search-filters .input-group-text {
        border: 2px solid #e9ecef;
        border-right: none;
        background: #f8f9fa;
        height: 50px;
        border-radius: 8px 0 0 8px;
    }

    .search-filters .input-group .form-control {
        border-left: none;
        border-radius: 0 8px 8px 0;
    }

    .search-filters .input-group:focus-within .input-group-text {
        border-color: #28a745;
        background: rgba(40, 167, 69, 0.1);
    }

    .search-filters .form-label i {
        color: #28a745;
        margin-right: 5px;
    }

    .search-title {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1.25rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #e9ecef;
    }

    .search-title i {
        color: #28a745;
        font-size: 1.3rem;
    }

    .warehouse-table {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
        position: relative;
        z-index: 1;
        clear: both;
    }

    .table {
        margin-bottom: 0;
        font-size: 0.9rem;
    }

    .table thead th {
        background: #f8f9fa;
        color: #495057;
        border: none;
        padding: 1rem 0.75rem;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        border-bottom: 2px solid #dee2e6;
    }

    .table tbody td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #eee;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    .warehouse-code {
        font-weight: 600;
        color: #495057;
        font-family: 'Courier New', monospace;
        background: #e9ecef;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.85rem;
    }

    .warehouse-name {
        font-weight: 600;
        color: #212529;
        margin-bottom: 0.25rem;
    }

    .warehouse-type-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 500;
        background: #e3f2fd;
        color: #1565c0;
        display: inline-block;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-block;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .btn-action {
        padding: 0.4rem 0.8rem;
        border-radius: 6px;
        border: none;
        font-size: 0.8rem;
        font-weight: 500;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        margin: 0 0.1rem;
    }

    .btn-view {
        background: #e3f2fd;
        color: #1976d2;
    }

    .btn-edit {
        background: #fff3e0;
        color: #f57c00;
    }

    .btn-delete {
        background: #ffebee;
        color: #d32f2f;
    }

    .btn-action:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #dee2e6;
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    /* ضمان الترتيب الصحيح للعناصر - قوي جداً */
    .main-content {
        display: flex !important;
        flex-direction: column !important;
    }

    .stats-section {
        order: 1 !important;
        margin-bottom: 2rem !important;
    }

    .search-section {
        order: 2 !important;
        margin-bottom: 1rem !important;
    }

    .table-section {
        order: 3 !important;
        margin-bottom: 2rem !important;
    }

    .pagination-section {
        order: 4 !important;
        margin-top: 2rem !important;
    }

    /* إزالة أي تأثيرات أخرى */
    .search-filters {
        position: static !important;
        float: none !important;
        transform: none !important;
        top: auto !important;
        left: auto !important;
        right: auto !important;
        bottom: auto !important;
    }

</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-building me-2"></i>
                    {% if user_language == 'en' %}Warehouse Definition{% else %}تعريف المخازن{% endif %}
                </h1>
                <p class="page-subtitle">{% if user_language == 'en' %}View and manage all warehouses and storage facilities{% else %}عرض وإدارة جميع المخازن والمستودعات{% endif %}</p>
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group shadow-sm me-2">
                    <a href="{% url 'warehouses:dashboard' %}" class="btn btn-success">
                        <i class="bi bi-boxes me-1"></i>
                        {% if user_language == 'en' %}Warehouse Management{% else %}إدارة المخازن{% endif %}
                    </a>
                    <a href="{% url 'definitions:warehouse_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>
                        {% if user_language == 'en' %}Add New{% else %}إضافة جديد{% endif %}
                    </a>
                </div>
                <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-right me-1"></i>
                    {% if user_language == 'en' %}Back to Definitions{% else %}عودة للتعريفات{% endif %}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Container -->
<div class="container-fluid main-content">

    <!-- SECTION 1: Statistics Cards -->
    <div class="stats-section">
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-number">{{ total_warehouses }}</div>
                <div class="stat-label">{% if user_language == 'en' %}Total Warehouses{% else %}إجمالي المخازن{% endif %}</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ page_obj.paginator.count }}</div>
                <div class="stat-label">{% if user_language == 'en' %}Displayed Warehouses{% else %}المخازن المعروضة{% endif %}</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ page_obj.paginator.num_pages }}</div>
                <div class="stat-label">{% if user_language == 'en' %}Number of Pages{% else %}عدد الصفحات{% endif %}</div>
            </div>
        </div>
    </div>

    <!-- SECTION 2: Search and Filters -->
    <div class="search-section">
        <div class="search-filters">
            <h5 class="search-title">
                <i class="bi bi-search"></i>
                {% if user_language == 'en' %}Search and Filter{% else %}البحث والتصفية{% endif %}
            </h5>
            <form method="get" class="row g-4">
                <div class="col-lg-5 col-md-6">
                    <label class="form-label">
                        <i class="bi bi-search me-1"></i>
                        {% if user_language == 'en' %}Search{% else %}البحث{% endif %}
                    </label>
                    <div class="input-group">
                        <span class="input-group-text bg-light border-end-0">
                            <i class="bi bi-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0" name="search" value="{{ search_query }}"
                               placeholder="{% if user_language == 'en' %}Search by name, code or manager...{% else %}البحث بالاسم أو الكود أو المدير...{% endif %}">
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <label class="form-label">
                        <i class="bi bi-funnel me-1"></i>
                        {% if user_language == 'en' %}Warehouse Type{% else %}نوع المخزن{% endif %}
                    </label>
                    <select class="form-select" name="type">
                        <option value="">{% if user_language == 'en' %}All Types{% else %}جميع الأنواع{% endif %}</option>
                        {% for value, label in warehouse_types %}
                            <option value="{{ value }}" {% if warehouse_type == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-lg-3 col-md-12 d-flex align-items-end gap-2">
                    <button type="submit" class="btn btn-success flex-fill">
                        <i class="bi bi-search me-2"></i>{% if user_language == 'en' %}Search{% else %}بحث{% endif %}
                    </button>
                    <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-outline-secondary flex-fill">
                        <i class="bi bi-arrow-clockwise me-2"></i>{% if user_language == 'en' %}Reset{% else %}إعادة تعيين{% endif %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- SECTION 3: Warehouses Table -->
    <div class="table-section">
        <div class="warehouse-table">
            {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th style="width: 10%;">{% if user_language == 'en' %}Code{% else %}الكود{% endif %}</th>
                        <th style="width: 20%;">{% if user_language == 'en' %}Warehouse Name{% else %}اسم المخزن{% endif %}</th>
                        <th style="width: 15%;">{% if user_language == 'en' %}Type{% else %}النوع{% endif %}</th>
                        <th style="width: 15%;">{% if user_language == 'en' %}Manager{% else %}المدير{% endif %}</th>
                        <th style="width: 10%;">{% if user_language == 'en' %}Phone{% else %}الهاتف{% endif %}</th>
                        <th style="width: 10%;">{% if user_language == 'en' %}Status{% else %}الحالة{% endif %}</th>
                        <th style="width: 10%;">{% if user_language == 'en' %}Created Date{% else %}تاريخ الإنشاء{% endif %}</th>
                        <th style="width: 10%;">{% if user_language == 'en' %}Actions{% else %}الإجراءات{% endif %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for warehouse in page_obj %}
                    <tr>
                        <td>
                            <span class="warehouse-code">{{ warehouse.code }}</span>
                        </td>
                        <td>
                            <div class="warehouse-name">{{ warehouse.name }}</div>
                            {% if warehouse.address %}
                            <small class="text-muted">
                                <i class="bi bi-geo-alt me-1"></i>{{ warehouse.address|truncatechars:25 }}
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="warehouse-type-badge">
                                {{ warehouse.get_warehouse_type_display }}
                            </span>
                        </td>
                        <td>
                            {% if warehouse.manager_name %}
                                <i class="bi bi-person me-1"></i>{{ warehouse.manager_name }}
                            {% else %}
                                <span class="text-muted">{% if user_language == 'en' %}Not specified{% else %}غير محدد{% endif %}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if warehouse.phone %}
                                <a href="tel:{{ warehouse.phone }}" class="text-decoration-none">
                                    <i class="bi bi-telephone me-1"></i>{{ warehouse.phone }}
                                </a>
                            {% else %}
                                <span class="text-muted">{% if user_language == 'en' %}Not specified{% else %}غير محدد{% endif %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="status-badge {% if warehouse.is_active %}status-active{% else %}status-inactive{% endif %}">
                                <i class="bi bi-{% if warehouse.is_active %}check-circle{% else %}x-circle{% endif %} me-1"></i>
                                {% if user_language == 'en' %}{{ warehouse.is_active|yesno:"Active,Inactive" }}{% else %}{{ warehouse.is_active|yesno:"نشط,غير نشط" }}{% endif %}
                            </span>
                        </td>
                        <td>
                            <small>{{ warehouse.created_at|date:"d/m/Y" }}</small>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'definitions:warehouse_detail' warehouse.id %}"
                                   class="btn-action btn-view"
                                   title="{% if user_language == 'en' %}View Details{% else %}عرض التفاصيل{% endif %}">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'definitions:warehouse_edit' warehouse.id %}"
                                   class="btn-action btn-edit"
                                   title="{% if user_language == 'en' %}Edit{% else %}تعديل{% endif %}">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <form method="post" action="{% url 'definitions:warehouse_delete' warehouse.id %}" style="display: inline;" class="delete-form">
                                    {% csrf_token %}
                                    <button type="button"
                                            class="btn-action btn-delete delete-warehouse-btn"
                                            title="{% if user_language == 'en' %}Delete{% else %}حذف{% endif %}"
                                            data-warehouse-name="{{ warehouse.name }}"
                                            data-warehouse-id="{{ warehouse.id }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-state">
            <i class="bi bi-building"></i>
            <h4 class="mt-3">{% if user_language == 'en' %}No warehouses found{% else %}لا توجد مخازن{% endif %}</h4>
            <p class="mb-3">{% if user_language == 'en' %}No warehouses found matching the search criteria{% else %}لم يتم العثور على أي مخازن تطابق معايير البحث{% endif %}</p>
            <a href="{% url 'definitions:warehouse_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>{% if user_language == 'en' %}Add New Warehouse{% else %}إضافة مخزن جديد{% endif %}
            </a>
        </div>
        {% endif %}
        </div>
    </div>

    <!-- SECTION 4: Pagination Last -->
    <div class="pagination-section">
        {% if page_obj.has_other_pages %}
        <div class="pagination-wrapper">
            <nav aria-label="{% if user_language == 'en' %}Page navigation{% else %}تنقل الصفحات{% endif %}">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">{% if user_language == 'en' %}First{% else %}الأولى{% endif %}</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">{% if user_language == 'en' %}Previous{% else %}السابقة{% endif %}</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">{% if user_language == 'en' %}{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}{% else %}{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}{% endif %}</span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">{% if user_language == 'en' %}Next{% else %}التالية{% endif %}</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">{% if user_language == 'en' %}Last{% else %}الأخيرة{% endif %}</a>
                    </li>
                {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>

</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-warehouse-btn');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const warehouseName = this.getAttribute('data-warehouse-name');
            const form = this.closest('form');

            // تأكيد بسيط مع JavaScript
            if (confirm(`هل أنت متأكد من حذف المخزن: ${warehouseName}؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
                // تغيير نص الزر أثناء المعالجة
                this.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>جاري الحذف...';
                this.disabled = true;

                // إرسال النموذج
                form.submit();
            }
        });
    });
});
</script>
{% endblock %}
