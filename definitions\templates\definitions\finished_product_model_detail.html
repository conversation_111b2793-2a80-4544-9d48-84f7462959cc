{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    .info-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-left: 4px solid #007bff;
    }
    .cost-breakdown {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    .stage-item {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-left: 3px solid #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:finished_product_model_edit' model.id %}" class="btn btn-warning">
                    <i class="bi bi-pencil me-2"></i>تعديل
                </a>
                <a href="{% url 'definitions:finished_product_model_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- المعلومات الأساسية -->
    <div class="content-card">
        <h3><i class="bi bi-info-circle me-2"></i>المعلومات الأساسية</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="info-card">
                    <h5>كود النموذج</h5>
                    <p class="mb-0"><strong>{{ model.code }}</strong></p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-card">
                    <h5>اسم النموذج</h5>
                    <p class="mb-0">{{ model.name }}</p>
                    {% if model.name_en %}
                        <small class="text-muted">{{ model.name_en }}</small>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="info-card">
                    <h5>نوع المنتج</h5>
                    <span class="badge bg-info">{{ model.get_product_type_display }}</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-card">
                    <h5>مستوى الجودة</h5>
                    <span class="badge bg-secondary">{{ model.get_quality_level_display }}</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-card">
                    <h5>الحالة</h5>
                    {% if model.is_approved %}
                        <span class="badge bg-success">معتمد</span>
                    {% elif model.is_active %}
                        <span class="badge bg-warning">نشط</span>
                    {% else %}
                        <span class="badge bg-danger">غير نشط</span>
                    {% endif %}
                </div>
            </div>
        </div>
        {% if model.description %}
        <div class="info-card">
            <h5>الوصف</h5>
            <p class="mb-0">{{ model.description }}</p>
        </div>
        {% endif %}
    </div>

    <!-- معلومات الإنتاج -->
    <div class="content-card">
        <h3><i class="bi bi-gear me-2"></i>معلومات الإنتاج</h3>
        <div class="row">
            <div class="col-md-4">
                <div class="info-card">
                    <h5>المنتج النهائي</h5>
                    <p class="mb-0">{{ model.final_product.name }}</p>
                    <small class="text-muted">{{ model.final_product.code }}</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-card">
                    <h5>الكمية المنتجة</h5>
                    <p class="mb-0">{{ model.quantity_produced }} {{ model.unit_of_measure.name }}</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-card">
                    <h5>وقت الإنتاج</h5>
                    <p class="mb-0">{{ model.production_time_hours }} ساعة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليل التكاليف -->
    <div class="content-card">
        <h3><i class="bi bi-currency-dollar me-2"></i>تحليل التكاليف</h3>
        <div class="cost-breakdown">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>تكلفة المواد</h5>
                        <h3 class="text-primary">{{ model.estimated_material_cost|floatformat:2 }}</h3>
                        <small>جنيه</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>تكلفة العمالة</h5>
                        <h3 class="text-info">{{ model.labor_cost|floatformat:2 }}</h3>
                        <small>جنيه</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>التكاليف الإضافية</h5>
                        <h3 class="text-warning">{{ model.overhead_cost|floatformat:2 }}</h3>
                        <small>جنيه</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>إجمالي التكلفة</h5>
                        <h3 class="text-success">{{ model.total_estimated_cost|floatformat:2 }}</h3>
                        <small>جنيه</small>
                    </div>
                </div>
            </div>
        </div>
        
        {% if model.target_selling_price %}
        <div class="row">
            <div class="col-md-6">
                <div class="info-card">
                    <h5>سعر البيع المستهدف</h5>
                    <p class="mb-0">{{ model.target_selling_price|floatformat:2 }} جنيه</p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-card">
                    <h5>هامش الربح المقدر</h5>
                    <p class="mb-0">{{ model.estimated_profit_margin|floatformat:1 }}%</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- الجودة والمواصفات -->
    {% if model.specifications or model.quality_standards or model.testing_requirements %}
    <div class="content-card">
        <h3><i class="bi bi-award me-2"></i>الجودة والمواصفات</h3>
        <div class="row">
            {% if model.specifications %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>المواصفات الفنية</h5>
                    <p class="mb-0">{{ model.specifications|linebreaks }}</p>
                </div>
            </div>
            {% endif %}
            {% if model.quality_standards %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>معايير الجودة</h5>
                    <p class="mb-0">{{ model.quality_standards|linebreaks }}</p>
                </div>
            </div>
            {% endif %}
            {% if model.testing_requirements %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>متطلبات الاختبار</h5>
                    <p class="mb-0">{{ model.testing_requirements|linebreaks }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- معلومات إضافية -->
    <div class="content-card">
        <h3><i class="bi bi-info-square me-2"></i>معلومات إضافية</h3>
        <div class="row">
            {% if model.shelf_life_days %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>مدة الصلاحية</h5>
                    <p class="mb-0">{{ model.shelf_life_days }} يوم</p>
                </div>
            </div>
            {% endif %}
            {% if model.storage_conditions %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>شروط التخزين</h5>
                    <p class="mb-0">{{ model.storage_conditions }}</p>
                </div>
            </div>
            {% endif %}
            {% if model.packaging_requirements %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>متطلبات التعبئة</h5>
                    <p class="mb-0">{{ model.packaging_requirements }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- مراحل الإنتاج -->
    {% if model.stages.exists %}
    <div class="content-card">
        <h3><i class="bi bi-list-ol me-2"></i>مراحل الإنتاج</h3>
        {% for stage_link in model.stages.all %}
        <div class="stage-item">
            <div class="row align-items-center">
                <div class="col-md-1">
                    <span class="badge bg-primary">{{ stage_link.sequence_in_model }}</span>
                </div>
                <div class="col-md-4">
                    <h6 class="mb-0">{{ stage_link.production_stage.name }}</h6>
                    <small class="text-muted">{{ stage_link.production_stage.get_stage_type_display }}</small>
                </div>
                <div class="col-md-2">
                    <small>المدة المقدرة</small>
                    <p class="mb-0">{{ stage_link.estimated_duration_hours }} ساعة</p>
                </div>
                <div class="col-md-2">
                    <small>التكلفة المقدرة</small>
                    <p class="mb-0">{{ stage_link.estimated_cost|floatformat:2 }} جنيه</p>
                </div>
                <div class="col-md-2">
                    <small>الكمية المطلوبة</small>
                    <p class="mb-0">{{ stage_link.required_quantity }}</p>
                </div>
                <div class="col-md-1">
                    {% if stage_link.is_mandatory %}
                        <span class="badge bg-danger">إجباري</span>
                    {% else %}
                        <span class="badge bg-secondary">اختياري</span>
                    {% endif %}
                </div>
            </div>
            {% if stage_link.special_instructions %}
            <div class="mt-2">
                <small class="text-muted">تعليمات خاصة: {{ stage_link.special_instructions }}</small>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- معلومات النظام -->
    <div class="content-card">
        <h3><i class="bi bi-clock-history me-2"></i>معلومات النظام</h3>
        <div class="row">
            <div class="col-md-3">
                <div class="info-card">
                    <h5>تاريخ الإنشاء</h5>
                    <p class="mb-0">{{ model.created_at|date:"d/m/Y H:i" }}</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-card">
                    <h5>أنشئ بواسطة</h5>
                    <p class="mb-0">{{ model.created_by.get_full_name|default:model.created_by.username }}</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-card">
                    <h5>آخر تحديث</h5>
                    <p class="mb-0">{{ model.updated_at|date:"d/m/Y H:i" }}</p>
                </div>
            </div>
            {% if model.approved_by %}
            <div class="col-md-3">
                <div class="info-card">
                    <h5>معتمد بواسطة</h5>
                    <p class="mb-0">{{ model.approved_by.get_full_name|default:model.approved_by.username }}</p>
                    <small class="text-muted">{{ model.approval_date|date:"d/m/Y" }}</small>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
