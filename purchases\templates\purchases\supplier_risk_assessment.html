{% extends 'base.html' %}
{% load static %}

{% block title %}تقييم مخاطر المورد - {{ supplier.name }} - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .risk-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .risk-level {
        text-align: center;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
    }

    .risk-level.low {
        background: linear-gradient(45deg, #d4edda, #c3e6cb);
        color: #155724;
    }

    .risk-level.medium {
        background: linear-gradient(45deg, #fff3cd, #ffeaa7);
        color: #856404;
    }

    .risk-level.high {
        background: linear-gradient(45deg, #f8d7da, #f5c6cb);
        color: #721c24;
    }

    .risk-level.critical {
        background: linear-gradient(45deg, #dc3545, #c82333);
        color: white;
    }

    .risk-score {
        font-size: 4rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .risk-label {
        font-size: 1.5rem;
        font-weight: 600;
    }

    .metric-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .metric-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #6f42c1;
    }

    .metric-title {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 15px;
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #6f42c1;
        margin-bottom: 10px;
    }

    .metric-description {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .progress-bar-custom {
        height: 20px;
        border-radius: 10px;
        background: #e9ecef;
        overflow: hidden;
        margin-top: 10px;
    }

    .progress-fill {
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 0.9rem;
    }

    .progress-fill.low {
        background: linear-gradient(45deg, #28a745, #20c997);
    }

    .progress-fill.medium {
        background: linear-gradient(45deg, #ffc107, #e0a800);
    }

    .progress-fill.high {
        background: linear-gradient(45deg, #fd7e14, #e8590c);
    }

    .progress-fill.critical {
        background: linear-gradient(45deg, #dc3545, #c82333);
    }

    .form-section {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .section-title {
        color: #6f42c1;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
    }

    .form-control, .form-range {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-range:focus {
        border-color: #6f42c1;
        box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    }

    .btn-save {
        background: linear-gradient(45deg, #6f42c1, #5a32a3);
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
        color: white;
    }

    .btn-cancel {
        background: #6c757d;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }

    .range-value {
        font-weight: bold;
        color: #6f42c1;
        font-size: 1.1rem;
    }

    .recommendations {
        background: linear-gradient(45deg, #e3f2fd, #ffffff);
        border: 1px solid #2196f3;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }

    .recommendations h5 {
        color: #2196f3;
        margin-bottom: 15px;
    }

    .recommendation-item {
        background: white;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        border-left: 4px solid #2196f3;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-shield-exclamation"></i>
                    تقييم مخاطر المورد
                </h1>
                <p class="mb-0">{{ supplier.name }} - تحليل شامل للمخاطر المالية والتشغيلية</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'purchases:supplier_detail' supplier.pk %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right"></i>
                    العودة للمورد
                </a>
            </div>
        </div>
    </div>

    <!-- مستوى المخاطر الحالي -->
    <div class="risk-level {{ risk_assessment.overall_risk_level }}">
        <div class="risk-score">{{ risk_assessment.risk_score }}/100</div>
        <div class="risk-label">{{ risk_assessment.get_overall_risk_level_display }}</div>
        <div class="progress-bar-custom mt-3">
            <div class="progress-fill {{ risk_assessment.overall_risk_level }}" 
                 style="width: {{ risk_assessment.risk_score }}%;">
                {{ risk_assessment.risk_score }}%
            </div>
        </div>
    </div>

    <!-- مؤشرات المخاطر -->
    <div class="risk-card">
        <h3 class="section-title">
            <i class="bi bi-graph-up"></i>
            مؤشرات المخاطر الحالية
        </h3>
        
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-title">تاريخ الدفع</div>
                <div class="metric-value">{{ risk_assessment.payment_history_score }}%</div>
                <div class="metric-description">نسبة الدفع في الوقت المحدد</div>
                <div class="progress-bar-custom">
                    <div class="progress-fill {% if risk_assessment.payment_history_score >= 80 %}low{% elif risk_assessment.payment_history_score >= 60 %}medium{% elif risk_assessment.payment_history_score >= 40 %}high{% else %}critical{% endif %}" 
                         style="width: {{ risk_assessment.payment_history_score }}%;">
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-title">استخدام الائتمان</div>
                <div class="metric-value">{{ risk_assessment.credit_utilization }}%</div>
                <div class="metric-description">نسبة استخدام حد الائتمان</div>
                <div class="progress-bar-custom">
                    <div class="progress-fill {% if risk_assessment.credit_utilization <= 50 %}low{% elif risk_assessment.credit_utilization <= 75 %}medium{% elif risk_assessment.credit_utilization <= 90 %}high{% else %}critical{% endif %}" 
                         style="width: {{ risk_assessment.credit_utilization }}%;">
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-title">متوسط التأخير</div>
                <div class="metric-value">{{ risk_assessment.average_payment_delay }}</div>
                <div class="metric-description">أيام تأخير الدفع</div>
            </div>

            <div class="metric-card">
                <div class="metric-title">معدل تنفيذ الطلبات</div>
                <div class="metric-value">{{ risk_assessment.order_fulfillment_rate }}%</div>
                <div class="metric-description">نسبة تنفيذ الطلبات بنجاح</div>
                <div class="progress-bar-custom">
                    <div class="progress-fill {% if risk_assessment.order_fulfillment_rate >= 95 %}low{% elif risk_assessment.order_fulfillment_rate >= 85 %}medium{% elif risk_assessment.order_fulfillment_rate >= 70 %}high{% else %}critical{% endif %}" 
                         style="width: {{ risk_assessment.order_fulfillment_rate }}%;">
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-title">درجة الجودة</div>
                <div class="metric-value">{{ risk_assessment.quality_score }}%</div>
                <div class="metric-description">تقييم جودة المنتجات</div>
                <div class="progress-bar-custom">
                    <div class="progress-fill {% if risk_assessment.quality_score >= 90 %}low{% elif risk_assessment.quality_score >= 75 %}medium{% elif risk_assessment.quality_score >= 60 %}high{% else %}critical{% endif %}" 
                         style="width: {{ risk_assessment.quality_score }}%;">
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-title">أداء التسليم</div>
                <div class="metric-value">{{ risk_assessment.delivery_performance }}%</div>
                <div class="metric-description">نسبة التسليم في الوقت المحدد</div>
                <div class="progress-bar-custom">
                    <div class="progress-fill {% if risk_assessment.delivery_performance >= 95 %}low{% elif risk_assessment.delivery_performance >= 85 %}medium{% elif risk_assessment.delivery_performance >= 70 %}high{% else %}critical{% endif %}" 
                         style="width: {{ risk_assessment.delivery_performance }}%;">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج تحديث التقييم -->
    <form method="post">
        {% csrf_token %}
        <div class="form-section">
            <h3 class="section-title">
                <i class="bi bi-pencil-square"></i>
                تحديث تقييم المخاطر
            </h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">درجة تاريخ الدفع (0-100)</label>
                        <input type="range" class="form-range" name="payment_history_score" 
                               min="0" max="100" value="{{ risk_assessment.payment_history_score }}"
                               oninput="updateRangeValue(this, 'payment_history_value')">
                        <div class="range-value" id="payment_history_value">{{ risk_assessment.payment_history_score }}%</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">نسبة استخدام الائتمان (%)</label>
                        <input type="range" class="form-range" name="credit_utilization" 
                               min="0" max="100" step="0.1" value="{{ risk_assessment.credit_utilization }}"
                               oninput="updateRangeValue(this, 'credit_utilization_value')">
                        <div class="range-value" id="credit_utilization_value">{{ risk_assessment.credit_utilization }}%</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">متوسط تأخير الدفع (أيام)</label>
                        <input type="number" class="form-control" name="average_payment_delay" 
                               min="0" value="{{ risk_assessment.average_payment_delay }}">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">معدل تنفيذ الطلبات (%)</label>
                        <input type="range" class="form-range" name="order_fulfillment_rate" 
                               min="0" max="100" step="0.1" value="{{ risk_assessment.order_fulfillment_rate }}"
                               oninput="updateRangeValue(this, 'fulfillment_value')">
                        <div class="range-value" id="fulfillment_value">{{ risk_assessment.order_fulfillment_rate }}%</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">درجة الجودة (0-100)</label>
                        <input type="range" class="form-range" name="quality_score" 
                               min="0" max="100" value="{{ risk_assessment.quality_score }}"
                               oninput="updateRangeValue(this, 'quality_value')">
                        <div class="range-value" id="quality_value">{{ risk_assessment.quality_score }}%</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">أداء التسليم (0-100)</label>
                        <input type="range" class="form-range" name="delivery_performance" 
                               min="0" max="100" value="{{ risk_assessment.delivery_performance }}"
                               oninput="updateRangeValue(this, 'delivery_value')">
                        <div class="range-value" id="delivery_value">{{ risk_assessment.delivery_performance }}%</div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">ملاحظات التقييم</label>
                <textarea class="form-control" name="notes" rows="4" 
                          placeholder="ملاحظات إضافية حول تقييم المخاطر">{{ risk_assessment.notes }}</textarea>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="text-center">
            <button type="submit" class="btn btn-save me-3">
                <i class="bi bi-check-circle"></i>
                حفظ التقييم
            </button>
            <a href="{% url 'purchases:supplier_detail' supplier.pk %}" class="btn btn-cancel">
                <i class="bi bi-x-circle"></i>
                إلغاء
            </a>
        </div>
    </form>

    <!-- التوصيات -->
    <div class="recommendations">
        <h5>
            <i class="bi bi-lightbulb"></i>
            التوصيات بناءً على التقييم
        </h5>
        
        {% if risk_assessment.overall_risk_level == 'low' %}
            <div class="recommendation-item">
                <strong>مورد موثوق:</strong> يمكن زيادة حد الائتمان وتمديد فترات السداد.
            </div>
        {% elif risk_assessment.overall_risk_level == 'medium' %}
            <div class="recommendation-item">
                <strong>مراقبة دورية:</strong> متابعة أداء الدفع وتقييم شهري للوضع.
            </div>
        {% elif risk_assessment.overall_risk_level == 'high' %}
            <div class="recommendation-item">
                <strong>تشديد الشروط:</strong> تقليل حد الائتمان وطلب ضمانات إضافية.
            </div>
        {% else %}
            <div class="recommendation-item">
                <strong>مخاطر عالية:</strong> إيقاف التعامل أو طلب دفع مقدم فقط.
            </div>
        {% endif %}
        
        <div class="recommendation-item">
            <strong>حد الائتمان المقترح:</strong> {{ risk_assessment.recommended_credit_limit|default:"يحتاج تحديد" }} ج.م
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function updateRangeValue(range, targetId) {
        document.getElementById(targetId).textContent = range.value + '%';
    }
</script>
{% endblock %}
