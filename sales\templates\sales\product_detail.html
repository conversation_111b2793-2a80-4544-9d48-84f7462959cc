<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المنتج: {{ product.name }} - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .product-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .product-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .product-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }
        
        .product-code {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .info-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f093fb;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            display: flex;
            align-items: center;
        }
        
        .info-label i {
            margin-left: 8px;
            color: #f093fb;
        }
        
        .info-value {
            font-weight: 600;
            color: #333;
        }
        
        .price-card {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-bottom: 25px;
        }
        
        .price-title {
            font-size: 1.2rem;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .price-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stock-status {
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .stock-normal { background: #d4edda; color: #155724; }
        .stock-low { background: #fff3cd; color: #856404; }
        .stock-out { background: #f8d7da; color: #721c24; }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn-action {
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-edit {
            background: linear-gradient(45deg, #ffc107, #ff8f00);
            color: white;
        }
        
        .btn-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 193, 7, 0.3);
            color: white;
        }
        
        .btn-delete {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .btn-delete:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(220, 53, 69, 0.3);
            color: white;
        }
        
        .btn-back {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
        }
        
        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3);
            color: white;
        }
        
        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto 15px;
            color: white;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #333;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .icon-sales { background: linear-gradient(45deg, #28a745, #20c997); }
        .icon-stock { background: linear-gradient(45deg, #17a2b8, #138496); }
        .icon-profit { background: linear-gradient(45deg, #ffc107, #e0a800); }
        .icon-orders { background: linear-gradient(45deg, #6f42c1, #5a32a3); }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-box-seam"></i>
                        تفاصيل المنتج
                    </h1>
                    <p class="mb-0">عرض تفصيلي لبيانات المنتج والإحصائيات</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'sales:product_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- بطاقة المنتج الرئيسية -->
        <div class="product-card">
            <div class="row">
                <div class="col-md-4">
                    {% if product.image %}
                        <img src="{{ product.image.url }}" alt="{{ product.name }}" class="product-image">
                    {% else %}
                        <div class="product-image d-flex align-items-center justify-content-center bg-light">
                            <i class="bi bi-image" style="font-size: 4rem; color: #6c757d;"></i>
                        </div>
                    {% endif %}
                </div>
                
                <div class="col-md-8">
                    <div class="product-title">{{ product.name }}</div>
                    <div class="product-code">كود: {{ product.code }}</div>
                    
                    {% if product.description %}
                        <p class="text-muted mb-4">{{ product.description }}</p>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="price-card">
                                <div class="price-title">سعر التجزئة</div>
                                <div class="price-value">{{ product.unit_price_retail }}</div>
                                <div>جنيه مصري</div>
                            </div>
                        </div>
                        
                        {% if product.unit_price_wholesale %}
                            <div class="col-md-4">
                                <div class="price-card" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                    <div class="price-title">سعر الجملة</div>
                                    <div class="price-value">{{ product.unit_price_wholesale }}</div>
                                    <div>جنيه مصري</div>
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if product.cost_price %}
                            <div class="col-md-4">
                                <div class="price-card" style="background: linear-gradient(135deg, #28a745, #20c997);">
                                    <div class="price-title">سعر التكلفة</div>
                                    <div class="price-value">{{ product.cost_price }}</div>
                                    <div>جنيه مصري</div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="stock-status {% if product.stock_quantity == 0 %}stock-out{% elif product.stock_quantity <= product.min_stock_level %}stock-low{% else %}stock-normal{% endif %}">
                        {% if product.stock_quantity == 0 %}
                            <i class="bi bi-x-circle"></i> نفد المخزون
                        {% elif product.stock_quantity <= product.min_stock_level %}
                            <i class="bi bi-exclamation-triangle"></i> مخزون منخفض: {{ product.stock_quantity }} {{ product.unit }}
                        {% else %}
                            <i class="bi bi-check-circle"></i> متوفر: {{ product.stock_quantity }} {{ product.unit }}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات المنتج -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon icon-sales">
                    <i class="bi bi-graph-up"></i>
                </div>
                <div class="stat-number">{{ product_stats.total_sales|floatformat:0 }}</div>
                <div class="stat-label">إجمالي المبيعات (ج.م)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-stock">
                    <i class="bi bi-boxes"></i>
                </div>
                <div class="stat-number">{{ product.stock_quantity }}</div>
                <div class="stat-label">المخزون الحالي ({{ product.unit }})</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-profit">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="stat-number">{{ product_stats.profit_margin|floatformat:1 }}%</div>
                <div class="stat-label">هامش الربح</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-orders">
                    <i class="bi bi-receipt"></i>
                </div>
                <div class="stat-number">{{ product_stats.total_orders }}</div>
                <div class="stat-label">عدد الطلبات</div>
            </div>
        </div>

        <!-- معلومات تفصيلية -->
        <div class="row">
            <div class="col-md-6">
                <div class="info-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        المعلومات الأساسية
                    </h3>
                    
                    <div class="info-row">
                        <div class="info-label">
                            <i class="bi bi-tag"></i>
                            الفئة:
                        </div>
                        <div class="info-value">{{ product.category|default:"غير محدد" }}</div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">
                            <i class="bi bi-rulers"></i>
                            وحدة القياس:
                        </div>
                        <div class="info-value">{{ product.unit }}</div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">
                            <i class="bi bi-calendar-plus"></i>
                            تاريخ الإضافة:
                        </div>
                        <div class="info-value">{{ product.created_at|date:"Y/m/d H:i" }}</div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">
                            <i class="bi bi-calendar-check"></i>
                            آخر تحديث:
                        </div>
                        <div class="info-value">{{ product.updated_at|date:"Y/m/d H:i" }}</div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="info-section">
                    <h3 class="section-title">
                        <i class="bi bi-boxes"></i>
                        إدارة المخزون
                    </h3>
                    
                    <div class="info-row">
                        <div class="info-label">
                            <i class="bi bi-box"></i>
                            الكمية الحالية:
                        </div>
                        <div class="info-value">{{ product.stock_quantity }} {{ product.unit }}</div>
                    </div>
                    
                    <div class="info-row">
                        <div class="info-label">
                            <i class="bi bi-arrow-down"></i>
                            الحد الأدنى:
                        </div>
                        <div class="info-value">{{ product.min_stock_level|default:"غير محدد" }} {{ product.unit }}</div>
                    </div>
                    
                    {% if product.max_stock_level %}
                        <div class="info-row">
                            <div class="info-label">
                                <i class="bi bi-arrow-up"></i>
                                الحد الأقصى:
                            </div>
                            <div class="info-value">{{ product.max_stock_level }} {{ product.unit }}</div>
                        </div>
                    {% endif %}
                    
                    <div class="info-row">
                        <div class="info-label">
                            <i class="bi bi-speedometer"></i>
                            حالة المخزون:
                        </div>
                        <div class="info-value">
                            {% if product.stock_quantity == 0 %}
                                <span class="text-danger">نفد المخزون</span>
                            {% elif product.stock_quantity <= product.min_stock_level %}
                                <span class="text-warning">مخزون منخفض</span>
                            {% else %}
                                <span class="text-success">متوفر</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسم بياني لحركة المبيعات -->
        <div class="chart-container">
            <h3 class="section-title">حركة المبيعات (آخر 12 شهر)</h3>
            <canvas id="salesChart" width="400" height="200"></canvas>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <a href="{% url 'sales:product_edit' product.pk %}" class="btn-action btn-edit">
                <i class="bi bi-pencil"></i>
                تعديل المنتج
            </a>
            
            <button type="button" class="btn-action btn-delete" onclick="confirmDelete()">
                <i class="bi bi-trash"></i>
                حذف المنتج
            </button>
            
            <a href="{% url 'sales:product_list' %}" class="btn-action btn-back">
                <i class="bi bi-arrow-left"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .info-section');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });

            // رسم بياني للمبيعات
            const ctx = document.getElementById('salesChart').getContext('2d');
            const salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                    datasets: [{
                        label: 'المبيعات (ج.م)',
                        data: {{ monthly_sales|safe }},
                        borderColor: 'rgb(240, 147, 251)',
                        backgroundColor: 'rgba(240, 147, 251, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ج.م';
                                }
                            }
                        }
                    }
                }
            });
        });

        function confirmDelete() {
            if (confirm('هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.')) {
                // إرسال طلب حذف
                fetch("{% url 'sales:product_delete' product.pk %}", {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => {
                    if (response.ok) {
                        window.location.href = "{% url 'sales:product_list' %}";
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                });
            }
        }
    </script>
</body>
</html>
