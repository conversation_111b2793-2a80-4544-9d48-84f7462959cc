{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<style>
    /* Base Form Styles */
    body {
        background: #ffffff;
        min-height: 100vh;
    }

    /* Form Container */
    .form-container {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        color: #333;
    }

    .form-title {
        font-size: 2rem;
        font-weight: 800;
        color: #333;
        margin-bottom: 2rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    /* Form Sections */
    .form-section {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Form Controls */
    .form-label {
        color: #333;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .form-control, .form-select {
        background: #ffffff;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 0.75rem 1rem;
        color: #333;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .form-control::placeholder {
        color: #999;
    }

    .form-control:focus, .form-select:focus {
        background: #ffffff;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        color: #333;
        outline: none;
    }

    .form-select option {
        background: #ffffff;
        color: #333;
    }

    /* Buttons */
    .btn {
        padding: 0.75rem 2rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .btn-primary {
        background: #007bff;
        color: white;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
    }

    .btn-secondary:hover {
        background: #545b62;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    /* Form Check */
    .form-check {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .form-check-input {
        background: #ffffff;
        border: 1px solid #ddd;
        margin-right: 0.5rem;
    }

    .form-check-input:checked {
        background: #007bff;
        border-color: #007bff;
    }

    .form-check-label {
        color: #333;
        font-weight: 600;
    }

    /* Help Text */
    .form-text {
        color: #666;
        font-size: 0.8rem;
        margin-top: 0.5rem;
    }

    /* Required Field Indicator */
    .required::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    /* Error Messages */
    .invalid-feedback {
        color: #dc3545;
        font-weight: 600;
        margin-top: 0.5rem;
    }

    .is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
    }

    /* Action Buttons Container */
    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e0e0e0;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .form-container {
            padding: 1.5rem;
        }
        
        .form-title {
            font-size: 1.5rem;
        }
        
        .btn {
            padding: 0.6rem 1.5rem;
            font-size: 0.8rem;
        }
        
        .form-section {
            padding: 1rem;
        }

        .form-actions {
            flex-direction: column;
            gap: 1rem;
        }

        .form-actions .btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% block extra_form_css %}{% endblock %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-container">
                <h1 class="form-title">
                    {% block form_icon %}
                    <i class="bi bi-plus-circle"></i>
                    {% endblock %}
                    {% block form_title %}نموذج جديد{% endblock %}
                </h1>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" {% block form_attributes %}{% endblock %}>
                    {% csrf_token %}
                    
                    {% block form_content %}
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            {% if user_language == 'en' %}Basic Information{% else %}المعلومات الأساسية{% endif %}
                        </h3>
                        <p>محتوى النموذج هنا...</p>
                    </div>
                    {% endblock %}

                    <div class="form-actions">
                        <a href="{% block back_url %}#{% endblock %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>{% if user_language == 'en' %}Back{% else %}عودة{% endif %}
                        </a>
                        
                        <div class="d-flex gap-2">
                            {% block extra_buttons %}{% endblock %}
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>
                                {% block submit_text %}{% if user_language == 'en' %}Save{% else %}حفظ{% endif %}{% endblock %}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة بسيطة للنموذج
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('button[type="submit"]');
    
    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // التحقق من الحقول المطلوبة
            const requiredFields = form.querySelectorAll('[required]');
            let hasErrors = false;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    hasErrors = true;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (hasErrors) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
            
            // تعطيل الزر لمنع الإرسال المتكرر
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
        });
    }
});

{% block extra_js %}{% endblock %}
</script>
{% endblock %}
