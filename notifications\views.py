from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from .models import Notification, NotificationSettings
import json


@login_required
def get_notifications(request):
    """الحصول على الإشعارات للمستخدم الحالي"""
    try:
        notifications = Notification.objects.filter(
            recipient=request.user
        ).order_by('-created_at')[:10]

        # تعيين الإشعارات كمُشاهدة
        notifications.update(is_seen=True)

        notifications_data = []
        for notification in notifications:
            notifications_data.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'type': notification.notification_type,
                'priority': notification.priority,
                'is_read': notification.is_read,
                'created_at': notification.time_since_created(),
                'icon': notification.get_type_icon(),
                'color': notification.get_type_color(),
                'action_url': notification.action_url,
                'sender': notification.sender.get_full_name() if notification.sender else None,
            })

        unread_count = Notification.objects.filter(
            recipient=request.user,
            is_read=False
        ).count()

    except Exception as e:
        # في حالة عدم وجود الجداول، إرجاع بيانات فارغة
        notifications_data = []
        unread_count = 0

    return JsonResponse({
        'notifications': notifications_data,
        'unread_count': unread_count
    })


@login_required
def get_unread_count(request):
    """الحصول على عدد الإشعارات غير المقروءة"""
    try:
        count = Notification.objects.filter(
            recipient=request.user,
            is_read=False
        ).count()
    except Exception:
        count = 0

    return JsonResponse({'count': count})


@login_required
@require_POST
def mark_as_read(request, notification_id):
    """تعيين إشعار كمقروء"""
    try:
        notification = get_object_or_404(
            Notification,
            id=notification_id,
            recipient=request.user
        )
        
        notification.mark_as_read()
        
        return JsonResponse({
            'success': True,
            'message': 'تم تعيين الإشعار كمقروء'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_POST
def mark_all_as_read(request):
    """تعيين جميع الإشعارات كمقروءة"""
    try:
        updated = Notification.objects.filter(
            recipient=request.user,
            is_read=False
        ).update(
            is_read=True,
            read_at=timezone.now()
        )
        
        return JsonResponse({
            'success': True,
            'message': f'تم تعيين {updated} إشعار كمقروء',
            'updated_count': updated
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
def notifications_page(request):
    """صفحة الإشعارات الكاملة"""
    try:
        notifications = Notification.objects.filter(
            recipient=request.user
        ).order_by('-created_at')

        # تطبيق الفلترة
        filter_type = request.GET.get('type')
        if filter_type:
            notifications = notifications.filter(notification_type=filter_type)

        filter_status = request.GET.get('status')
        if filter_status == 'unread':
            notifications = notifications.filter(is_read=False)
        elif filter_status == 'read':
            notifications = notifications.filter(is_read=True)

        # البحث
        search_query = request.GET.get('search')
        if search_query:
            notifications = notifications.filter(
                Q(title__icontains=search_query) |
                Q(message__icontains=search_query)
            )

        # التصفح
        paginator = Paginator(notifications, 20)
        page_number = request.GET.get('page')
        notifications_page = paginator.get_page(page_number)

        # الإحصائيات
        stats = {
            'total': Notification.objects.filter(recipient=request.user).count(),
            'unread': Notification.objects.filter(recipient=request.user, is_read=False).count(),
            'today': Notification.objects.filter(
                recipient=request.user,
                created_at__date=timezone.now().date()
            ).count(),
        }

        # أنواع الإشعارات للفلترة
        notification_types = Notification.objects.filter(
            recipient=request.user
        ).values_list('notification_type', flat=True).distinct()

    except Exception:
        # في حالة عدم وجود الجداول
        notifications_page = []
        stats = {'total': 0, 'unread': 0, 'today': 0}
        notification_types = []
        filter_type = None
        filter_status = None
        search_query = None

    context = {
        'notifications': notifications_page,
        'stats': stats,
        'notification_types': notification_types,
        'current_type': filter_type,
        'current_status': filter_status,
        'search_query': search_query,
    }

    return render(request, 'notifications/notifications.html', context)


@login_required
def notification_settings(request):
    """إعدادات الإشعارات"""
    settings, created = NotificationSettings.objects.get_or_create(
        user=request.user
    )
    
    if request.method == 'POST':
        # تحديث الإعدادات
        settings.email_notifications = request.POST.get('email_notifications') == 'on'
        settings.browser_notifications = request.POST.get('browser_notifications') == 'on'
        settings.sound_notifications = request.POST.get('sound_notifications') == 'on'
        settings.notify_messages = request.POST.get('notify_messages') == 'on'
        settings.notify_inventory = request.POST.get('notify_inventory') == 'on'
        settings.notify_orders = request.POST.get('notify_orders') == 'on'
        settings.notify_payments = request.POST.get('notify_payments') == 'on'
        settings.notify_system = request.POST.get('notify_system') == 'on'
        
        settings.save()
        
        return JsonResponse({
            'success': True,
            'message': 'تم حفظ الإعدادات بنجاح'
        })
    
    context = {
        'settings': settings
    }
    
    return render(request, 'notifications/settings.html', context)


def create_notification(recipient, title, message, notification_type='info', 
                       priority='normal', sender=None, action_url=None, icon=None):
    """دالة مساعدة لإنشاء إشعار"""
    
    # التحقق من إعدادات المستخدم
    try:
        user_settings = NotificationSettings.objects.get(user=recipient)
        
        # التحقق من نوع الإشعار
        if notification_type == 'message' and not user_settings.notify_messages:
            return None
        elif notification_type == 'inventory' and not user_settings.notify_inventory:
            return None
        elif notification_type == 'order' and not user_settings.notify_orders:
            return None
        elif notification_type == 'payment' and not user_settings.notify_payments:
            return None
        elif notification_type == 'system' and not user_settings.notify_system:
            return None
            
    except NotificationSettings.DoesNotExist:
        # إنشاء إعدادات افتراضية
        NotificationSettings.objects.create(user=recipient)
    
    # إنشاء الإشعار
    notification = Notification.objects.create(
        recipient=recipient,
        title=title,
        message=message,
        notification_type=notification_type,
        priority=priority,
        sender=sender,
        action_url=action_url,
        icon=icon or Notification().get_type_icon()
    )
    
    return notification


def create_system_notification(title, message, priority='normal'):
    """إنشاء إشعار نظام لجميع المستخدمين"""
    from django.contrib.auth.models import User
    
    users = User.objects.filter(is_active=True)
    notifications = []
    
    for user in users:
        notification = create_notification(
            recipient=user,
            title=title,
            message=message,
            notification_type='system',
            priority=priority
        )
        if notification:
            notifications.append(notification)
    
    return notifications
