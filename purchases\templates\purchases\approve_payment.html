{% extends 'base.html' %}
{% load static %}

{% block title %}مراجعة الموافقة - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .approval-details {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .section-title {
        color: #2196f3;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .detail-item {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #2196f3;
    }

    .detail-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 8px;
        font-weight: 600;
    }

    .detail-value {
        font-size: 1.1rem;
        font-weight: bold;
        color: #2c3e50;
    }

    .amount-highlight {
        font-size: 2rem;
        font-weight: bold;
        color: #2196f3;
        text-align: center;
        padding: 20px;
        background: linear-gradient(45deg, #e3f2fd, #ffffff);
        border-radius: 15px;
        margin-bottom: 20px;
    }

    .justification-box {
        background: #e3f2fd;
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #2196f3;
        margin-bottom: 20px;
    }

    .action-section {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .btn-approve {
        background: linear-gradient(45deg, #4caf50, #45a049);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        margin-right: 15px;
    }

    .btn-approve:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(76, 175, 80, 0.3);
        color: white;
    }

    .btn-reject {
        background: linear-gradient(45deg, #f44336, #d32f2f);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        margin-right: 15px;
    }

    .btn-reject:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(244, 67, 54, 0.3);
        color: white;
    }

    .btn-cancel {
        background: #6c757d;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #2196f3;
        box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
    }

    .alert-warning {
        background: linear-gradient(45deg, #fff3cd, #ffeaa7);
        border: none;
        border-radius: 10px;
        color: #856404;
    }

    .days-pending {
        background: #fff3cd;
        color: #856404;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        display: inline-block;
    }

    .priority-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        display: inline-block;
    }

    .priority-high {
        background: #f8d7da;
        color: #721c24;
    }

    .priority-medium {
        background: #fff3cd;
        color: #856404;
    }

    .priority-low {
        background: #d4edda;
        color: #155724;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-clipboard-check"></i>
                    مراجعة طلب الموافقة
                </h1>
                <p class="mb-0">مراجعة والموافقة على دفعة للمورد {{ approval.payment.supplier.name }}</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'purchases:payment_approvals' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right"></i>
                    العودة للموافقات
                </a>
            </div>
        </div>
    </div>

    <!-- تفاصيل الطلب -->
    <div class="approval-details">
        <h3 class="section-title">
            <i class="bi bi-info-circle"></i>
            تفاصيل طلب الموافقة
        </h3>
        
        <div class="amount-highlight">
            {{ approval.payment.total_amount|floatformat:2 }} ج.م
        </div>
        
        <div class="detail-grid">
            <div class="detail-item">
                <div class="detail-label">المورد</div>
                <div class="detail-value">{{ approval.payment.supplier.name }}</div>
            </div>
            
            <div class="detail-item">
                <div class="detail-label">طلب بواسطة</div>
                <div class="detail-value">{{ approval.requested_by.get_full_name|default:approval.requested_by.username }}</div>
            </div>
            
            <div class="detail-item">
                <div class="detail-label">تاريخ الطلب</div>
                <div class="detail-value">{{ approval.requested_at|date:"Y-m-d H:i" }}</div>
            </div>
            
            <div class="detail-item">
                <div class="detail-label">أيام الانتظار</div>
                <div class="detail-value">
                    <span class="days-pending">{{ approval.days_pending }} يوم</span>
                </div>
            </div>
            
            <div class="detail-item">
                <div class="detail-label">الأولوية</div>
                <div class="detail-value">
                    <span class="priority-badge priority-{{ approval.priority }}">{{ approval.get_priority_display }}</span>
                </div>
            </div>
            
            <div class="detail-item">
                <div class="detail-label">طريقة الدفع</div>
                <div class="detail-value">{{ approval.payment.get_payment_method_display }}</div>
            </div>
        </div>
        
        <div class="justification-box">
            <h5><i class="bi bi-chat-quote"></i> مبرر الدفعة:</h5>
            <p class="mb-0">{{ approval.justification }}</p>
        </div>
        
        {% if approval.payment.reference_number %}
            <div class="alert alert-info">
                <strong>رقم المرجع:</strong> {{ approval.payment.reference_number }}
            </div>
        {% endif %}
        
        {% if approval.payment.notes %}
            <div class="alert alert-info">
                <strong>ملاحظات الدفعة:</strong> {{ approval.payment.notes }}
            </div>
        {% endif %}
    </div>

    <!-- إجراءات الموافقة -->
    <div class="action-section">
        <h3 class="section-title">
            <i class="bi bi-gear"></i>
            إجراءات الموافقة
        </h3>
        
        {% if approval.days_pending > 7 %}
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>تنبيه:</strong> هذا الطلب معلق منذ أكثر من أسبوع. يُنصح بالمراجعة العاجلة.
            </div>
        {% endif %}
        
        <form method="post">
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">الإجراء المطلوب</label>
                        <select name="action" class="form-select" required onchange="toggleReasonField(this)">
                            <option value="">اختر الإجراء</option>
                            <option value="approve">الموافقة على الدفعة</option>
                            <option value="reject">رفض الدفعة</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">ملاحظات إضافية</label>
                        <input type="text" name="notes" class="form-control" 
                               placeholder="ملاحظات اختيارية">
                    </div>
                </div>
            </div>
            
            <div class="mb-3" id="rejectionReasonField" style="display: none;">
                <label class="form-label">سبب الرفض <span class="text-danger">*</span></label>
                <textarea name="rejection_reason" class="form-control" rows="3" 
                          placeholder="يرجى توضيح سبب رفض الدفعة"></textarea>
            </div>
            
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-approve" onclick="return confirmAction('approve')">
                    <i class="bi bi-check-circle"></i>
                    تنفيذ الإجراء
                </button>
                <a href="{% url 'purchases:payment_approvals' %}" class="btn btn-cancel">
                    <i class="bi bi-x-circle"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function toggleReasonField(select) {
        const reasonField = document.getElementById('rejectionReasonField');
        const reasonTextarea = document.querySelector('textarea[name="rejection_reason"]');
        
        if (select.value === 'reject') {
            reasonField.style.display = 'block';
            reasonTextarea.required = true;
        } else {
            reasonField.style.display = 'none';
            reasonTextarea.required = false;
        }
    }
    
    function confirmAction(action) {
        const selectedAction = document.querySelector('select[name="action"]').value;
        
        if (!selectedAction) {
            alert('يرجى اختيار الإجراء المطلوب');
            return false;
        }
        
        if (selectedAction === 'approve') {
            return confirm('هل أنت متأكد من الموافقة على هذه الدفعة؟');
        } else if (selectedAction === 'reject') {
            const reason = document.querySelector('textarea[name="rejection_reason"]').value;
            if (!reason.trim()) {
                alert('يرجى إدخال سبب الرفض');
                return false;
            }
            return confirm('هل أنت متأكد من رفض هذه الدفعة؟');
        }
        
        return true;
    }
</script>
{% endblock %}
