from django import forms
from django.contrib.auth.models import User
from .models import AssetCategory, Asset, AssetMaintenance, AssetTransfer, AssetDisposal

class AssetCategoryForm(forms.ModelForm):
    """نموذج إضافة/تعديل فئات الأصول"""
    class Meta:
        model = AssetCategory
        fields = ['name', 'description', 'depreciation_rate', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الفئة'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف الفئة'}),
            'depreciation_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class AssetForm(forms.ModelForm):
    """نموذج إضافة/تعديل الأصول - مبسط"""
    class Meta:
        model = Asset
        fields = [
            'name', 'asset_code', 'category', 'purchase_date', 'purchase_price',
            'useful_life_years', 'status', 'condition', 'location', 'responsible_person'
        ]
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم الأصل',
                'required': True
            }),
            'asset_code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'كود الأصل (سيتم إنشاؤه تلقائياً إذا ترك فارغاً)'
            }),
            'category': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'purchase_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'required': True
            }),
            'purchase_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00',
                'required': True
            }),
            'useful_life_years': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'value': '5',
                'required': True
            }),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'condition': forms.Select(attrs={'class': 'form-select'}),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الموقع (اختياري)'
            }),
            'responsible_person': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تبسيط قائمة المستخدمين
        self.fields['responsible_person'].queryset = User.objects.filter(is_active=True)
        self.fields['responsible_person'].empty_label = "اختر المسؤول (اختياري)"
        self.fields['responsible_person'].required = False

        # تعيين قيم افتراضية
        if not self.instance.pk:  # للأصول الجديدة فقط
            self.fields['status'].initial = 'active'
            self.fields['condition'].initial = 'excellent'
            self.fields['useful_life_years'].initial = 5
            self.fields['salvage_value'].initial = 0

    def clean_asset_code(self):
        """إنشاء كود تلقائي إذا لم يتم تحديده"""
        asset_code = self.cleaned_data.get('asset_code')
        if not asset_code:
            # إنشاء كود تلقائي
            import random
            import string
            random_suffix = ''.join(random.choices(string.digits, k=4))
            asset_code = f"AST-{random_suffix}"

            # التأكد من عدم وجود كود مشابه
            while Asset.objects.filter(asset_code=asset_code).exists():
                random_suffix = ''.join(random.choices(string.digits, k=4))
                asset_code = f"AST-{random_suffix}"

        return asset_code

    def save(self, commit=True):
        """حفظ مبسط مع قيم افتراضية"""
        asset = super().save(commit=False)

        # تعيين قيم افتراضية للحقول المخفية
        if not hasattr(asset, 'salvage_value') or asset.salvage_value is None:
            asset.salvage_value = 0

        if commit:
            asset.save()
        return asset

class AssetMaintenanceForm(forms.ModelForm):
    """نموذج إضافة/تعديل صيانة الأصول"""
    class Meta:
        model = AssetMaintenance
        fields = ['asset', 'maintenance_type', 'description', 'maintenance_date', 'cost', 'vendor', 'notes']
        widgets = {
            'asset': forms.Select(attrs={'class': 'form-select'}),
            'maintenance_type': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف الصيانة'}),
            'maintenance_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'vendor': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مقدم الخدمة'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['asset'].queryset = Asset.objects.filter(status='active')

class AssetTransferForm(forms.ModelForm):
    """نموذج تحويل الأصول"""
    class Meta:
        model = AssetTransfer
        fields = ['asset', 'from_location', 'to_location', 'from_person', 'to_person', 'transfer_date', 'reason', 'notes']
        widgets = {
            'asset': forms.Select(attrs={'class': 'form-select'}),
            'from_location': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'من الموقع'}),
            'to_location': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'إلى الموقع'}),
            'from_person': forms.Select(attrs={'class': 'form-select'}),
            'to_person': forms.Select(attrs={'class': 'form-select'}),
            'transfer_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'سبب التحويل'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['asset'].queryset = Asset.objects.filter(status='active')
        self.fields['from_person'].queryset = User.objects.filter(is_active=True)
        self.fields['to_person'].queryset = User.objects.filter(is_active=True)
        self.fields['from_person'].empty_label = "اختر المسؤول الحالي"
        self.fields['to_person'].empty_label = "اختر المسؤول الجديد"

class AssetDisposalForm(forms.ModelForm):
    """نموذج التخلص من الأصول"""
    class Meta:
        model = AssetDisposal
        fields = ['asset', 'disposal_type', 'disposal_date', 'disposal_value', 'buyer_details', 'reason', 'notes']
        widgets = {
            'asset': forms.Select(attrs={'class': 'form-select'}),
            'disposal_type': forms.Select(attrs={'class': 'form-select'}),
            'disposal_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'disposal_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'buyer_details': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'تفاصيل المشتري'}),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'سبب التخلص'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['asset'].queryset = Asset.objects.filter(status='active')

class AssetSearchForm(forms.Form):
    """نموذج البحث في الأصول"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم أو الكود أو الرقم التسلسلي...'
        })
    )
    category = forms.ModelChoiceField(
        queryset=AssetCategory.objects.filter(is_active=True),
        required=False,
        empty_label="جميع الفئات",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Asset.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    condition = forms.ChoiceField(
        choices=[('', 'جميع الأحوال')] + Asset.CONDITION_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    responsible_person = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        required=False,
        empty_label="جميع المسؤولين",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
