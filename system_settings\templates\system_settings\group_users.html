{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .group-users-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .group-users-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .user-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 10px;
        padding: 15px;
        transition: all 0.3s ease;
    }
    
    .user-card:hover {
        border-color: #667eea;
        box-shadow: 0 3px 10px rgba(102, 126, 234, 0.1);
    }
    
    .user-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .user-details h6 {
        margin: 0;
        color: #495057;
        font-weight: 600;
    }
    
    .user-details small {
        color: #6c757d;
    }
    
    .btn-remove {
        background: #ffebee;
        color: #d32f2f;
        border: 1px solid #ffcdd2;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        text-decoration: none;
        transition: all 0.2s ease;
    }
    
    .btn-remove:hover {
        background: #d32f2f;
        color: white;
        text-decoration: none;
    }
    
    .btn-add {
        background: #e8f5e8;
        color: #388e3c;
        border: 1px solid #c8e6c9;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        text-decoration: none;
        transition: all 0.2s ease;
    }
    
    .btn-add:hover {
        background: #388e3c;
        color: white;
        text-decoration: none;
    }
    
    .section-title {
        background: #f8f9fa;
        padding: 10px 15px;
        border-radius: 5px;
        margin: 20px 0 10px 0;
        font-weight: 600;
        color: #495057;
    }
    
    .group-info {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .group-info h5 {
        color: #856404;
        margin-bottom: 5px;
    }
    
    .group-info p {
        color: #856404;
        margin: 0;
        font-size: 14px;
    }
    
    .no-users {
        text-align: center;
        padding: 30px;
        color: #6c757d;
    }
    
    .no-users i {
        font-size: 36px;
        margin-bottom: 10px;
        color: #dee2e6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="group-users-header">
        <h2><i class="fas fa-users me-2"></i>{{ title }}</h2>
        <p class="mb-0">إدارة المستخدمين في المجموعة</p>
    </div>

    <!-- Group Info -->
    <div class="group-info">
        <h5><i class="fas fa-users-cog me-2"></i>{{ group.name }}</h5>
        <p>إجمالي المستخدمين في هذه المجموعة: {{ users.count }}</p>
    </div>

    <!-- Current Users -->
    <div class="group-users-container">
        <div class="section-title">
            <i class="fas fa-users me-2"></i>المستخدمون الحاليون في المجموعة
        </div>
        
        {% if users %}
            {% for user in users %}
            <div class="user-card">
                <div class="user-info">
                    <div class="user-details">
                        <h6>
                            <i class="fas fa-user me-2"></i>
                            {{ user.username }}
                            {% if user.first_name or user.last_name %}
                                ({{ user.first_name }} {{ user.last_name }})
                            {% endif %}
                        </h6>
                        <small>
                            <i class="fas fa-envelope me-1"></i>{{ user.email|default:"لا يوجد بريد إلكتروني" }}
                        </small>
                    </div>
                    <div>
                        <form method="post" style="display: inline;">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="remove">
                            <input type="hidden" name="user_id" value="{{ user.id }}">
                            <button type="submit" class="btn-remove" 
                                    onclick="return confirm('هل أنت متأكد من إزالة {{ user.username }} من المجموعة؟')">
                                <i class="fas fa-times me-1"></i>إزالة
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="no-users">
                <i class="fas fa-user-slash"></i>
                <h5>لا يوجد مستخدمون</h5>
                <p>لا يوجد مستخدمون في هذه المجموعة حالياً</p>
            </div>
        {% endif %}
    </div>

    <!-- Available Users -->
    {% if available_users %}
    <div class="group-users-container">
        <div class="section-title">
            <i class="fas fa-user-plus me-2"></i>المستخدمون المتاحون للإضافة
        </div>
        
        {% for user in available_users %}
        <div class="user-card">
            <div class="user-info">
                <div class="user-details">
                    <h6>
                        <i class="fas fa-user me-2"></i>
                        {{ user.username }}
                        {% if user.first_name or user.last_name %}
                            ({{ user.first_name }} {{ user.last_name }})
                        {% endif %}
                    </h6>
                    <small>
                        <i class="fas fa-envelope me-1"></i>{{ user.email|default:"لا يوجد بريد إلكتروني" }}
                    </small>
                </div>
                <div>
                    <form method="post" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="action" value="add">
                        <input type="hidden" name="user_id" value="{{ user.id }}">
                        <button type="submit" class="btn-add" 
                                onclick="return confirm('هل أنت متأكد من إضافة {{ user.username }} إلى المجموعة؟')">
                            <i class="fas fa-plus me-1"></i>إضافة
                        </button>
                    </form>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Back Button -->
    <div class="text-center mt-4">
        <a href="{% url 'system_settings:permissions_management' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة لإدارة الصلاحيات
        </a>
        <a href="{% url 'system_settings:roles_management' %}" class="btn btn-primary ms-2">
            <i class="fas fa-users-cog me-2"></i>إدارة الأدوار
        </a>
    </div>
</div>
{% endblock %}
