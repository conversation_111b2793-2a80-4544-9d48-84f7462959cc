<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص النموذج</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: white;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .debug {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h2>تشخيص نموذج إضافة الشخص</h2>
    
    <div class="debug">
        <h3>معلومات التشخيص:</h3>
        <p id="status">جاري التحميل...</p>
    </div>
    
    <form id="personForm" method="post" action="http://127.0.0.1:8000/definitions/persons/create/">
        <div class="form-group">
            <label for="code">كود الشخص *</label>
            <input type="text" id="code" name="code" value="TEST001" required>
        </div>
        
        <div class="form-group">
            <label for="name">الاسم *</label>
            <input type="text" id="name" name="name" value="شخص تجريبي" required>
        </div>
        
        <div class="form-group">
            <label for="person_type">نوع الشخص *</label>
            <select id="person_type" name="person_type" required>
                <option value="">-- اختر نوع الشخص --</option>
                <option value="customer" selected>عميل</option>
                <option value="supplier">مورد</option>
                <option value="employee">موظف</option>
                <option value="both">عميل ومورد</option>
                <option value="other">أخرى</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="is_active" name="is_active" checked>
                شخص نشط
            </label>
        </div>
        
        <button type="submit">إرسال النموذج</button>
        <button type="button" onclick="debugForm()">تشخيص النموذج</button>
    </form>

    <div id="result" class="debug" style="display: none;">
        <h3>نتيجة التشخيص:</h3>
        <pre id="resultText"></pre>
    </div>

    <script>
        // تحديث حالة التشخيص
        document.getElementById('status').innerHTML = 'الصفحة محملة بنجاح';
        
        // معالج إرسال النموذج
        document.getElementById('personForm').addEventListener('submit', function(e) {
            console.log('Form submission started');
            document.getElementById('status').innerHTML = 'جاري إرسال النموذج...';
            
            // التحقق من الحقول المطلوبة
            const code = document.getElementById('code').value.trim();
            const name = document.getElementById('name').value.trim();
            const personType = document.getElementById('person_type').value;
            
            if (!code || !name || !personType) {
                e.preventDefault();
                alert('يرجى ملء جميع الحقول المطلوبة');
                document.getElementById('status').innerHTML = 'فشل: حقول مطلوبة فارغة';
                return false;
            }
            
            console.log('Form validation passed');
            document.getElementById('status').innerHTML = 'تم التحقق من البيانات، جاري الإرسال...';
        });
        
        function debugForm() {
            const form = document.getElementById('personForm');
            const formData = new FormData(form);
            
            let debugInfo = 'معلومات النموذج:\n';
            debugInfo += 'Action: ' + form.action + '\n';
            debugInfo += 'Method: ' + form.method + '\n\n';
            debugInfo += 'البيانات:\n';
            
            for (let [key, value] of formData.entries()) {
                debugInfo += key + ': ' + value + '\n';
            }
            
            document.getElementById('result').style.display = 'block';
            document.getElementById('resultText').textContent = debugInfo;
            
            console.log('Form debug info:', debugInfo);
        }
        
        // تحديث الحالة كل ثانية
        setInterval(function() {
            const now = new Date().toLocaleTimeString();
            if (document.getElementById('status').innerHTML === 'الصفحة محملة بنجاح') {
                document.getElementById('status').innerHTML = 'الصفحة محملة بنجاح - ' + now;
            }
        }, 1000);
    </script>
</body>
</html>
