{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .permissions-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .permissions-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .content-type-section {
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 15px;
        overflow: hidden;
        border: 1px solid #e9ecef;
    }
    
    .content-type-header {
        background: #e9ecef;
        padding: 12px 15px;
        font-weight: 600;
        color: #495057;
        border-bottom: 1px solid #dee2e6;
    }
    
    .permissions-grid {
        padding: 15px;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 10px;
    }
    
    .permission-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: white;
        border-radius: 5px;
        border: 1px solid #e9ecef;
        transition: all 0.2s ease;
    }
    
    .permission-item:hover {
        background: #f8f9fa;
        border-color: #667eea;
    }
    
    .permission-item input[type="checkbox"] {
        margin-left: 10px;
        transform: scale(1.2);
    }
    
    .permission-item label {
        margin: 0;
        cursor: pointer;
        font-size: 14px;
        flex: 1;
    }
    
    .btn-save {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        color: white;
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        color: white;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }
    
    .select-all-section {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .btn-select-all, .btn-deselect-all {
        background: #2196f3;
        border: none;
        color: white;
        padding: 8px 20px;
        border-radius: 20px;
        font-size: 14px;
        margin: 0 5px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .btn-select-all:hover, .btn-deselect-all:hover {
        background: #1976d2;
    }
    
    .group-info {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .group-info h5 {
        color: #856404;
        margin-bottom: 5px;
    }
    
    .group-info p {
        color: #856404;
        margin: 0;
        font-size: 14px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="permissions-header">
        <h2><i class="fas fa-edit me-2"></i>{{ title }}</h2>
        <p class="mb-0">تحديد الصلاحيات المخصصة للمجموعة</p>
    </div>

    <!-- Group Info -->
    <div class="group-info">
        <h5><i class="fas fa-users me-2"></i>{{ group.name }}</h5>
        <p>عدد المستخدمين في هذه المجموعة: {{ group.user_set.count }}</p>
    </div>

    <!-- Select All Section -->
    <div class="select-all-section">
        <button type="button" class="btn-select-all" onclick="selectAll()">
            <i class="fas fa-check-square me-1"></i>تحديد الكل
        </button>
        <button type="button" class="btn-deselect-all" onclick="deselectAll()">
            <i class="fas fa-square me-1"></i>إلغاء تحديد الكل
        </button>
    </div>

    <!-- Form -->
    <form method="post" action="{% url 'system_settings:edit_group_permissions' group.id %}" id="permissionsForm">
        {% csrf_token %}
        
        <div class="permissions-container">
            {% for content_type, permissions in permissions_by_content_type.items %}
            <div class="content-type-section">
                <div class="content-type-header">
                    <i class="fas fa-folder me-2"></i>{{ content_type }}
                    <span class="float-end">
                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                onclick="toggleSection('{{ content_type|slugify }}')">
                            <i class="fas fa-check-square me-1"></i>تحديد القسم
                        </button>
                    </span>
                </div>
                <div class="permissions-grid" id="section-{{ content_type|slugify }}">
                    {% for permission in permissions %}
                    <div class="permission-item">
                        <input type="checkbox" 
                               name="permissions" 
                               value="{{ permission.id }}" 
                               id="perm_{{ permission.id }}"
                               {% if permission.id in current_permissions %}checked{% endif %}>
                        <label for="perm_{{ permission.id }}">
                            <strong>{{ permission.name }}</strong>
                            <br><small class="text-muted">{{ permission.codename }}</small>
                        </label>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-4">
            <button type="submit" class="btn-save" id="saveBtn">
                <i class="fas fa-save me-2"></i>حفظ التغييرات
            </button>
            <button type="button" class="btn btn-success ms-2" onclick="saveWithAjax()">
                <i class="fas fa-cloud-upload-alt me-2"></i>حفظ بـ AJAX
            </button>
            <a href="{% url 'system_settings:permissions_management' %}" class="btn-cancel ms-3">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>

        <!-- Debug Info -->
        <div class="mt-3 text-center">
            <small class="text-muted">
                المجموعة: {{ group.name }} | ID: {{ group.id }}
            </small>
            <br>
            <button type="button" class="btn btn-sm btn-warning mt-2" onclick="testForm()">
                <i class="fas fa-bug me-1"></i>اختبار النموذج
            </button>
        </div>
    </form>
</div>

<script>
function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="permissions"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

function deselectAll() {
    const checkboxes = document.querySelectorAll('input[name="permissions"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

function toggleSection(sectionId) {
    const section = document.getElementById('section-' + sectionId);
    const checkboxes = section.querySelectorAll('input[type="checkbox"]');
    
    // تحقق من حالة أول checkbox لتحديد الإجراء
    const firstCheckbox = checkboxes[0];
    const shouldCheck = !firstCheckbox.checked;
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = shouldCheck;
    });
}

function testForm() {
    const form = document.getElementById('permissionsForm');
    const checkedBoxes = document.querySelectorAll('input[name="permissions"]:checked');
    const formData = new FormData(form);

    console.log('Form action:', form.action);
    console.log('Form method:', form.method);
    console.log('عدد الصلاحيات المحددة:', checkedBoxes.length);
    console.log('CSRF Token:', formData.get('csrfmiddlewaretoken'));

    alert(`تفاصيل النموذج:
- الإجراء: ${form.action}
- الطريقة: ${form.method}
- عدد الصلاحيات المحددة: ${checkedBoxes.length}
- CSRF Token موجود: ${formData.get('csrfmiddlewaretoken') ? 'نعم' : 'لا'}`);
}

// تأكيد بسيط قبل الحفظ
document.getElementById('permissionsForm').addEventListener('submit', function(e) {
    const checkedBoxes = document.querySelectorAll('input[name="permissions"]:checked');
    console.log('عدد الصلاحيات المحددة:', checkedBoxes.length);

    if (!confirm('هل أنت متأكد من حفظ هذه التغييرات؟')) {
        e.preventDefault();
        return false;
    }

    // إظهار رسالة تحميل
    const submitBtn = document.querySelector('.btn-save');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    submitBtn.disabled = true;

    return true;
});

function saveWithAjax() {
    const form = document.getElementById('permissionsForm');
    const formData = new FormData(form);
    const checkedBoxes = document.querySelectorAll('input[name="permissions"]:checked');

    console.log('بدء الحفظ بـ AJAX...');
    console.log('عدد الصلاحيات المحددة:', checkedBoxes.length);

    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (response.redirected) {
            console.log('تم التوجيه إلى:', response.url);
            window.location.href = response.url;
        } else {
            return response.text();
        }
    })
    .then(data => {
        if (data) {
            console.log('Response data:', data);
        }
        alert('تم الحفظ بنجاح!');
        window.location.href = '{% url "system_settings:permissions_management" %}';
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في الحفظ: ' + error.message);
    });
}
</script>
{% endblock %}
