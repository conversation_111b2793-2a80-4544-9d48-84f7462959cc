# Generated by Django 5.2.4 on 2025-07-14 02:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('sent', 'مرسلة'), ('delivered', 'تم التسليم'), ('read', 'مقروءة'), ('archived', 'مؤرشفة')], default='sent', max_length=10, verbose_name='الحالة')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروءة')),
                ('is_starred', models.BooleanField(default=False, verbose_name='مميزة')),
                ('is_deleted_by_sender', models.BooleanField(default=False, verbose_name='محذوفة من المرسل')),
                ('is_deleted_by_recipient', models.BooleanField(default=False, verbose_name='محذوفة من المستقبل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='message_attachments/', verbose_name='مرفق')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_messages', to=settings.AUTH_USER_MODEL, verbose_name='المستقبل')),
                ('reply_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='messaging.message', verbose_name='رد على')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to=settings.AUTH_USER_MODEL, verbose_name='المرسل')),
            ],
            options={
                'verbose_name': 'رسالة',
                'verbose_name_plural': 'الرسائل',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MessageAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='message_attachments/', verbose_name='الملف')),
                ('filename', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('file_size', models.PositiveIntegerField(verbose_name='حجم الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='messaging.message', verbose_name='الرسالة')),
            ],
            options={
                'verbose_name': 'مرفق رسالة',
                'verbose_name_plural': 'مرفقات الرسائل',
            },
        ),
        migrations.CreateModel(
            name='MessageThread',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('participants', models.ManyToManyField(to=settings.AUTH_USER_MODEL, verbose_name='المشاركين')),
            ],
            options={
                'verbose_name': 'سلسلة رسائل',
                'verbose_name_plural': 'سلاسل الرسائل',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='UserMessageSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_notifications', models.BooleanField(default=True, verbose_name='إشعارات البريد الإلكتروني')),
                ('sound_notifications', models.BooleanField(default=True, verbose_name='إشعارات صوتية')),
                ('auto_read_receipts', models.BooleanField(default=True, verbose_name='إيصالات القراءة التلقائية')),
                ('block_unknown_senders', models.BooleanField(default=False, verbose_name='حظر المرسلين غير المعروفين')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='message_settings', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'إعدادات الرسائل',
                'verbose_name_plural': 'إعدادات الرسائل',
            },
        ),
    ]
