{% extends 'base.html' %}
{% load static %}
{% load warehouse_extras %}

{% block title %}تحويل مخزون{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        min-height: 100vh;
    }

    .transfer-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        box-shadow: 0 15px 50px rgba(0, 123, 255, 0.37);
    }

    .form-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 3rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 50px rgba(0, 123, 255, 0.37);
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.8rem;
        font-weight: 800;
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .form-control, .form-select {
        border-radius: 15px;
        border: 2px solid rgba(0, 123, 255, 0.2);
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        font-size: 1rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
        outline: none;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-transfer {
        background: linear-gradient(135deg, #007bff, #6610f2);
        border: none;
        border-radius: 15px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 700;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
        min-width: 200px;
    }

    .btn-transfer:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(0, 123, 255, 0.5);
        color: white;
    }

    .btn-cancel {
        background: linear-gradient(135deg, #6c757d, #495057);
        border: none;
        border-radius: 15px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
        text-decoration: none;
        display: inline-block;
    }

    .btn-cancel:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.5);
        color: white;
        text-decoration: none;
    }

    .info-card {
        background: linear-gradient(135deg, #d1ecf1, #bee5eb);
        border-radius: 15px;
        padding: 1.5rem;
        border-left: 5px solid #17a2b8;
        margin-bottom: 2rem;
    }

    .info-card h6 {
        color: #0c5460;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .info-card ul {
        margin-bottom: 0;
        color: #0c5460;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .input-group-text {
        background: linear-gradient(135deg, #007bff, #6610f2);
        color: white;
        border: none;
        border-radius: 15px 0 0 15px;
        font-weight: 600;
    }

    .transfer-flow {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 2rem 0;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .warehouse-box {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        min-width: 200px;
        border: 2px solid #007bff;
        flex: 1;
        max-width: 300px;
    }

    .warehouse-box h6 {
        color: #007bff;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .warehouse-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
    }

    .transfer-arrow {
        font-size: 2rem;
        color: #007bff;
        animation: moveRight 2s ease-in-out infinite;
        margin: 0 1rem;
    }

    @keyframes moveRight {
        0%, 100% { transform: translateX(0); }
        50% { transform: translateX(10px); }
    }

    @media (max-width: 768px) {
        .transfer-flow {
            flex-direction: column;
        }
        
        .transfer-arrow {
            transform: rotate(90deg);
            animation: moveDown 2s ease-in-out infinite;
        }
        
        @keyframes moveDown {
            0%, 100% { transform: rotate(90deg) translateX(0); }
            50% { transform: rotate(90deg) translateX(10px); }
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Transfer Header -->
<div class="transfer-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-arrow-left-right me-2"></i>تحويل مخزون
                </h1>
                <p class="mb-0 opacity-75">تحويل مخزون بين المخازن</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group">
                    <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-light">
                        <i class="bi bi-list-ul me-1"></i>المخزون
                    </a>
                    <a href="{% url 'warehouses:dashboard' %}" class="btn btn-light">
                        <i class="bi bi-arrow-left me-1"></i>لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Info Card -->
    <div class="info-card">
        <h6><i class="bi bi-info-circle me-2"></i>معلومات التحويل</h6>
        <ul>
            <li>سيتم خصم الكمية من المخزن المصدر وإضافتها للمخزن المستهدف</li>
            <li>سيتم إنشاء حركتين: صرف من المصدر وإضافة للمستهدف</li>
            <li>سيتم حساب متوسط التكلفة تلقائياً في المخزن المستهدف</li>
            <li>تأكد من توفر الكمية المطلوبة في المخزن المصدر</li>
        </ul>
    </div>

    <!-- Transfer Form -->
    <div class="form-section">
        <h2 class="section-title">
            <i class="bi bi-arrow-repeat"></i>
            بيانات تحويل المخزون
        </h2>

        <form method="post" id="transferForm">
            {% csrf_token %}
            
            <div class="row g-4">
                <!-- From Warehouse -->
                <div class="col-md-6">
                    <label for="from_warehouse" class="form-label required-field">
                        <i class="bi bi-building"></i>من المخزن
                    </label>
                    <select name="from_warehouse" id="from_warehouse" class="form-select" required>
                        <option value="">اختر المخزن المصدر</option>
                        {% for warehouse in warehouses %}
                        <option value="{{ warehouse.id }}">{{ warehouse.name }} ({{ warehouse.code }})</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- To Warehouse -->
                <div class="col-md-6">
                    <label for="to_warehouse" class="form-label required-field">
                        <i class="bi bi-building-fill"></i>إلى المخزن
                    </label>
                    <select name="to_warehouse" id="to_warehouse" class="form-select" required>
                        <option value="">اختر المخزن المستهدف</option>
                        {% for warehouse in warehouses %}
                        <option value="{{ warehouse.id }}">{{ warehouse.name }} ({{ warehouse.code }})</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Product Selection -->
                <div class="col-md-6">
                    <label for="product" class="form-label required-field">
                        <i class="bi bi-box"></i>المنتج
                    </label>
                    <select name="product" id="product" class="form-select" required>
                        <option value="">اختر المنتج</option>
                        {% for product in products %}
                        <option value="{{ product.id }}">{{ product.name }} ({{ product.code }})</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Quantity -->
                <div class="col-md-6">
                    <label for="quantity" class="form-label required-field">
                        <i class="bi bi-123"></i>الكمية المطلوبة
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">الكمية</span>
                        <input type="number" 
                               name="quantity" 
                               id="quantity" 
                               class="form-control" 
                               step="0.001" 
                               min="0.001" 
                               placeholder="أدخل الكمية المطلوبة"
                               required>
                    </div>
                </div>

                <!-- Notes -->
                <div class="col-12">
                    <label for="notes" class="form-label">
                        <i class="bi bi-journal-text"></i>ملاحظات
                    </label>
                    <textarea name="notes" 
                              id="notes" 
                              class="form-control" 
                              rows="3" 
                              placeholder="ملاحظات إضافية حول التحويل (اختياري)"></textarea>
                </div>
            </div>

            <!-- Transfer Flow Visualization -->
            <div class="transfer-flow" id="transferFlow" style="display: none;">
                <div class="warehouse-box">
                    <h6>من</h6>
                    <div class="warehouse-name" id="fromName">-</div>
                </div>
                
                <div class="transfer-arrow">
                    <i class="bi bi-arrow-right"></i>
                </div>
                
                <div class="warehouse-box">
                    <h6>إلى</h6>
                    <div class="warehouse-name" id="toName">-</div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="text-center mt-4">
                <input type="submit" class="btn btn-transfer me-3" value="تحويل المخزون">
                <a href="{% url 'warehouses:transfer_history' %}" class="btn btn-info me-3">
                    <i class="bi bi-clock-history me-2"></i>سجل التحويلات
                </a>
                <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-cancel">
                    <i class="bi bi-x-circle me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fromSelect = document.getElementById('from_warehouse');
    const toSelect = document.getElementById('to_warehouse');
    const productSelect = document.getElementById('product');
    const quantityInput = document.getElementById('quantity');
    const transferFlow = document.getElementById('transferFlow');
    const submitBtn = document.getElementById('submitBtn');

    function updateFlow() {
        const from = fromSelect.options[fromSelect.selectedIndex];
        const to = toSelect.options[toSelect.selectedIndex];
        
        if (from.value && to.value) {
            document.getElementById('fromName').textContent = from.text;
            document.getElementById('toName').textContent = to.text;
            transferFlow.style.display = 'flex';
            
            if (from.value === to.value) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>نفس المخزن محدد';
                submitBtn.style.background = 'linear-gradient(135deg, #dc3545, #fd7e14)';
            } else {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="bi bi-arrow-left-right me-2"></i>تحويل المخزون';
                submitBtn.style.background = 'linear-gradient(135deg, #007bff, #6610f2)';
            }
        } else {
            transferFlow.style.display = 'none';
        }
    }

    fromSelect.addEventListener('change', updateFlow);
    toSelect.addEventListener('change', updateFlow);

    document.getElementById('transferForm').addEventListener('submit', function(e) {
        const quantity = parseFloat(quantityInput.value);
        const fromWarehouse = fromSelect.value;
        const toWarehouse = toSelect.value;

        if (!fromWarehouse || !toWarehouse) {
            e.preventDefault();
            alert('يرجى اختيار المخزن المصدر والمستهدف');
            return;
        }

        if (fromWarehouse === toWarehouse) {
            e.preventDefault();
            alert('لا يمكن التحويل إلى نفس المخزن');
            return;
        }

        if (quantity <= 0) {
            e.preventDefault();
            alert('يجب أن تكون الكمية أكبر من صفر');
            quantityInput.focus();
            return;
        }

        // Confirm before submitting
        const fromName = fromSelect.options[fromSelect.selectedIndex].text;
        const toName = toSelect.options[toSelect.selectedIndex].text;
        const productName = productSelect.options[productSelect.selectedIndex].text;
        
        if (!confirm(`هل أنت متأكد من تحويل ${quantity} من ${productName} من ${fromName} إلى ${toName}؟`)) {
            e.preventDefault();
            return;
        }

        // Disable submit button to prevent double submission
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري التحويل...';
    });
});
</script>
{% endblock %}
