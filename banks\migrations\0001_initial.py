# Generated by Django 5.2.4 on 2025-07-12 22:57

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Bank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم البنك')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='كود البنك')),
                ('swift_code', models.CharField(blank=True, max_length=11, null=True, verbose_name='كود SWIFT')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, null=True, verbose_name='الموقع الإلكتروني')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'بنك',
                'verbose_name_plural': 'البنوك',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BankAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_name', models.CharField(max_length=200, verbose_name='اسم الحساب')),
                ('account_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الحساب')),
                ('iban', models.CharField(blank=True, max_length=34, null=True, verbose_name='رقم IBAN')),
                ('account_type', models.CharField(choices=[('checking', 'حساب جاري'), ('savings', 'حساب توفير'), ('fixed_deposit', 'وديعة ثابتة'), ('credit', 'حساب ائتماني')], max_length=20, verbose_name='نوع الحساب')),
                ('currency', models.CharField(choices=[('SAR', 'ريال سعودي'), ('USD', 'دولار أمريكي'), ('EUR', 'يورو'), ('GBP', 'جنيه إسترليني')], default='SAR', max_length=3, verbose_name='العملة')),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الافتتاحي')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الحالي')),
                ('minimum_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأدنى للرصيد')),
                ('overdraft_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='حد السحب على المكشوف')),
                ('interest_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الفائدة (%)')),
                ('account_manager', models.CharField(blank=True, max_length=200, null=True, verbose_name='مدير الحساب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='banks.bank', verbose_name='البنك')),
            ],
            options={
                'verbose_name': 'حساب بنكي',
                'verbose_name_plural': 'الحسابات البنكية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BankTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_date', models.DateField(verbose_name='تاريخ المعاملة')),
                ('transaction_type', models.CharField(choices=[('deposit', 'إيداع'), ('withdrawal', 'سحب'), ('transfer_in', 'تحويل وارد'), ('transfer_out', 'تحويل صادر'), ('fee', 'رسوم'), ('interest', 'فوائد'), ('adjustment', 'تسوية')], max_length=20, verbose_name='نوع المعاملة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='المبلغ')),
                ('description', models.TextField(verbose_name='وصف المعاملة')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم المرجع')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('failed', 'فاشل')], default='pending', max_length=20, verbose_name='الحالة')),
                ('balance_before', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='الرصيد قبل المعاملة')),
                ('balance_after', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='الرصيد بعد المعاملة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='banks.bankaccount', verbose_name='الحساب')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'معاملة بنكية',
                'verbose_name_plural': 'المعاملات البنكية',
                'ordering': ['-transaction_date', '-created_at'],
            },
        ),
    ]
