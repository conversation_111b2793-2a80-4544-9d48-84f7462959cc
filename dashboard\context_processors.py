from .models import UserSettings
from .translations import get_translation, get_user_language

def user_settings(request):
    """إضافة إعدادات المستخدم لجميع templates"""
    if request.user.is_authenticated:
        user_settings = UserSettings.get_or_create_for_user(request.user)
        user_language = get_user_language(user_settings)

        # دالة الترجمة للاستخدام في templates
        def translate(key):
            return get_translation(key, user_language)

        return {
            'user_settings': user_settings,
            'user_language': user_language,
            'translate': translate,
        }
    return {
        'user_settings': None,
        'user_language': 'ar',
        'translate': lambda key: get_translation(key, 'ar'),
    }
