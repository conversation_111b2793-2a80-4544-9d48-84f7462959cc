# Generated by Django 5.2.4 on 2025-07-16 17:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_notifications', models.BooleanField(default=True, verbose_name='إشعارات البريد الإلكتروني')),
                ('daily_reports', models.BooleanField(default=False, verbose_name='التقارير اليومية')),
                ('push_notifications', models.BooleanField(default=True, verbose_name='الإشعارات المنبثقة')),
                ('sound_notifications', models.BooleanField(default=False, verbose_name='الأصوات')),
                ('dark_mode', models.BooleanField(default=False, verbose_name='الوضع الليلي')),
                ('font_size', models.CharField(choices=[('small', 'صغير'), ('medium', 'متوسط'), ('large', 'كبير')], default='medium', max_length=10, verbose_name='حجم الخط')),
                ('language', models.CharField(choices=[('ar', 'العربية'), ('en', 'English')], default='ar', max_length=5, verbose_name='اللغة')),
                ('timezone', models.CharField(choices=[('Africa/Cairo', 'القاهرة (GMT+2)'), ('Asia/Riyadh', 'الرياض (GMT+3)'), ('Asia/Dubai', 'دبي (GMT+4)')], default='Africa/Cairo', max_length=50, verbose_name='المنطقة الزمنية')),
                ('two_factor', models.BooleanField(default=False, verbose_name='المصادقة الثنائية')),
                ('login_alerts', models.BooleanField(default=True, verbose_name='تنبيهات تسجيل الدخول')),
                ('session_timeout', models.BooleanField(default=False, verbose_name='انتهاء الجلسة التلقائي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='settings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'إعدادات المستخدم',
                'verbose_name_plural': 'إعدادات المستخدمين',
            },
        ),
    ]
