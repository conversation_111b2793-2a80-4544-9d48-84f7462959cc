{% extends 'base.html' %}

{% block title %}فواتير الشراء - نظام أوساريك{% endblock %}

{% block extra_css %}
    <style>
        .page-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .search-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stats-summary {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            border-color: #dc3545;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.15);
        }
        
        .stat-card.received {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .stat-card.paid {
            background: linear-gradient(135deg, #cce5ff 0%, #b3d9ff 100%);
        }
        
        .stat-card.overdue {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }
        
        .invoices-table {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .table {
            border-radius: 15px;
            overflow: hidden;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            padding: 15px;
            font-weight: 600;
        }
        
        .table tbody tr {
            transition: all 0.3s ease;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: scale(1.01);
        }
        
        .table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.85rem;
        }
        
        .status-received {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .status-paid {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }
        
        .status-overdue {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .status-cancelled {
            background: linear-gradient(45deg, #6c757d, #5a6268);
            color: white;
        }
        
        .btn-action {
            padding: 8px 12px;
            border-radius: 10px;
            border: none;
            margin: 0 2px;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
        }
        
        .btn-view {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
        }
        
        .btn-edit {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: white;
        }
        
        .btn-delete {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .btn-print {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            color: white;
        }
        
        .pagination {
            justify-content: center;
            margin-top: 30px;
        }
        
        .page-link {
            border-radius: 10px;
            margin: 0 5px;
            border: 2px solid #dc3545;
            color: #dc3545;
            padding: 10px 15px;
        }
        
        .page-link:hover {
            background-color: #dc3545;
            color: white;
        }
        
        .page-item.active .page-link {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        
        .search-input {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 12px 20px;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
        
        .btn-search {
            border-radius: 25px;
            padding: 12px 25px;
            background: linear-gradient(45deg, #dc3545, #c82333);
            border: none;
            color: white;
            font-weight: 600;
        }
        
        .btn-add {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-add:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
            color: white;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-receipt"></i>
                        فواتير الشراء
                    </h1>
                    <p class="mb-0">إدارة وتتبع جميع فواتير الشراء في النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'purchases:purchase_invoice_create' %}" class="btn btn-add me-2">
                        <i class="bi bi-plus-circle"></i>
                        فاتورة شراء جديدة
                    </a>
                    <a href="{% url 'purchases:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-right"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>

        <!-- عرض الرسائل -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- إحصائيات سريعة -->
        <div class="stats-summary">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card received">
                        <h3>{{ total_invoices }}</h3>
                        <p class="mb-0">إجمالي الفواتير</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card paid">
                        <h3>{{ paid_invoices }}</h3>
                        <p class="mb-0">فواتير مدفوعة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card overdue">
                        <h3>{{ pending_invoices }}</h3>
                        <p class="mb-0">فواتير معلقة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h3>{{ total_amount|floatformat:2 }} ج.م</h3>
                        <p class="mb-0">إجمالي المبلغ</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم البحث والفلترة -->
        <div class="search-section">
            <form method="get" class="row align-items-end">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control search-input" 
                           placeholder="البحث برقم الفاتورة أو اسم المورد..." 
                           value="{{ search }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">حالة الفاتورة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="received" {% if request.GET.status == 'received' %}selected{% endif %}>مستلمة</option>
                        <option value="paid" {% if request.GET.status == 'paid' %}selected{% endif %}>مدفوعة</option>
                        <option value="overdue" {% if request.GET.status == 'overdue' %}selected{% endif %}>متأخرة</option>
                        <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>ملغية</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">المورد</label>
                    <select name="supplier" class="form-select">
                        <option value="">جميع الموردين</option>
                        {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}" {% if request.GET.supplier == supplier.id|stringformat:"s" %}selected{% endif %}>
                                {{ supplier.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-search w-100">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </form>
        </div>

        <!-- جدول الفواتير -->
        <div class="invoices-table">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>المورد</th>
                            <th>تاريخ الفاتورة</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices %}
                            <tr>
                                <td>
                                    <strong>{{ invoice.invoice_number }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ invoice.supplier.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ invoice.supplier.phone }}</small>
                                    </div>
                                </td>
                                <td>{{ invoice.invoice_date|date:"d/m/Y" }}</td>
                                <td>
                                    {% if invoice.due_date %}
                                        {{ invoice.due_date|date:"d/m/Y" }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ invoice.total_amount|floatformat:2 }} ج.م</strong>
                                </td>
                                <td>
                                    {% if invoice.status == 'received' %}
                                        <span class="status-badge status-received">مستلمة</span>
                                    {% elif invoice.status == 'paid' %}
                                        <span class="status-badge status-paid">مدفوعة</span>
                                    {% elif invoice.status == 'overdue' %}
                                        <span class="status-badge status-overdue">متأخرة</span>
                                    {% elif invoice.status == 'cancelled' %}
                                        <span class="status-badge status-cancelled">ملغية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'purchases:purchase_invoice_detail' invoice.pk %}" 
                                           class="btn btn-action btn-view" title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{% url 'purchases:purchase_invoice_edit' invoice.pk %}" 
                                           class="btn btn-action btn-edit" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{% url 'purchases:purchase_invoice_print' invoice.pk %}" 
                                           class="btn btn-action btn-print" title="طباعة">
                                            <i class="bi bi-printer"></i>
                                        </a>
                                        <button type="button" class="btn btn-action btn-delete" 
                                                onclick="confirmDelete('{{ invoice.invoice_number }}', '{% url 'purchases:purchase_invoice_delete' invoice.pk %}')" 
                                                title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% empty %}
                            <tr>
                                <td colspan="7" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="bi bi-receipt" style="font-size: 3rem;"></i>
                                        <h5 class="mt-3">لا توجد فواتير شراء</h5>
                                        <p>لم يتم العثور على أي فواتير شراء مطابقة للبحث</p>
                                        <a href="{% url 'purchases:purchase_invoice_create' %}" class="btn btn-add">
                                            <i class="bi bi-plus-circle"></i>
                                            إضافة فاتورة شراء جديدة
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- الترقيم -->
            {% if invoices.has_other_pages %}
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination">
                        {% if invoices.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ invoices.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}

                        {% for num in invoices.paginator.page_range %}
                            {% if invoices.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}">
                                        {{ num }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if invoices.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ invoices.next_page_number }}{% if search %}&search={{ search }}{% endif %}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        function confirmDelete(invoiceNumber, deleteUrl) {
            if (confirm(`هل أنت متأكد من حذف فاتورة الشراء "${invoiceNumber}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                window.location.href = deleteUrl;
            }
        }
    </script>
{% endblock %}
