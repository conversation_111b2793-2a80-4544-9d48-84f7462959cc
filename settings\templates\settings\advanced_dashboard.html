<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات والخدمات المتقدمة - أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .dashboard-container {
            padding: 2rem 0;
        }
        
        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .dashboard-title {
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #718096;
            font-weight: 500;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .content-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f7fafc;
        }
        
        .content-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .log-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .log-item:hover {
            background: #f7fafc;
        }
        
        .log-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.75rem;
            font-size: 0.9rem;
            color: white;
        }
        
        .log-content {
            flex: 1;
        }
        
        .log-message {
            font-weight: 500;
            color: #2d3748;
            margin-bottom: 0.25rem;
        }
        
        .log-meta {
            font-size: 0.8rem;
            color: #718096;
        }
        
        .user-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .user-item:hover {
            background: #f7fafc;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-left: 0.75rem;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-weight: 500;
            color: #2d3748;
            margin-bottom: 0.25rem;
        }
        
        .user-meta {
            font-size: 0.8rem;
            color: #718096;
        }
        
        .module-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .module-item:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .module-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .module-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        .module-name {
            font-weight: 500;
            color: #2d3748;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-enabled {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 12px;
            text-decoration: none;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container dashboard-container">
        <!-- رأس لوحة التحكم -->
        <div class="dashboard-header animate__animated animate__fadeInDown">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="dashboard-title">الإعدادات والخدمات المتقدمة</h1>
                    <p class="text-muted mb-0">إدارة شاملة لجميع إعدادات النظام والمستخدمين والصلاحيات</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="/" class="btn btn-outline-primary">
                        <i class="bi bi-house"></i> العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="stats-grid animate__animated animate__fadeInUp">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stat-number">{{ stats.total_users }}</div>
                <div class="stat-label">إجمالي المستخدمين</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #48bb78, #38a169);">
                    <i class="bi bi-person-check"></i>
                </div>
                <div class="stat-number">{{ stats.active_users }}</div>
                <div class="stat-label">المستخدمين النشطين</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #ed8936, #dd6b20);">
                    <i class="bi bi-shield-check"></i>
                </div>
                <div class="stat-number">{{ stats.total_roles }}</div>
                <div class="stat-label">الأدوار والصلاحيات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #9f7aea, #805ad5);">
                    <i class="bi bi-grid-3x3-gap"></i>
                </div>
                <div class="stat-number">{{ stats.enabled_modules }}</div>
                <div class="stat-label">الوحدات المفعلة</div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-grid animate__animated animate__fadeInUp animate__delay-1s">
            <!-- سجلات النظام الأخيرة -->
            <div class="content-card">
                <div class="content-header">
                    <h3 class="content-title">
                        <i class="bi bi-journal-text"></i>
                        سجلات النظام الأخيرة
                    </h3>
                    <a href="#" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>

                <div class="logs-list">
                    {% for log in recent_logs %}
                    <div class="log-item">
                        <div class="log-icon" style="background:
                            {% if log.level == 'error' %}#e53e3e
                            {% elif log.level == 'warning' %}#dd6b20
                            {% elif log.level == 'info' %}#3182ce
                            {% else %}#38a169{% endif %};">
                            <i class="bi bi-{% if log.level == 'error' %}exclamation-triangle{% elif log.level == 'warning' %}exclamation-circle{% else %}info-circle{% endif %}"></i>
                        </div>
                        <div class="log-content">
                            <div class="log-message">{{ log.message|truncatechars:60 }}</div>
                            <div class="log-meta">
                                {% if log.user %}{{ log.user.username }}{% else %}النظام{% endif %} •
                                {{ log.created_at|timesince }} مضت
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-journal" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                        لا توجد سجلات حديثة
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- المستخدمين النشطين -->
            <div class="content-card">
                <div class="content-header">
                    <h3 class="content-title">
                        <i class="bi bi-people"></i>
                        المستخدمين النشطين
                    </h3>
                    <a href="#" class="btn btn-sm btn-outline-primary">إدارة المستخدمين</a>
                </div>

                <div class="users-list">
                    {% for user in active_users %}
                    <div class="user-item">
                        <div class="user-avatar">
                            {% if user.first_name %}{{ user.first_name.0|upper }}{% elif user.username %}{{ user.username.0|upper }}{% else %}U{% endif %}
                        </div>
                        <div class="user-info">
                            <div class="user-name">{{ user.get_full_name|default:user.username }}</div>
                            <div class="user-meta">
                                {% if user.profile.department %}{{ user.profile.get_department_display }}{% endif %}
                                {% if user.last_login %} • آخر دخول {{ user.last_login|timesince }} مضت{% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-person" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                        لا يوجد مستخدمين نشطين
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="quick-actions animate__animated animate__fadeInUp animate__delay-2s">
            <a href="#" class="action-btn">
                <i class="bi bi-gear"></i>
                إعدادات النظام
            </a>

            <a href="#" class="action-btn">
                <i class="bi bi-people"></i>
                إدارة المستخدمين
            </a>

            <a href="#" class="action-btn">
                <i class="bi bi-shield-check"></i>
                الأدوار والصلاحيات
            </a>

            <a href="#" class="action-btn">
                <i class="bi bi-journal-text"></i>
                سجلات النظام
            </a>

            <a href="#" class="action-btn">
                <i class="bi bi-cloud-download"></i>
                النسخ الاحتياطي
            </a>

            <a href="#" class="action-btn">
                <i class="bi bi-envelope"></i>
                إعدادات البريد
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الإحصائيات كل 30 ثانية
        setInterval(() => {
            // يمكن إضافة AJAX لتحديث الإحصائيات
        }, 30000);

        // تأثيرات تفاعلية
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
