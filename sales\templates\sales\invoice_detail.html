{% extends 'base.html' %}
{% load static %}

{% block title %}فاتورة {{ invoice.invoice_number }}{% endblock %}

{% block extra_css %}
<style>
    .invoice-detail {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .invoice-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .invoice-body {
        padding: 30px;
    }
    
    .info-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .status-badge {
        font-size: 0.9rem;
        padding: 8px 16px;
        border-radius: 20px;
    }
    
    .status-draft { background: #6c757d; }
    .status-sent { background: #17a2b8; }
    .status-paid { background: #28a745; }
    .status-overdue { background: #dc3545; }
    .status-cancelled { background: #6c757d; }
    
    .items-table {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .total-section {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 25px;
        border-radius: 10px;
        margin-top: 20px;
    }
    
    .action-buttons {
        margin-top: 30px;
        text-align: center;
    }
    
    .btn-action {
        margin: 5px;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    }
    
    .print-section {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="invoice-detail">
                <!-- رأس الفاتورة -->
                <div class="invoice-header">
                    <h1 class="mb-3">
                        <i class="bi bi-receipt-cutoff"></i>
                        فاتورة رقم {{ invoice.invoice_number }}
                    </h1>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>{{ invoice.get_invoice_type_display }}</h5>
                        </div>
                        <div class="col-md-6">
                            <span class="status-badge status-{{ invoice.status }}">
                                {{ invoice.get_status_display }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="invoice-body">
                    <!-- معلومات الفاتورة -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-section">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-person-circle"></i>
                                    معلومات العميل
                                </h5>
                                <p><strong>الاسم:</strong> {{ invoice.customer.name }}</p>
                                <p><strong>النوع:</strong> {{ invoice.customer.get_customer_type_display }}</p>
                                {% if invoice.customer.phone %}
                                    <p><strong>الهاتف:</strong> {{ invoice.customer.phone }}</p>
                                {% endif %}
                                {% if invoice.customer.address %}
                                    <p><strong>العنوان:</strong> {{ invoice.customer.address }}</p>
                                {% endif %}
                                {% if invoice.customer.tax_number %}
                                    <p><strong>الرقم الضريبي:</strong> {{ invoice.customer.tax_number }}</p>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-section">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-calendar-event"></i>
                                    تفاصيل الفاتورة
                                </h5>
                                <p><strong>تاريخ الفاتورة:</strong> {{ invoice.invoice_date }}</p>
                                <p><strong>تاريخ الاستحقاق:</strong> {{ invoice.due_date }}</p>
                                <p><strong>طريقة الدفع:</strong> {{ invoice.get_payment_method_display }}</p>
                                {% if invoice.representative %}
                                    <p><strong>المندوب:</strong> {{ invoice.representative.full_name }}</p>
                                {% endif %}
                                {% if invoice.vehicle %}
                                    <p><strong>السيارة:</strong> {{ invoice.vehicle.plate_number }}</p>
                                {% endif %}
                                {% if invoice.order %}
                                    <p><strong>أمر البيع:</strong> {{ invoice.order.order_number }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- حالة الطباعة -->
                    {% if invoice.is_printed %}
                        <div class="print-section">
                            <i class="bi bi-printer text-success"></i>
                            <strong class="text-success">تم طباعة هذه الفاتورة</strong>
                        </div>
                    {% endif %}

                    <!-- عناصر الفاتورة -->
                    <div class="items-table">
                        <table class="table table-striped mb-0">
                            <thead class="table-primary">
                                <tr>
                                    <th>#</th>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>الوحدة</th>
                                    <th>سعر الوحدة</th>
                                    <th>الخصم %</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items %}
                                    <tr>
                                        <td>{{ forloop.counter }}</td>
                                        <td>
                                            <strong>{{ item.product.name }}</strong><br>
                                            <small class="text-muted">{{ item.product.code }}</small>
                                        </td>
                                        <td>{{ item.quantity }}</td>
                                        <td>{{ item.product.unit }}</td>
                                        <td>{{ item.unit_price }} ج.م</td>
                                        <td>{{ item.discount_percentage }}%</td>
                                        <td><strong>{{ item.total_price }} ج.م</strong></td>
                                    </tr>
                                {% empty %}
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">لا توجد عناصر في هذه الفاتورة</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- إجمالي الفاتورة -->
                    <div class="total-section">
                        <div class="row">
                            <div class="col-md-8">
                                <h5>
                                    <i class="bi bi-calculator"></i>
                                    ملخص الفاتورة
                                </h5>
                                {% if invoice.notes %}
                                    <p class="mb-0"><strong>ملاحظات:</strong> {{ invoice.notes }}</p>
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <span>{{ invoice.subtotal }} ج.م</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الخصم ({{ invoice.discount_percentage }}%):</span>
                                    <span>{{ invoice.calculated_discount_amount }} ج.م</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الضريبة ({{ invoice.tax_percentage }}%):</span>
                                    <span>{{ invoice.tax_amount }} ج.م</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong style="font-size: 1.2rem;">الإجمالي النهائي:</strong>
                                    <strong style="font-size: 1.2rem;">{{ invoice.total_amount }} ج.م</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار العمليات -->
                    <div class="action-buttons">
                        <a href="{% url 'sales:invoice_print_pdf' invoice.pk %}" class="btn btn-danger btn-action" target="_blank">
                            <i class="bi bi-file-earmark-pdf"></i>
                            طباعة PDF
                        </a>
                        
                        <a href="{% url 'sales:invoice_edit' invoice.pk %}" class="btn btn-warning btn-action">
                            <i class="bi bi-pencil-square"></i>
                            تعديل
                        </a>
                        
                        {% if invoice.status == 'draft' %}
                            <button class="btn btn-success btn-action" onclick="updateStatus('sent')">
                                <i class="bi bi-send"></i>
                                إرسال
                            </button>
                        {% endif %}
                        
                        {% if invoice.status == 'sent' %}
                            <button class="btn btn-success btn-action" onclick="updateStatus('paid')">
                                <i class="bi bi-check-circle"></i>
                                تأكيد الدفع
                            </button>
                        {% endif %}
                        
                        <a href="{% url 'sales:invoice_list' %}" class="btn btn-secondary btn-action">
                            <i class="bi bi-arrow-left"></i>
                            العودة للقائمة
                        </a>
                        
                        {% if invoice.status == 'draft' %}
                            <button class="btn btn-outline-danger btn-action" onclick="confirmDelete()">
                                <i class="bi bi-trash"></i>
                                حذف
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateStatus(newStatus) {
    if (confirm('هل أنت متأكد من تغيير حالة الفاتورة؟')) {
        // يمكن إضافة AJAX call هنا لتحديث الحالة
        alert('سيتم تطوير هذه الوظيفة قريباً');
    }
}

function confirmDelete() {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        window.location.href = "{% url 'sales:invoice_delete' invoice.pk %}";
    }
}

// طباعة الصفحة
function printInvoice() {
    window.print();
}

// اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        printInvoice();
    }
});
</script>
{% endblock %}
