<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير أداء المناديب - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .filter-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .performance-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #ff9a9e;
        }
        
        .performance-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .rep-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .rep-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
        }
        
        .rep-rank {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .performance-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .stat-item:hover {
            background: #e9ecef;
            transform: scale(1.05);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .progress-section {
            margin-top: 20px;
        }
        
        .progress-item {
            margin-bottom: 15px;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }
        
        .progress {
            height: 10px;
            border-radius: 10px;
            background: #e9ecef;
        }
        
        .progress-bar {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            border-radius: 10px;
        }
        
        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            text-align: center;
            border-bottom: 3px solid #ff9a9e;
            padding-bottom: 15px;
        }
        
        .summary-stats {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
        }
        
        .summary-item {
            text-align: center;
            padding: 25px;
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: white;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(255, 154, 158, 0.3);
        }
        
        .summary-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .summary-label {
            font-size: 1.1rem;
            font-weight: 500;
            opacity: 0.9;
        }
        
        .btn-export {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 154, 158, 0.3);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-person-badge"></i>
                        تقرير أداء المناديب
                    </h1>
                    <p class="mb-0">تحليل شامل لأداء المناديب ومبيعاتهم</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-export me-2" onclick="exportReport()">
                        <i class="bi bi-download"></i>
                        تصدير التقرير
                    </button>
                    <a href="{% url 'sales:reports_dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
        </div>

        <!-- قسم الفلترة -->
        <div class="filter-section">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">المندوب</label>
                    <select name="representative" class="form-select">
                        <option value="">جميع المناديب</option>
                        {% for rep in all_representatives %}
                            <option value="{{ rep.id }}" {% if representative_id == rep.id %}selected{% endif %}>
                                {{ rep.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                        تطبيق الفلتر
                    </button>
                </div>
            </form>
        </div>

        <!-- الإحصائيات العامة -->
        <div class="summary-stats">
            <h3 class="section-title">الإحصائيات العامة</h3>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-number">{{ total_representatives }}</div>
                    <div class="summary-label">إجمالي المناديب</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">{{ total_sales|floatformat:0 }}</div>
                    <div class="summary-label">إجمالي المبيعات (ج.م)</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">{{ total_invoices }}</div>
                    <div class="summary-label">إجمالي الفواتير</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">{{ average_sale|floatformat:0 }}</div>
                    <div class="summary-label">متوسط المبيعات (ج.م)</div>
                </div>
            </div>
        </div>

        <!-- أداء المناديب -->
        {% if representatives_performance %}
            <div class="row">
                {% for rep_data in representatives_performance %}
                    <div class="col-lg-6">
                        <div class="performance-card">
                            <div class="rep-header">
                                <div class="rep-name">{{ rep_data.representative.name }}</div>
                                <div class="rep-rank">المرتبة #{{ forloop.counter }}</div>
                            </div>
                            
                            <div class="performance-stats">
                                <div class="stat-item">
                                    <div class="stat-number">{{ rep_data.total_sales|floatformat:0 }}</div>
                                    <div class="stat-label">إجمالي المبيعات (ج.م)</div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-number">{{ rep_data.total_invoices }}</div>
                                    <div class="stat-label">عدد الفواتير</div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-number">{{ rep_data.average_sale|floatformat:0 }}</div>
                                    <div class="stat-label">متوسط الفاتورة (ج.م)</div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-number">{{ rep_data.customers_count }}</div>
                                    <div class="stat-label">عدد العملاء</div>
                                </div>
                            </div>
                            
                            <div class="progress-section">
                                <div class="progress-item">
                                    <div class="progress-label">
                                        <span>نسبة المبيعات من الإجمالي</span>
                                        <span>{{ rep_data.sales_percentage|floatformat:1 }}%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: {{ rep_data.sales_percentage }}%"></div>
                                    </div>
                                </div>
                                
                                <div class="progress-item">
                                    <div class="progress-label">
                                        <span>نسبة الفواتير من الإجمالي</span>
                                        <span>{{ rep_data.invoices_percentage|floatformat:1 }}%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: {{ rep_data.invoices_percentage }}%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="{% url 'sales:representative_detail_report' rep_data.representative.id %}?date_from={{ date_from }}&date_to={{ date_to }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    تفاصيل الأداء
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- رسم بياني للمقارنة -->
            <div class="chart-container">
                <h3 class="section-title">مقارنة أداء المناديب</h3>
                <canvas id="performanceChart" width="400" height="200"></canvas>
            </div>

            <!-- رسم بياني للمبيعات الشهرية -->
            <div class="chart-container">
                <h3 class="section-title">المبيعات الشهرية</h3>
                <canvas id="monthlyChart" width="400" height="200"></canvas>
            </div>
        {% else %}
            <div class="empty-state">
                <i class="bi bi-person-badge"></i>
                <h3>لا توجد بيانات</h3>
                <p>لم يتم العثور على بيانات أداء للمناديب في الفترة المحددة</p>
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.performance-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });

            // رسم بياني للمقارنة
            {% if representatives_performance %}
                const performanceCtx = document.getElementById('performanceChart').getContext('2d');
                const performanceChart = new Chart(performanceCtx, {
                    type: 'bar',
                    data: {
                        labels: [{% for rep_data in representatives_performance %}'{{ rep_data.representative.name }}'{% if not forloop.last %},{% endif %}{% endfor %}],
                        datasets: [{
                            label: 'المبيعات (ج.م)',
                            data: [{% for rep_data in representatives_performance %}{{ rep_data.total_sales }}{% if not forloop.last %},{% endif %}{% endfor %}],
                            backgroundColor: 'rgba(255, 154, 158, 0.8)',
                            borderColor: 'rgba(255, 154, 158, 1)',
                            borderWidth: 2,
                            borderRadius: 10
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value.toLocaleString() + ' ج.م';
                                    }
                                }
                            }
                        }
                    }
                });

                // رسم بياني للمبيعات الشهرية
                const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
                const monthlyChart = new Chart(monthlyCtx, {
                    type: 'line',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                        datasets: [
                            {% for rep_data in representatives_performance|slice:":3" %}
                            {
                                label: '{{ rep_data.representative.name }}',
                                data: [
                                    {% for month in rep_data.monthly_sales %}{{ month }}{% if not forloop.last %},{% endif %}{% endfor %}
                                ],
                                borderColor: `hsl({{ forloop.counter0|mul:60 }}, 70%, 60%)`,
                                backgroundColor: `hsla({{ forloop.counter0|mul:60 }}, 70%, 60%, 0.1)`,
                                borderWidth: 3,
                                fill: true,
                                tension: 0.4
                            }{% if not forloop.last %},{% endif %}
                            {% endfor %}
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value.toLocaleString() + ' ج.م';
                                    }
                                }
                            }
                        }
                    }
                });
            {% endif %}
        });

        function exportReport() {
            // تصدير التقرير إلى PDF أو Excel
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'pdf');
            window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
        }
    </script>
</body>
</html>
