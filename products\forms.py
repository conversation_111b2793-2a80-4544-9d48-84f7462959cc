from django import forms
from .models import Product

class ProductForm(forms.ModelForm):
    class Meta:
        model = Product
        fields = ['name', 'sku', 'quantity', 'min_quantity', 'price']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المنتج'
            }),
            'sku': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رمز المنتج (SKU)'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'الكمية المتوفرة',
                'min': '0'
            }),
            'min_quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'الحد الأدنى للكمية',
                'min': '0'
            }),
            'price': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'السعر',
                'step': '0.01',
                'min': '0'
            }),
        }
        labels = {
            'name': 'اسم المنتج',
            'sku': 'رمز المنتج (SKU)',
            'quantity': 'الكمية المتوفرة',
            'min_quantity': 'الحد الأدنى للكمية',
            'price': 'السعر (ر.س)',
        }

    def clean_sku(self):
        sku = self.cleaned_data.get('sku')
        if sku:
            # Check if SKU already exists (excluding current instance if editing)
            existing = Product.objects.filter(sku=sku)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise forms.ValidationError('رمز المنتج موجود بالفعل. يرجى اختيار رمز آخر.')
        
        return sku

    def clean_min_quantity(self):
        min_quantity = self.cleaned_data.get('min_quantity')
        if min_quantity is not None and min_quantity < 0:
            raise forms.ValidationError('الحد الأدنى للكمية يجب أن يكون صفر أو أكثر.')
        return min_quantity

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity < 0:
            raise forms.ValidationError('الكمية يجب أن تكون صفر أو أكثر.')
        return quantity

    def clean_price(self):
        price = self.cleaned_data.get('price')
        if price is not None and price <= 0:
            raise forms.ValidationError('السعر يجب أن يكون أكبر من صفر.')
        return price
