from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

class ReportCategory(models.Model):
    """فئات التقارير"""
    CATEGORY_TYPES = [
        ('financial', 'تقارير مالية'),
        ('sales', 'تقارير المبيعات'),
        ('purchases', 'تقارير المشتريات'),
        ('inventory', 'تقارير المخزون'),
        ('hr', 'تقارير الموارد البشرية'),
        ('accounting', 'تقارير محاسبية'),
        ('assets', 'تقارير الأصول'),
        ('branches', 'تقارير الفروع'),
        ('custom', 'تقارير مخصصة'),
    ]

    name = models.CharField(max_length=100, verbose_name="اسم الفئة")
    category_type = models.CharField(max_length=20, choices=CATEGORY_TYPES, verbose_name="نوع الفئة")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    icon = models.CharField(max_length=50, default='bi-file-earmark-text', verbose_name="أيقونة")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    order = models.PositiveIntegerField(default=0, verbose_name="الترتيب")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "فئة تقرير"
        verbose_name_plural = "فئات التقارير"
        ordering = ['order', 'name']

    def __str__(self):
        return self.name

class Report(models.Model):
    """التقارير"""
    REPORT_TYPES = [
        ('table', 'جدول'),
        ('chart', 'رسم بياني'),
        ('summary', 'ملخص'),
        ('detailed', 'تفصيلي'),
        ('dashboard', 'لوحة تحكم'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('draft', 'مسودة'),
    ]

    category = models.ForeignKey(ReportCategory, related_name='reports', on_delete=models.CASCADE, verbose_name="الفئة")
    name = models.CharField(max_length=200, verbose_name="اسم التقرير")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES, verbose_name="نوع التقرير")
    query = models.TextField(verbose_name="استعلام التقرير")
    parameters = models.JSONField(default=dict, blank=True, verbose_name="معاملات التقرير")
    template_name = models.CharField(max_length=200, blank=True, null=True, verbose_name="اسم القالب")
    is_public = models.BooleanField(default=True, verbose_name="عام")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تقرير"
        verbose_name_plural = "التقارير"
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.category.name} - {self.name}"

class ReportExecution(models.Model):
    """تنفيذ التقارير"""
    STATUS_CHOICES = [
        ('pending', 'معلق'),
        ('running', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('failed', 'فشل'),
    ]

    report = models.ForeignKey(Report, related_name='executions', on_delete=models.CASCADE, verbose_name="التقرير")
    executed_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="نفذ بواسطة")
    parameters = models.JSONField(default=dict, blank=True, verbose_name="المعاملات المستخدمة")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="الحالة")
    start_time = models.DateTimeField(auto_now_add=True, verbose_name="وقت البداية")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="وقت الانتهاء")
    execution_time = models.DurationField(null=True, blank=True, verbose_name="مدة التنفيذ")
    result_count = models.PositiveIntegerField(default=0, verbose_name="عدد النتائج")
    error_message = models.TextField(blank=True, null=True, verbose_name="رسالة الخطأ")
    file_path = models.CharField(max_length=500, blank=True, null=True, verbose_name="مسار الملف")

    class Meta:
        verbose_name = "تنفيذ تقرير"
        verbose_name_plural = "تنفيذات التقارير"
        ordering = ['-start_time']

    def __str__(self):
        return f"{self.report.name} - {self.start_time.strftime('%Y-%m-%d %H:%M')}"

    def calculate_execution_time(self):
        """حساب مدة التنفيذ"""
        if self.end_time:
            self.execution_time = self.end_time - self.start_time
            self.save()

class SavedReport(models.Model):
    """التقارير المحفوظة"""
    report = models.ForeignKey(Report, on_delete=models.CASCADE, verbose_name="التقرير")
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    name = models.CharField(max_length=200, verbose_name="اسم التقرير المحفوظ")
    parameters = models.JSONField(default=dict, verbose_name="المعاملات")
    is_favorite = models.BooleanField(default=False, verbose_name="مفضل")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الحفظ")

    class Meta:
        verbose_name = "تقرير محفوظ"
        verbose_name_plural = "التقارير المحفوظة"
        unique_together = ['report', 'user', 'name']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.user.username}"

class ReportSchedule(models.Model):
    """جدولة التقارير"""
    FREQUENCY_CHOICES = [
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('quarterly', 'ربع سنوي'),
        ('yearly', 'سنوي'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('paused', 'متوقف مؤقتاً'),
    ]

    report = models.ForeignKey(Report, related_name='schedules', on_delete=models.CASCADE, verbose_name="التقرير")
    name = models.CharField(max_length=200, verbose_name="اسم الجدولة")
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, verbose_name="التكرار")
    parameters = models.JSONField(default=dict, verbose_name="المعاملات")
    recipients = models.JSONField(default=list, verbose_name="المستقبلين")
    next_run = models.DateTimeField(verbose_name="التشغيل التالي")
    last_run = models.DateTimeField(null=True, blank=True, verbose_name="آخر تشغيل")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "جدولة تقرير"
        verbose_name_plural = "جدولة التقارير"
        ordering = ['next_run']

    def __str__(self):
        return f"{self.name} - {self.get_frequency_display()}"
