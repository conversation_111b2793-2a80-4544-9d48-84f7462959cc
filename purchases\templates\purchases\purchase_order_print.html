<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة أمر شراء رقم {{ order.order_number }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
            .page-break { page-break-before: always; }
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 2px solid #ddd;
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .company-name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .company-info {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .invoice-title {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-bottom: 3px solid #ffc107;
        }
        
        .invoice-number {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }
        
        .invoice-date {
            color: #666;
            font-size: 1.1rem;
        }
        
        .invoice-details {
            padding: 30px;
        }
        
        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .detail-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        
        .section-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #ffc107;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .detail-label {
            font-weight: 600;
            color: #555;
        }
        
        .detail-value {
            font-weight: 600;
            color: #333;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border: 1px solid #ddd;
        }
        
        .items-table th {
            background: #ffc107;
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: 700;
            border: 1px solid #e0a800;
        }
        
        .items-table td {
            padding: 12px 10px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        .items-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .product-name {
            text-align: right;
            font-weight: 600;
        }
        
        .product-code {
            font-size: 0.9rem;
            color: #666;
        }
        
        .totals-section {
            background: #f8f9fa;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }
        
        .total-row:last-child {
            border-bottom: none;
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 2px solid #ffc107;
        }
        
        .total-label {
            font-weight: 600;
        }
        
        .total-value {
            font-weight: 700;
        }
        
        .notes-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            background: #f9f9f9;
        }
        
        .footer-section {
            text-align: center;
            padding: 20px;
            border-top: 2px solid #ffc107;
            color: #666;
        }
        
        .signature-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }
        
        .signature-box {
            text-align: center;
            border: 1px solid #ddd;
            padding: 40px 20px;
            border-radius: 8px;
        }
        
        .signature-title {
            font-weight: 700;
            margin-bottom: 30px;
            color: #333;
        }
        
        .signature-line {
            border-top: 2px solid #333;
            margin-top: 30px;
            padding-top: 10px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            margin-top: 10px;
        }
        
        .status-draft { background: #e2e3e5; color: #495057; }
        .status-sent { background: #cce5ff; color: #004085; }
        .status-confirmed { background: #d1ecf1; color: #0c5460; }
        .status-received { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .print-info {
            font-size: 0.8rem;
            color: #999;
            text-align: center;
            margin-top: 20px;
        }
        
        .print-button {
            background: #ffc107;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 20px auto;
            display: block;
        }
        
        .print-button:hover {
            background: #e0a800;
        }
    </style>
</head>
<body>
    <div class="no-print">
        <button class="print-button" onclick="window.print()">
            طباعة أمر الشراء
        </button>
    </div>

    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="company-name">شركة أوساريك للتجارة</div>
            <div class="company-info">
                العنوان: شارع التحرير، القاهرة، مصر<br>
                الهاتف: 0*********0 | البريد الإلكتروني: <EMAIL><br>
                الرقم الضريبي: *********
            </div>
        </div>

        <!-- عنوان أمر الشراء -->
        <div class="invoice-title">
            <div class="invoice-number">أمر شراء رقم {{ order.order_number }}</div>
            <div class="invoice-date">تاريخ الأمر: {{ order.order_date|date:"Y/m/d" }}</div>
            <span class="status-badge status-{{ order.status }}">
                {% if order.status == 'draft' %}
                    مسودة
                {% elif order.status == 'sent' %}
                    مرسل
                {% elif order.status == 'confirmed' %}
                    مؤكد
                {% elif order.status == 'received' %}
                    مستلم
                {% elif order.status == 'cancelled' %}
                    ملغي
                {% endif %}
            </span>
        </div>

        <!-- تفاصيل أمر الشراء -->
        <div class="invoice-details">
            <div class="details-grid">
                <!-- بيانات المورد -->
                <div class="detail-section">
                    <div class="section-title">بيانات المورد</div>
                    
                    <div class="detail-row">
                        <span class="detail-label">اسم المورد:</span>
                        <span class="detail-value">{{ order.supplier.name }}</span>
                    </div>
                    
                    {% if order.supplier.phone %}
                        <div class="detail-row">
                            <span class="detail-label">الهاتف:</span>
                            <span class="detail-value">{{ order.supplier.phone }}</span>
                        </div>
                    {% endif %}
                    
                    {% if order.supplier.email %}
                        <div class="detail-row">
                            <span class="detail-label">البريد الإلكتروني:</span>
                            <span class="detail-value">{{ order.supplier.email }}</span>
                        </div>
                    {% endif %}
                    
                    {% if order.supplier.address %}
                        <div class="detail-row">
                            <span class="detail-label">العنوان:</span>
                            <span class="detail-value">{{ order.supplier.address }}</span>
                        </div>
                    {% endif %}
                </div>

                <!-- تفاصيل أمر الشراء -->
                <div class="detail-section">
                    <div class="section-title">تفاصيل أمر الشراء</div>
                    
                    <div class="detail-row">
                        <span class="detail-label">تاريخ الأمر:</span>
                        <span class="detail-value">{{ order.order_date|date:"Y/m/d" }}</span>
                    </div>
                    
                    {% if order.expected_delivery_date %}
                        <div class="detail-row">
                            <span class="detail-label">تاريخ التسليم المتوقع:</span>
                            <span class="detail-value">{{ order.expected_delivery_date|date:"Y/m/d" }}</span>
                        </div>
                    {% endif %}
                    
                    {% if order.reference_number %}
                        <div class="detail-row">
                            <span class="detail-label">الرقم المرجعي:</span>
                            <span class="detail-value">{{ order.reference_number }}</span>
                        </div>
                    {% endif %}
                    
                    {% if order.priority %}
                        <div class="detail-row">
                            <span class="detail-label">الأولوية:</span>
                            <span class="detail-value">
                                {% if order.priority == 'high' %}
                                    عالية
                                {% elif order.priority == 'medium' %}
                                    متوسطة
                                {% elif order.priority == 'low' %}
                                    منخفضة
                                {% endif %}
                            </span>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- جدول الأصناف -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="40%">المنتج</th>
                        <th width="15%">الكمية</th>
                        <th width="15%">السعر</th>
                        <th width="10%">الخصم</th>
                        <th width="15%">الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in order.items.all %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td class="product-name">
                                <strong>{{ item.product.name }}</strong><br>
                                <span class="product-code">كود: {{ item.product.code }}</span>
                            </td>
                            <td>{{ item.quantity }} {{ item.product.unit }}</td>
                            <td>{{ item.unit_price|floatformat:2 }} ج.م</td>
                            <td>{{ item.discount_percentage|default:0 }}%</td>
                            <td>{{ item.total_price|floatformat:2 }} ج.م</td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="6" style="text-align: center; color: #999;">
                                لا توجد أصناف في هذا الأمر
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- الإجماليات -->
            <div class="totals-section">
                <div class="total-row">
                    <span class="total-label">المجموع الفرعي:</span>
                    <span class="total-value">{{ order.subtotal|floatformat:2 }} ج.م</span>
                </div>
                
                <div class="total-row">
                    <span class="total-label">إجمالي الخصم:</span>
                    <span class="total-value">{{ order.total_discount|floatformat:2 }} ج.م</span>
                </div>
                
                <div class="total-row">
                    <span class="total-label">الإجمالي النهائي:</span>
                    <span class="total-value">{{ order.total_amount|floatformat:2 }} ج.م</span>
                </div>
            </div>

            <!-- عنوان التسليم والملاحظات -->
            {% if order.delivery_address or order.special_instructions or order.notes %}
                <div class="notes-section">
                    <div class="section-title">تفاصيل إضافية</div>
                    
                    {% if order.delivery_address %}
                        <div class="detail-row">
                            <span class="detail-label">عنوان التسليم:</span>
                            <span class="detail-value">{{ order.delivery_address }}</span>
                        </div>
                    {% endif %}
                    
                    {% if order.special_instructions %}
                        <div class="detail-row">
                            <span class="detail-label">تعليمات خاصة:</span>
                            <span class="detail-value">{{ order.special_instructions }}</span>
                        </div>
                    {% endif %}
                    
                    {% if order.notes %}
                        <div class="detail-row">
                            <span class="detail-label">ملاحظات:</span>
                            <span class="detail-value">{{ order.notes }}</span>
                        </div>
                    {% endif %}
                </div>
            {% endif %}

            <!-- قسم التوقيعات -->
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-title">المشتري</div>
                    <div class="signature-line">التوقيع والتاريخ</div>
                </div>
                
                <div class="signature-box">
                    <div class="signature-title">المورد</div>
                    <div class="signature-line">التوقيع والتاريخ</div>
                </div>
                
                <div class="signature-box">
                    <div class="signature-title">المستلم</div>
                    <div class="signature-line">التوقيع والتاريخ</div>
                </div>
            </div>
        </div>

        <!-- تذييل الفاتورة -->
        <div class="footer-section">
            <p><strong>شروط وأحكام:</strong></p>
            <p>
                • يرجى مراجعة الأصناف والكميات قبل التوقيع<br>
                • أي تغيير في الأمر يتطلب موافقة كتابية<br>
                • التسليم حسب التاريخ المحدد في الأمر
            </p>
            
            <div class="print-info">
                تم طباعة هذا الأمر في: {{ "now"|date:"Y/m/d H:i" }}
            </div>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
