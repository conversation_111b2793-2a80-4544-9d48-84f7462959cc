#!/usr/bin/env python
import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from warehouses.models import InventoryItem
from manufacturing.models import ManufacturingOrderRawMaterial
from django.contrib.auth.models import User

def create_inventory():
    user = User.objects.first()
    
    print("إنشاء مخزون للمواد الخام...")
    
    for material in ManufacturingOrderRawMaterial.objects.all():
        inventory, created = InventoryItem.objects.get_or_create(
            warehouse=material.warehouse,
            product=material.raw_material,
            defaults={
                'quantity_on_hand': material.required_quantity + 10,
                'minimum_stock_level': 5,
                'maximum_stock_level': 100,
                'reorder_point': 10,
                'created_by': user
            }
        )
        if created:
            print(f'تم إنشاء مخزون جديد: {material.raw_material.name} - {inventory.quantity_on_hand}')
        else:
            print(f'مخزون موجود: {material.raw_material.name} - {inventory.quantity_on_hand}')
    
    print("\nالمخزون الحالي:")
    for item in InventoryItem.objects.all():
        print(f'- {item.product.name}: {item.quantity_on_hand} في {item.warehouse.name}')

if __name__ == '__main__':
    create_inventory()
