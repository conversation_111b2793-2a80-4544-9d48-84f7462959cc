<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الخادم - أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
        }
        
        .status-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .status-success {
            color: #28a745;
        }
        
        .status-error {
            color: #dc3545;
        }
        
        .btn-test {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            color: white;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .instructions {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: right;
        }
        
        .step {
            margin-bottom: 1rem;
            padding: 0.5rem;
            border-right: 3px solid #667eea;
            padding-right: 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div id="status-icon" class="status-icon">
            <i class="bi bi-hourglass-split"></i>
        </div>
        
        <h1 class="mb-3">اختبار خادم Django</h1>
        <p class="text-muted mb-4">جاري التحقق من حالة الخادم...</p>
        
        <div id="status-message" class="mb-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
        
        <div class="d-grid gap-2">
            <button class="btn btn-test" onclick="testServer()">
                <i class="bi bi-arrow-clockwise"></i>
                إعادة اختبار الخادم
            </button>
            
            <button class="btn btn-test" onclick="openDashboard()">
                <i class="bi bi-speedometer2"></i>
                فتح لوحة التحكم
            </button>
            
            <button class="btn btn-test" onclick="openSettings()">
                <i class="bi bi-gear"></i>
                فتح الإعدادات المتقدمة
            </button>
        </div>
        
        <div class="instructions">
            <h5><i class="bi bi-check-circle text-success"></i> تم إصلاح جميع المشاكل!</h5>

            <div class="step">
                <strong>✅ المشكلة الأولى:</strong>
                <br>تضارب الأسماء بين <code>osaric/settings.py</code> و <code>settings/</code> app
                <br><small class="text-success">الحل: تم تغيير اسم التطبيق إلى <code>system_settings</code></small>
            </div>

            <div class="step">
                <strong>✅ المشكلة الثانية:</strong>
                <br>خطأ <code>NoReverseMatch: 'settings' is not a registered namespace</code>
                <br><small class="text-success">الحل: تم تحديث جميع المراجع في templates و URLs</small>
            </div>

            <div class="step">
                <strong>✅ المشكلة الثالثة:</strong>
                <br>خطأ <code>no such table: system_settings_systemsettings</code>
                <br><small class="text-success">الحل: تم إنشاء جداول قاعدة البيانات</small>
            </div>

            <div class="step">
                <strong>🚀 الحل النهائي:</strong>
                <br>شغل الملف <code>run_server.bat</code> لإصلاح كل شيء تلقائياً
                <br>أو شغل <code>python fix_database.py</code> ثم <code>python manage.py runserver</code>
            </div>

            <div class="step">
                <strong>🔗 الروابط المحدثة:</strong>
                <br>• الصفحة الرئيسية: <code>http://127.0.0.1:8000/</code>
                <br>• إعدادات النظام: <code>http://127.0.0.1:8000/settings/</code>
                <br>• إعدادات متقدمة: <code>http://127.0.0.1:8000/settings/system/</code>
            </div>
        </div>
    </div>

    <script>
        // اختبار الخادم تلقائياً عند تحميل الصفحة
        window.onload = function() {
            setTimeout(testServer, 1000);
        };

        function testServer() {
            const statusIcon = document.getElementById('status-icon');
            const statusMessage = document.getElementById('status-message');
            
            // محاولة الاتصال بالخادم
            fetch('http://127.0.0.1:8000/')
                .then(response => {
                    if (response.ok) {
                        statusIcon.innerHTML = '<i class="bi bi-check-circle status-success"></i>';
                        statusMessage.innerHTML = '<div class="alert alert-success">✅ الخادم يعمل بنجاح!</div>';
                    } else {
                        throw new Error('Server responded with error');
                    }
                })
                .catch(error => {
                    statusIcon.innerHTML = '<i class="bi bi-x-circle status-error"></i>';
                    statusMessage.innerHTML = '<div class="alert alert-danger">❌ الخادم غير متاح. يرجى اتباع التعليمات أدناه.</div>';
                });
        }

        function openDashboard() {
            window.open('http://127.0.0.1:8000/', '_blank');
        }

        function openSettings() {
            window.open('http://127.0.0.1:8000/settings/advanced/', '_blank');
        }
    </script>
</body>
</html>
