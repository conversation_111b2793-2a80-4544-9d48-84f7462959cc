{% extends 'base.html' %}
{% load static %}
{% load warehouse_extras %}

{% block title %}تقارير المخزون{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        min-height: 100vh;
    }

    .reports-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        box-shadow: 0 15px 50px rgba(40, 167, 69, 0.37);
    }

    .filter-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 50px rgba(40, 167, 69, 0.37);
        margin-bottom: 2rem;
    }

    .report-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: 25px;
        padding: 3rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 50px rgba(40, 167, 69, 0.37);
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.8rem;
        font-weight: 800;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .report-tabs {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .report-tab {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border: 2px solid #28a745;
        border-radius: 15px;
        padding: 1rem 1.5rem;
        text-decoration: none;
        color: #28a745;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .report-tab:hover {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }

    .report-tab.active {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        border: 2px solid #28a745;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(40, 167, 69, 0.2);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin: 0 auto 1rem;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        color: #28a745;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 600;
    }

    .form-control, .form-select {
        border-radius: 15px;
        border: 2px solid rgba(40, 167, 69, 0.2);
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
        outline: none;
    }

    .btn-filter {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        border-radius: 15px;
        padding: 0.75rem 1.5rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.5);
        color: white;
    }

    .table-responsive {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        font-weight: 600;
        padding: 1rem;
    }

    .table tbody td {
        padding: 1rem;
        border-color: #e9ecef;
        vertical-align: middle;
    }

    .abc-section {
        margin-bottom: 2rem;
    }

    .abc-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-bottom: 1rem;
        border-left: 5px solid #28a745;
    }

    .abc-a { border-left-color: #dc3545; }
    .abc-b { border-left-color: #ffc107; }
    .abc-c { border-left-color: #28a745; }

    .progress-custom {
        height: 10px;
        border-radius: 10px;
        background: #e9ecef;
        overflow: hidden;
    }

    .progress-bar-custom {
        height: 100%;
        background: linear-gradient(135deg, #28a745, #20c997);
        border-radius: 10px;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<!-- Reports Header -->
<div class="reports-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-graph-up me-2"></i>تقارير المخزون
                </h1>
                <p class="mb-0 opacity-75">تقارير وتحليلات شاملة للمخزون</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group">
                    <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-light">
                        <i class="bi bi-list-ul me-1"></i>المخزون
                    </a>
                    <a href="{% url 'warehouses:dashboard' %}" class="btn btn-light">
                        <i class="bi bi-arrow-left me-1"></i>لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Filter Section -->
    <div class="filter-section">
        <h5 class="mb-3"><i class="bi bi-funnel me-2"></i>فلاتر التقرير</h5>
        
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">نوع التقرير</label>
                <select name="type" class="form-select">
                    <option value="summary" {% if report_type == 'summary' %}selected{% endif %}>ملخص المخزون</option>
                    <option value="movements" {% if report_type == 'movements' %}selected{% endif %}>حركات المخزون</option>
                    <option value="abc_analysis" {% if report_type == 'abc_analysis' %}selected{% endif %}>تحليل ABC</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">المخزن</label>
                <select name="warehouse" class="form-select">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if selected_warehouse and selected_warehouse.id == warehouse.id %}selected{% endif %}>
                        {{ warehouse.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from|date:'Y-m-d' }}">
            </div>
            
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to|date:'Y-m-d' }}">
            </div>
            
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-filter w-100">
                    <i class="bi bi-search me-1"></i>تطبيق
                </button>
            </div>
        </form>
    </div>

    <!-- Report Content -->
    {% if report_type == 'summary' %}
    <div class="report-section">
        <h2 class="section-title">
            <i class="bi bi-clipboard-data"></i>
            ملخص المخزون
        </h2>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-boxes"></i>
                </div>
                <div class="stat-number">{{ total_items }}</div>
                <div class="stat-label">إجمالي العناصر</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="stat-number">{{ total_value|number_format }}</div>
                <div class="stat-label">إجمالي القيمة (ج.م)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <div class="stat-number">{{ low_stock_items.count }}</div>
                <div class="stat-label">مخزون منخفض</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-x-circle"></i>
                </div>
                <div class="stat-number">{{ out_of_stock_items.count }}</div>
                <div class="stat-label">مخزون نافد</div>
            </div>
        </div>

        <!-- Value Classification -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number text-success">{{ high_value_items }}</div>
                    <div class="stat-label">عناصر عالية القيمة (>10,000 ج.م)</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number text-warning">{{ medium_value_items }}</div>
                    <div class="stat-label">عناصر متوسطة القيمة (5,000-10,000 ج.م)</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-number text-info">{{ low_value_items }}</div>
                    <div class="stat-label">عناصر منخفضة القيمة (<5,000 ج.م)</div>
                </div>
            </div>
        </div>

        <!-- Top Items Table -->
        {% if inventory_items %}
        <h5 class="mb-3">أهم العناصر</h5>
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>المخزن</th>
                        <th>الكمية</th>
                        <th>سعر التكلفة الحقيقي</th>
                        <th>إجمالي القيمة</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in inventory_items %}
                    <tr>
                        <td>
                            <strong>{{ item.product.name }}</strong><br>
                            <small class="text-muted">{{ item.product.code }}</small>
                        </td>
                        <td>{{ item.warehouse.name }}</td>
                        <td>{{ item.quantity_on_hand }}</td>
                        <td>{{ item.product.cost_price|floatformat:2 }} ج.م</td>
                        <td>
                            {% widthratio item.quantity_on_hand 1 item.product.cost_price as real_total_value %}
                            {{ real_total_value|floatformat:2 }} ج.م
                        </td>
                        <td>
                            {% if item.quantity_on_hand == 0 %}
                                <span class="badge bg-danger">نافد</span>
                            {% elif item.quantity_on_hand <= item.minimum_stock and item.minimum_stock > 0 %}
                                <span class="badge bg-warning">منخفض</span>
                            {% else %}
                                <span class="badge bg-success">متاح</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </div>

    {% elif report_type == 'movements' %}
    <div class="report-section">
        <h2 class="section-title">
            <i class="bi bi-arrow-repeat"></i>
            حركات المخزون
        </h2>

        <!-- Movement Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-list-ul"></i>
                </div>
                <div class="stat-number">{{ total_transactions }}</div>
                <div class="stat-label">إجمالي الحركات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-plus-circle"></i>
                </div>
                <div class="stat-number">{{ total_receipts }}</div>
                <div class="stat-label">حركات الاستلام</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-dash-circle"></i>
                </div>
                <div class="stat-number">{{ total_issues }}</div>
                <div class="stat-label">حركات الصرف</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="bi bi-arrow-left-right"></i>
                </div>
                <div class="stat-number">{{ total_transfers }}</div>
                <div class="stat-label">حركات التحويل</div>
            </div>
        </div>

        <!-- Value Statistics -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="stat-card">
                    <div class="stat-number text-success">{{ receipts_value|number_format }}</div>
                    <div class="stat-label">قيمة الاستلام (ج.م)</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stat-card">
                    <div class="stat-number text-danger">{{ issues_value|number_format }}</div>
                    <div class="stat-label">قيمة الصرف (ج.م)</div>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        {% if transactions %}
        <h5 class="mb-3">أحدث الحركات</h5>
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الحركة</th>
                        <th>النوع</th>
                        <th>المنتج</th>
                        <th>المخزن</th>
                        <th>الكمية</th>
                        <th>القيمة</th>
                        <th>التاريخ</th>
                        <th>المستخدم</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction.transaction_number }}</td>
                        <td>
                            {% if transaction.transaction_type == 'receipt' %}
                                <span class="badge bg-success">استلام</span>
                            {% elif transaction.transaction_type == 'issue' %}
                                <span class="badge bg-danger">صرف</span>
                            {% elif transaction.transaction_type == 'transfer_in' %}
                                <span class="badge bg-primary">تحويل وارد</span>
                            {% elif transaction.transaction_type == 'transfer_out' %}
                                <span class="badge bg-warning">تحويل صادر</span>
                            {% else %}
                                <span class="badge bg-info">تعديل</span>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ transaction.inventory_item.product.name }}</strong><br>
                            <small class="text-muted">{{ transaction.inventory_item.product.code }}</small>
                        </td>
                        <td>{{ transaction.inventory_item.warehouse.name }}</td>
                        <td>{{ transaction.quantity }}</td>
                        <td>{{ transaction.total_cost|floatformat:2 }} ج.م</td>
                        <td>{{ transaction.created_at|date:"d/m/Y H:i" }}</td>
                        <td>{{ transaction.created_by.username }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </div>

    {% elif report_type == 'abc_analysis' %}
    <div class="report-section">
        <h2 class="section-title">
            <i class="bi bi-bar-chart"></i>
            تحليل ABC
        </h2>

        <div class="row">
            <!-- Category A -->
            <div class="col-md-4">
                <div class="abc-section">
                    <div class="abc-header abc-a">
                        <h5 class="mb-1 text-danger">فئة A - عالية القيمة</h5>
                        <small>80% من القيمة الإجمالية</small>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-danger">{{ a_items|length }}</div>
                        <div class="stat-label">عنصر</div>
                    </div>
                </div>
            </div>

            <!-- Category B -->
            <div class="col-md-4">
                <div class="abc-section">
                    <div class="abc-header abc-b">
                        <h5 class="mb-1 text-warning">فئة B - متوسطة القيمة</h5>
                        <small>15% من القيمة الإجمالية</small>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-warning">{{ b_items|length }}</div>
                        <div class="stat-label">عنصر</div>
                    </div>
                </div>
            </div>

            <!-- Category C -->
            <div class="col-md-4">
                <div class="abc-section">
                    <div class="abc-header abc-c">
                        <h5 class="mb-1 text-success">فئة C - منخفضة القيمة</h5>
                        <small>5% من القيمة الإجمالية</small>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number text-success">{{ c_items|length }}</div>
                        <div class="stat-label">عنصر</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ABC Items Details -->
        {% if a_items %}
        <h5 class="mb-3 text-danger">عناصر فئة A (الأكثر أهمية)</h5>
        <div class="table-responsive mb-4">
            <table class="table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>المخزن</th>
                        <th>الكمية</th>
                        <th>القيمة</th>
                        <th>النسبة من الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in a_items|slice:":10" %}
                    <tr>
                        <td>
                            <strong>{{ item.product.name }}</strong><br>
                            <small class="text-muted">{{ item.product.code }}</small>
                        </td>
                        <td>{{ item.warehouse.name }}</td>
                        <td>{{ item.quantity_on_hand }}</td>
                        <td>
                            {% widthratio item.quantity_on_hand 1 item.product.cost_price as real_total_value %}
                            {{ real_total_value|floatformat:2 }} ج.م
                        </td>
                        <td>
                            {% if total_value > 0 %}
                                {{ item.total_value|percentage:total_value }}%
                            {% else %}
                                0%
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}
