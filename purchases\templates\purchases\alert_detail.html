{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل التنبيه - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .alert-details {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .alert-details.critical {
        border-left: 5px solid #dc3545;
        background: linear-gradient(45deg, #fff5f5, #ffffff);
    }

    .alert-details.high {
        border-left: 5px solid #fd7e14;
        background: linear-gradient(45deg, #fff8f0, #ffffff);
    }

    .alert-details.medium {
        border-left: 5px solid #ffc107;
        background: linear-gradient(45deg, #fffbf0, #ffffff);
    }

    .alert-details.low {
        border-left: 5px solid #28a745;
        background: linear-gradient(45deg, #f0fff4, #ffffff);
    }

    .alert-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 20px;
    }

    .alert-priority {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 20px;
    }

    .priority-critical {
        background: #dc3545;
        color: white;
    }

    .priority-high {
        background: #fd7e14;
        color: white;
    }

    .priority-medium {
        background: #ffc107;
        color: #212529;
    }

    .priority-low {
        background: #28a745;
        color: white;
    }

    .alert-message {
        font-size: 1.1rem;
        line-height: 1.6;
        color: #495057;
        margin-bottom: 30px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .detail-item {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
    }

    .detail-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 8px;
        font-weight: 600;
    }

    .detail-value {
        font-size: 1.2rem;
        font-weight: bold;
        color: #2c3e50;
    }

    .amount-highlight {
        color: #dc3545;
        font-size: 1.5rem;
    }

    .action-buttons {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        text-align: center;
    }

    .btn-resolve {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        margin-right: 15px;
    }

    .btn-resolve:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .btn-mark-read {
        background: linear-gradient(45deg, #17a2b8, #138496);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        margin-right: 15px;
    }

    .btn-mark-read:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(23, 162, 184, 0.3);
        color: white;
    }

    .btn-back {
        background: #6c757d;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-back:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }

    .related-info {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .related-info h5 {
        color: #2196f3;
        margin-bottom: 15px;
    }

    .info-item {
        background: white;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .status-resolved {
        background: #d4edda;
        color: #155724;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        display: inline-block;
    }

    .status-unread {
        background: #f8d7da;
        color: #721c24;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-bell-fill"></i>
                    تفاصيل التنبيه
                </h1>
                <p class="mb-0">مراجعة تفاصيل التنبيه واتخاذ الإجراء المناسب</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'purchases:alerts_dashboard' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right"></i>
                    العودة للتنبيهات
                </a>
            </div>
        </div>
    </div>

    <!-- تفاصيل التنبيه -->
    <div class="alert-details {{ alert.priority }}">
        <div class="alert-title">{{ alert.title }}</div>
        
        <div class="alert-priority priority-{{ alert.priority }}">
            {{ alert.get_priority_display }}
        </div>
        
        {% if alert.is_resolved %}
            <div class="status-resolved">
                <i class="bi bi-check-circle"></i>
                تم الحل
            </div>
        {% elif not alert.is_read %}
            <div class="status-unread">
                <i class="bi bi-exclamation-circle"></i>
                غير مقروء
            </div>
        {% endif %}
        
        <div class="alert-message">
            {{ alert.message }}
        </div>
        
        <div class="detail-grid">
            <div class="detail-item">
                <div class="detail-label">نوع التنبيه</div>
                <div class="detail-value">{{ alert.get_alert_type_display }}</div>
            </div>
            
            <div class="detail-item">
                <div class="detail-label">تاريخ الإنشاء</div>
                <div class="detail-value">{{ alert.created_at|date:"Y-m-d H:i" }}</div>
            </div>
            
            {% if alert.amount %}
                <div class="detail-item">
                    <div class="detail-label">المبلغ</div>
                    <div class="detail-value amount-highlight">{{ alert.amount|floatformat:2 }} ج.م</div>
                </div>
            {% endif %}
            
            {% if alert.due_date %}
                <div class="detail-item">
                    <div class="detail-label">تاريخ الاستحقاق</div>
                    <div class="detail-value">{{ alert.due_date }}</div>
                </div>
            {% endif %}
            
            {% if alert.days_overdue > 0 %}
                <div class="detail-item">
                    <div class="detail-label">أيام التأخير</div>
                    <div class="detail-value amount-highlight">{{ alert.days_overdue }} يوم</div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- معلومات ذات صلة -->
    {% if alert.supplier or alert.invoice %}
        <div class="related-info">
            <h5>
                <i class="bi bi-link-45deg"></i>
                معلومات ذات صلة
            </h5>
            
            {% if alert.supplier %}
                <div class="info-item">
                    <div>
                        <strong>المورد:</strong> {{ alert.supplier.name }}
                    </div>
                    <div>
                        <a href="{% url 'purchases:supplier_detail' alert.supplier.pk %}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye"></i> عرض المورد
                        </a>
                    </div>
                </div>
            {% endif %}
            
            {% if alert.invoice %}
                <div class="info-item">
                    <div>
                        <strong>الفاتورة:</strong> {{ alert.invoice.invoice_number }}
                        <br>
                        <small class="text-muted">الحالة: {{ alert.invoice.get_status_display }}</small>
                    </div>
                    <div>
                        <a href="{% url 'purchases:purchase_invoice_detail' alert.invoice.pk %}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye"></i> عرض الفاتورة
                        </a>
                    </div>
                </div>
            {% endif %}
            
            {% if alert.payment %}
                <div class="info-item">
                    <div>
                        <strong>الدفعة:</strong> {{ alert.payment.total_amount|floatformat:2 }} ج.م
                        <br>
                        <small class="text-muted">تاريخ: {{ alert.payment.payment_date }}</small>
                    </div>
                </div>
            {% endif %}
        </div>
    {% endif %}

    <!-- أزرار الإجراءات -->
    {% if not alert.is_resolved %}
        <div class="action-buttons">
            <h5 class="mb-4">الإجراءات المتاحة</h5>
            
            <form method="post" style="display: inline;">
                {% csrf_token %}
                <input type="hidden" name="action" value="resolve">
                <button type="submit" class="btn btn-resolve" onclick="return confirm('هل تريد تحديد هذا التنبيه كمحلول؟')">
                    <i class="bi bi-check-circle"></i>
                    حل التنبيه
                </button>
            </form>
            
            {% if not alert.is_read %}
                <form method="post" style="display: inline;">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="mark_read">
                    <button type="submit" class="btn btn-mark-read">
                        <i class="bi bi-eye"></i>
                        تحديد كمقروء
                    </button>
                </form>
            {% endif %}
            
            <a href="{% url 'purchases:alerts_dashboard' %}" class="btn btn-back">
                <i class="bi bi-arrow-left"></i>
                العودة
            </a>
        </div>
    {% else %}
        <div class="action-buttons">
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i>
                <strong>تم حل هذا التنبيه</strong>
                {% if alert.resolved_by %}
                    بواسطة {{ alert.resolved_by.get_full_name|default:alert.resolved_by.username }}
                    في {{ alert.resolved_at|date:"Y-m-d H:i" }}
                {% endif %}
            </div>
            
            <a href="{% url 'purchases:alerts_dashboard' %}" class="btn btn-back">
                <i class="bi bi-arrow-left"></i>
                العودة للتنبيهات
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
