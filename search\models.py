from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class SearchHistory(models.Model):
    """تاريخ البحث للمستخدمين"""
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='search_history',
        verbose_name="المستخدم"
    )
    
    query = models.CharField(
        max_length=500,
        verbose_name="استعلام البحث"
    )
    
    results_count = models.IntegerField(
        default=0,
        verbose_name="عدد النتائج"
    )
    
    search_type = models.CharField(
        max_length=50,
        default='global',
        verbose_name="نوع البحث"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="تاريخ البحث"
    )
    
    execution_time = models.FloatField(
        null=True,
        blank=True,
        verbose_name="وقت التنفيذ (ثانية)"
    )

    class Meta:
        verbose_name = "تاريخ البحث"
        verbose_name_plural = "تاريخ البحث"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.query}"


class PopularSearch(models.Model):
    """البحثات الشائعة"""
    
    query = models.CharField(
        max_length=500,
        unique=True,
        verbose_name="استعلام البحث"
    )
    
    search_count = models.IntegerField(
        default=1,
        verbose_name="عدد مرات البحث"
    )
    
    last_searched = models.DateTimeField(
        auto_now=True,
        verbose_name="آخر بحث"
    )

    class Meta:
        verbose_name = "بحث شائع"
        verbose_name_plural = "البحثات الشائعة"
        ordering = ['-search_count', '-last_searched']

    def __str__(self):
        return f"{self.query} ({self.search_count})"


class SearchSuggestion(models.Model):
    """اقتراحات البحث"""
    
    keyword = models.CharField(
        max_length=200,
        unique=True,
        verbose_name="الكلمة المفتاحية"
    )
    
    suggestion = models.CharField(
        max_length=200,
        verbose_name="الاقتراح"
    )
    
    category = models.CharField(
        max_length=50,
        verbose_name="الفئة"
    )
    
    priority = models.IntegerField(
        default=1,
        verbose_name="الأولوية"
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name="نشط"
    )

    class Meta:
        verbose_name = "اقتراح بحث"
        verbose_name_plural = "اقتراحات البحث"
        ordering = ['-priority', 'suggestion']

    def __str__(self):
        return f"{self.keyword} -> {self.suggestion}"
