from django import forms
from .models import Bank, BankAccount, BankTransaction

class BankForm(forms.ModelForm):
    class Meta:
        model = Bank
        fields = ['name', 'code', 'swift_code', 'address', 'phone', 'email', 'website', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم البنك'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز البنك'}),
            'swift_code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز SWIFT'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'عنوان البنك'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'website': forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'الموقع الإلكتروني'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': 'اسم البنك',
            'code': 'رمز البنك',
            'swift_code': 'رمز SWIFT',
            'address': 'العنوان',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'website': 'الموقع الإلكتروني',
            'is_active': 'نشط',
        }

class BankAccountForm(forms.ModelForm):
    class Meta:
        model = BankAccount
        fields = ['bank', 'account_name', 'account_number', 'iban', 'account_type',
                 'currency', 'opening_balance', 'minimum_balance', 'overdraft_limit',
                 'interest_rate', 'account_manager', 'notes', 'is_active']
        widgets = {
            'bank': forms.Select(attrs={'class': 'form-select'}),
            'account_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الحساب'}),
            'account_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الحساب'}),
            'iban': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم IBAN'}),
            'account_type': forms.Select(attrs={'class': 'form-select'}),
            'currency': forms.Select(attrs={'class': 'form-select'}),
            'opening_balance': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'minimum_balance': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'overdraft_limit': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'interest_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'account_manager': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مدير الحساب'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class BankTransactionForm(forms.ModelForm):
    class Meta:
        model = BankTransaction
        fields = ['account', 'transaction_type', 'amount', 'description',
                 'reference_number', 'transaction_date', 'status']
        widgets = {
            'account': forms.Select(attrs={'class': 'form-select'}),
            'transaction_type': forms.Select(attrs={'class': 'form-select'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف المعاملة'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم المرجع'}),
            'transaction_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['account'].queryset = BankAccount.objects.filter(is_active=True)

class BankSearchForm(forms.Form):
    """نموذج البحث في البنوك والحسابات"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم أو الكود أو رقم الحساب...'
        })
    )
    bank = forms.ModelChoiceField(
        queryset=Bank.objects.filter(is_active=True),
        required=False,
        empty_label="جميع البنوك",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    account_type = forms.ChoiceField(
        choices=[('', 'جميع أنواع الحسابات')] + BankAccount.ACCOUNT_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    currency = forms.ChoiceField(
        choices=[('', 'جميع العملات')] + BankAccount.CURRENCY_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
