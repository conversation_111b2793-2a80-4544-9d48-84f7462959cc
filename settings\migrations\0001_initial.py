# Generated by Django 5.2.4 on 2025-07-12 23:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(choices=[('general', 'عام'), ('company', 'بيانات الشركة'), ('financial', 'مالي'), ('inventory', 'مخزون'), ('sales', 'مبيعات'), ('purchases', 'مشتريات'), ('hr', 'موارد بشرية'), ('security', 'أمان'), ('notifications', 'إشعارات'), ('backup', 'نسخ احتياطي'), ('integration', 'تكامل')], max_length=20, verbose_name='الفئة')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='المفتاح')),
                ('name', models.CharField(max_length=200, verbose_name='الاسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('setting_type', models.CharField(choices=[('text', 'نص'), ('number', 'رقم'), ('boolean', 'صحيح/خطأ'), ('email', 'بريد إلكتروني'), ('url', 'رابط'), ('json', 'JSON'), ('file', 'ملف')], max_length=20, verbose_name='نوع الإعداد')),
                ('value', models.TextField(blank=True, null=True, verbose_name='القيمة')),
                ('default_value', models.TextField(blank=True, null=True, verbose_name='القيمة الافتراضية')),
                ('is_required', models.BooleanField(default=False, verbose_name='مطلوب')),
                ('is_editable', models.BooleanField(default=True, verbose_name='قابل للتعديل')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعداد النظام',
                'verbose_name_plural': 'إعدادات النظام',
                'ordering': ['category', 'order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='BackupSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الجدولة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري')], max_length=20, verbose_name='التكرار')),
                ('backup_time', models.TimeField(verbose_name='وقت النسخ الاحتياطي')),
                ('include_database', models.BooleanField(default=True, verbose_name='تضمين قاعدة البيانات')),
                ('include_media', models.BooleanField(default=True, verbose_name='تضمين الملفات')),
                ('retention_days', models.PositiveIntegerField(default=30, verbose_name='أيام الاحتفاظ')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('paused', 'متوقف مؤقتاً')], default='active', max_length=20, verbose_name='الحالة')),
                ('last_backup', models.DateTimeField(blank=True, null=True, verbose_name='آخر نسخة احتياطية')),
                ('next_backup', models.DateTimeField(blank=True, null=True, verbose_name='النسخة الاحتياطية التالية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'جدولة نسخة احتياطية',
                'verbose_name_plural': 'جدولة النسخ الاحتياطية',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BackupHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('backup_date', models.DateTimeField(verbose_name='تاريخ النسخة الاحتياطية')),
                ('status', models.CharField(choices=[('running', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('failed', 'فشل')], max_length=20, verbose_name='الحالة')),
                ('file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='مسار الملف')),
                ('file_size', models.BigIntegerField(default=0, verbose_name='حجم الملف (بايت)')),
                ('duration', models.DurationField(blank=True, null=True, verbose_name='مدة النسخ')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='رسالة الخطأ')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('schedule', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='backups', to='settings.backupschedule', verbose_name='الجدولة')),
            ],
            options={
                'verbose_name': 'تاريخ نسخة احتياطية',
                'verbose_name_plural': 'تاريخ النسخ الاحتياطية',
                'ordering': ['-backup_date'],
            },
        ),
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.CharField(choices=[('debug', 'تصحيح'), ('info', 'معلومات'), ('warning', 'تحذير'), ('error', 'خطأ'), ('critical', 'حرج')], max_length=20, verbose_name='المستوى')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('module', models.CharField(blank=True, max_length=100, null=True, verbose_name='الوحدة')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='وكيل المستخدم')),
                ('extra_data', models.JSONField(blank=True, default=dict, verbose_name='بيانات إضافية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل النظام',
                'verbose_name_plural': 'سجلات النظام',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preference_type', models.CharField(choices=[('theme', 'المظهر'), ('language', 'اللغة'), ('timezone', 'المنطقة الزمنية'), ('notifications', 'الإشعارات'), ('dashboard', 'لوحة التحكم'), ('reports', 'التقارير')], max_length=20, verbose_name='نوع التفضيل')),
                ('key', models.CharField(max_length=100, verbose_name='المفتاح')),
                ('value', models.TextField(verbose_name='القيمة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'تفضيل المستخدم',
                'verbose_name_plural': 'تفضيلات المستخدمين',
                'ordering': ['user', 'preference_type', 'key'],
                'unique_together': {('user', 'key')},
            },
        ),
    ]
