<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

مشروع أوساريك هو نظام متكامل لإدارة الحسابات والمخزون باستخدام Django وPostgreSQL. يجب أن تكون جميع الأكواد قابلة للتطوير، آمنة، وتدعم تعدد المستخدمين. واجهة المستخدم يجب أن تكون احترافية باستخدام Bootstrap، مع دعم REST API ولوحة تحكم حديثة ورسوم بيانية.
