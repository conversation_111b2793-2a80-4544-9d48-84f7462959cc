{% extends 'base.html' %}
{% load static %}

{% block title %}تسويات المخزون{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
    }

    .adjustment-container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .page-title {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .step-container {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid #e0e0e0;
    }

    .step-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f0f0f0;
    }

    .step-number {
        background: linear-gradient(135deg, #1e3c72, #2a5298);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 1rem;
    }

    .step-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
        margin: 0;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #555;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control, .form-select {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 0.75rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        width: 100%;
    }

    .form-control:focus, .form-select:focus {
        border-color: #2a5298;
        box-shadow: 0 0 0 0.2rem rgba(42, 82, 152, 0.25);
        outline: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #1e3c72, #2a5298);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(42, 82, 152, 0.3);
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }

    .info-box {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
    }

    .info-box.success {
        background: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .adjustment-type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }

    .adjustment-type-card {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }

    .adjustment-type-card:hover {
        border-color: #2a5298;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(42, 82, 152, 0.2);
    }

    .adjustment-type-card.selected {
        border-color: #2a5298;
        background: #f0f4ff;
    }

    .adjustment-type-card .icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #2a5298;
    }

    .adjustment-type-card .title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .adjustment-type-card .description {
        font-size: 0.9rem;
        color: #666;
    }

    .hidden {
        display: none;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -0.5rem;
    }

    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
        padding: 0 0.5rem;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0 0.5rem;
    }

    @media (max-width: 768px) {
        .col-md-6 {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="adjustment-container">
    <!-- Page Title -->
    <div class="page-title">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="bi bi-sliders me-2"></i>تسويات المخزون</h1>
                <p class="mb-0">نظام تسوية المخزون المتقدم - خطوة بخطوة</p>
            </div>
            <div>
                <a href="{% url 'warehouses:adjustments_history' %}" class="btn btn-light">
                    <i class="bi bi-clock-history me-2"></i>سجل التسويات
                </a>
                <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-light">
                    <i class="bi bi-arrow-left me-2"></i>قائمة المخزون
                </a>
            </div>
        </div>
    </div>

    <!-- Step 1: Selection -->
    <div class="step-container" id="step1">
        <div class="step-header">
            <div class="step-number">1</div>
            <h3 class="step-title">اختيار المخزن والمنتج</h3>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">المخزن</label>
                    <select id="warehouseSelect" class="form-select">
                        <option value="">-- اختر المخزن --</option>
                        {% for warehouse in warehouses %}
                        <option value="{{ warehouse.id }}" data-name="{{ warehouse.name }}">
                            {{ warehouse.name }} ({{ warehouse.code }})
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">المنتج</label>
                    <select id="productSelect" class="form-select">
                        <option value="">-- اختر المنتج --</option>
                        {% for product in products %}
                        <option value="{{ product.id }}" data-name="{{ product.name }}" data-code="{{ product.code }}">
                            {{ product.name }} ({{ product.code }})
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>

        <div class="text-center">
            <button type="button" class="btn btn-primary" id="nextStep1">
                التالي <i class="bi bi-arrow-left ms-2"></i>
            </button>
        </div>
    </div>

    <!-- Step 2: Current Stock Info -->
    <div class="step-container hidden" id="step2">
        <div class="step-header">
            <div class="step-number">2</div>
            <h3 class="step-title">معلومات المخزون الحالي</h3>
        </div>

        <div id="stockInfo" class="info-box">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>

        <div class="text-center">
            <button type="button" class="btn btn-secondary me-2" id="backStep2">
                <i class="bi bi-arrow-right me-2"></i>السابق
            </button>
            <button type="button" class="btn btn-primary" id="nextStep2">
                التالي <i class="bi bi-arrow-left ms-2"></i>
            </button>
        </div>
    </div>

    <!-- Step 3: Adjustment Type -->
    <div class="step-container hidden" id="step3">
        <div class="step-header">
            <div class="step-number">3</div>
            <h3 class="step-title">نوع التسوية</h3>
        </div>

        <div class="adjustment-type-grid">
            <div class="adjustment-type-card" data-type="increase">
                <div class="icon"><i class="bi bi-plus-circle"></i></div>
                <div class="title">زيادة المخزون</div>
                <div class="description">إضافة كمية جديدة</div>
            </div>
            <div class="adjustment-type-card" data-type="decrease">
                <div class="icon"><i class="bi bi-dash-circle"></i></div>
                <div class="title">تقليل المخزون</div>
                <div class="description">خصم كمية موجودة</div>
            </div>
            <div class="adjustment-type-card" data-type="set">
                <div class="icon"><i class="bi bi-arrow-clockwise"></i></div>
                <div class="title">تحديد الكمية</div>
                <div class="description">تعيين كمية محددة</div>
            </div>
            <div class="adjustment-type-card" data-type="revalue">
                <div class="icon"><i class="bi bi-currency-dollar"></i></div>
                <div class="title">إعادة التقييم</div>
                <div class="description">تغيير التكلفة فقط</div>
            </div>
        </div>

        <div class="text-center">
            <button type="button" class="btn btn-secondary me-2" id="backStep3">
                <i class="bi bi-arrow-right me-2"></i>السابق
            </button>
            <button type="button" class="btn btn-primary" id="nextStep3">
                التالي <i class="bi bi-arrow-left ms-2"></i>
            </button>
        </div>
    </div>

    <!-- Step 4: Adjustment Details -->
    <div class="step-container hidden" id="step4">
        <div class="step-header">
            <div class="step-number">4</div>
            <h3 class="step-title">تفاصيل التسوية</h3>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">الكمية</label>
                    <input type="number" id="quantityInput" class="form-control" step="0.001" min="0.001" placeholder="أدخل الكمية">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">تكلفة الوحدة (ج.م)</label>
                    <input type="number" id="unitCostInput" class="form-control" step="0.01" min="0" placeholder="تكلفة الوحدة">
                    <div class="form-text" id="priceHints">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">سبب التسوية</label>
                    <select id="reasonSelect" class="form-select">
                        <option value="">-- اختر السبب --</option>
                        {% for reason in adjustment_reasons %}
                        <option value="{{ reason }}">{{ reason }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">تاريخ التسوية</label>
                    <input type="datetime-local" id="adjustmentDate" class="form-control" value="{{ current_datetime }}">
                </div>
            </div>
            <div class="col-12">
                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea id="notesInput" class="form-control" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                </div>
            </div>
        </div>

        <div class="text-center">
            <button type="button" class="btn btn-secondary me-2" id="backStep4">
                <i class="bi bi-arrow-right me-2"></i>السابق
            </button>
            <button type="button" class="btn btn-primary" id="nextStep4">
                التالي <i class="bi bi-arrow-left ms-2"></i>
            </button>
        </div>
    </div>

    <!-- Step 5: Summary and Confirmation -->
    <div class="step-container hidden" id="step5">
        <div class="step-header">
            <div class="step-number">5</div>
            <h3 class="step-title">مراجعة وتأكيد التسوية</h3>
        </div>

        <div id="summaryContent">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>

        <div class="text-center">
            <button type="button" class="btn btn-secondary me-2" id="backStep5">
                <i class="bi bi-arrow-right me-2"></i>السابق
            </button>
            <button type="button" class="btn btn-primary" id="submitAdjustment">
                <i class="bi bi-check-circle me-2"></i>تطبيق التسوية
            </button>
        </div>
    </div>

    <!-- Hidden Form for Submission -->
    <form id="adjustmentForm" method="post" style="display: none;">
        {% csrf_token %}
        <input type="hidden" name="warehouse" id="hiddenWarehouse">
        <input type="hidden" name="product" id="hiddenProduct">
        <input type="hidden" name="adjustment_type" id="hiddenAdjustmentType">
        <input type="hidden" name="quantity" id="hiddenQuantity">
        <input type="hidden" name="unit_cost" id="hiddenUnitCost">
        <input type="hidden" name="reason" id="hiddenReason">
        <input type="hidden" name="adjustment_date" id="hiddenAdjustmentDate">
        <input type="hidden" name="notes" id="hiddenNotes">
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // متغيرات عامة
    let currentStep = 1;
    let adjustmentData = {};
    let stockData = {};

    // العناصر
    const warehouseSelect = document.getElementById('warehouseSelect');
    const productSelect = document.getElementById('productSelect');
    const quantityInput = document.getElementById('quantityInput');
    const unitCostInput = document.getElementById('unitCostInput');
    const reasonSelect = document.getElementById('reasonSelect');
    const adjustmentDate = document.getElementById('adjustmentDate');
    const notesInput = document.getElementById('notesInput');

    // Step 1: Next button
    document.getElementById('nextStep1').addEventListener('click', function() {
        if (!warehouseSelect.value || !productSelect.value) {
            alert('يرجى اختيار المخزن والمنتج');
            return;
        }

        adjustmentData.warehouse = warehouseSelect.value;
        adjustmentData.product = productSelect.value;
        adjustmentData.warehouseName = warehouseSelect.options[warehouseSelect.selectedIndex].dataset.name;
        adjustmentData.productName = productSelect.options[productSelect.selectedIndex].dataset.name;
        adjustmentData.productCode = productSelect.options[productSelect.selectedIndex].dataset.code;

        loadStockInfo();
        showStep(2);
    });

    // Step 2: Navigation
    document.getElementById('backStep2').addEventListener('click', () => showStep(1));
    document.getElementById('nextStep2').addEventListener('click', () => showStep(3));

    // Step 3: Adjustment type selection
    document.querySelectorAll('.adjustment-type-card').forEach(card => {
        card.addEventListener('click', function() {
            document.querySelectorAll('.adjustment-type-card').forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            adjustmentData.adjustmentType = this.dataset.type;
        });
    });

    document.getElementById('backStep3').addEventListener('click', () => showStep(2));
    document.getElementById('nextStep3').addEventListener('click', function() {
        if (!adjustmentData.adjustmentType) {
            alert('يرجى اختيار نوع التسوية');
            return;
        }
        setupStep4();
        showStep(4);
    });

    // Step 4: Navigation
    document.getElementById('backStep4').addEventListener('click', () => showStep(3));
    document.getElementById('nextStep4').addEventListener('click', function() {
        if (!validateStep4()) return;

        adjustmentData.quantity = parseFloat(quantityInput.value);
        adjustmentData.unitCost = parseFloat(unitCostInput.value);
        adjustmentData.reason = reasonSelect.value;
        adjustmentData.adjustmentDate = adjustmentDate.value;
        adjustmentData.notes = notesInput.value;

        showSummary();
        showStep(5);
    });

    // Step 5: Navigation
    document.getElementById('backStep5').addEventListener('click', () => showStep(4));
    document.getElementById('submitAdjustment').addEventListener('click', submitForm);

    function showStep(stepNumber) {
        // إخفاء جميع الخطوات
        for (let i = 1; i <= 5; i++) {
            document.getElementById(`step${i}`).classList.add('hidden');
        }

        // إظهار الخطوة المطلوبة
        document.getElementById(`step${stepNumber}`).classList.remove('hidden');
        currentStep = stepNumber;

        // التمرير إلى أعلى
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    function loadStockInfo() {
        const stockInfoDiv = document.getElementById('stockInfo');

        // إظهار حالة التحميل
        stockInfoDiv.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل بيانات المخزون...</p>
            </div>
        `;

        // طلب البيانات من الخادم
        fetch(`/warehouses/api/stock-info/?warehouse_id=${adjustmentData.warehouse}&product_id=${adjustmentData.product}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // حفظ البيانات
                    stockData = {
                        currentQuantity: data.current_quantity,
                        currentValue: data.current_value,
                        productCostPrice: data.product_cost_price,
                        productSellingPrice: data.product_selling_price,
                        productWholesalePrice: data.product_wholesale_price
                    };

                    // عرض البيانات
                    stockInfoDiv.innerHTML = `
                        <h5><i class="bi bi-info-circle me-2"></i>معلومات المخزون والأسعار</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary"><i class="bi bi-box me-1"></i>معلومات المخزون</h6>
                                <p><strong>المخزن:</strong> ${data.warehouse_name} (${data.warehouse_code})</p>
                                <p><strong>المنتج:</strong> ${data.product_name}</p>
                                <p><strong>كود المنتج:</strong> ${data.product_code}</p>
                                <p><strong>الكمية الحالية:</strong> <span class="text-success">${data.current_quantity.toFixed(3)}</span></p>
                                <p><strong>إجمالي القيمة الحقيقية:</strong> <span class="text-success">${data.current_value.toFixed(2)} ج.م</span></p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info"><i class="bi bi-tags me-1"></i>أسعار المنتج الحقيقية</h6>
                                <p><strong>سعر التكلفة:</strong>
                                    <span class="${data.product_cost_price > 0 ? 'text-primary fw-bold' : 'text-muted'}">${data.product_cost_price.toFixed(2)} ج.م</span>
                                    ${data.product_cost_price === 0 ? '<small class="text-muted">(غير محدد)</small>' : ''}
                                </p>
                                <p><strong>سعر البيع:</strong>
                                    <span class="${data.product_selling_price > 0 ? 'text-info' : 'text-muted'}">${data.product_selling_price.toFixed(2)} ج.م</span>
                                    ${data.product_selling_price === 0 ? '<small class="text-muted">(غير محدد)</small>' : ''}
                                </p>
                                <p><strong>سعر الجملة:</strong>
                                    <span class="${data.product_wholesale_price > 0 ? 'text-info' : 'text-muted'}">${data.product_wholesale_price.toFixed(2)} ج.م</span>
                                    ${data.product_wholesale_price === 0 ? '<small class="text-muted">(غير محدد)</small>' : ''}
                                </p>
                                <hr>
                                <p><strong>سعر التسوية:</strong> <span class="text-primary fw-bold">${data.product_cost_price.toFixed(2)} ج.م</span></p>
                                <small class="text-success">سيتم استخدام سعر التكلفة الحقيقي من التعريفات</small>
                            </div>
                        </div>
                    `;

                    // إذا كانت الكمية صفر، أظهر تحذير
                    if (data.current_quantity === 0) {
                        stockInfoDiv.innerHTML += `
                            <div class="alert alert-warning mt-3">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تنبيه:</strong> لا يوجد مخزون حالي لهذا المنتج في هذا المخزن.
                            </div>
                        `;
                    }
                } else {
                    stockInfoDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-circle me-2"></i>
                            <strong>خطأ:</strong> ${data.error}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading stock info:', error);
                stockInfoDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-circle me-2"></i>
                        <strong>خطأ:</strong> فشل في تحميل بيانات المخزون. يرجى المحاولة مرة أخرى.
                    </div>
                `;
            });
    }

    function setupStep4() {
        // تعيين القيم الافتراضية حسب نوع التسوية
        if (adjustmentData.adjustmentType === 'revalue') {
            quantityInput.value = '0';
            quantityInput.disabled = true;
            quantityInput.parentElement.style.opacity = '0.5';
        } else {
            quantityInput.disabled = false;
            quantityInput.parentElement.style.opacity = '1';

            // إذا كان نوع التسوية "تحديد الكمية"، ضع الكمية الحالية كقيمة افتراضية
            if (adjustmentData.adjustmentType === 'set') {
                quantityInput.value = stockData.currentQuantity.toFixed(3);
            } else {
                quantityInput.value = '';
            }
        }

        // تعيين سعر التكلفة الحقيقي للمنتج
        unitCostInput.value = stockData.productCostPrice.toFixed(2);

        // تحديث تسميات الحقول حسب نوع التسوية
        const quantityLabel = quantityInput.parentElement.querySelector('.form-label');
        if (adjustmentData.adjustmentType === 'increase') {
            quantityLabel.textContent = 'الكمية المضافة';
        } else if (adjustmentData.adjustmentType === 'decrease') {
            quantityLabel.textContent = 'الكمية المخصومة';
        } else if (adjustmentData.adjustmentType === 'set') {
            quantityLabel.textContent = 'الكمية الجديدة';
        } else if (adjustmentData.adjustmentType === 'revalue') {
            quantityLabel.textContent = 'الكمية (غير قابل للتعديل)';
        }

        // عرض تلميحات الأسعار
        const priceHints = document.getElementById('priceHints');
        let hintsHtml = `<small class="text-success"><strong>تم تعيين سعر التكلفة الحقيقي: ${stockData.productCostPrice.toFixed(2)} ج.م</strong></small><br>`;

        if (stockData.productSellingPrice > 0 || stockData.productWholesalePrice > 0) {
            hintsHtml += '<small class="text-muted"><strong>أسعار أخرى متاحة:</strong><br>';

            if (stockData.productSellingPrice > 0) {
                hintsHtml += `• سعر البيع: ${stockData.productSellingPrice.toFixed(2)} ج.م<br>`;
            }
            if (stockData.productWholesalePrice > 0) {
                hintsHtml += `• سعر الجملة: ${stockData.productWholesalePrice.toFixed(2)} ج.م<br>`;
            }

            hintsHtml += '</small>';
        }

        priceHints.innerHTML = hintsHtml;

        // إضافة أزرار سريعة لاختيار أسعار أخرى (إذا كانت متاحة)
        let buttonsHtml = '<div class="mt-2">';
        let hasAlternatives = false;

        if (stockData.productSellingPrice > 0) {
            buttonsHtml += `<button type="button" class="btn btn-sm btn-outline-success me-1" onclick="setPrice(${stockData.productSellingPrice})">
                استخدم سعر البيع (${stockData.productSellingPrice.toFixed(2)})
            </button>`;
            hasAlternatives = true;
        }

        if (stockData.productWholesalePrice > 0) {
            buttonsHtml += `<button type="button" class="btn btn-sm btn-outline-info me-1" onclick="setPrice(${stockData.productWholesalePrice})">
                استخدم سعر الجملة (${stockData.productWholesalePrice.toFixed(2)})
            </button>`;
            hasAlternatives = true;
        }

        buttonsHtml += '</div>';

        if (hasAlternatives) {
            priceHints.innerHTML += buttonsHtml;
        }
    }

    // دالة لتعيين السعر
    window.setPrice = function(price) {
        unitCostInput.value = price.toFixed(2);
        unitCostInput.focus();
    }

    function validateStep4() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const unitCost = parseFloat(unitCostInput.value) || 0;

        // التحقق من الكمية (إلا في حالة إعادة التقييم)
        if (adjustmentData.adjustmentType !== 'revalue') {
            if (quantity <= 0) {
                alert('يرجى إدخال كمية صحيحة أكبر من صفر');
                quantityInput.focus();
                return false;
            }

            // التحقق من عدم خصم أكثر من المتاح
            if (adjustmentData.adjustmentType === 'decrease' && quantity > stockData.currentQuantity) {
                alert(`لا يمكن خصم ${quantity.toFixed(3)} لأن الكمية المتاحة هي ${stockData.currentQuantity.toFixed(3)} فقط`);
                quantityInput.focus();
                return false;
            }

            // التحقق من الكمية السالبة في حالة "تحديد الكمية"
            if (adjustmentData.adjustmentType === 'set' && quantity < 0) {
                alert('لا يمكن أن تكون الكمية الجديدة سالبة');
                quantityInput.focus();
                return false;
            }
        }

        // التحقق من التكلفة
        if (unitCost < 0) {
            alert('لا يمكن أن تكون تكلفة الوحدة سالبة');
            unitCostInput.focus();
            return false;
        }

        // التحقق من سبب التسوية
        if (!reasonSelect.value) {
            alert('يرجى اختيار سبب التسوية');
            reasonSelect.focus();
            return false;
        }

        // التحقق من تاريخ التسوية
        if (!adjustmentDate.value) {
            alert('يرجى تحديد تاريخ التسوية');
            adjustmentDate.focus();
            return false;
        }

        return true;
    }

    function showSummary() {
        let newQuantity = stockData.currentQuantity;
        let difference = 0;
        let costChange = adjustmentData.unitCost - stockData.productCostPrice;

        // حساب الكمية الجديدة والفرق
        if (adjustmentData.adjustmentType === 'increase') {
            newQuantity += adjustmentData.quantity;
            difference = adjustmentData.quantity;
        } else if (adjustmentData.adjustmentType === 'decrease') {
            newQuantity -= adjustmentData.quantity;
            difference = -adjustmentData.quantity;
        } else if (adjustmentData.adjustmentType === 'set') {
            newQuantity = adjustmentData.quantity;
            difference = adjustmentData.quantity - stockData.currentQuantity;
        } else if (adjustmentData.adjustmentType === 'revalue') {
            newQuantity = stockData.currentQuantity; // الكمية لا تتغير
            difference = 0;
        }

        const oldValue = stockData.currentQuantity * stockData.productCostPrice;
        const newValue = newQuantity * adjustmentData.unitCost;
        const valueChange = newValue - oldValue;

        const summaryDiv = document.getElementById('summaryContent');
        summaryDiv.innerHTML = `
            <div class="info-box success">
                <h5><i class="bi bi-check-circle me-2"></i>ملخص التسوية</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>المخزن:</strong> ${adjustmentData.warehouseName}</p>
                        <p><strong>المنتج:</strong> ${adjustmentData.productName}</p>
                        <p><strong>نوع التسوية:</strong> ${getAdjustmentTypeName(adjustmentData.adjustmentType)}</p>
                        <p><strong>السبب:</strong> ${adjustmentData.reason}</p>
                        <p><strong>تاريخ التسوية:</strong> ${new Date(adjustmentData.adjustmentDate).toLocaleString('ar-EG')}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الكمية قبل التسوية:</strong> ${stockData.currentQuantity.toFixed(3)}</p>
                        <p><strong>الكمية بعد التسوية:</strong> ${newQuantity.toFixed(3)}</p>
                        ${difference !== 0 ? `<p><strong>فرق الكمية:</strong> <span class="${difference >= 0 ? 'text-success' : 'text-danger'}">${difference >= 0 ? '+' : ''}${difference.toFixed(3)}</span></p>` : ''}
                        <p><strong>سعر التكلفة الحقيقي:</strong> ${stockData.productCostPrice.toFixed(2)} ج.م</p>
                        <p><strong>سعر التسوية المطبق:</strong> ${adjustmentData.unitCost.toFixed(2)} ج.م</p>
                        ${Math.abs(costChange) > 0.01 ? `<p><strong>فرق السعر:</strong> <span class="${costChange >= 0 ? 'text-success' : 'text-danger'}">${costChange >= 0 ? '+' : ''}${costChange.toFixed(2)} ج.م</span></p>` : ''}
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <p><strong>القيمة قبل التسوية:</strong> ${oldValue.toFixed(2)} ج.م</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>القيمة بعد التسوية:</strong> ${newValue.toFixed(2)} ج.م</p>
                    </div>
                    <div class="col-12">
                        <p><strong>فرق القيمة الإجمالي:</strong> <span class="${valueChange >= 0 ? 'text-success' : 'text-danger'}">${valueChange >= 0 ? '+' : ''}${valueChange.toFixed(2)} ج.م</span></p>
                    </div>
                </div>
                ${adjustmentData.notes ? `<div class="mt-3"><p><strong>ملاحظات:</strong> ${adjustmentData.notes}</p></div>` : ''}
            </div>
        `;
    }

    function getAdjustmentTypeName(type) {
        const names = {
            'increase': 'زيادة المخزون',
            'decrease': 'تقليل المخزون',
            'set': 'تحديد الكمية',
            'revalue': 'إعادة التقييم'
        };
        return names[type] || type;
    }

    function submitForm() {
        if (!confirm('هل أنت متأكد من تطبيق هذه التسوية؟\n\nلا يمكن التراجع عن هذه العملية.')) {
            return;
        }

        // ملء النموذج المخفي
        document.getElementById('hiddenWarehouse').value = adjustmentData.warehouse;
        document.getElementById('hiddenProduct').value = adjustmentData.product;
        document.getElementById('hiddenAdjustmentType').value = adjustmentData.adjustmentType;
        document.getElementById('hiddenQuantity').value = adjustmentData.quantity || 0;
        document.getElementById('hiddenUnitCost').value = adjustmentData.unitCost;
        document.getElementById('hiddenReason').value = adjustmentData.reason;
        document.getElementById('hiddenAdjustmentDate').value = adjustmentData.adjustmentDate;
        document.getElementById('hiddenNotes').value = adjustmentData.notes;

        // تعطيل الزر وإظهار التحميل
        const submitBtn = document.getElementById('submitAdjustment');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري التطبيق...';

        // إرسال النموذج
        document.getElementById('adjustmentForm').submit();
    }
});
</script>
{% endblock %}
