#!/usr/bin/env python
"""
سكريبت سريع لتشغيل خادم Django بعد إصلاح مشكلة تضارب الأسماء
"""

import os
import sys

# تأكد من أننا في المجلد الصحيح
os.chdir(r"d:\osaric try")

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')

try:
    import django
    from django.core.management import execute_from_command_line
    
    print("🔧 تم إصلاح مشكلة تضارب الأسماء!")
    print("✅ تم تغيير 'settings' app إلى 'system_settings'")
    print("✅ تم تحديث INSTALLED_APPS")
    print("✅ تم تحديث URLs")
    
    print("\n🚀 تشغيل خادم Django...")
    
    # تشغيل الخادم مباشرة
    execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8000'])
    
except ImportError as e:
    print(f"❌ خطأ في استيراد Django: {e}")
    print("تأكد من تفعيل البيئة الافتراضية")
except Exception as e:
    print(f"❌ خطأ: {e}")
    print("جرب تشغيل: python manage.py runserver")

if __name__ == "__main__":
    pass
