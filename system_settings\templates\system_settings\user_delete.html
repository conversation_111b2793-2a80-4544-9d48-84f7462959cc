{% extends 'base.html' %}
{% load static %}

{% block title %}حذف المستخدم - {{ user.username }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'system_settings:dashboard' %}">الإعدادات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'system_settings:users_management' %}">إدارة المستخدمين</a></li>
        <li class="breadcrumb-item"><a href="{% url 'system_settings:user_detail' user.id %}">{{ user.username }}</a></li>
        <li class="breadcrumb-item active">حذف</li>
    </ol>
</nav>
{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        max-width: 600px;
        margin: 2rem auto;
        padding: 2rem;
    }

    .delete-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(220, 38, 38, 0.1);
        overflow: hidden;
        border: 2px solid #fecaca;
    }

    .delete-header {
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .delete-body {
        padding: 2rem;
    }

    .user-info {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    .user-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin: 0 auto 1rem;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        font-weight: 700;
        overflow: hidden;
    }

    .user-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .warning-box {
        background: #fef3c7;
        border: 2px solid #f59e0b;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .warning-title {
        color: #92400e;
        font-weight: 700;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .warning-text {
        color: #92400e;
        margin: 0;
    }

    .danger-box {
        background: #fef2f2;
        border: 2px solid #dc2626;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .danger-title {
        color: #dc2626;
        font-weight: 700;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .danger-text {
        color: #dc2626;
        margin: 0;
    }

    .btn-danger {
        background: #dc2626;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        background: #b91c1c;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 38, 38, 0.3);
    }

    .btn-secondary {
        background: #6b7280;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
    }

    .user-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-top: 1rem;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem;
        background: white;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .detail-label {
        font-weight: 600;
        color: #374151;
    }

    .detail-value {
        color: #6b7280;
    }

    @media (max-width: 768px) {
        .user-details {
            grid-template-columns: 1fr;
        }
        
        .delete-container {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="delete-container">
    <div class="delete-card">
        <div class="delete-header">
            <h2><i class="bi bi-exclamation-triangle"></i> حذف المستخدم</h2>
            <p>تأكيد حذف المستخدم من النظام</p>
        </div>

        <div class="delete-body">
            <!-- معلومات المستخدم -->
            <div class="user-info">
                <div class="user-avatar">
                    {% if user.profile and user.profile.avatar %}
                        <img src="{{ user.profile.avatar.url }}" alt="{{ user.get_full_name|default:user.username }}">
                    {% else %}
                        {{ user.first_name.0|default:user.username.0|upper }}
                    {% endif %}
                </div>
                <h4>{{ user.get_full_name|default:user.username }}</h4>
                <p style="color: #6b7280; margin: 0;">@{{ user.username }}</p>
                
                <div class="user-details">
                    <div class="detail-item">
                        <span class="detail-label">البريد الإلكتروني:</span>
                        <span class="detail-value">{{ user.email|default:"غير محدد" }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الانضمام:</span>
                        <span class="detail-value">{{ user.date_joined|date:"d/m/Y" }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">آخر دخول:</span>
                        <span class="detail-value">{{ user.last_login|date:"d/m/Y H:i"|default:"لم يسجل دخول" }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الحالة:</span>
                        <span class="detail-value">
                            {% if user.is_active %}نشط{% else %}غير نشط{% endif %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- تحذير -->
            <div class="warning-box">
                <div class="warning-title">
                    <i class="bi bi-exclamation-triangle"></i>
                    تحذير مهم
                </div>
                <p class="warning-text">
                    أنت على وشك حذف هذا المستخدم من النظام. هذا الإجراء سيؤثر على:
                </p>
                <ul style="color: #92400e; margin-top: 0.5rem;">
                    <li>جميع البيانات المرتبطة بهذا المستخدم</li>
                    <li>السجلات والتقارير التي أنشأها</li>
                    <li>الصلاحيات والأدوار المخصصة له</li>
                </ul>
            </div>

            <!-- تحذير خطر -->
            <div class="danger-box">
                <div class="danger-title">
                    <i class="bi bi-shield-exclamation"></i>
                    إجراء غير قابل للتراجع
                </div>
                <p class="danger-text">
                    <strong>لا يمكن التراجع عن هذا الإجراء!</strong> 
                    سيتم حذف جميع بيانات المستخدم نهائياً من النظام.
                </p>
            </div>

            <!-- نموذج التأكيد -->
            <form method="post">
                {% csrf_token %}
                <div style="display: flex; gap: 1rem; justify-content: center;">
                    <button type="submit" class="btn-danger">
                        <i class="bi bi-trash"></i> نعم، احذف المستخدم
                    </button>
                    <a href="{% url 'system_settings:user_detail' user.id %}" class="btn-secondary">
                        <i class="bi bi-arrow-left"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
