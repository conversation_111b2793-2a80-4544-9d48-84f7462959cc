from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.http import JsonResponse
from datetime import datetime, timedelta
from .models import Asset, AssetCategory, AssetMaintenance, AssetTransfer, AssetDisposal
from .forms import (AssetCategoryForm, AssetForm, AssetMaintenanceForm,
                   AssetTransferForm, AssetDisposalForm, AssetSearchForm)

@login_required
def assets_dashboard(request):
    """لوحة تحكم الأصول الثابتة"""
    # إحصائيات عامة
    total_assets = Asset.objects.count()
    active_assets = Asset.objects.filter(status='active').count()
    total_categories = AssetCategory.objects.filter(is_active=True).count()

    # إحصائيات مالية
    total_purchase_value = Asset.objects.aggregate(total=Sum('purchase_price'))['total'] or 0

    # حساب القيمة الدفترية الحالية
    current_book_value = 0
    for asset in Asset.objects.all():
        current_book_value += asset.current_book_value

    total_depreciation = total_purchase_value - current_book_value

    # إحصائيات الصيانة
    maintenance_this_month = AssetMaintenance.objects.filter(
        maintenance_date__month=datetime.now().month,
        maintenance_date__year=datetime.now().year
    ).count()

    maintenance_cost_this_year = AssetMaintenance.objects.filter(
        maintenance_date__year=datetime.now().year
    ).aggregate(total=Sum('cost'))['total'] or 0

    # الأصول حسب الحالة
    assets_by_status = Asset.objects.values('status').annotate(count=Count('id'))

    # أحدث الأصول
    recent_assets = Asset.objects.select_related('category').order_by('-created_at')[:5]

    # أحدث الصيانة
    recent_maintenance = AssetMaintenance.objects.select_related('asset').order_by('-maintenance_date')[:5]

    context = {
        'total_assets': total_assets,
        'active_assets': active_assets,
        'total_categories': total_categories,
        'total_purchase_value': total_purchase_value,
        'current_book_value': current_book_value,
        'total_depreciation': total_depreciation,
        'maintenance_this_month': maintenance_this_month,
        'maintenance_cost_this_year': maintenance_cost_this_year,
        'assets_by_status': assets_by_status,
        'recent_assets': recent_assets,
        'recent_maintenance': recent_maintenance,
    }
    return render(request, 'assets/dashboard.html', context)

# ========== إدارة فئات الأصول ==========
@login_required
def category_list(request):
    """قائمة فئات الأصول"""
    search = request.GET.get('search', '')
    categories = AssetCategory.objects.all()

    if search:
        categories = categories.filter(Q(name__icontains=search))

    paginator = Paginator(categories, 20)
    page_number = request.GET.get('page')
    categories = paginator.get_page(page_number)

    context = {
        'categories': categories,
        'search': search,
    }
    return render(request, 'assets/category_list.html', context)

@login_required
def category_create(request):
    """إضافة فئة أصول جديدة"""
    if request.method == 'POST':
        form = AssetCategoryForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة فئة الأصول بنجاح')
            return redirect('assets:category_list')
    else:
        form = AssetCategoryForm()

    context = {'form': form, 'title': 'إضافة فئة أصول جديدة'}
    return render(request, 'assets/category_form.html', context)

@login_required
def category_edit(request, pk):
    """تعديل فئة أصول"""
    category = get_object_or_404(AssetCategory, pk=pk)
    if request.method == 'POST':
        form = AssetCategoryForm(request.POST, instance=category)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث فئة الأصول بنجاح')
            return redirect('assets:category_list')
    else:
        form = AssetCategoryForm(instance=category)

    context = {'form': form, 'title': 'تعديل فئة الأصول', 'category': category}
    return render(request, 'assets/category_form.html', context)

@login_required
def category_delete(request, pk):
    """حذف فئة أصول"""
    category = get_object_or_404(AssetCategory, pk=pk)
    if request.method == 'POST':
        category.delete()
        messages.success(request, 'تم حذف فئة الأصول بنجاح')
        return redirect('assets:category_list')

    context = {'category': category}
    return render(request, 'assets/category_confirm_delete.html', context)

# ========== إدارة الأصول ==========
@login_required
def asset_list(request):
    """قائمة الأصول"""
    form = AssetSearchForm(request.GET)
    assets = Asset.objects.select_related('category', 'responsible_person')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        category = form.cleaned_data.get('category')
        status = form.cleaned_data.get('status')
        condition = form.cleaned_data.get('condition')
        responsible_person = form.cleaned_data.get('responsible_person')

        if search:
            assets = assets.filter(
                Q(name__icontains=search) |
                Q(asset_code__icontains=search) |
                Q(serial_number__icontains=search)
            )

        if category:
            assets = assets.filter(category=category)

        if status:
            assets = assets.filter(status=status)

        if condition:
            assets = assets.filter(condition=condition)

        if responsible_person:
            assets = assets.filter(responsible_person=responsible_person)

    paginator = Paginator(assets, 20)
    page_number = request.GET.get('page')
    assets = paginator.get_page(page_number)

    context = {
        'assets': assets,
        'form': form,
    }
    return render(request, 'assets/asset_list.html', context)

@login_required
def asset_create(request):
    """إضافة أصل جديد"""
    if request.method == 'POST':
        form = AssetForm(request.POST)
        if form.is_valid():
            try:
                asset = form.save()
                messages.success(request, f'تم إضافة الأصل "{asset.name}" بنجاح')
                return redirect('assets:asset_list')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء حفظ الأصل: {str(e)}')
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء في النموذج')
    else:
        form = AssetForm()

    context = {
        'form': form,
        'title': 'إضافة أصل جديد',
        'asset': None  # للتمييز بين الإضافة والتعديل
    }
    return render(request, 'assets/asset_form.html', context)

@login_required
def asset_detail(request, pk):
    """تفاصيل الأصل"""
    asset = get_object_or_404(Asset, pk=pk)
    maintenances = asset.maintenances.order_by('-maintenance_date')[:10]
    transfers = asset.transfers.order_by('-transfer_date')[:10]

    context = {
        'asset': asset,
        'maintenances': maintenances,
        'transfers': transfers,
    }
    return render(request, 'assets/asset_detail.html', context)

@login_required
def asset_edit(request, pk):
    """تعديل أصل"""
    asset = get_object_or_404(Asset, pk=pk)
    if request.method == 'POST':
        form = AssetForm(request.POST, instance=asset)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات الأصل بنجاح')
            return redirect('assets:asset_detail', pk=asset.pk)
    else:
        form = AssetForm(instance=asset)

    context = {'form': form, 'title': 'تعديل الأصل', 'asset': asset}
    return render(request, 'assets/asset_form.html', context)

@login_required
def asset_delete(request, pk):
    """حذف أصل"""
    asset = get_object_or_404(Asset, pk=pk)
    if request.method == 'POST':
        asset.delete()
        messages.success(request, 'تم حذف الأصل بنجاح')
        return redirect('assets:asset_list')

    context = {'asset': asset}
    return render(request, 'assets/asset_confirm_delete.html', context)
