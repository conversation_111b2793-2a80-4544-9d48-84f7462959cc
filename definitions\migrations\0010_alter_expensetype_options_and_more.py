# Generated by Django 5.2.4 on 2025-07-14 00:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0009_assetbrand_notes_assetbrand_updated_at'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='expensetype',
            options={'ordering': ['code'], 'verbose_name': 'نوع مصروف', 'verbose_name_plural': 'أنواع المصروفات'},
        ),
        migrations.RemoveField(
            model_name='expensetype',
            name='is_tax_deductible',
        ),
        migrations.RemoveField(
            model_name='expensetype',
            name='requires_approval',
        ),
        migrations.AddField(
            model_name='expensetype',
            name='category',
            field=models.CharField(choices=[('operational', 'تشغيلي'), ('administrative', 'إداري')], default='operational', max_length=20, verbose_name='التصنيف'),
        ),
        migrations.AddField(
            model_name='expensetype',
            name='expense_type',
            field=models.CharField(choices=[('fixed', 'ثابت'), ('variable', 'متغير')], default='fixed', max_length=20, verbose_name='نوع المصروف'),
        ),
        migrations.AddField(
            model_name='expensetype',
            name='name_en',
            field=models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية'),
        ),
        migrations.AddField(
            model_name='expensetype',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AddField(
            model_name='expensetype',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
    ]
