{% extends 'base.html' %}

{% block title %}{{ warehouse.name }} - تفاصيل المخزن - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'warehouses:dashboard' %}">المخازن</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'warehouses:warehouse_list' %}">قائمة المخازن</a></li>
                    <li class="breadcrumb-item active">{{ warehouse.name }}</li>
                </ol>
            </nav>
            <h1 class="page-title">{{ warehouse.name }}</h1>
            <p class="page-subtitle">تفاصيل المخزن ومعلومات المخزون</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'warehouses:warehouse_edit' warehouse.pk %}" class="btn btn-primary">
                <i class="bi bi-pencil me-2"></i>تعديل المخزن
            </a>
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-three-dots"></i>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#">
                        <i class="bi bi-plus me-2"></i>إضافة منطقة
                    </a></li>
                    <li><a class="dropdown-item" href="#">
                        <i class="bi bi-box me-2"></i>إضافة مخزون
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#">
                        <i class="bi bi-file-earmark-pdf me-2"></i>طباعة التقرير
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Warehouse Info Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-info-circle me-2"></i>معلومات المخزن
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold text-muted">اسم المخزن:</td>
                            <td>{{ warehouse.name }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">رمز المخزن:</td>
                            <td><span class="badge bg-primary">{{ warehouse.code }}</span></td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">نوع المخزن:</td>
                            <td><span class="badge bg-info">{{ warehouse.get_warehouse_type_display }}</span></td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">الفرع:</td>
                            <td>{{ warehouse.branch.name }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">الحالة:</td>
                            <td>
                                {% if warehouse.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold text-muted">السعة:</td>
                            <td>
                                {% if warehouse.capacity %}
                                    {{ warehouse.capacity }} متر مكعب
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">المسؤول:</td>
                            <td>{{ warehouse.manager_name|default:"غير محدد" }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">رقم الهاتف:</td>
                            <td>{{ warehouse.phone|default:"غير محدد" }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">البريد الإلكتروني:</td>
                            <td>{{ warehouse.email|default:"غير محدد" }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold text-muted">تاريخ الإنشاء:</td>
                            <td>{{ warehouse.created_at|date:"Y/m/d H:i" }}</td>
                        </tr>
                    </table>
                </div>
            </div>
            {% if warehouse.address %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="fw-bold text-muted">العنوان:</h6>
                        <p class="mb-0">{{ warehouse.address }}</p>
                    </div>
                </div>
            {% endif %}
            {% if warehouse.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="fw-bold text-muted">ملاحظات:</h6>
                        <p class="mb-0">{{ warehouse.notes }}</p>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-box text-primary" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-3 mb-1">{{ total_products }}</h4>
                    <p class="text-muted mb-0">أصناف المنتجات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-boxes text-success" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-3 mb-1">{{ total_quantity|floatformat:0 }}</h4>
                    <p class="text-muted mb-0">إجمالي الكميات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-3 mb-1">{{ low_stock_count }}</h4>
                    <p class="text-muted mb-0">منتجات منخفضة</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-geo-alt text-info" style="font-size: 2.5rem;"></i>
                    <h4 class="mt-3 mb-1">{{ locations_count }}</h4>
                    <p class="text-muted mb-0">مواقع التخزين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Warehouse Zones and Recent Movements -->
    <div class="row g-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-diagram-3 me-2"></i>مناطق المخزن
                    </h5>
                    <a href="#" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-plus me-1"></i>إضافة منطقة
                    </a>
                </div>
                <div class="card-body">
                    {% if zones %}
                        <div class="list-group list-group-flush">
                            {% for zone in zones %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <h6 class="mb-1">{{ zone.name }}</h6>
                                        <small class="text-muted">{{ zone.code }}</small>
                                        {% if zone.temperature_controlled %}
                                            <span class="badge bg-info ms-2">مكيف</span>
                                        {% endif %}
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" title="عرض المواقع">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-diagram-3 text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">لا توجد مناطق محددة</p>
                            <a href="#" class="btn btn-sm btn-primary mt-2">
                                <i class="bi bi-plus me-1"></i>إضافة أول منطقة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>آخر حركات المخزون
                    </h5>
                    <a href="{% url 'warehouses:stock_movements' %}?warehouse={{ warehouse.id }}" class="btn btn-sm btn-outline-primary">
                        عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_movements %}
                        <div class="list-group list-group-flush">
                            {% for movement in recent_movements %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                    <div>
                                        <h6 class="mb-1">{{ movement.product.name }}</h6>
                                        <small class="text-muted">{{ movement.get_movement_type_display }}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge {% if movement.movement_type == 'in' %}bg-success{% else %}bg-danger{% endif %}">
                                            {% if movement.movement_type == 'in' %}+{% else %}-{% endif %}{{ movement.quantity }}
                                        </span>
                                        <br>
                                        <small class="text-muted">{{ movement.created_at|date:"m/d H:i" }}</small>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-arrow-left-right text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">لا توجد حركات حديثة</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .table-borderless td {
        border: none;
        padding: 0.5rem 0;
    }
    
    .list-group-item {
        border: none;
        border-bottom: 1px solid #dee2e6;
    }
    
    .list-group-item:last-child {
        border-bottom: none;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
</style>
{% endblock %}
