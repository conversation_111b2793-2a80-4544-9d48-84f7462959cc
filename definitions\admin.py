from django.contrib import admin
from .models import (
    WarehouseDefinition, ProductCategory, ProductDefinition,
    PersonDefinition, AssetGroup, ExpenseType, ExpenseName,
    RevenueType, RevenueName, ProfitCenter, FinishedProductModel,
    ProductionStage, ProductionModelStage
)

@admin.register(WarehouseDefinition)
class WarehouseDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'warehouse_type', 'is_active']
    list_filter = ['warehouse_type', 'is_active']
    search_fields = ['code', 'name']

@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'parent', 'is_active']
    list_filter = ['is_active']
    search_fields = ['code', 'name']

@admin.register(ProductDefinition)
class ProductDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'category', 'product_type', 'cost_price', 'selling_price', 'is_active']
    list_filter = ['product_type', 'category', 'is_active']
    search_fields = ['code', 'name']

@admin.register(PersonDefinition)
class PersonDefinitionAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'person_type', 'phone', 'is_active']
    list_filter = ['person_type', 'is_active']
    search_fields = ['code', 'name', 'phone']

@admin.register(AssetGroup)
class AssetGroupAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'depreciation_rate', 'useful_life_years', 'is_active']
    list_filter = ['is_active']
    search_fields = ['code', 'name']

@admin.register(ExpenseType)
class ExpenseTypeAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'is_active']
    list_filter = ['is_active']
    search_fields = ['code', 'name']

@admin.register(ExpenseName)
class ExpenseNameAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'expense_type', 'is_active']
    list_filter = ['expense_type', 'is_active']
    search_fields = ['code', 'name']

@admin.register(RevenueType)
class RevenueTypeAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'is_active']
    list_filter = ['is_active']
    search_fields = ['code', 'name']

@admin.register(RevenueName)
class RevenueNameAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'revenue_type', 'is_active']
    list_filter = ['revenue_type', 'is_active']
    search_fields = ['code', 'name']

@admin.register(ProfitCenter)
class ProfitCenterAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'is_active']
    list_filter = ['is_active']
    search_fields = ['code', 'name']


@admin.register(FinishedProductModel)
class FinishedProductModelAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'product_type', 'quality_level', 'final_product', 'is_active', 'is_approved']
    list_filter = ['product_type', 'quality_level', 'is_active', 'is_approved']
    search_fields = ['code', 'name', 'final_product__name']
    readonly_fields = ['total_estimated_cost', 'estimated_profit_margin', 'created_at', 'updated_at']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('code', 'name', 'name_en', 'description', 'product_type', 'quality_level')
        }),
        ('معلومات الإنتاج', {
            'fields': ('final_product', 'quantity_produced', 'unit_of_measure', 'production_time_hours')
        }),
        ('التكاليف', {
            'fields': ('labor_cost', 'overhead_cost', 'estimated_material_cost', 'estimated_total_cost', 'target_selling_price')
        }),
        ('الجودة والمواصفات', {
            'fields': ('specifications', 'quality_standards', 'testing_requirements')
        }),
        ('معلومات إضافية', {
            'fields': ('shelf_life_days', 'storage_conditions', 'packaging_requirements')
        }),
        ('الحالة والاعتماد', {
            'fields': ('is_active', 'is_approved', 'approval_date', 'approved_by')
        }),
        ('معلومات النظام', {
            'fields': ('total_estimated_cost', 'estimated_profit_margin', 'created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ProductionStage)
class ProductionStageAdmin(admin.ModelAdmin):
    list_display = ['sequence_number', 'code', 'name', 'stage_type', 'standard_duration_hours', 'status', 'is_critical']
    list_filter = ['stage_type', 'status', 'is_critical', 'is_optional', 'can_run_parallel']
    search_fields = ['code', 'name']
    readonly_fields = ['total_cost_per_hour', 'estimated_stage_cost', 'created_at', 'updated_at']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('code', 'name', 'name_en', 'description', 'stage_type', 'sequence_number')
        }),
        ('الوقت والتكلفة', {
            'fields': ('standard_duration_hours', 'setup_time_hours', 'labor_cost_per_hour', 'overhead_cost_per_hour')
        }),
        ('المتطلبات', {
            'fields': ('required_skills', 'required_equipment', 'safety_requirements')
        }),
        ('معايير الجودة', {
            'fields': ('quality_checkpoints', 'acceptance_criteria', 'rejection_criteria')
        }),
        ('التسلسل والعلاقات', {
            'fields': ('previous_stage', 'can_run_parallel')
        }),
        ('الحالة والخصائص', {
            'fields': ('status', 'is_critical', 'is_optional')
        }),
        ('معلومات النظام', {
            'fields': ('total_cost_per_hour', 'estimated_stage_cost', 'created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )


class ProductionModelStageInline(admin.TabularInline):
    model = ProductionModelStage
    extra = 1
    fields = ['production_stage', 'sequence_in_model', 'estimated_duration_hours', 'estimated_cost', 'is_mandatory']


@admin.register(ProductionModelStage)
class ProductionModelStageAdmin(admin.ModelAdmin):
    list_display = ['production_model', 'production_stage', 'sequence_in_model', 'estimated_duration_hours', 'estimated_cost', 'is_active']
    list_filter = ['is_active', 'is_mandatory', 'production_model__product_type']
    search_fields = ['production_model__name', 'production_stage__name']
