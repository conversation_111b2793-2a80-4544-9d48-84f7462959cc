from django.contrib import admin
from .models import <PERSON><PERSON>istory, PopularSearch, SearchSuggestion


@admin.register(SearchHistory)
class SearchHistoryAdmin(admin.ModelAdmin):
    list_display = ['user', 'query', 'results_count', 'search_type', 'execution_time', 'created_at']
    list_filter = ['search_type', 'created_at', 'results_count']
    search_fields = ['query', 'user__username', 'user__email']
    readonly_fields = ['created_at']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(PopularSearch)
class PopularSearchAdmin(admin.ModelAdmin):
    list_display = ['query', 'search_count', 'last_searched']
    list_filter = ['last_searched', 'search_count']
    search_fields = ['query']
    readonly_fields = ['last_searched']
    ordering = ['-search_count', '-last_searched']


@admin.register(SearchSuggestion)
class SearchSuggestionAdmin(admin.ModelAdmin):
    list_display = ['keyword', 'suggestion', 'category', 'priority', 'is_active']
    list_filter = ['category', 'priority', 'is_active']
    search_fields = ['keyword', 'suggestion']
    list_editable = ['priority', 'is_active']
