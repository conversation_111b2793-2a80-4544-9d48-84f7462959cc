{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم الخزائن{% endblock %}

{% block extra_css %}
<style>
    .treasuries-header {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
    }

    .stats-card {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-bottom: 2rem;
    }

    .quick-action-btn {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(23,162,184,0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Treasuries Header -->
<div class="treasuries-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-safe me-2"></i>
                    لوحة تحكم الخزائن
                </h1>
                <p class="mb-0 opacity-75">إدارة شاملة للخزائن النقدية والمعاملات المالية</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <i class="bi bi-calendar me-2"></i>
                    {{ "now"|date:"Y/m/d" }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container-fluid">
    <div class="quick-actions">
        <a href="{% url 'treasuries:treasury_create' %}" class="quick-action-btn">
            <i class="bi bi-plus-circle"></i>
            إضافة خزينة جديدة
        </a>
        <a href="{% url 'treasuries:transaction_create' %}" class="quick-action-btn">
            <i class="bi bi-arrow-down-up"></i>
            إضافة معاملة
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-arrow-left-right"></i>
            تحويل بين الخزائن
        </a>
        <a href="{% url 'treasuries:transaction_list' %}" class="quick-action-btn">
            <i class="bi bi-list-ul"></i>
            عرض المعاملات
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(23, 162, 184, 0.1); color: #17a2b8;">
                    <i class="bi bi-safe"></i>
                </div>
                <div class="stats-number" style="color: #17a2b8;">{{ total_treasuries }}</div>
                <p class="stats-label">إجمالي الخزائن</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(40, 167, 69, 0.1); color: #28a745;">
                    <i class="bi bi-arrow-down-up"></i>
                </div>
                <div class="stats-number text-success">{{ total_transactions }}</div>
                <p class="stats-label">إجمالي المعاملات</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(0, 123, 255, 0.1); color: #007bff;">
                    <i class="bi bi-arrow-left-right"></i>
                </div>
                <div class="stats-number text-primary">{{ total_transfers }}</div>
                <p class="stats-label">التحويلات</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-clock"></i>
                </div>
                <div class="stats-number text-warning">{{ monthly_transactions }}</div>
                <p class="stats-label">معاملات هذا الشهر</p>
            </div>
        </div>
    </div>

    <!-- Financial Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(32, 201, 151, 0.1); color: #20c997;">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="stats-number text-info">{{ total_balance|floatformat:2 }}</div>
                <p class="stats-label">إجمالي الأرصدة (ريال)</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <div class="stats-number text-warning">{{ pending_transactions }}</div>
                <p class="stats-label">معاملات معلقة</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-arrow-down"></i>
                </div>
                <div class="stats-number text-danger">{{ low_balance_treasuries }}</div>
                <p class="stats-label">خزائن برصيد منخفض</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(111, 66, 193, 0.1); color: #6f42c1;">
                    <i class="bi bi-arrow-up"></i>
                </div>
                <div class="stats-number" style="color: #6f42c1;">{{ high_balance_treasuries }}</div>
                <p class="stats-label">خزائن برصيد مرتفع</p>
            </div>
        </div>
    </div>

    <!-- Recent Transactions and Transfers -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث المعاملات</h5>
                    <a href="{% url 'treasuries:transaction_list' %}" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الخزينة</th>
                                    <th>نوع المعاملة</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>{{ transaction.treasury.name }}</td>
                                    <td>
                                        {% if transaction.transaction_type == 'receipt' %}
                                            <span class="badge bg-success">قبض</span>
                                        {% elif transaction.transaction_type == 'payment' %}
                                            <span class="badge bg-danger">دفع</span>
                                        {% elif transaction.transaction_type == 'transfer_in' %}
                                            <span class="badge bg-primary">تحويل وارد</span>
                                        {% elif transaction.transaction_type == 'transfer_out' %}
                                            <span class="badge bg-warning">تحويل صادر</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ transaction.get_transaction_type_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ transaction.amount|floatformat:2 }} ريال</td>
                                    <td>{{ transaction.transaction_date }}</td>
                                    <td>
                                        {% if transaction.status == 'completed' %}
                                            <span class="badge bg-success">مكتمل</span>
                                        {% elif transaction.status == 'pending' %}
                                            <span class="badge bg-warning">معلق</span>
                                        {% else %}
                                            <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-arrow-down-up text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد معاملات حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث التحويلات</h5>
                    <a href="#" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_transfers %}
                    <div class="list-group list-group-flush">
                        {% for transfer in recent_transfers %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ transfer.amount|floatformat:2 }} ريال</h6>
                                    <p class="mb-1 small">من {{ transfer.from_treasury.name }} إلى {{ transfer.to_treasury.name }}</p>
                                    <small class="text-muted">{{ transfer.transfer_date }}</small>
                                </div>
                                <div>
                                    {% if transfer.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                    {% elif transfer.status == 'pending' %}
                                        <span class="badge bg-warning">معلق</span>
                                    {% else %}
                                        <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-arrow-left-right text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد تحويلات حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
