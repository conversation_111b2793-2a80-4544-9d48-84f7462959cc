#!/usr/bin/env python3
"""
خادم مؤقت للوصول من الهاتف
"""
import socket
import subprocess
import sys
import os

def get_local_ip():
    """الحصول على IP المحلي"""
    try:
        # إنشاء اتصال مؤقت لمعرفة IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def main():
    # الحصول على IP
    local_ip = get_local_ip()
    
    print("=" * 50)
    print("🚀 خادم أوساريك للهاتف المحمول")
    print("=" * 50)
    print(f"📱 IP الكمبيوتر: {local_ip}")
    print(f"🌐 رابط الهاتف: http://{local_ip}:8000/accounts/login/")
    print("=" * 50)
    print("📋 تعليمات:")
    print("1. تأكد أن الهاتف والكمبيوتر على نفس الشبكة")
    print("2. افتح متصفح الهاتف")
    print(f"3. اكتب: http://{local_ip}:8000/accounts/login/")
    print("4. استخدم: admin / admin123")
    print("=" * 50)
    
    # تشغيل خادم Django
    try:
        os.chdir("D:/osaric try")
        subprocess.run([
            sys.executable, "manage.py", "runserver", f"0.0.0.0:8000"
        ])
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("\n🔧 جرب تشغيل الأمر يدوياً:")
        print("python manage.py runserver 0.0.0.0:8000")

if __name__ == "__main__":
    main()
