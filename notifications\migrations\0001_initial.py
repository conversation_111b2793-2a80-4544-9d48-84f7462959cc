# Generated manually

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم القالب')),
                ('title_template', models.CharField(max_length=200, verbose_name='قالب العنوان')),
                ('message_template', models.TextField(verbose_name='قالب الرسالة')),
                ('notification_type', models.CharField(choices=[('info', 'معلومات'), ('success', 'نجاح'), ('warning', 'تحذير'), ('error', 'خطأ'), ('system', 'نظام'), ('message', 'رسالة'), ('inventory', 'مخزون'), ('order', 'طلب'), ('payment', 'دفعة'), ('user', 'مستخدم')], max_length=20, verbose_name='نوع الإشعار')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('icon', models.CharField(default='bi-bell', max_length=50, verbose_name='الأيقونة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'قالب إشعار',
                'verbose_name_plural': 'قوالب الإشعارات',
            },
        ),
        migrations.CreateModel(
            name='NotificationSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_notifications', models.BooleanField(default=True, verbose_name='إشعارات البريد الإلكتروني')),
                ('browser_notifications', models.BooleanField(default=True, verbose_name='إشعارات المتصفح')),
                ('sound_notifications', models.BooleanField(default=True, verbose_name='إشعارات صوتية')),
                ('notify_messages', models.BooleanField(default=True, verbose_name='إشعارات الرسائل')),
                ('notify_inventory', models.BooleanField(default=True, verbose_name='إشعارات المخزون')),
                ('notify_orders', models.BooleanField(default=True, verbose_name='إشعارات الطلبات')),
                ('notify_payments', models.BooleanField(default=True, verbose_name='إشعارات المدفوعات')),
                ('notify_system', models.BooleanField(default=True, verbose_name='إشعارات النظام')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_settings', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'إعدادات الإشعارات',
                'verbose_name_plural': 'إعدادات الإشعارات',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('notification_type', models.CharField(choices=[('info', 'معلومات'), ('success', 'نجاح'), ('warning', 'تحذير'), ('error', 'خطأ'), ('system', 'نظام'), ('message', 'رسالة'), ('inventory', 'مخزون'), ('order', 'طلب'), ('payment', 'دفعة'), ('user', 'مستخدم')], default='info', max_length=20, verbose_name='نوع الإشعار')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('is_seen', models.BooleanField(default=False, verbose_name='تم عرضه')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('action_url', models.URLField(blank=True, null=True, verbose_name='رابط الإجراء')),
                ('icon', models.CharField(default='bi-bell', max_length=50, verbose_name='الأيقونة')),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستقبل')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to=settings.AUTH_USER_MODEL, verbose_name='المرسل')),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
                'ordering': ['-created_at'],
            },
        ),
    ]
