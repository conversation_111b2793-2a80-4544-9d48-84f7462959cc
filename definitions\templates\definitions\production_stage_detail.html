{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    .info-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-left: 4px solid #007bff;
    }
    .cost-breakdown {
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
    }
    .stage-flow {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border: 2px solid #e9ecef;
    }
    .stage-flow.current {
        border-color: #007bff;
        background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    }
    .stage-flow.previous {
        border-color: #28a745;
        background: linear-gradient(135deg, #e8f5e8, #c3e6c3);
    }
    .stage-flow.next {
        border-color: #ffc107;
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-list-ol me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:production_stage_edit' stage.id %}" class="btn btn-warning">
                    <i class="bi bi-pencil me-2"></i>تعديل
                </a>
                <a href="{% url 'definitions:production_stage_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- المعلومات الأساسية -->
    <div class="content-card">
        <h3><i class="bi bi-info-circle me-2"></i>المعلومات الأساسية</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="info-card">
                    <h5>كود المرحلة</h5>
                    <p class="mb-0"><strong>{{ stage.code }}</strong></p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-card">
                    <h5>اسم المرحلة</h5>
                    <p class="mb-0">{{ stage.name }}</p>
                    {% if stage.name_en %}
                        <small class="text-muted">{{ stage.name_en }}</small>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-3">
                <div class="info-card">
                    <h5>رقم التسلسل</h5>
                    <span class="badge bg-primary fs-6">{{ stage.sequence_number }}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-card">
                    <h5>نوع المرحلة</h5>
                    <span class="badge bg-info">{{ stage.get_stage_type_display }}</span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-card">
                    <h5>الحالة</h5>
                    {% if stage.status == 'active' %}
                        <span class="badge bg-success">نشط</span>
                    {% elif stage.status == 'inactive' %}
                        <span class="badge bg-danger">غير نشط</span>
                    {% elif stage.status == 'under_review' %}
                        <span class="badge bg-warning">قيد المراجعة</span>
                    {% else %}
                        <span class="badge bg-secondary">ملغي</span>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-3">
                <div class="info-card">
                    <h5>الخصائص</h5>
                    {% if stage.is_critical %}
                        <span class="badge bg-danger">حرجة</span>
                    {% endif %}
                    {% if stage.is_optional %}
                        <span class="badge bg-secondary">اختيارية</span>
                    {% endif %}
                    {% if stage.can_run_parallel %}
                        <span class="badge bg-info">متوازية</span>
                    {% endif %}
                </div>
            </div>
        </div>
        {% if stage.description %}
        <div class="info-card">
            <h5>الوصف</h5>
            <p class="mb-0">{{ stage.description|linebreaks }}</p>
        </div>
        {% endif %}
    </div>

    <!-- الوقت والتكلفة -->
    <div class="content-card">
        <h3><i class="bi bi-clock me-2"></i>الوقت والتكلفة</h3>
        <div class="cost-breakdown">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>المدة المعيارية</h5>
                        <h3 class="text-primary">{{ stage.standard_duration_hours }}</h3>
                        <small>ساعة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>وقت الإعداد</h5>
                        <h3 class="text-info">{{ stage.setup_time_hours }}</h3>
                        <small>ساعة</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>تكلفة العمالة/ساعة</h5>
                        <h3 class="text-warning">{{ stage.labor_cost_per_hour|floatformat:2 }}</h3>
                        <small>جنيه</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>التكاليف الإضافية/ساعة</h5>
                        <h3 class="text-secondary">{{ stage.overhead_cost_per_hour|floatformat:2 }}</h3>
                        <small>جنيه</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="info-card">
                    <h5>إجمالي التكلفة في الساعة</h5>
                    <p class="mb-0">{{ stage.total_cost_per_hour|floatformat:2 }} جنيه</p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="info-card">
                    <h5>التكلفة المقدرة للمرحلة</h5>
                    <p class="mb-0">{{ stage.estimated_stage_cost|floatformat:2 }} جنيه</p>
                </div>
            </div>
        </div>
    </div>

    <!-- المتطلبات -->
    {% if stage.required_skills or stage.required_equipment or stage.safety_requirements %}
    <div class="content-card">
        <h3><i class="bi bi-tools me-2"></i>المتطلبات</h3>
        <div class="row">
            {% if stage.required_skills %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>المهارات المطلوبة</h5>
                    <p class="mb-0">{{ stage.required_skills|linebreaks }}</p>
                </div>
            </div>
            {% endif %}
            {% if stage.required_equipment %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>المعدات المطلوبة</h5>
                    <p class="mb-0">{{ stage.required_equipment|linebreaks }}</p>
                </div>
            </div>
            {% endif %}
            {% if stage.safety_requirements %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>متطلبات السلامة</h5>
                    <p class="mb-0">{{ stage.safety_requirements|linebreaks }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- معايير الجودة -->
    {% if stage.quality_checkpoints or stage.acceptance_criteria or stage.rejection_criteria %}
    <div class="content-card">
        <h3><i class="bi bi-award me-2"></i>معايير الجودة</h3>
        <div class="row">
            {% if stage.quality_checkpoints %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>نقاط فحص الجودة</h5>
                    <p class="mb-0">{{ stage.quality_checkpoints|linebreaks }}</p>
                </div>
            </div>
            {% endif %}
            {% if stage.acceptance_criteria %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>معايير القبول</h5>
                    <p class="mb-0">{{ stage.acceptance_criteria|linebreaks }}</p>
                </div>
            </div>
            {% endif %}
            {% if stage.rejection_criteria %}
            <div class="col-md-4">
                <div class="info-card">
                    <h5>معايير الرفض</h5>
                    <p class="mb-0">{{ stage.rejection_criteria|linebreaks }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- تدفق المراحل -->
    <div class="content-card">
        <h3><i class="bi bi-diagram-3 me-2"></i>تدفق المراحل</h3>
        
        <!-- المرحلة السابقة -->
        {% if stage.previous_stage %}
        <div class="stage-flow previous">
            <div class="row align-items-center">
                <div class="col-md-1">
                    <i class="bi bi-arrow-up-circle text-success" style="font-size: 2rem;"></i>
                </div>
                <div class="col-md-11">
                    <h6 class="mb-1">المرحلة السابقة</h6>
                    <p class="mb-0">{{ stage.previous_stage.sequence_number }}. {{ stage.previous_stage.name }}</p>
                    <small class="text-muted">{{ stage.previous_stage.get_stage_type_display }}</small>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- المرحلة الحالية -->
        <div class="stage-flow current">
            <div class="row align-items-center">
                <div class="col-md-1">
                    <i class="bi bi-circle-fill text-primary" style="font-size: 2rem;"></i>
                </div>
                <div class="col-md-11">
                    <h6 class="mb-1">المرحلة الحالية</h6>
                    <p class="mb-0">{{ stage.sequence_number }}. {{ stage.name }}</p>
                    <small class="text-muted">{{ stage.get_stage_type_display }}</small>
                </div>
            </div>
        </div>

        <!-- المراحل التالية -->
        {% if stage.next_stages.exists %}
        {% for next_stage in stage.next_stages.all %}
        <div class="stage-flow next">
            <div class="row align-items-center">
                <div class="col-md-1">
                    <i class="bi bi-arrow-down-circle text-warning" style="font-size: 2rem;"></i>
                </div>
                <div class="col-md-11">
                    <h6 class="mb-1">المرحلة التالية</h6>
                    <p class="mb-0">{{ next_stage.sequence_number }}. {{ next_stage.name }}</p>
                    <small class="text-muted">{{ next_stage.get_stage_type_display }}</small>
                </div>
            </div>
        </div>
        {% endfor %}
        {% endif %}

        {% if stage.can_run_parallel %}
        <div class="alert alert-info mt-3">
            <i class="bi bi-info-circle me-2"></i>
            <strong>ملاحظة:</strong> يمكن تنفيذ هذه المرحلة بالتوازي مع مراحل أخرى
        </div>
        {% endif %}
    </div>

    <!-- معلومات النظام -->
    <div class="content-card">
        <h3><i class="bi bi-clock-history me-2"></i>معلومات النظام</h3>
        <div class="row">
            <div class="col-md-4">
                <div class="info-card">
                    <h5>تاريخ الإنشاء</h5>
                    <p class="mb-0">{{ stage.created_at|date:"d/m/Y H:i" }}</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-card">
                    <h5>أنشئ بواسطة</h5>
                    <p class="mb-0">{{ stage.created_by.get_full_name|default:stage.created_by.username }}</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-card">
                    <h5>آخر تحديث</h5>
                    <p class="mb-0">{{ stage.updated_at|date:"d/m/Y H:i" }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
