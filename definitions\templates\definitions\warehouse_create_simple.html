{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء مخزن جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-building me-2"></i>
                        إنشاء مخزن جديد
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- عرض رسائل الخطأ أو النجاح -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- النموذج البسيط -->
                    <form method="post" action="{% url 'definitions:warehouse_create_simple' %}">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- كود المخزن -->
                            <div class="col-md-6 mb-3">
                                <label for="code" class="form-label">كود المخزن *</label>
                                <input type="text" class="form-control" id="code" name="code" 
                                       value="{{ form_data.code|default:'' }}" required>
                            </div>
                            
                            <!-- اسم المخزن -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المخزن *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ form_data.name|default:'' }}" required>
                            </div>
                        </div>

                        <div class="row">
                            <!-- نوع المخزن -->
                            <div class="col-md-6 mb-3">
                                <label for="warehouse_type" class="form-label">نوع المخزن *</label>
                                <select class="form-select" id="warehouse_type" name="warehouse_type" required>
                                    <option value="">اختر نوع المخزن</option>
                                    <option value="main" {% if form_data.warehouse_type == 'main' %}selected{% endif %}>مخزن رئيسي</option>
                                    <option value="branch" {% if form_data.warehouse_type == 'branch' %}selected{% endif %}>مخزن فرعي</option>
                                    <option value="retail" {% if form_data.warehouse_type == 'retail' %}selected{% endif %}>مخزن تجزئة</option>
                                    <option value="wholesale" {% if form_data.warehouse_type == 'wholesale' %}selected{% endif %}>مخزن جملة</option>
                                </select>
                            </div>
                            
                            <!-- مدير المخزن -->
                            <div class="col-md-6 mb-3">
                                <label for="manager_name" class="form-label">مدير المخزن</label>
                                <input type="text" class="form-control" id="manager_name" name="manager_name" 
                                       value="{{ form_data.manager_name|default:'' }}">
                            </div>
                        </div>

                        <div class="row">
                            <!-- العنوان -->
                            <div class="col-md-6 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3">{{ form_data.address|default:'' }}</textarea>
                            </div>
                            
                            <!-- رقم الهاتف -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="phone" name="phone" 
                                       value="{{ form_data.phone|default:'' }}">
                            </div>
                        </div>

                        <!-- الخيارات -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {% if form_data.is_active or not form_data %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        المخزن نشط
                                    </label>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allow_negative_stock" name="allow_negative_stock"
                                           {% if form_data.allow_negative_stock %}checked{% endif %}>
                                    <label class="form-check-label" for="allow_negative_stock">
                                        السماح بالمخزون السالب
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ والإلغاء -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-success btn-lg me-3">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ المخزن
                            </button>
                            <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-secondary btn-lg">
                                <i class="bi bi-x-circle me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    padding: 1.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn {
    border-radius: 8px;
    padding: 12px 30px;
    font-weight: 600;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}
</style>
{% endblock %}
