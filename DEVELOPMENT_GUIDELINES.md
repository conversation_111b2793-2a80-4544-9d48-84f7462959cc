# دليل التطوير - نظام أوساريك

## إرشادات التصميم والتطوير

### 🔙 **أزرار العودة - إجباري في جميع الصفحات**

يجب إضافة زر العودة في **جميع** صفحات النظام بدون استثناء. هذا مطلب أساسي من المستخدم.

#### **مواقع أزرار العودة:**

1. **في رأس الصفحة** (بجانب العنوان):
```html
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="bi bi-icon"></i>
        عنوان الصفحة
    </h1>
    <a href="{% url 'app:dashboard' %}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-right"></i>
        العودة
    </a>
</div>
```

2. **في منطقة الأزرار** (مع أزرار الإجراءات):
```html
<div class="action-buttons">
    <!-- أزرار أخرى -->
    <a href="{% url 'app:list' %}" class="btn btn-outline-primary">
        <i class="bi bi-arrow-right"></i>
        العودة
    </a>
</div>
```

3. **في النماذج** (في رأس النموذج):
```html
<div class="col-md-4 text-end">
    <a href="{% url 'app:list' %}" class="btn btn-outline-light">
        <i class="bi bi-arrow-right"></i>
        العودة
    </a>
</div>
```

#### **أنواع أزرار العودة حسب السياق:**

- **من صفحة التفاصيل**: العودة إلى القائمة
- **من صفحة الإنشاء/التعديل**: العودة إلى القائمة
- **من صفحة القائمة**: العودة إلى لوحة التحكم
- **من صفحة الطباعة**: العودة إلى التفاصيل
- **من صفحة فرعية**: العودة إلى الصفحة الرئيسية

#### **أمثلة مطبقة في النظام:**

✅ **صفحة حركات المخزون**: زر العودة إلى لوحة المخازن
✅ **صفحة تفاصيل فاتورة الشراء**: زر العودة إلى قائمة الفواتير  
✅ **صفحة إنشاء فاتورة الشراء**: زر العودة إلى قائمة الفواتير
✅ **صفحة قائمة فواتير الشراء**: زر العودة إلى لوحة المشتريات
✅ **صفحة طباعة فاتورة الشراء**: زر العودة إلى تفاصيل الفاتورة

### 🎨 **تصميم أزرار العودة:**

#### **الألوان المستخدمة:**
- `btn-outline-primary`: للصفحات العادية
- `btn-outline-light`: للصفحات ذات الخلفية الملونة
- `btn-outline-secondary`: للصفحات الثانوية

#### **الأيقونات:**
- `bi-arrow-right`: للعودة (اتجاه اليمين للعربية)
- `bi-arrow-left`: للعودة (اتجاه اليسار للإنجليزية)

#### **النصوص:**
- "العودة": للأزرار المختصرة
- "العودة إلى القائمة": للأزرار التفصيلية
- "العودة إلى لوحة التحكم": للصفحات الرئيسية

### 📋 **قائمة مراجعة للمطور:**

عند إنشاء صفحة جديدة، تأكد من:

- [ ] إضافة زر العودة في رأس الصفحة
- [ ] تحديد الوجهة الصحيحة للعودة
- [ ] استخدام الأيقونة المناسبة
- [ ] استخدام اللون المناسب للتصميم
- [ ] اختبار عمل الزر
- [ ] التأكد من وضوح النص

### 🚫 **أخطاء شائعة يجب تجنبها:**

- ❌ نسيان إضافة زر العودة
- ❌ ربط الزر بوجهة خاطئة
- ❌ استخدام أيقونة خاطئة
- ❌ عدم تطابق تصميم الزر مع الصفحة
- ❌ عدم اختبار عمل الزر

### 📝 **ملاحظات إضافية:**

1. **الأولوية**: زر العودة له أولوية عالية في التصميم
2. **الوضوح**: يجب أن يكون الزر واضحاً ومرئياً
3. **الاتساق**: نفس التصميم في جميع الصفحات
4. **الوظيفة**: يجب أن يعمل الزر دائماً
5. **التجربة**: يحسن تجربة المستخدم بشكل كبير

---

**تذكر**: زر العودة ليس اختيارياً، بل مطلب أساسي من المستخدم!
