from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.utils import timezone
from .models import Message, MessageThread, UserMessageSettings
import json


@login_required
def inbox(request):
    """صندوق الوارد"""
    received_messages = Message.objects.filter(
        recipient=request.user,
        is_deleted_by_recipient=False
    ).select_related('sender').order_by('-created_at')
    
    # تطبيق الفلترة
    status_filter = request.GET.get('status')
    if status_filter:
        if status_filter == 'unread':
            received_messages = received_messages.filter(is_read=False)
        elif status_filter == 'read':
            received_messages = received_messages.filter(is_read=True)
        elif status_filter == 'starred':
            received_messages = received_messages.filter(is_starred=True)
    
    # البحث
    search_query = request.GET.get('search')
    if search_query:
        received_messages = received_messages.filter(
            Q(subject__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(sender__username__icontains=search_query)
        )
    
    # التصفح
    paginator = Paginator(received_messages, 20)
    page_number = request.GET.get('page')
    messages_page = paginator.get_page(page_number)
    
    # الإحصائيات
    stats = {
        'total': Message.objects.filter(recipient=request.user, is_deleted_by_recipient=False).count(),
        'unread': Message.objects.filter(recipient=request.user, is_read=False, is_deleted_by_recipient=False).count(),
        'starred': Message.objects.filter(recipient=request.user, is_starred=True, is_deleted_by_recipient=False).count(),
    }
    
    context = {
        'messages': messages_page,
        'stats': stats,
        'current_filter': status_filter,
        'search_query': search_query,
    }
    
    return render(request, 'messaging/inbox_simple.html', context)


@login_required
def sent_messages(request):
    """الرسائل المرسلة"""
    sent_messages = Message.objects.filter(
        sender=request.user,
        is_deleted_by_sender=False
    ).select_related('recipient').order_by('-created_at')
    
    # البحث
    search_query = request.GET.get('search')
    if search_query:
        sent_messages = sent_messages.filter(
            Q(subject__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(recipient__username__icontains=search_query)
        )
    
    # التصفح
    paginator = Paginator(sent_messages, 20)
    page_number = request.GET.get('page')
    messages_page = paginator.get_page(page_number)
    
    context = {
        'messages': messages_page,
        'search_query': search_query,
    }
    
    return render(request, 'messaging/sent.html', context)


@login_required
def compose_message(request):
    """إنشاء رسالة جديدة"""
    if request.method == 'POST':
        recipient_id = request.POST.get('recipient')
        subject = request.POST.get('subject')
        content = request.POST.get('content')
        priority = request.POST.get('priority', 'normal')
        
        try:
            recipient = User.objects.get(id=recipient_id)
            
            message = Message.objects.create(
                sender=request.user,
                recipient=recipient,
                subject=subject,
                content=content,
                priority=priority
            )
            
            from dashboard.context_processors import user_settings
            user_context = user_settings(request)
            user_language = user_context.get('user_language', 'ar')

            if user_language == 'en':
                messages.success(request, 'Message sent successfully')
            else:
                messages.success(request, 'تم إرسال الرسالة بنجاح')
            return redirect('messaging:inbox')
            
        except User.DoesNotExist:
            if user_language == 'en':
                messages.error(request, 'Selected user does not exist')
            else:
                messages.error(request, 'المستخدم المحدد غير موجود')
        except Exception as e:
            if user_language == 'en':
                messages.error(request, f'An error occurred: {str(e)}')
            else:
                messages.error(request, f'حدث خطأ: {str(e)}')
    
    # الحصول على قائمة المستخدمين
    users = User.objects.exclude(id=request.user.id).order_by('username')
    
    # إذا كان هناك معرف مستخدم في الرابط
    recipient_id = request.GET.get('to')
    selected_recipient = None
    if recipient_id:
        try:
            selected_recipient = User.objects.get(id=recipient_id)
        except User.DoesNotExist:
            pass
    
    context = {
        'users': users,
        'selected_recipient': selected_recipient,
    }
    
    return render(request, 'messaging/compose_simple.html', context)


@login_required
def view_message(request, message_id):
    """عرض رسالة"""
    message = get_object_or_404(Message, id=message_id)
    
    # التحقق من الصلاحية
    if message.recipient != request.user and message.sender != request.user:
        from dashboard.context_processors import user_settings
        user_context = user_settings(request)
        user_language = user_context.get('user_language', 'ar')

        if user_language == 'en':
            messages.error(request, 'You do not have permission to view this message')
        else:
            messages.error(request, 'ليس لديك صلاحية لعرض هذه الرسالة')
        return redirect('messaging:inbox')
    
    # تعيين الرسالة كمقروءة إذا كان المستخدم هو المستقبل
    if message.recipient == request.user and not message.is_read:
        message.mark_as_read()
    
    # الحصول على الردود
    replies = Message.objects.filter(reply_to=message).order_by('created_at')
    
    context = {
        'message': message,
        'replies': replies,
    }
    
    return render(request, 'messaging/view_message.html', context)


@login_required
def reply_message(request, message_id):
    """الرد على رسالة"""
    original_message = get_object_or_404(Message, id=message_id)
    
    # التحقق من الصلاحية
    if original_message.recipient != request.user and original_message.sender != request.user:
        messages.error(request, 'ليس لديك صلاحية للرد على هذه الرسالة')
        return redirect('messaging:inbox')
    
    if request.method == 'POST':
        content = request.POST.get('content')
        
        if content:
            # تحديد المستقبل (إذا كنت المرسل الأصلي، فالرد للمستقبل والعكس)
            if original_message.sender == request.user:
                recipient = original_message.recipient
            else:
                recipient = original_message.sender
            
            reply = Message.objects.create(
                sender=request.user,
                recipient=recipient,
                subject=f"رد: {original_message.subject}",
                content=content,
                reply_to=original_message,
                priority=original_message.priority
            )
            
            from dashboard.context_processors import user_settings
            user_context = user_settings(request)
            user_language = user_context.get('user_language', 'ar')

            if user_language == 'en':
                messages.success(request, 'Reply sent successfully')
            else:
                messages.success(request, 'تم إرسال الرد بنجاح')
            return redirect('messaging:view_message', message_id=original_message.id)
        else:
            if user_language == 'en':
                messages.error(request, 'Please write reply content')
            else:
                messages.error(request, 'يرجى كتابة محتوى الرد')
    
    context = {
        'original_message': original_message,
    }
    
    return render(request, 'messaging/reply.html', context)


@login_required
def delete_message(request, message_id):
    """حذف رسالة"""
    message = get_object_or_404(Message, id=message_id)
    
    # التحقق من الصلاحية
    if message.recipient != request.user and message.sender != request.user:
        from dashboard.context_processors import user_settings
        user_context = user_settings(request)
        user_language = user_context.get('user_language', 'ar')

        if user_language == 'en':
            messages.error(request, 'You do not have permission to delete this message')
        else:
            messages.error(request, 'ليس لديك صلاحية لحذف هذه الرسالة')
        return redirect('messaging:inbox')
    
    # تعيين الرسالة كمحذوفة
    if message.recipient == request.user:
        message.is_deleted_by_recipient = True
    if message.sender == request.user:
        message.is_deleted_by_sender = True
    
    message.save()
    
    if user_language == 'en':
        messages.success(request, 'Message deleted successfully')
    else:
        messages.success(request, 'تم حذف الرسالة بنجاح')
    return redirect('messaging:inbox')


@login_required
def star_message(request, message_id):
    """تمييز رسالة"""
    if request.method == 'POST':
        message = get_object_or_404(Message, id=message_id)
        
        # التحقق من الصلاحية
        if message.recipient != request.user:
            return JsonResponse({'success': False, 'error': 'ليس لديك صلاحية'})
        
        message.is_starred = not message.is_starred
        message.save()
        
        return JsonResponse({
            'success': True,
            'is_starred': message.is_starred
        })
    
    return JsonResponse({'success': False, 'error': 'طريقة غير صحيحة'})


@login_required
def get_unread_count(request):
    """الحصول على عدد الرسائل غير المقروءة"""
    count = Message.objects.filter(
        recipient=request.user,
        is_read=False,
        is_deleted_by_recipient=False
    ).count()
    
    return JsonResponse({'count': count})


@login_required
def get_recent_messages(request):
    """الحصول على الرسائل الحديثة للشريط العلوي"""
    recent_messages = Message.objects.filter(
        recipient=request.user,
        is_deleted_by_recipient=False
    ).select_related('sender').order_by('-created_at')[:5]
    
    messages_data = []
    for msg in recent_messages:
        messages_data.append({
            'id': msg.id,
            'sender': msg.sender.get_full_name() or msg.sender.username,
            'subject': msg.subject,
            'content': msg.content[:100] + '...' if len(msg.content) > 100 else msg.content,
            'time': msg.time_since_sent(),
            'is_read': msg.is_read,
            'priority': msg.priority,
            'priority_color': msg.get_priority_color(),
        })
    
    return JsonResponse({'messages': messages_data})


@login_required
def search_users(request):
    """البحث عن المستخدمين"""
    query = request.GET.get('q', '')
    
    if len(query) >= 2:
        users = User.objects.filter(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query)
        ).exclude(id=request.user.id)[:10]
        
        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'full_name': user.get_full_name() or user.username,
                'email': user.email,
            })
        
        return JsonResponse({'users': users_data})
    
    return JsonResponse({'users': []})
