{% extends 'base.html' %}

{% block title %}إدارة العملاء - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">إدارة العملاء</h1>
            <p class="page-subtitle">عرض وإدارة جميع العملاء في النظام</p>
        </div>
        <a href="{% url 'customers:customer_create' %}" class="btn btn-primary">
            <i class="bi bi-person-plus me-2"></i>إضافة عميل جديد
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}" 
                               placeholder="البحث في الأسماء، الهواتف، أو البريد الإلكتروني...">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search me-1"></i>بحث
                        </button>
                        <a href="{% url 'customers:customer_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-people text-primary" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-0">{{ total_customers }}</h4>
                    <p class="text-muted mb-0">إجمالي العملاء</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-person-check text-success" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-0">{{ page_obj.paginator.count }}</h4>
                    <p class="text-muted mb-0">نتائج البحث</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">قائمة العملاء</h5>
            <span class="badge bg-primary">{{ page_obj.paginator.count }} عميل</span>
        </div>
        <div class="card-body p-0">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>العنوان</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in page_obj %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                {{ customer.name.0|upper }}
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ customer.name }}</h6>
                                                <small class="text-muted">#{{ customer.id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if customer.phone %}
                                            <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                                <i class="bi bi-telephone me-1"></i>{{ customer.phone }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if customer.email %}
                                            <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                                <i class="bi bi-envelope me-1"></i>{{ customer.email }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if customer.address %}
                                            <span title="{{ customer.address }}">
                                                {{ customer.address|truncatechars:30 }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ customer.created_at|date:"Y/m/d" }}
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'customers:customer_detail' customer.pk %}" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="عرض التفاصيل">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="{% url 'customers:customer_edit' customer.pk %}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a href="{% url 'customers:customer_delete' customer.pk %}" 
                                               class="btn btn-sm btn-outline-danger" 
                                               title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <div class="card-footer">
                        <nav aria-label="صفحات العملاء">
                            <ul class="pagination justify-content-center mb-0">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">الأولى</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابقة</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالية</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">الأخيرة</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد عملاء</h4>
                    <p class="text-muted">لم يتم العثور على أي عملاء مطابقين لمعايير البحث.</p>
                    <a href="{% url 'customers:customer_create' %}" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>إضافة أول عميل
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        font-weight: 600;
    }
    
    .table th {
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem;
        margin-left: 2px;
    }
</style>
{% endblock %}
