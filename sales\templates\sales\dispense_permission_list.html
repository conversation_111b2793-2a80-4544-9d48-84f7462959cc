{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة أذونات الصرف{% endblock %}

{% block extra_css %}
<style>
    .permission-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .permission-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .page-header {
        background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
    }
    
    .search-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    .permission-header {
        background: linear-gradient(45deg, #6f42c1, #5a2d91);
        color: white;
        padding: 20px;
    }
    
    .permission-number {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .permission-date {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .permission-body {
        padding: 20px;
    }
    
    .permission-details {
        margin-bottom: 15px;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 5px 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .detail-value {
        font-weight: 600;
        color: #333;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-pending {
        background: #ffc107;
        color: #212529;
    }
    
    .status-approved {
        background: #28a745;
        color: white;
    }
    
    .purpose-display {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 8px;
        font-size: 0.9rem;
        color: #495057;
        margin-bottom: 15px;
        border-left: 4px solid #6f42c1;
    }
    
    .value-display {
        font-size: 1.2rem;
        font-weight: 700;
        color: #6f42c1;
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-top: 15px;
    }
    
    .btn-create {
        background: linear-gradient(45deg, #6f42c1, #5a2d91);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    .warehouse-badge {
        background: #e3f2fd;
        color: #1976d2;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        margin-bottom: 10px;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-file-earmark-text"></i>
                    إدارة أذونات الصرف
                </h1>
                <p class="mb-0">إدارة ومتابعة أذونات صرف البضائع من المخازن</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'sales:dispense_permission_create' %}" class="btn btn-create">
                    <i class="bi bi-plus-circle"></i>
                    إذن صرف جديد
                </a>
            </div>
        </div>
    </div>

    <!-- قسم البحث والفلترة -->
    <div class="search-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="رقم الإذن، المندوب، أو الغرض" 
                       value="{{ search }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {% if status == 'pending' %}selected{% endif %}>في الانتظار</option>
                    <option value="approved" {% if status == 'approved' %}selected{% endif %}>معتمد</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">المخزن</label>
                <select name="warehouse" class="form-select">
                    <option value="">جميع المخازن</option>
                    {% for wh in warehouses %}
                        <option value="{{ wh }}" {% if warehouse == wh %}selected{% endif %}>
                            {{ wh }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            <div class="col-md-1 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search"></i>
                </button>
                <a href="{% url 'sales:dispense_permission_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- قائمة أذونات الصرف -->
    {% if permissions %}
        <div class="row">
            {% for permission in permissions %}
                <div class="col-lg-6 col-xl-4">
                    <div class="permission-card">
                        <div class="permission-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="permission-number">{{ permission.permission_number }}</div>
                                    <div class="permission-date">{{ permission.dispense_date }}</div>
                                </div>
                                <span class="status-badge {% if permission.is_approved %}status-approved{% else %}status-pending{% endif %}">
                                    {% if permission.is_approved %}معتمد{% else %}في الانتظار{% endif %}
                                </span>
                            </div>
                        </div>
                        
                        <div class="permission-body">
                            <div class="warehouse-badge">
                                <i class="bi bi-building"></i>
                                {{ permission.warehouse }}
                            </div>
                            
                            <div class="purpose-display">
                                <strong>الغرض:</strong> {{ permission.purpose }}
                            </div>
                            
                            <div class="permission-details">
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-person text-primary"></i>
                                        المندوب:
                                    </span>
                                    <span class="detail-value">{{ permission.representative.full_name }}</span>
                                </div>
                                {% if permission.vehicle %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-truck text-info"></i>
                                            السيارة:
                                        </span>
                                        <span class="detail-value">{{ permission.vehicle.plate_number }}</span>
                                    </div>
                                {% endif %}
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-list-ol text-warning"></i>
                                        عدد العناصر:
                                    </span>
                                    <span class="detail-value">{{ permission.items.count }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-person-badge text-success"></i>
                                        أنشئ بواسطة:
                                    </span>
                                    <span class="detail-value">{{ permission.created_by.get_full_name }}</span>
                                </div>
                                {% if permission.is_approved and permission.approved_by %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-check-circle text-success"></i>
                                            اعتمد بواسطة:
                                        </span>
                                        <span class="detail-value">{{ permission.approved_by.get_full_name }}</span>
                                    </div>
                                {% endif %}
                                {% if permission.notes %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-chat-text text-muted"></i>
                                            ملاحظات:
                                        </span>
                                        <span class="detail-value">{{ permission.notes|truncatechars:30 }}</span>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="value-display">
                                {{ permission.total_value }} ج.م
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="{% url 'sales:dispense_permission_detail' permission.pk %}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    عرض التفاصيل
                                </a>
                                {% if not permission.is_approved %}
                                    <a href="{% url 'sales:dispense_permission_edit' permission.pk %}" 
                                       class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-pencil"></i>
                                        تعديل
                                    </a>
                                    <a href="{% url 'sales:dispense_permission_approve' permission.pk %}" 
                                       class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-check-circle"></i>
                                        اعتماد
                                    </a>
                                {% else %}
                                    <a href="{% url 'sales:dispense_permission_print' permission.pk %}" 
                                       class="btn btn-outline-info btn-sm" target="_blank">
                                        <i class="bi bi-printer"></i>
                                        طباعة
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- الترقيم -->
        {% if permissions.has_other_pages %}
            <nav aria-label="ترقيم الصفحات" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if permissions.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ permissions.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if warehouse %}&warehouse={{ warehouse }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                السابق
                            </a>
                        </li>
                    {% endif %}

                    {% for num in permissions.paginator.page_range %}
                        {% if permissions.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > permissions.number|add:'-3' and num < permissions.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if warehouse %}&warehouse={{ warehouse }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                    {{ num }}
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if permissions.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ permissions.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if warehouse %}&warehouse={{ warehouse }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                التالي
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="empty-state">
            <i class="bi bi-file-earmark-text"></i>
            <h3>لا توجد أذونات صرف</h3>
            <p>لم يتم العثور على أي أذونات صرف تطابق معايير البحث</p>
            <a href="{% url 'sales:dispense_permission_create' %}" class="btn btn-create">
                <i class="bi bi-plus-circle"></i>
                إنشاء أول إذن صرف
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تأثيرات بصرية للبطاقات
    $('.permission-card').hover(
        function() {
            $(this).find('.permission-number').addClass('text-warning');
        },
        function() {
            $(this).find('.permission-number').removeClass('text-warning');
        }
    );
});
</script>
{% endblock %}
