#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from warehouses.models import InventoryItem
from manufacturing.models import ManufacturingInventoryTransaction
from decimal import Decimal

def debug_product():
    print("البحث عن منتج جركن صابون أوساريك...")
    
    # البحث عن عناصر المخزون
    items = InventoryItem.objects.filter(product__name__icontains='جركن صابون أوساريك')
    print(f'تم العثور على {items.count()} عنصر مخزون')
    
    for item in items:
        print(f'المنتج: {item.product.name}')
        print(f'المخزن: {item.warehouse.name}')
        print(f'الكمية: {item.quantity_on_hand}')
        print(f'متوسط التكلفة: {item.average_cost}')
        print(f'آخر تكلفة: {item.last_cost}')
        print(f'إجمالي القيمة: {item.total_value}')
        print(f'سعر التكلفة من تعريف المنتج: {item.product.cost_price}')
        print('-' * 50)
        
        # البحث عن حركات التصنيع
        transactions = ManufacturingInventoryTransaction.objects.filter(
            product=item.product,
            warehouse=item.warehouse,
            transaction_type='finished_goods_production'
        ).order_by('-created_at')
        
        print(f'حركات التصنيع: {transactions.count()}')
        
        for trans in transactions:
            print(f'  أمر التصنيع: {trans.manufacturing_order.order_number}')
            print(f'  الكمية: {trans.quantity}')
            print(f'  تكلفة الوحدة: {trans.unit_cost}')
            print(f'  إجمالي التكلفة: {trans.total_cost}')
            print(f'  التكلفة الفعلية للأمر: {trans.manufacturing_order.total_actual_cost}')
            print('-' * 30)
        
        # إصلاح التكلفة إذا كانت صفر
        if item.average_cost == 0 and transactions.exists():
            print('محاولة إصلاح التكلفة...')
            
            total_quantity = Decimal('0')
            total_cost = Decimal('0')
            
            for trans in transactions:
                total_quantity += trans.quantity
                total_cost += trans.total_cost or Decimal('0')
            
            if total_quantity > 0:
                average_cost = total_cost / total_quantity
                item.average_cost = average_cost
                item.last_cost = average_cost
                item.total_value = item.quantity_on_hand * item.average_cost
                item.save()
                
                print(f'تم إصلاح التكلفة: {average_cost}')
                print(f'إجمالي القيمة الجديدة: {item.total_value}')
        
        print('=' * 70)

if __name__ == '__main__':
    debug_product()
