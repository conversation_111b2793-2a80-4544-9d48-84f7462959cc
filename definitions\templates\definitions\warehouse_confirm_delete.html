{% extends 'base.html' %}
{% load static %}

{% block title %}حذف المخزن - {{ warehouse.name }}{% endblock %}

{% block extra_css %}
<style>
    .delete-header {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .delete-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid #dc3545;
    }

    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 1rem;
    }

    .warehouse-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
    }

    .info-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
    }

    .info-value {
        color: #212529;
    }

    .danger-list {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }

    .danger-list h6 {
        color: #721c24;
        margin-bottom: 1rem;
    }

    .danger-list ul {
        color: #721c24;
        margin-bottom: 0;
    }

    .btn-confirm-delete {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-confirm-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
        color: white;
    }

    .btn-cancel {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        color: white;
    }

    .alternative-actions {
        background: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }

    .alternative-actions h6 {
        color: #0c5460;
        margin-bottom: 1rem;
    }

    .btn-alternative {
        background: #17a2b8;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 500;
        color: white;
        text-decoration: none;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-alternative:hover {
        background: #138496;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Delete Header -->
<div class="delete-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    تأكيد حذف المخزن
                </h1>
                <p class="mb-0 opacity-75">هذا الإجراء لا يمكن التراجع عنه</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:warehouse_detail' warehouse.id %}" class="btn btn-light">
                    <i class="bi bi-arrow-right me-1"></i>العودة للتفاصيل
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="delete-card">
                <div class="text-center">
                    <i class="bi bi-exclamation-triangle warning-icon"></i>
                    <h3 class="text-danger mb-3">تحذير: حذف المخزن</h3>
                    <p class="lead">أنت على وشك حذف المخزن التالي نهائياً من النظام:</p>
                </div>

                <!-- Warehouse Information -->
                <div class="warehouse-info">
                    <h5 class="mb-3">
                        <i class="bi bi-building me-2"></i>معلومات المخزن
                    </h5>
                    
                    <div class="info-item">
                        <span class="info-label">كود المخزن:</span>
                        <span class="info-value"><strong>{{ warehouse.code }}</strong></span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">اسم المخزن:</span>
                        <span class="info-value">{{ warehouse.name }}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">نوع المخزن:</span>
                        <span class="info-value">{{ warehouse.get_warehouse_type_display }}</span>
                    </div>
                    
                    {% if warehouse.manager_name %}
                    <div class="info-item">
                        <span class="info-label">مدير المخزن:</span>
                        <span class="info-value">{{ warehouse.manager_name }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="info-item">
                        <span class="info-label">تاريخ الإنشاء:</span>
                        <span class="info-value">{{ warehouse.created_at|date:"d/m/Y" }}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="info-label">الحالة:</span>
                        <span class="info-value">
                            <span class="badge {% if warehouse.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                {{ warehouse.is_active|yesno:"نشط,غير نشط" }}
                            </span>
                        </span>
                    </div>
                </div>

                <!-- Danger Warning -->
                <div class="danger-list">
                    <h6>
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        تحذير: سيتم حذف البيانات التالية نهائياً:
                    </h6>
                    <ul>
                        <li>جميع أرصدة المخزون المرتبطة بهذا المخزن</li>
                        <li>جميع حركات المخزون (إدخال، إخراج، نقل)</li>
                        <li>جميع تسويات المخزون</li>
                        <li>جميع التقارير المرتبطة بالمخزن</li>
                        <li>أي بيانات أخرى مرتبطة بهذا المخزن</li>
                    </ul>
                </div>

                <!-- Alternative Actions -->
                <div class="alternative-actions">
                    <h6>
                        <i class="bi bi-lightbulb me-2"></i>
                        بدائل أخرى بدلاً من الحذف:
                    </h6>
                    <p class="mb-3">بدلاً من حذف المخزن نهائياً، يمكنك:</p>
                    
                    <a href="{% url 'definitions:warehouse_edit' warehouse.id %}" class="btn-alternative">
                        <i class="bi bi-pencil me-1"></i>تعديل بيانات المخزن
                    </a>
                    
                    {% if warehouse.is_active %}
                    <button class="btn-alternative" onclick="deactivateWarehouse()">
                        <i class="bi bi-pause-circle me-1"></i>إلغاء تفعيل المخزن
                    </button>
                    {% endif %}
                    
                    <a href="{% url 'definitions:warehouse_detail' warehouse.id %}" class="btn-alternative">
                        <i class="bi bi-eye me-1"></i>مراجعة التفاصيل مرة أخرى
                    </a>
                </div>

                <!-- Confirmation Form -->
                <form method="post" class="text-center" id="deleteForm">
                    {% csrf_token %}

                    <div class="mb-4">
                        <div class="form-check d-inline-block">
                            <input type="checkbox" id="confirmDelete" class="form-check-input" required>
                            <label for="confirmDelete" class="form-check-label">
                                أؤكد أنني أفهم أن هذا الإجراء لا يمكن التراجع عنه
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-confirm-delete btn-lg me-3" id="deleteBtn" disabled>
                        <i class="bi bi-trash me-2"></i>
                        نعم، احذف المخزن نهائياً
                    </button>

                    <a href="{% url 'definitions:warehouse_detail' warehouse.id %}" class="btn btn-cancel btn-lg">
                        <i class="bi bi-x-circle me-2"></i>
                        إلغاء - عدم الحذف
                    </a>
                </form>
            </div>
        </div>
    </div>

    <!-- Final Warning -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-danger text-center">
                <h6><i class="bi bi-exclamation-triangle me-2"></i>تحذير أخير:</h6>
                <p class="mb-0">
                    <strong>حذف المخزن سيؤثر على جميع البيانات المرتبطة به في النظام.</strong><br>
                    تأكد من أنك لا تحتاج لهذا المخزن أو بياناته في المستقبل قبل المتابعة.
                </p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteBtn = document.getElementById('deleteBtn');
    const deleteForm = document.getElementById('deleteForm');

    // تفعيل/تعطيل زر الحذف حسب checkbox
    confirmCheckbox.addEventListener('change', function() {
        deleteBtn.disabled = !this.checked;
    });

    // معالجة إرسال النموذج
    deleteForm.addEventListener('submit', function(e) {
        console.log('DEBUG: Form submit event triggered');
        e.preventDefault();

        console.log('DEBUG: Form submission prevented, showing confirmation');

        // التحقق من تأكيد المستخدم
        if (confirm(
            'هل أنت متأكد تماماً من حذف هذا المخزن؟\n\n' +
            'سيتم حذف:\n' +
            '- المخزن: {{ warehouse.name }}\n' +
            '- جميع البيانات المرتبطة\n\n' +
            'هذا الإجراء لا يمكن التراجع عنه!'
        )) {
            console.log('DEBUG: User confirmed deletion');

            // تغيير نص الزر وتعطيله
            deleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري المعالجة...';
            deleteBtn.disabled = true;

            console.log('DEBUG: Button text changed, submitting form in 500ms');

            // إرسال النموذج
            setTimeout(() => {
                console.log('DEBUG: Submitting form now');
                deleteForm.submit();
            }, 500);
        } else {
            console.log('DEBUG: User cancelled deletion');
        }
    });
});

function deactivateWarehouse() {
    if (confirm('هل تريد إلغاء تفعيل المخزن بدلاً من حذفه؟\n\nهذا سيمنع استخدامه في العمليات الجديدة مع الاحتفاظ بالبيانات.')) {
        // يمكن إضافة AJAX call هنا لإلغاء التفعيل
        alert('سيتم تطوير هذه الميزة قريباً');
    }
}
</script>
{% endblock %}
