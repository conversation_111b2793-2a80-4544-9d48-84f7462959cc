@echo off
cd /d "d:\osaric try"
echo 🔧 إصلاح مشاكل Django...

echo ▶️ إصلاح قاعدة البيانات...
python fix_database.py

echo ▶️ تطبيق migrations الأساسية...
python manage.py migrate --fake-initial

echo ▶️ إنشاء migrations للتطبيقات الجديدة...
python manage.py makemigrations notifications
python manage.py makemigrations search
python manage.py makemigrations messaging

echo ▶️ تطبيق migrations الجديدة...
python manage.py migrate

echo ✅ تم إصلاح مشكلة تضارب الأسماء بين settings.py و settings app
echo ✅ تم إنشاء جداول system_settings في قاعدة البيانات

echo 🚀 تشغيل الخادم...
python manage.py runserver 127.0.0.1:8000

pause
