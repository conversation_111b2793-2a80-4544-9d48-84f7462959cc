{% extends 'base.html' %}

{% block title %}إدارة المخازن - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'warehouses:dashboard' %}">المخازن</a></li>
                    <li class="breadcrumb-item active">قائمة المخازن</li>
                </ol>
            </nav>
            <h1 class="page-title">إدارة المخازن</h1>
            <p class="page-subtitle">عرض وإدارة جميع المخازن والمستودعات</p>
        </div>
        <a href="{% url 'warehouses:warehouse_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>إضافة مخزن جديد
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}" 
                               placeholder="البحث في أسماء المخازن أو الرموز...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        {% for type_code, type_name in warehouse_types %}
                            <option value="{{ type_code }}" {% if warehouse_type == type_code %}selected{% endif %}>
                                {{ type_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search me-1"></i>بحث
                        </button>
                        <a href="{% url 'warehouses:warehouse_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Warehouses Grid -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">قائمة المخازن</h5>
            <span class="badge bg-primary">{{ page_obj.paginator.count }} مخزن</span>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="row g-4">
                    {% for warehouse in page_obj %}
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 warehouse-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="warehouse-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center">
                                            {% if warehouse.warehouse_type == 'main' %}
                                                <i class="bi bi-building"></i>
                                            {% elif warehouse.warehouse_type == 'branch' %}
                                                <i class="bi bi-geo-alt"></i>
                                            {% elif warehouse.warehouse_type == 'temporary' %}
                                                <i class="bi bi-clock"></i>
                                            {% elif warehouse.warehouse_type == 'damaged' %}
                                                <i class="bi bi-exclamation-triangle"></i>
                                            {% elif warehouse.warehouse_type == 'returns' %}
                                                <i class="bi bi-arrow-return-left"></i>
                                            {% else %}
                                                <i class="bi bi-building"></i>
                                            {% endif %}
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{% url 'warehouses:warehouse_detail' warehouse.pk %}">
                                                    <i class="bi bi-eye me-2"></i>عرض التفاصيل
                                                </a></li>
                                                <li><a class="dropdown-item" href="{% url 'warehouses:warehouse_edit' warehouse.pk %}">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="{% url 'warehouses:warehouse_delete' warehouse.pk %}">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <h5 class="card-title">{{ warehouse.name }}</h5>
                                    <p class="text-muted small mb-2">الرمز: {{ warehouse.code }}</p>
                                    <p class="text-muted small mb-2">الفرع: {{ warehouse.branch.name }}</p>
                                    
                                    <div class="mb-3">
                                        <span class="badge bg-info">{{ warehouse.get_warehouse_type_display }}</span>
                                        {% if warehouse.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-danger">غير نشط</span>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-6">
                                            <div class="border-end">
                                                <h6 class="mb-0">0</h6>
                                                <small class="text-muted">المنتجات</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="mb-0">
                                                {% if warehouse.capacity %}
                                                    {{ warehouse.capacity }} م³
                                                {% else %}
                                                    غير محدد
                                                {% endif %}
                                            </h6>
                                            <small class="text-muted">السعة</small>
                                        </div>
                                    </div>
                                    
                                    {% if warehouse.manager_name or warehouse.phone %}
                                        <div class="mb-3">
                                            {% if warehouse.manager_name %}
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="bi bi-person me-2 text-muted"></i>
                                                    <small>{{ warehouse.manager_name }}</small>
                                                </div>
                                            {% endif %}
                                            {% if warehouse.phone %}
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-telephone me-2 text-muted"></i>
                                                    <small>{{ warehouse.phone }}</small>
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                    
                                    <div class="d-flex gap-2">
                                        <a href="{% url 'warehouses:warehouse_detail' warehouse.pk %}" class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="bi bi-eye me-1"></i>عرض
                                        </a>
                                        <a href="{% url 'warehouses:warehouse_edit' warehouse.pk %}" class="btn btn-outline-success btn-sm">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{% url 'warehouses:warehouse_delete' warehouse.pk %}" class="btn btn-outline-danger btn-sm">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="صفحات المخازن" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_type %}&type={{ warehouse_type }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد مخازن</h4>
                    <p class="text-muted">لم يتم العثور على أي مخازن مطابقة لمعايير البحث.</p>
                    <a href="{% url 'warehouses:warehouse_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة أول مخزن
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .warehouse-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .warehouse-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .warehouse-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .border-end {
        border-right: 1px solid #dee2e6 !important;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
</style>
{% endblock %}
