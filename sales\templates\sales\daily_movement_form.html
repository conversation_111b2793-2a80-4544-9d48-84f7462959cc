{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .movement-form {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin: 20px 0;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding: 25px;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        background: #f8f9fa;
    }
    
    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #007bff;
    }
    
    .calculation-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 10px;
        margin-top: 20px;
    }
    
    .auto-calc-btn {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .auto-calc-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .amount-input {
        font-size: 1.1rem;
        font-weight: 600;
        text-align: center;
        background: white;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        padding: 12px;
    }
    
    .amount-input:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .net-display {
        font-size: 1.5rem;
        font-weight: 700;
        text-align: center;
        padding: 20px;
        border-radius: 10px;
        margin-top: 15px;
    }
    
    .net-positive {
        background: #d4edda;
        color: #155724;
        border: 2px solid #c3e6cb;
    }
    
    .net-negative {
        background: #f8d7da;
        color: #721c24;
        border: 2px solid #f5c6cb;
    }
    
    .net-zero {
        background: #e2e3e5;
        color: #383d41;
        border: 2px solid #d6d8db;
    }
    
    .info-card {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-top: 10px;
        border-left: 4px solid #2196f3;
    }
    
    .warning-card {
        background: #fff3cd;
        padding: 15px;
        border-radius: 8px;
        margin-top: 10px;
        border-left: 4px solid #ffc107;
    }
    
    .calculation-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .calc-label {
        font-weight: 500;
    }
    
    .calc-value {
        font-weight: 600;
        font-size: 1.1rem;
    }
    
    .btn-save {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        color: white;
        padding: 15px 30px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 123, 255, 0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="movement-form">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="text-primary">
                        <i class="bi bi-calendar-check"></i>
                        {{ title }}
                    </h2>
                    <a href="{% url 'sales:daily_movement_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>

                <form method="post" id="movement-form">
                    {% csrf_token %}
                    
                    <!-- معلومات الحركة الأساسية -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            معلومات الحركة
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المندوب</label>
                                    {{ form.representative }}
                                    {% if movement %}
                                        <div class="info-card">
                                            <small><strong>المندوب:</strong> {{ movement.representative.full_name }}</small>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الحركة</label>
                                    {{ form.movement_date }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">السيارة</label>
                                    {{ form.vehicle }}
                                </div>
                            </div>
                        </div>
                        
                        {% if movement and movement.is_closed %}
                            <div class="warning-card">
                                <i class="bi bi-exclamation-triangle text-warning"></i>
                                <strong>تنبيه:</strong> هذه الحركة مغلقة ولا يمكن تعديلها
                            </div>
                        {% endif %}
                    </div>

                    <!-- المبالغ المالية -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-cash-stack"></i>
                            المبالغ المالية
                        </h4>
                        
                        {% if movement %}
                            <div class="text-center mb-3">
                                <button type="button" class="auto-calc-btn" id="auto-calculate">
                                    <i class="bi bi-calculator"></i>
                                    حساب تلقائي من النظام
                                </button>
                                <small class="d-block text-muted mt-2">
                                    سيتم حساب المبالغ تلقائياً من الفواتير والمدفوعات المسجلة
                                </small>
                            </div>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرصيد الافتتاحي</label>
                                    {{ form.opening_cash }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إجمالي المبيعات</label>
                                    {{ form.total_sales }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إجمالي التحصيلات</label>
                                    {{ form.total_collections }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إجمالي المرتجعات</label>
                                    {{ form.total_returns }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المصروفات</label>
                                    {{ form.expenses }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرصيد الختامي</label>
                                    {{ form.closing_cash }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            {{ form.notes }}
                        </div>
                    </div>

                    <!-- حساب صافي الحركة -->
                    <div class="calculation-section">
                        <div class="row">
                            <div class="col-md-8">
                                <h5>
                                    <i class="bi bi-calculator"></i>
                                    حساب صافي الحركة
                                </h5>
                                <div class="calculation-row">
                                    <span class="calc-label">الرصيد الافتتاحي:</span>
                                    <span class="calc-value" id="calc-opening">0.00 ج.م</span>
                                </div>
                                <div class="calculation-row">
                                    <span class="calc-label">+ المبيعات:</span>
                                    <span class="calc-value" id="calc-sales">0.00 ج.م</span>
                                </div>
                                <div class="calculation-row">
                                    <span class="calc-label">+ التحصيلات:</span>
                                    <span class="calc-value" id="calc-collections">0.00 ج.م</span>
                                </div>
                                <div class="calculation-row">
                                    <span class="calc-label">- المرتجعات:</span>
                                    <span class="calc-value" id="calc-returns">0.00 ج.م</span>
                                </div>
                                <div class="calculation-row">
                                    <span class="calc-label">- المصروفات:</span>
                                    <span class="calc-value" id="calc-expenses">0.00 ج.م</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div id="net-movement" class="net-display net-zero">
                                    <div>صافي الحركة</div>
                                    <div id="net-value">0.00 ج.م</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-save">
                            <i class="bi bi-save"></i>
                            حفظ الحركة اليومية
                        </button>
                        <a href="{% url 'sales:daily_movement_list' %}" class="btn btn-secondary btn-lg ms-2">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // حساب صافي الحركة
    function calculateNetMovement() {
        var opening = parseFloat($('#id_opening_cash').val()) || 0;
        var sales = parseFloat($('#id_total_sales').val()) || 0;
        var collections = parseFloat($('#id_total_collections').val()) || 0;
        var returns = parseFloat($('#id_total_returns').val()) || 0;
        var expenses = parseFloat($('#id_expenses').val()) || 0;
        
        var net = opening + sales + collections - returns - expenses;
        
        // تحديث العرض
        $('#calc-opening').text(opening.toFixed(2) + ' ج.م');
        $('#calc-sales').text(sales.toFixed(2) + ' ج.م');
        $('#calc-collections').text(collections.toFixed(2) + ' ج.م');
        $('#calc-returns').text(returns.toFixed(2) + ' ج.م');
        $('#calc-expenses').text(expenses.toFixed(2) + ' ج.م');
        $('#net-value').text(net.toFixed(2) + ' ج.م');
        
        // تحديث لون صافي الحركة
        var netDiv = $('#net-movement');
        netDiv.removeClass('net-positive net-negative net-zero');
        
        if (net > 0) {
            netDiv.addClass('net-positive');
        } else if (net < 0) {
            netDiv.addClass('net-negative');
        } else {
            netDiv.addClass('net-zero');
        }
        
        // تحديث الرصيد الختامي
        $('#id_closing_cash').val(net.toFixed(2));
    }

    // تحديث الحسابات عند تغيير القيم
    $('#id_opening_cash, #id_total_sales, #id_total_collections, #id_total_returns, #id_expenses').on('input', function() {
        calculateNetMovement();
    });

    // الحساب التلقائي
    $('#auto-calculate').click(function() {
        var btn = $(this);
        var originalText = btn.html();
        
        btn.html('<i class="bi bi-hourglass-split"></i> جاري الحساب...').prop('disabled', true);
        
        {% if movement %}
        $.get('{% url "sales:daily_movement_auto_calculate" movement.pk %}', function(data) {
            if (data.success) {
                $('#id_total_sales').val(data.total_sales.toFixed(2));
                $('#id_total_collections').val(data.total_collections.toFixed(2));
                $('#id_total_returns').val(data.total_returns.toFixed(2));
                
                calculateNetMovement();
                
                // إظهار رسالة نجاح
                var alert = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                    '<i class="bi bi-check-circle"></i> تم الحساب التلقائي بنجاح!' +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                    '</div>');
                
                $('.movement-form').prepend(alert);
                
                setTimeout(function() {
                    alert.fadeOut();
                }, 3000);
            } else {
                alert('حدث خطأ في الحساب التلقائي');
            }
        }).fail(function() {
            alert('حدث خطأ في الاتصال بالخادم');
        }).always(function() {
            btn.html(originalText).prop('disabled', false);
        });
        {% endif %}
    });

    // حساب أولي
    calculateNetMovement();
    
    // تحديد تاريخ اليوم كافتراضي
    if (!$('#id_movement_date').val()) {
        var today = new Date().toISOString().split('T')[0];
        $('#id_movement_date').val(today);
    }
});
</script>
{% endblock %}
