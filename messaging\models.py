from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class Message(models.Model):
    """نموذج الرسائل"""
    
    PRIORITY_CHOICES = [
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('high', 'عالية'),
        ('urgent', 'عاجلة'),
    ]
    
    STATUS_CHOICES = [
        ('sent', 'مرسلة'),
        ('delivered', 'تم التسليم'),
        ('read', 'مقروءة'),
        ('archived', 'مؤرشفة'),
    ]
    
    sender = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='sent_messages',
        verbose_name="المرسل"
    )
    
    recipient = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='received_messages',
        verbose_name="المستقبل"
    )
    
    subject = models.CharField(
        max_length=200, 
        verbose_name="الموضوع"
    )
    
    content = models.TextField(
        verbose_name="المحتوى"
    )
    
    priority = models.CharField(
        max_length=10, 
        choices=PRIORITY_CHOICES, 
        default='normal',
        verbose_name="الأولوية"
    )
    
    status = models.CharField(
        max_length=10, 
        choices=STATUS_CHOICES, 
        default='sent',
        verbose_name="الحالة"
    )
    
    is_read = models.BooleanField(
        default=False,
        verbose_name="مقروءة"
    )
    
    is_starred = models.BooleanField(
        default=False,
        verbose_name="مميزة"
    )
    
    is_deleted_by_sender = models.BooleanField(
        default=False,
        verbose_name="محذوفة من المرسل"
    )
    
    is_deleted_by_recipient = models.BooleanField(
        default=False,
        verbose_name="محذوفة من المستقبل"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="تاريخ الإرسال"
    )
    
    read_at = models.DateTimeField(
        null=True, 
        blank=True,
        verbose_name="تاريخ القراءة"
    )
    
    # مرفقات (اختيارية)
    attachment = models.FileField(
        upload_to='message_attachments/',
        null=True,
        blank=True,
        verbose_name="مرفق"
    )
    
    # رد على رسالة أخرى
    reply_to = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies',
        verbose_name="رد على"
    )

    class Meta:
        verbose_name = "رسالة"
        verbose_name_plural = "الرسائل"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.subject} - من {self.sender.username} إلى {self.recipient.username}"

    def mark_as_read(self):
        """تعيين الرسالة كمقروءة"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.status = 'read'
            self.save()

    def get_priority_color(self):
        """الحصول على لون الأولوية"""
        colors = {
            'low': '#6b7280',
            'normal': '#3b82f6',
            'high': '#f59e0b',
            'urgent': '#ef4444'
        }
        return colors.get(self.priority, '#3b82f6')

    def get_priority_icon(self):
        """الحصول على أيقونة الأولوية"""
        icons = {
            'low': 'bi-arrow-down',
            'normal': 'bi-dash',
            'high': 'bi-arrow-up',
            'urgent': 'bi-exclamation-triangle'
        }
        return icons.get(self.priority, 'bi-dash')

    def time_since_sent(self):
        """حساب الوقت منذ الإرسال"""
        now = timezone.now()
        diff = now - self.created_at
        
        if diff.days > 0:
            return f"منذ {diff.days} يوم"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"منذ {hours} ساعة"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"منذ {minutes} دقيقة"
        else:
            return "الآن"


class MessageThread(models.Model):
    """سلسلة الرسائل"""
    
    participants = models.ManyToManyField(
        User,
        verbose_name="المشاركين"
    )
    
    subject = models.CharField(
        max_length=200,
        verbose_name="الموضوع"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="تاريخ الإنشاء"
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="آخر تحديث"
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name="نشطة"
    )

    class Meta:
        verbose_name = "سلسلة رسائل"
        verbose_name_plural = "سلاسل الرسائل"
        ordering = ['-updated_at']

    def __str__(self):
        return self.subject

    def get_last_message(self):
        """الحصول على آخر رسالة في السلسلة"""
        return self.messages.first()

    def get_unread_count(self, user):
        """عدد الرسائل غير المقروءة للمستخدم"""
        return self.messages.filter(
            recipient=user,
            is_read=False
        ).count()


class MessageAttachment(models.Model):
    """مرفقات الرسائل"""
    
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        related_name='attachments',
        verbose_name="الرسالة"
    )
    
    file = models.FileField(
        upload_to='message_attachments/',
        verbose_name="الملف"
    )
    
    filename = models.CharField(
        max_length=255,
        verbose_name="اسم الملف"
    )
    
    file_size = models.PositiveIntegerField(
        verbose_name="حجم الملف"
    )
    
    uploaded_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="تاريخ الرفع"
    )

    class Meta:
        verbose_name = "مرفق رسالة"
        verbose_name_plural = "مرفقات الرسائل"

    def __str__(self):
        return self.filename

    def get_file_size_display(self):
        """عرض حجم الملف بشكل مقروء"""
        size = self.file_size
        if size < 1024:
            return f"{size} بايت"
        elif size < 1024 * 1024:
            return f"{size // 1024} كيلوبايت"
        else:
            return f"{size // (1024 * 1024)} ميجابايت"


class UserMessageSettings(models.Model):
    """إعدادات الرسائل للمستخدم"""
    
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='message_settings',
        verbose_name="المستخدم"
    )
    
    email_notifications = models.BooleanField(
        default=True,
        verbose_name="إشعارات البريد الإلكتروني"
    )
    
    sound_notifications = models.BooleanField(
        default=True,
        verbose_name="إشعارات صوتية"
    )
    
    auto_read_receipts = models.BooleanField(
        default=True,
        verbose_name="إيصالات القراءة التلقائية"
    )
    
    block_unknown_senders = models.BooleanField(
        default=False,
        verbose_name="حظر المرسلين غير المعروفين"
    )

    class Meta:
        verbose_name = "إعدادات الرسائل"
        verbose_name_plural = "إعدادات الرسائل"

    def __str__(self):
        return f"إعدادات {self.user.username}"
