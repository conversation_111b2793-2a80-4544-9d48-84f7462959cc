from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey


class Notification(models.Model):
    """نموذج الإشعارات"""
    
    TYPE_CHOICES = [
        ('info', 'معلومات'),
        ('success', 'نجاح'),
        ('warning', 'تحذير'),
        ('error', 'خطأ'),
        ('system', 'نظام'),
        ('message', 'رسالة'),
        ('inventory', 'مخزون'),
        ('order', 'طلب'),
        ('payment', 'دفعة'),
        ('user', 'مستخدم'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('high', 'عالية'),
        ('urgent', 'عاجلة'),
    ]
    
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name="المستقبل"
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name="العنوان"
    )
    
    message = models.TextField(
        verbose_name="الرسالة"
    )
    
    notification_type = models.CharField(
        max_length=20,
        choices=TYPE_CHOICES,
        default='info',
        verbose_name="نوع الإشعار"
    )
    
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='normal',
        verbose_name="الأولوية"
    )
    
    is_read = models.BooleanField(
        default=False,
        verbose_name="مقروء"
    )
    
    is_seen = models.BooleanField(
        default=False,
        verbose_name="تم عرضه"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="تاريخ الإنشاء"
    )
    
    read_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="تاريخ القراءة"
    )
    
    # ربط بكائن آخر (اختياري)
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    
    # رابط الإجراء (اختياري)
    action_url = models.URLField(
        null=True,
        blank=True,
        verbose_name="رابط الإجراء"
    )
    
    # أيقونة الإشعار
    icon = models.CharField(
        max_length=50,
        default='bi-bell',
        verbose_name="الأيقونة"
    )
    
    # معرف المرسل (اختياري)
    sender = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sent_notifications',
        verbose_name="المرسل"
    )

    class Meta:
        verbose_name = "إشعار"
        verbose_name_plural = "الإشعارات"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.recipient.username}"

    def mark_as_read(self):
        """تعيين الإشعار كمقروء"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()

    def mark_as_seen(self):
        """تعيين الإشعار كمُشاهد"""
        if not self.is_seen:
            self.is_seen = True
            self.save()

    def get_type_color(self):
        """الحصول على لون نوع الإشعار"""
        colors = {
            'info': '#3b82f6',
            'success': '#10b981',
            'warning': '#f59e0b',
            'error': '#ef4444',
            'system': '#8b5cf6',
            'message': '#06b6d4',
            'inventory': '#f97316',
            'order': '#84cc16',
            'payment': '#ec4899',
            'user': '#6366f1',
        }
        return colors.get(self.notification_type, '#3b82f6')

    def get_type_icon(self):
        """الحصول على أيقونة نوع الإشعار"""
        icons = {
            'info': 'bi-info-circle',
            'success': 'bi-check-circle',
            'warning': 'bi-exclamation-triangle',
            'error': 'bi-x-circle',
            'system': 'bi-gear',
            'message': 'bi-chat-dots',
            'inventory': 'bi-box',
            'order': 'bi-cart',
            'payment': 'bi-cash-coin',
            'user': 'bi-person',
        }
        return icons.get(self.notification_type, 'bi-bell')

    def time_since_created(self):
        """حساب الوقت منذ الإنشاء"""
        now = timezone.now()
        diff = now - self.created_at
        
        if diff.days > 0:
            return f"منذ {diff.days} يوم"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"منذ {hours} ساعة"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"منذ {minutes} دقيقة"
        else:
            return "الآن"

    def get_priority_color(self):
        """الحصول على لون الأولوية"""
        colors = {
            'low': '#6b7280',
            'normal': '#3b82f6',
            'high': '#f59e0b',
            'urgent': '#ef4444'
        }
        return colors.get(self.priority, '#3b82f6')


class NotificationSettings(models.Model):
    """إعدادات الإشعارات للمستخدم"""
    
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='notification_settings',
        verbose_name="المستخدم"
    )
    
    email_notifications = models.BooleanField(
        default=True,
        verbose_name="إشعارات البريد الإلكتروني"
    )
    
    browser_notifications = models.BooleanField(
        default=True,
        verbose_name="إشعارات المتصفح"
    )
    
    sound_notifications = models.BooleanField(
        default=True,
        verbose_name="إشعارات صوتية"
    )
    
    # إعدادات أنواع الإشعارات
    notify_messages = models.BooleanField(
        default=True,
        verbose_name="إشعارات الرسائل"
    )
    
    notify_inventory = models.BooleanField(
        default=True,
        verbose_name="إشعارات المخزون"
    )
    
    notify_orders = models.BooleanField(
        default=True,
        verbose_name="إشعارات الطلبات"
    )
    
    notify_payments = models.BooleanField(
        default=True,
        verbose_name="إشعارات المدفوعات"
    )
    
    notify_system = models.BooleanField(
        default=True,
        verbose_name="إشعارات النظام"
    )

    class Meta:
        verbose_name = "إعدادات الإشعارات"
        verbose_name_plural = "إعدادات الإشعارات"

    def __str__(self):
        return f"إعدادات {self.user.username}"


class NotificationTemplate(models.Model):
    """قوالب الإشعارات"""
    
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="اسم القالب"
    )
    
    title_template = models.CharField(
        max_length=200,
        verbose_name="قالب العنوان"
    )
    
    message_template = models.TextField(
        verbose_name="قالب الرسالة"
    )
    
    notification_type = models.CharField(
        max_length=20,
        choices=Notification.TYPE_CHOICES,
        verbose_name="نوع الإشعار"
    )
    
    priority = models.CharField(
        max_length=10,
        choices=Notification.PRIORITY_CHOICES,
        default='normal',
        verbose_name="الأولوية"
    )
    
    icon = models.CharField(
        max_length=50,
        default='bi-bell',
        verbose_name="الأيقونة"
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name="نشط"
    )

    class Meta:
        verbose_name = "قالب إشعار"
        verbose_name_plural = "قوالب الإشعارات"

    def __str__(self):
        return self.name

    def create_notification(self, recipient, context=None, sender=None, action_url=None):
        """إنشاء إشعار من القالب"""
        context = context or {}
        
        title = self.title_template.format(**context)
        message = self.message_template.format(**context)
        
        return Notification.objects.create(
            recipient=recipient,
            title=title,
            message=message,
            notification_type=self.notification_type,
            priority=self.priority,
            icon=self.icon,
            sender=sender,
            action_url=action_url
        )
