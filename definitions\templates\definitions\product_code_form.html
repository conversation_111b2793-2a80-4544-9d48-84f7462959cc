{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if action == 'edit' %}
تعديل كود الصنف - {{ product_code.code }}
{% else %}
إضافة كود صنف جديد
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Product Code Form Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        --warning-gradient: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
        --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --glass-bg: rgba(255, 255, 255, 0.15);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-backdrop: blur(15px);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --radius: 12px;
        --radius-lg: 20px;
    }

    .content-area {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #11998e 50%, #38ef7d 75%, #ff9a56 100%);
        background-attachment: fixed;
        background-size: 400% 400%;
        animation: gradientShift 20s ease infinite;
        min-height: 100vh;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Form Container */
    .form-container {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: var(--radius-lg);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
        color: white;
    }

    .form-title {
        font-size: 2rem;
        font-weight: 800;
        color: white;
        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        margin-bottom: 2rem;
        text-align: center;
    }

    /* Enhanced Form Controls */
    .form-label {
        color: white;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-control, .form-select {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--radius);
        padding: 0.75rem 1rem;
        color: white;
        backdrop-filter: blur(8px);
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .form-control::placeholder {
        color: rgba(255,255,255,0.7);
    }

    .form-control:focus, .form-select:focus {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        color: white;
        outline: none;
    }

    .form-select option {
        background: #333;
        color: white;
    }

    /* Enhanced Buttons */
    .btn {
        padding: 0.75rem 2rem;
        border-radius: var(--radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .btn-primary {
        background: var(--primary-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
    }

    .btn-outline-light {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        backdrop-filter: blur(8px);
    }

    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
        color: white;
        text-decoration: none;
    }

    /* Form Check */
    .form-check {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius);
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .form-check-input {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin-right: 0.5rem;
    }

    .form-check-input:checked {
        background: var(--success-gradient);
        border-color: rgba(255, 255, 255, 0.5);
    }

    .form-check-label {
        color: white;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    /* Error Messages */
    .invalid-feedback {
        color: #ff6b6b;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        margin-top: 0.5rem;
    }

    .is-invalid {
        border-color: #ff6b6b !important;
        box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2) !important;
    }

    /* Help Text */
    .form-text {
        color: rgba(255,255,255,0.8);
        font-size: 0.8rem;
        margin-top: 0.5rem;
        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    }

    /* Required Field Indicator */
    .required::after {
        content: " *";
        color: #ff6b6b;
        font-weight: bold;
    }

    /* Code Type Badges */
    .code-type-info {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius);
        padding: 1rem;
        margin-top: 1rem;
    }

    .code-type-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
        margin: 0.2rem;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .code-type-badge.supplier { background: var(--primary-gradient); }
    .code-type-badge.manufacturer { background: var(--success-gradient); }
    .code-type-badge.customer { background: var(--warning-gradient); }
    .code-type-badge.internal { background: var(--info-gradient); }
    .code-type-badge.old_system { background: var(--primary-gradient); }
    .code-type-badge.barcode { background: var(--success-gradient); }
    .code-type-badge.sku { background: var(--warning-gradient); }

    /* Responsive */
    @media (max-width: 768px) {
        .form-container {
            padding: 1.5rem;
        }
        
        .form-title {
            font-size: 1.5rem;
        }
        
        .btn {
            padding: 0.6rem 1.5rem;
            font-size: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <h1 class="form-title">
                    <i class="bi bi-upc-scan me-3"></i>
                    {% if action == 'edit' %}
                    تعديل كود الصنف
                    {% else %}
                    إضافة كود صنف جديد
                    {% endif %}
                </h1>

                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="product" class="form-label required">
                                <i class="bi bi-box-seam"></i>الصنف
                            </label>
                            <select class="form-select {% if form_data.product and not product %}is-invalid{% endif %}" 
                                    id="product" 
                                    name="product" 
                                    required>
                                <option value="">-- اختر الصنف --</option>
                                {% for product in products %}
                                <option value="{{ product.id }}" 
                                        {% if action == 'edit' and product_code.product.id == product.id %}selected
                                        {% elif form_data.product == product.id|stringformat:"s" %}selected{% endif %}>
                                    {{ product.code }} - {{ product.name }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">اختر الصنف الذي تريد إضافة كود بديل له</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="code_type" class="form-label required">
                                <i class="bi bi-tags"></i>نوع الكود
                            </label>
                            <select class="form-select {% if form_data.code_type and not code_type %}is-invalid{% endif %}" 
                                    id="code_type" 
                                    name="code_type" 
                                    required>
                                <option value="">-- اختر نوع الكود --</option>
                                {% for type_code, type_name in code_types %}
                                <option value="{{ type_code }}" 
                                        {% if action == 'edit' and product_code.code_type == type_code %}selected
                                        {% elif form_data.code_type == type_code %}selected{% endif %}>
                                    {{ type_name }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">نوع الكود البديل (مورد، عميل، داخلي، إلخ)</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="code" class="form-label required">
                                <i class="bi bi-hash"></i>الكود البديل
                            </label>
                            <input type="text" 
                                   class="form-control {% if form_data.code and not code %}is-invalid{% endif %}" 
                                   id="code" 
                                   name="code" 
                                   value="{% if action == 'edit' %}{{ product_code.code }}{% else %}{{ form_data.code|default:'' }}{% endif %}"
                                   placeholder="مثال: SUP001، CUST123"
                                   required>
                            <div class="form-text">الكود البديل للصنف</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="reference_number" class="form-label">
                                <i class="bi bi-bookmark"></i>رقم المرجع
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="reference_number" 
                                   name="reference_number" 
                                   value="{% if action == 'edit' %}{{ product_code.reference_number }}{% else %}{{ form_data.reference_number|default:'' }}{% endif %}"
                                   placeholder="رقم مرجعي اختياري">
                            <div class="form-text">رقم مرجعي للكود (اختياري)</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="supplier_name" class="form-label">
                                <i class="bi bi-building"></i>اسم المورد/الجهة
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="supplier_name" 
                                   name="supplier_name" 
                                   value="{% if action == 'edit' %}{{ product_code.supplier_name }}{% else %}{{ form_data.supplier_name|default:'' }}{% endif %}"
                                   placeholder="اسم المورد أو الجهة">
                            <div class="form-text">اسم المورد أو الجهة المرتبطة بالكود</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="description" class="form-label">
                                <i class="bi bi-file-text"></i>الوصف
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="description" 
                                   name="description" 
                                   value="{% if action == 'edit' %}{{ product_code.description }}{% else %}{{ form_data.description|default:'' }}{% endif %}"
                                   placeholder="وصف إضافي للكود">
                            <div class="form-text">وصف إضافي أو ملاحظات</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="is_active" 
                                       name="is_active"
                                       {% if action == 'edit' and product_code.is_active %}checked
                                       {% elif action != 'edit' %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    <i class="bi bi-toggle-on me-1"></i>كود نشط
                                </label>
                                <div class="form-text">تفعيل أو إلغاء تفعيل الكود</div>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       id="is_primary" 
                                       name="is_primary"
                                       {% if action == 'edit' and product_code.is_primary %}checked{% endif %}>
                                <label class="form-check-label" for="is_primary">
                                    <i class="bi bi-star me-1"></i>كود أساسي
                                </label>
                                <div class="form-text">تحديد الكود كأساسي لهذا النوع</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'definitions:product_code_list' %}" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left me-2"></i>إلغاء والعودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            {% if action == 'edit' %}
                            حفظ التعديلات
                            {% else %}
                            إضافة الكود
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
