<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 10px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>اختبار API للمنتجات</h1>
    
    <div class="test-section">
        <h2>اختبار جلب بيانات المنتج</h2>
        <input type="number" id="productId" placeholder="معرف المنتج" value="1">
        <button onclick="testProductAPI()">اختبار API</button>
        <div id="result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>قائمة المنتجات المتاحة</h2>
        <button onclick="loadProducts()">تحميل المنتجات</button>
        <div id="products" class="result"></div>
    </div>

    <script>
        function getCSRFToken() {
            const token = document.querySelector('[name=csrfmiddlewaretoken]');
            return token ? token.value : '';
        }

        async function testProductAPI() {
            const productId = document.getElementById('productId').value;
            const resultDiv = document.getElementById('result');
            
            if (!productId) {
                resultDiv.innerHTML = '<div class="error">يرجى إدخال معرف المنتج</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div>جاري التحميل...</div>';
                
                const response = await fetch(`/manufacturing/api/product-cost/${productId}/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>نجح الطلب!</h3>
                            <p><strong>الاسم:</strong> ${data.name}</p>
                            <p><strong>الكود:</strong> ${data.code}</p>
                            <p><strong>سعر التكلفة:</strong> ${data.cost_price} ج.م</p>
                            <p><strong>سعر البيع:</strong> ${data.selling_price} ج.م</p>
                            <p><strong>وحدة القياس:</strong> ${data.main_unit_name}</p>
                            <p><strong>نوع المنتج:</strong> ${data.product_type}</p>
                            <p><strong>يوجد سعر:</strong> ${data.has_cost_price ? 'نعم' : 'لا'}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>فشل الطلب</h3>
                            <p><strong>الخطأ:</strong> ${data.error}</p>
                            <p><strong>كود الحالة:</strong> ${response.status}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>خطأ في الاتصال</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function loadProducts() {
            const productsDiv = document.getElementById('products');
            
            try {
                productsDiv.innerHTML = '<div>جاري تحميل المنتجات...</div>';
                
                // هذا مجرد مثال - يمكن إنشاء API منفصل لقائمة المنتجات
                productsDiv.innerHTML = `
                    <div class="success">
                        <h3>منتجات للاختبار:</h3>
                        <p>جرب المعرفات التالية: 1, 2, 3, 4</p>
                        <p>أو تحقق من قاعدة البيانات للحصول على معرفات صحيحة</p>
                    </div>
                `;
            } catch (error) {
                productsDiv.innerHTML = `
                    <div class="error">
                        <h3>خطأ في تحميل المنتجات</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
