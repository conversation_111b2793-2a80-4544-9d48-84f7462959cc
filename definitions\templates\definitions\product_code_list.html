{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة أكواد الأصناف{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Product Codes Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        --warning-gradient: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
        --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --purple-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --teal-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --orange-gradient: linear-gradient(135deg, #ff9a56 0%, #ff6b95 100%);
        --glass-bg: rgba(255, 255, 255, 0.15);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-backdrop: blur(15px);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --radius: 12px;
        --radius-lg: 20px;
    }

    .content-area {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #11998e 50%, #38ef7d 75%, #ff9a56 100%);
        background-attachment: fixed;
        background-size: 400% 400%;
        animation: gradientShift 20s ease infinite;
        min-height: 100vh;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Page Header */
    .page-header {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-light);
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .page-subtitle {
        font-size: 1.1rem;
        color: rgba(255,255,255,0.9);
        margin: 0.5rem 0 0 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }



    /* Filters */
    .filters-section {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-light);
        margin-bottom: 1.5rem;
    }

    .filters-title {
        color: white;
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .filters-row {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr auto;
        gap: 1rem;
        align-items: end;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-label {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .form-control {
        padding: 0.75rem 1rem;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--radius);
        font-size: 0.875rem;
        color: white;
        backdrop-filter: blur(4px);
        transition: all 0.3s ease;
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        color: white;
        outline: none;
    }

    .form-control option {
        background: #333;
        color: white;
    }

    /* Enhanced Buttons */
    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: var(--radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .btn-primary {
        background: var(--primary-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
    }

    .btn-outline-light {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        backdrop-filter: blur(8px);
    }

    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        color: white;
    }

    .btn-success {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(86, 171, 47, 0.4);
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(86, 171, 47, 0.6);
        color: white;
    }

    /* Enhanced Table */
    .table-container {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-light);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .table-header {
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
    }

    .table-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        margin: 0;
    }

    .table {
        width: 100%;
        margin: 0;
        color: white;
    }

    .table th {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
        padding: 1rem 0.75rem;
        text-align: center;
        font-weight: 700;
        color: white;
        border-bottom: 2px solid rgba(255, 255, 255, 0.3);
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        font-size: 0.875rem;
    }

    .table td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        color: white;
        background: rgba(255, 255, 255, 0.08);
        text-align: center;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        font-size: 0.875rem;
    }

    .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.005);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(31, 38, 135, 0.3);
    }

    /* Enhanced Badges */
    .badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.8rem;
        border-radius: 25px;
        font-size: 0.75rem;
        font-weight: 600;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        text-shadow: 0 1px 2px rgba(0,0,0,0.4);
        transition: all 0.3s ease;
        white-space: nowrap;
    }

    .badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    }

    .badge-supplier { background: var(--primary-gradient); color: white; }
    .badge-manufacturer { background: var(--success-gradient); color: white; }
    .badge-customer { background: var(--warning-gradient); color: white; }
    .badge-internal { background: var(--info-gradient); color: white; }
    .badge-old_system { background: var(--purple-gradient); color: white; }
    .badge-barcode { background: var(--teal-gradient); color: white; }
    .badge-sku { background: var(--orange-gradient); color: white; }

    .badge-active {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(86, 171, 47, 0.4);
    }

    .badge-inactive {
        background: var(--danger-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 65, 108, 0.4);
    }

    .badge-primary-yes {
        background: var(--warning-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(247, 151, 30, 0.4);
    }

    .badge-primary-no {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .action-buttons .btn-sm {
        padding: 0.5rem;
        border-radius: 8px;
        backdrop-filter: blur(4px);
        transition: all 0.3s ease;
        font-size: 0.75rem;
    }

    .action-buttons .btn-sm:hover {
        transform: scale(1.1);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .filters-row {
            grid-template-columns: 1fr;
        }

        .page-title {
            font-size: 2rem;
        }

        .table-container {
            overflow-x: auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">
                    <i class="bi bi-upc-scan me-3"></i>
                    إدارة أكواد الأصناف
                </h1>
                <p class="page-subtitle">
                    إدارة الأكواد البديلة والمرجعية للأصناف من مختلف المصادر
                </p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>
                    عودة للتعريفات
                </a>
                <a href="{% url 'definitions:product_code_create' %}" class="btn btn-success">
                    <i class="bi bi-plus-lg me-2"></i>
                    إضافة كود جديد
                </a>
            </div>
        </div>
    </div>



    <!-- Filters -->
    <div class="filters-section">
        <h3 class="filters-title">
            <i class="bi bi-funnel me-2"></i>
            البحث والفلترة
        </h3>
        <form method="get" class="filters-form">
            <div class="filters-row">
                <div class="form-group">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control"
                           placeholder="البحث بالكود، اسم الصنف، أو اسم المورد..."
                           value="{{ search_query }}">
                </div>
                <div class="form-group">
                    <label class="form-label">نوع الكود</label>
                    <select name="code_type" class="form-control">
                        <option value="">جميع الأنواع</option>
                        {% for type_code, type_name in code_types %}
                        <option value="{{ type_code }}" {% if code_type == type_code %}selected{% endif %}>
                            {{ type_name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">الصنف</label>
                    <select name="product" class="form-control">
                        <option value="">جميع الأصناف</option>
                        {% for product in products %}
                        <option value="{{ product.id }}" {% if product_id == product.id|stringformat:"s" %}selected{% endif %}>
                            {{ product.code }} - {{ product.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Codes Table -->
    <div class="table-container">
        <div class="table-header">
            <h3 class="table-title">
                <i class="bi bi-table me-2"></i>
                قائمة أكواد الأصناف
                {% if search_query or code_type or product_id %}
                <small class="ms-2">({{ page_obj.paginator.count }} نتيجة)</small>
                {% endif %}
            </h3>
        </div>

        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>كود الصنف</th>
                        <th>اسم الصنف</th>
                        <th>نوع الكود</th>
                        <th>الكود البديل</th>
                        <th>المورد/الجهة</th>
                        <th>رقم المرجع</th>
                        <th>أساسي</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product_code in page_obj %}
                    <tr>
                        <td>
                            <strong>{{ product_code.product.code }}</strong>
                        </td>
                        <td>
                            <div>
                                <strong>{{ product_code.product.name }}</strong>
                                {% if product_code.product.name_en %}
                                <br><small class="text-muted">{{ product_code.product.name_en }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <span class="badge badge-{{ product_code.code_type }}">
                                {{ product_code.get_code_type_display }}
                            </span>
                        </td>
                        <td>
                            <strong>{{ product_code.code }}</strong>
                            {% if product_code.description %}
                            <br><small class="text-muted">{{ product_code.description }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if product_code.supplier_name %}
                            {{ product_code.supplier_name }}
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product_code.reference_number %}
                            {{ product_code.reference_number }}
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product_code.is_primary %}
                            <span class="badge badge-primary-yes">
                                <i class="bi bi-star-fill me-1"></i>أساسي
                            </span>
                            {% else %}
                            <span class="badge badge-primary-no">
                                <i class="bi bi-star me-1"></i>عادي
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product_code.is_active %}
                            <span class="badge badge-active">
                                <i class="bi bi-check-circle me-1"></i>نشط
                            </span>
                            {% else %}
                            <span class="badge badge-inactive">
                                <i class="bi bi-x-circle me-1"></i>غير نشط
                            </span>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                {{ product_code.created_at|date:"d/m/Y" }}
                                <br><small class="text-muted">{{ product_code.created_at|date:"H:i" }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'definitions:product_code_detail' product_code.id %}"
                                   class="btn btn-outline-light btn-sm" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'definitions:product_code_edit' product_code.id %}"
                                   class="btn btn-outline-light btn-sm" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'definitions:product_code_delete' product_code.id %}"
                                   class="btn btn-outline-light btn-sm text-danger" title="حذف"
                                   onclick="return confirm('هل أنت متأكد من حذف هذا الكود؟')">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="10" class="text-center py-5">
                            <div class="text-muted">
                                <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">لا توجد أكواد مسجلة</h5>
                                <p>ابدأ بإضافة أكواد بديلة للأصناف</p>
                                <a href="{% url 'definitions:product_code_create' %}" class="btn btn-success">
                                    <i class="bi bi-plus-circle me-2"></i>إضافة كود جديد
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
