{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 3rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    .section-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin: 2rem 0 1rem 0;
        border-left: 4px solid #007bff;
    }
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
    }
    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    .btn {
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-gear-wide-connected me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:finished_product_model_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <div class="form-card">
        <form method="post" novalidate>
            {% csrf_token %}
            
            <!-- المعلومات الأساسية -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-info-circle me-2"></i>المعلومات الأساسية</h4>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.code.id_for_label }}" class="form-label">{{ form.code.label }} <span class="text-danger">*</span></label>
                        {{ form.code }}
                        {% if form.code.errors %}
                            <div class="text-danger small">{{ form.code.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }} <span class="text-danger">*</span></label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger small">{{ form.name.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.name_en.id_for_label }}" class="form-label">{{ form.name_en.label }}</label>
                        {{ form.name_en }}
                        {% if form.name_en.errors %}
                            <div class="text-danger small">{{ form.name_en.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.product_type.id_for_label }}" class="form-label">{{ form.product_type.label }}</label>
                        {{ form.product_type }}
                        {% if form.product_type.errors %}
                            <div class="text-danger small">{{ form.product_type.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.quality_level.id_for_label }}" class="form-label">{{ form.quality_level.label }}</label>
                        {{ form.quality_level }}
                        {% if form.quality_level.errors %}
                            <div class="text-danger small">{{ form.quality_level.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                {{ form.description }}
                {% if form.description.errors %}
                    <div class="text-danger small">{{ form.description.errors.0 }}</div>
                {% endif %}
            </div>

            <!-- معلومات الإنتاج -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-gear me-2"></i>معلومات الإنتاج</h4>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.final_product.id_for_label }}" class="form-label">{{ form.final_product.label }} <span class="text-danger">*</span></label>
                        {{ form.final_product }}
                        {% if form.final_product.errors %}
                            <div class="text-danger small">{{ form.final_product.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.quantity_produced.id_for_label }}" class="form-label">{{ form.quantity_produced.label }} <span class="text-danger">*</span></label>
                        {{ form.quantity_produced }}
                        {% if form.quantity_produced.errors %}
                            <div class="text-danger small">{{ form.quantity_produced.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.unit_of_measure.id_for_label }}" class="form-label">{{ form.unit_of_measure.label }} <span class="text-danger">*</span></label>
                        {{ form.unit_of_measure }}
                        {% if form.unit_of_measure.errors %}
                            <div class="text-danger small">{{ form.unit_of_measure.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.production_time_hours.id_for_label }}" class="form-label">{{ form.production_time_hours.label }}</label>
                        {{ form.production_time_hours }}
                        {% if form.production_time_hours.errors %}
                            <div class="text-danger small">{{ form.production_time_hours.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.labor_cost.id_for_label }}" class="form-label">{{ form.labor_cost.label }}</label>
                        {{ form.labor_cost }}
                        {% if form.labor_cost.errors %}
                            <div class="text-danger small">{{ form.labor_cost.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.overhead_cost.id_for_label }}" class="form-label">{{ form.overhead_cost.label }}</label>
                        {{ form.overhead_cost }}
                        {% if form.overhead_cost.errors %}
                            <div class="text-danger small">{{ form.overhead_cost.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- التكاليف -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-currency-dollar me-2"></i>التكاليف والأسعار</h4>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.estimated_material_cost.id_for_label }}" class="form-label">{{ form.estimated_material_cost.label }}</label>
                        {{ form.estimated_material_cost }}
                        {% if form.estimated_material_cost.errors %}
                            <div class="text-danger small">{{ form.estimated_material_cost.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.estimated_total_cost.id_for_label }}" class="form-label">{{ form.estimated_total_cost.label }}</label>
                        {{ form.estimated_total_cost }}
                        {% if form.estimated_total_cost.errors %}
                            <div class="text-danger small">{{ form.estimated_total_cost.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.target_selling_price.id_for_label }}" class="form-label">{{ form.target_selling_price.label }}</label>
                        {{ form.target_selling_price }}
                        {% if form.target_selling_price.errors %}
                            <div class="text-danger small">{{ form.target_selling_price.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- الجودة والمواصفات -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-award me-2"></i>الجودة والمواصفات</h4>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.specifications.id_for_label }}" class="form-label">{{ form.specifications.label }}</label>
                        {{ form.specifications }}
                        {% if form.specifications.errors %}
                            <div class="text-danger small">{{ form.specifications.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.quality_standards.id_for_label }}" class="form-label">{{ form.quality_standards.label }}</label>
                        {{ form.quality_standards }}
                        {% if form.quality_standards.errors %}
                            <div class="text-danger small">{{ form.quality_standards.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.testing_requirements.id_for_label }}" class="form-label">{{ form.testing_requirements.label }}</label>
                        {{ form.testing_requirements }}
                        {% if form.testing_requirements.errors %}
                            <div class="text-danger small">{{ form.testing_requirements.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-info-square me-2"></i>معلومات إضافية</h4>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.shelf_life_days.id_for_label }}" class="form-label">{{ form.shelf_life_days.label }}</label>
                        {{ form.shelf_life_days }}
                        {% if form.shelf_life_days.errors %}
                            <div class="text-danger small">{{ form.shelf_life_days.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.storage_conditions.id_for_label }}" class="form-label">{{ form.storage_conditions.label }}</label>
                        {{ form.storage_conditions }}
                        {% if form.storage_conditions.errors %}
                            <div class="text-danger small">{{ form.storage_conditions.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.packaging_requirements.id_for_label }}" class="form-label">{{ form.packaging_requirements.label }}</label>
                        {{ form.packaging_requirements }}
                        {% if form.packaging_requirements.errors %}
                            <div class="text-danger small">{{ form.packaging_requirements.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- الحالة -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-toggle-on me-2"></i>الحالة</h4>
            </div>
            <div class="mb-3">
                <div class="form-check">
                    {{ form.is_active }}
                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                        {{ form.is_active.label }}
                    </label>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="row mt-4">
                <div class="col-12 text-center">
                    <button type="submit" class="btn btn-primary btn-lg me-3">
                        <i class="bi bi-check-circle me-2"></i>
                        {% if action == 'create' %}إنشاء النموذج{% else %}حفظ التغييرات{% endif %}
                    </button>
                    <a href="{% url 'definitions:finished_product_model_list' %}" class="btn btn-secondary btn-lg">
                        <i class="bi bi-x-circle me-2"></i>إلغاء
                    </a>
                </div>
            </div>

            {% if form.non_field_errors %}
                <div class="alert alert-danger mt-3">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}
        </form>
    </div>
</div>
{% endblock %}
