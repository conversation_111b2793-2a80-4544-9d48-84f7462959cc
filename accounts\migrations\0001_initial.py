# Generated by Django 5.2.4 on 2025-07-13 01:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم نوع الحساب')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='رمز النوع')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'نوع حساب',
                'verbose_name_plural': 'أنواع الحسابات',
            },
        ),
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الحساب')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رقم الحساب')),
                ('nature', models.CharField(choices=[('debit', 'مدين'), ('credit', 'دائن')], max_length=10, verbose_name='طبيعة الحساب')),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounts.account', verbose_name='الحساب الأب')),
                ('account_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.accounttype', verbose_name='نوع الحساب')),
            ],
            options={
                'verbose_name': 'حساب',
                'verbose_name_plural': 'الحسابات',
                'ordering': ['code'],
            },
        ),
    ]
