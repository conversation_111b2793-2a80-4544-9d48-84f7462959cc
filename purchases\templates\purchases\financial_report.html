{% extends 'base.html' %}
{% load static %}

{% block title %}التقرير المالي للمشتريات - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-left: 5px solid #6f42c1;
    }

    .stat-card h3 {
        margin: 0 0 10px 0;
        font-size: 2.5rem;
        font-weight: bold;
        color: #6f42c1;
    }

    .stat-card p {
        margin: 0;
        font-size: 1.1rem;
        color: #6c757d;
    }

    .stat-card.success {
        border-left-color: #28a745;
    }

    .stat-card.success h3 {
        color: #28a745;
    }

    .stat-card.warning {
        border-left-color: #ffc107;
    }

    .stat-card.warning h3 {
        color: #ffc107;
    }

    .stat-card.danger {
        border-left-color: #dc3545;
    }

    .stat-card.danger h3 {
        color: #dc3545;
    }

    .filters-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .chart-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .suppliers-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table {
        margin: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table tbody td {
        padding: 15px;
        vertical-align: middle;
        border-bottom: 1px solid #f0f0f0;
    }

    .progress-bar-custom {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
        overflow: hidden;
        margin-top: 5px;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(45deg, #6f42c1, #5a32a3);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .amount-highlight {
        font-size: 1.1rem;
        font-weight: bold;
        color: #6f42c1;
    }

    .btn-export {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .monthly-chart {
        height: 300px;
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        display: flex;
        align-items: end;
        justify-content: space-around;
    }

    .chart-bar {
        background: linear-gradient(to top, #6f42c1, #5a32a3);
        border-radius: 4px 4px 0 0;
        min-height: 20px;
        width: 40px;
        position: relative;
        transition: all 0.3s ease;
    }

    .chart-bar:hover {
        transform: scale(1.05);
    }

    .chart-label {
        position: absolute;
        bottom: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.8rem;
        color: #6c757d;
        white-space: nowrap;
    }

    .chart-value {
        position: absolute;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.8rem;
        font-weight: bold;
        color: #6f42c1;
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-calculator"></i>
                    التقرير المالي للمشتريات
                </h1>
                <p class="mb-0">تحليل مالي شامل لتكاليف المشتريات والمدفوعات</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'purchases:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house"></i>
                        لوحة التحكم
                    </a>
                    <button class="btn btn-export" onclick="window.print()">
                        <i class="bi bi-printer"></i>
                        طباعة التقرير
                    </button>
                    <a href="{% url 'purchases:reports' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-right"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات المالية -->
    <div class="stats-cards">
        <div class="stat-card">
            <h3>{{ total_amount|floatformat:0 }}</h3>
            <p>إجمالي المشتريات (ج.م)</p>
        </div>
        <div class="stat-card success">
            <h3>{{ paid_amount|floatformat:0 }}</h3>
            <p>المبالغ المدفوعة (ج.م)</p>
        </div>
        <div class="stat-card warning">
            <h3>{{ pending_amount|floatformat:0 }}</h3>
            <p>المبالغ المعلقة (ج.م)</p>
        </div>
        <div class="stat-card danger">
            <h3>{{ overdue_amount|floatformat:0 }}</h3>
            <p>المبالغ المتأخرة (ج.م)</p>
        </div>
        <div class="stat-card">
            <h3>{{ payment_percentage|floatformat:1 }}%</h3>
            <p>نسبة السداد</p>
        </div>
    </div>

    <!-- الفلاتر -->
    <div class="filters-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            
            <div class="col-md-4">
                <label class="form-label">المورد</label>
                <select name="supplier" class="form-select">
                    <option value="">جميع الموردين</option>
                    {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if supplier.id|stringformat:"s" == selected_supplier %}selected{% endif %}>
                            {{ supplier.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-funnel"></i>
                    تطبيق
                </button>
            </div>
        </form>
    </div>

    <!-- الرسم البياني الشهري -->
    <div class="chart-section">
        <h4 class="mb-4">
            <i class="bi bi-bar-chart"></i>
            التحليل الشهري للمشتريات
        </h4>
        <div class="monthly-chart">
            {% for month in monthly_data %}
                <div class="chart-bar" style="height: {% if month.total_amount > 0 %}{{ month.total_amount|floatformat:0|length|add:20 }}px{% else %}20px{% endif %};">
                    <div class="chart-value">{{ month.total_amount|floatformat:0 }}</div>
                    <div class="chart-label">{{ month.month }}</div>
                </div>
            {% endfor %}
        </div>
    </div>

    <!-- تحليل الموردين -->
    <div class="suppliers-table">
        <div class="table-header p-3">
            <h4 class="mb-0">
                <i class="bi bi-people"></i>
                أكبر 10 موردين (حسب قيمة المشتريات)
            </h4>
        </div>
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>المورد</th>
                    <th>عدد الفواتير</th>
                    <th>إجمالي المشتريات</th>
                    <th>متوسط الفاتورة</th>
                    <th>النسبة من الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for supplier in supplier_analysis %}
                    <tr>
                        <td>
                            <strong>{{ supplier.supplier__name }}</strong>
                        </td>
                        <td>
                            {{ supplier.invoice_count }} فاتورة
                        </td>
                        <td>
                            <span class="amount-highlight">{{ supplier.total_amount|floatformat:2 }} ج.م</span>
                        </td>
                        <td>
                            {{ supplier.avg_amount|floatformat:2 }} ج.م
                        </td>
                        <td>
                            {% widthratio supplier.total_amount total_amount 100 %}%
                            <div class="progress-bar-custom">
                                <div class="progress-fill" style="width: {% widthratio supplier.total_amount total_amount 100 %}%;"></div>
                            </div>
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="5" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد بيانات موردين</p>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- ملخص مالي -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header" style="background: #6f42c1; color: white;">
                    <h5 class="mb-0">الملخص المالي</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>إجمالي الفواتير:</strong>
                        </div>
                        <div class="col-6">
                            {{ total_invoices }} فاتورة
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <strong>إجمالي المشتريات:</strong>
                        </div>
                        <div class="col-6">
                            <span class="amount-highlight">{{ total_amount|floatformat:2 }} ج.م</span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <strong>نسبة السداد:</strong>
                        </div>
                        <div class="col-6">
                            <span class="text-success">{{ payment_percentage|floatformat:1 }}%</span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">
                            <strong>المتبقي للسداد:</strong>
                        </div>
                        <div class="col-6">
                            <span class="text-warning">{{ pending_amount|floatformat:2 }} ج.م</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين الطباعة
    window.addEventListener('beforeprint', function() {
        document.body.classList.add('printing');
    });
    
    window.addEventListener('afterprint', function() {
        document.body.classList.remove('printing');
    });
</script>

<style>
    @media print {
        .btn, .filters-section {
            display: none !important;
        }
        
        .page-header {
            background: #6f42c1 !important;
            -webkit-print-color-adjust: exact;
        }
        
        .table thead th {
            background: #6f42c1 !important;
            -webkit-print-color-adjust: exact;
        }
    }
</style>
{% endblock %}
