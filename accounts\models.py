from django.db import models

class AccountType(models.Model):
    """أنواع الحسابات"""
    name = models.CharField(max_length=100, verbose_name="اسم نوع الحساب")
    code = models.CharField(max_length=10, unique=True, verbose_name="رمز النوع")
    description = models.TextField(blank=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "نوع حساب"
        verbose_name_plural = "أنواع الحسابات"

    def __str__(self):
        return self.name

class Account(models.Model):
    """دليل الحسابات"""
    ACCOUNT_NATURE = [
        ('debit', 'مدين'),
        ('credit', 'دائن'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم الحساب")
    code = models.Cha<PERSON><PERSON><PERSON>(max_length=20, unique=True, verbose_name="رقم الحساب")
    account_type = models.ForeignKey(AccountType, on_delete=models.CASCADE, verbose_name="نوع الحساب")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="الحساب الأب")
    nature = models.CharField(max_length=10, choices=ACCOUNT_NATURE, verbose_name="طبيعة الحساب")
    balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    description = models.TextField(blank=True, verbose_name="الوصف")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "حساب"
        verbose_name_plural = "الحسابات"
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"
