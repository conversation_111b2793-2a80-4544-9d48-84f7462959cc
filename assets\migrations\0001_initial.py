# Generated by Django 5.2.4 on 2025-07-12 22:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AssetCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('depreciation_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الاستهلاك السنوي (%)')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'فئة أصل',
                'verbose_name_plural': 'فئات الأصول',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الأصل')),
                ('asset_code', models.CharField(max_length=50, unique=True, verbose_name='كود الأصل')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('purchase_date', models.DateField(verbose_name='تاريخ الشراء')),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='سعر الشراء')),
                ('supplier', models.CharField(blank=True, max_length=200, null=True, verbose_name='المورد')),
                ('useful_life_years', models.IntegerField(default=5, verbose_name='العمر الافتراضي (سنوات)')),
                ('salvage_value', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='القيمة المتبقية')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('maintenance', 'تحت الصيانة'), ('disposed', 'تم التخلص منه'), ('sold', 'تم البيع')], default='active', max_length=20, verbose_name='الحالة')),
                ('condition', models.CharField(choices=[('excellent', 'ممتاز'), ('good', 'جيد'), ('fair', 'مقبول'), ('poor', 'ضعيف')], default='excellent', max_length=20, verbose_name='حالة الأصل')),
                ('location', models.CharField(blank=True, max_length=200, null=True, verbose_name='الموقع')),
                ('serial_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='الرقم التسلسلي')),
                ('model', models.CharField(blank=True, max_length=100, null=True, verbose_name='الموديل')),
                ('manufacturer', models.CharField(blank=True, max_length=100, null=True, verbose_name='الشركة المصنعة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('responsible_person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المسؤول')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assets.assetcategory', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'أصل ثابت',
                'verbose_name_plural': 'الأصول الثابتة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AssetDisposal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('disposal_type', models.CharField(choices=[('sale', 'بيع'), ('scrap', 'خردة'), ('donation', 'تبرع'), ('trade', 'مقايضة')], max_length=20, verbose_name='نوع التخلص')),
                ('disposal_date', models.DateField(verbose_name='تاريخ التخلص')),
                ('disposal_value', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='قيمة التخلص')),
                ('buyer_details', models.TextField(blank=True, null=True, verbose_name='تفاصيل المشتري')),
                ('reason', models.TextField(verbose_name='سبب التخلص')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('asset', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='assets.asset', verbose_name='الأصل')),
            ],
            options={
                'verbose_name': 'التخلص من أصل',
                'verbose_name_plural': 'التخلص من الأصول',
                'ordering': ['-disposal_date'],
            },
        ),
        migrations.CreateModel(
            name='AssetMaintenance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('maintenance_type', models.CharField(choices=[('preventive', 'صيانة وقائية'), ('corrective', 'صيانة إصلاحية'), ('emergency', 'صيانة طارئة')], max_length=20, verbose_name='نوع الصيانة')),
                ('description', models.TextField(verbose_name='وصف الصيانة')),
                ('maintenance_date', models.DateField(verbose_name='تاريخ الصيانة')),
                ('cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='تكلفة الصيانة')),
                ('vendor', models.CharField(blank=True, max_length=200, null=True, verbose_name='مقدم الخدمة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenances', to='assets.asset', verbose_name='الأصل')),
                ('performed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة')),
            ],
            options={
                'verbose_name': 'صيانة أصل',
                'verbose_name_plural': 'صيانة الأصول',
                'ordering': ['-maintenance_date'],
            },
        ),
        migrations.CreateModel(
            name='AssetTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_location', models.CharField(max_length=200, verbose_name='من الموقع')),
                ('to_location', models.CharField(max_length=200, verbose_name='إلى الموقع')),
                ('transfer_date', models.DateField(verbose_name='تاريخ التحويل')),
                ('reason', models.TextField(verbose_name='سبب التحويل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='asset_transfers_approved', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers', to='assets.asset', verbose_name='الأصل')),
                ('from_person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='asset_transfers_from', to=settings.AUTH_USER_MODEL, verbose_name='من المسؤول')),
                ('to_person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='asset_transfers_to', to=settings.AUTH_USER_MODEL, verbose_name='إلى المسؤول')),
            ],
            options={
                'verbose_name': 'تحويل أصل',
                'verbose_name_plural': 'تحويلات الأصول',
                'ordering': ['-transfer_date'],
            },
        ),
    ]
