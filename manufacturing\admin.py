from django.contrib import admin
from .models import (
    ManufacturingOrder, ManufacturingOrderRawMaterial, ManufacturingOrderStage,
    ManufacturingInventoryTransaction, ManufacturingQualityCheck
)


class ManufacturingOrderRawMaterialInline(admin.TabularInline):
    model = ManufacturingOrderRawMaterial
    extra = 1
    fields = ['raw_material', 'required_quantity', 'unit_of_measure', 'unit_cost', 'total_cost', 'is_critical']
    readonly_fields = ['total_cost']


class ManufacturingOrderStageInline(admin.TabularInline):
    model = ManufacturingOrderStage
    extra = 1
    fields = ['stage_name', 'sequence_number', 'planned_start_date', 'planned_end_date', 'status', 'assigned_to']


@admin.register(ManufacturingOrder)
class ManufacturingOrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'final_product', 'quantity_to_produce', 'status', 'priority', 'order_date', 'expected_completion_date']
    list_filter = ['status', 'priority', 'order_date', 'expected_completion_date']
    search_fields = ['order_number', 'final_product__name', 'final_product__code']
    readonly_fields = ['total_estimated_cost', 'total_actual_cost', 'cost_variance', 'progress_percentage']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('order_number', 'order_date', 'expected_completion_date', 'actual_completion_date')
        }),
        ('المنتج والكميات', {
            'fields': ('final_product', 'quantity_to_produce', 'unit_of_measure')
        }),
        ('المخازن', {
            'fields': ('raw_materials_warehouse', 'finished_goods_warehouse')
        }),
        ('الحالة والأولوية', {
            'fields': ('status', 'priority')
        }),
        ('التكاليف المقدرة', {
            'fields': ('estimated_raw_material_cost', 'estimated_labor_cost', 'estimated_overhead_cost', 'total_estimated_cost')
        }),
        ('التكاليف الفعلية', {
            'fields': ('actual_raw_material_cost', 'actual_labor_cost', 'actual_overhead_cost', 'total_actual_cost', 'cost_variance')
        }),
        ('معلومات إضافية', {
            'fields': ('notes', 'special_instructions', 'quality_requirements')
        }),
        ('معلومات الموافقة والتنفيذ', {
            'fields': ('approved_by', 'approved_at', 'started_by', 'started_at', 'completed_by', 'progress_percentage'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )

    inlines = [ManufacturingOrderRawMaterialInline, ManufacturingOrderStageInline]


@admin.register(ManufacturingOrderRawMaterial)
class ManufacturingOrderRawMaterialAdmin(admin.ModelAdmin):
    list_display = ['manufacturing_order', 'raw_material', 'required_quantity', 'allocated_quantity', 'unit_cost', 'total_cost', 'is_available']
    list_filter = ['is_critical', 'is_available', 'manufacturing_order__status']
    search_fields = ['manufacturing_order__order_number', 'raw_material__name', 'raw_material__code']
    readonly_fields = ['total_cost', 'remaining_quantity', 'allocation_percentage']


@admin.register(ManufacturingOrderStage)
class ManufacturingOrderStageAdmin(admin.ModelAdmin):
    list_display = ['manufacturing_order', 'stage_name', 'sequence_number', 'status', 'progress_percentage', 'planned_start_date', 'planned_end_date', 'assigned_to']
    list_filter = ['status', 'quality_check_required', 'quality_check_passed']
    search_fields = ['manufacturing_order__order_number', 'stage_name']
    readonly_fields = ['estimated_labor_cost', 'actual_labor_cost', 'total_estimated_cost', 'total_actual_cost', 'is_overdue', 'duration_planned', 'duration_actual']


@admin.register(ManufacturingInventoryTransaction)
class ManufacturingInventoryTransactionAdmin(admin.ModelAdmin):
    list_display = ['manufacturing_order', 'transaction_type', 'product', 'warehouse', 'quantity', 'unit_cost', 'total_cost', 'transaction_date', 'is_processed']
    list_filter = ['transaction_type', 'is_processed', 'transaction_date', 'warehouse']
    search_fields = ['manufacturing_order__order_number', 'product__name', 'reference_number']
    readonly_fields = ['total_cost']


@admin.register(ManufacturingQualityCheck)
class ManufacturingQualityCheckAdmin(admin.ModelAdmin):
    list_display = ['manufacturing_order', 'check_type', 'result', 'score', 'check_date', 'inspector']
    list_filter = ['check_type', 'result', 'check_date']
    search_fields = ['manufacturing_order__order_number', 'inspector__username']
