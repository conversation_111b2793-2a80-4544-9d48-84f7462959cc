# Generated by Django 5.2.4 on 2025-07-20 23:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0002_remove_product_unit_price_customer_credit_days_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الدور')),
                ('role_type', models.CharField(choices=[('admin', 'مدير عام'), ('manager', 'مدير'), ('supervisor', 'مشرف'), ('representative', 'مندوب'), ('accountant', 'محاسب'), ('warehouse_keeper', 'أمين مخزن')], max_length=20, verbose_name='نوع الدور')),
                ('description', models.TextField(blank=True, verbose_name='وصف الدور')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'دور',
                'verbose_name_plural': 'الأدوار',
            },
        ),
        migrations.AddField(
            model_name='customer',
            name='current_balance',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='الرصيد الحالي'),
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي المبلغ'),
        ),
        migrations.CreateModel(
            name='AccessLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('view', 'عرض'), ('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('approve', 'اعتماد'), ('print', 'طباعة'), ('export', 'تصدير')], max_length=20, verbose_name='الإجراء')),
                ('module', models.CharField(max_length=50, verbose_name='الوحدة')),
                ('object_id', models.CharField(blank=True, max_length=100, verbose_name='معرف الكائن')),
                ('object_name', models.CharField(blank=True, max_length=200, verbose_name='اسم الكائن')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='الوقت')),
                ('success', models.BooleanField(default=True, verbose_name='نجح')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل الوصول',
                'verbose_name_plural': 'سجلات الوصول',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الصلاحية')),
                ('codename', models.CharField(max_length=100, unique=True, verbose_name='الكود')),
                ('permission_type', models.CharField(choices=[('view', 'عرض'), ('add', 'إضافة'), ('change', 'تعديل'), ('delete', 'حذف'), ('approve', 'اعتماد'), ('print', 'طباعة'), ('export', 'تصدير')], max_length=20, verbose_name='نوع الصلاحية')),
                ('module', models.CharField(choices=[('customers', 'العملاء'), ('products', 'المنتجات'), ('sales', 'المبيعات'), ('invoices', 'الفواتير'), ('orders', 'الطلبات'), ('returns', 'المرتجعات'), ('payments', 'المدفوعات'), ('inventory', 'المخزون'), ('reports', 'التقارير'), ('settings', 'الإعدادات'), ('users', 'المستخدمين'), ('vehicles', 'السيارات'), ('representatives', 'المناديب')], max_length=50, verbose_name='الوحدة')),
                ('description', models.TextField(blank=True, verbose_name='وصف الصلاحية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'صلاحية',
                'verbose_name_plural': 'الصلاحيات',
                'unique_together': {('permission_type', 'module')},
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعيين')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('assigned_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_roles', to=settings.AUTH_USER_MODEL, verbose_name='عُين بواسطة')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.role', verbose_name='الدور')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'دور المستخدم',
                'verbose_name_plural': 'أدوار المستخدمين',
                'unique_together': {('user', 'role')},
            },
        ),
    ]
