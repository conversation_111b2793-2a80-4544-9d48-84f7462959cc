from django.db import models
from django.contrib.auth.models import User

class UserSettings(models.Model):
    """إعدادات المستخدم"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='settings')

    # إعدادات الإشعارات
    email_notifications = models.BooleanField(default=True, verbose_name='إشعارات البريد الإلكتروني')
    daily_reports = models.BooleanField(default=False, verbose_name='التقارير اليومية')
    push_notifications = models.BooleanField(default=True, verbose_name='الإشعارات المنبثقة')
    sound_notifications = models.BooleanField(default=False, verbose_name='الأصوات')

    # إعدادات المظهر
    dark_mode = models.BooleanField(default=False, verbose_name='الوضع الليلي')
    font_size = models.CharField(
        max_length=10,
        choices=[
            ('small', 'صغير'),
            ('medium', 'متوسط'),
            ('large', 'كبير'),
        ],
        default='medium',
        verbose_name='حجم الخط'
    )
    language = models.CharField(
        max_length=5,
        choices=[
            ('ar', 'العربية'),
            ('en', 'English'),
        ],
        default='ar',
        verbose_name='اللغة'
    )
    timezone = models.CharField(
        max_length=50,
        choices=[
            ('Africa/Cairo', 'القاهرة (GMT+2)'),
            ('Asia/Riyadh', 'الرياض (GMT+3)'),
            ('Asia/Dubai', 'دبي (GMT+4)'),
        ],
        default='Africa/Cairo',
        verbose_name='المنطقة الزمنية'
    )

    # إعدادات الأمان
    two_factor = models.BooleanField(default=False, verbose_name='المصادقة الثنائية')
    login_alerts = models.BooleanField(default=True, verbose_name='تنبيهات تسجيل الدخول')
    session_timeout = models.BooleanField(default=False, verbose_name='انتهاء الجلسة التلقائي')

    # معلومات إضافية
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'إعدادات المستخدم'
        verbose_name_plural = 'إعدادات المستخدمين'

    def __str__(self):
        return f'إعدادات {self.user.username}'

    @classmethod
    def get_or_create_for_user(cls, user):
        """الحصول على إعدادات المستخدم أو إنشاؤها إذا لم تكن موجودة"""
        settings, created = cls.objects.get_or_create(user=user)
        return settings
