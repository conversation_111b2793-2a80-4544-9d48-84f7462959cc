from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal
from datetime import datetime, date

# ===== نماذج المناديب والسيارات =====

class SalesRepresentative(models.Model):
    """نموذج المناديب"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    employee_id = models.CharField(max_length=20, unique=True, verbose_name="رقم الموظف")
    phone = models.CharField(max_length=20, verbose_name="رقم الهاتف")
    address = models.TextField(blank=True, null=True, verbose_name="العنوان")
    hire_date = models.DateField(verbose_name="تاريخ التوظيف")
    commission_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة العمولة")
    target_monthly = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="الهدف الشهري")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مندوب مبيعات"
        verbose_name_plural = "مناديب المبيعات"
        ordering = ['user__first_name', 'user__last_name']

    def __str__(self):
        return f"{self.user.get_full_name()} ({self.employee_id})"

    @property
    def full_name(self):
        return self.user.get_full_name()

class Vehicle(models.Model):
    """نموذج السيارات"""
    VEHICLE_TYPES = [
        ('truck', 'شاحنة'),
        ('van', 'فان'),
        ('pickup', 'بيك أب'),
        ('car', 'سيارة'),
    ]

    plate_number = models.CharField(max_length=20, unique=True, verbose_name="رقم اللوحة")
    vehicle_type = models.CharField(max_length=20, choices=VEHICLE_TYPES, verbose_name="نوع السيارة")
    brand = models.CharField(max_length=50, verbose_name="الماركة")
    model = models.CharField(max_length=50, verbose_name="الموديل")
    year = models.IntegerField(verbose_name="سنة الصنع")
    capacity = models.DecimalField(max_digits=8, decimal_places=2, verbose_name="السعة (كيلو)")
    assigned_representative = models.ForeignKey(
        SalesRepresentative,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="المندوب المخصص"
    )
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "سيارة"
        verbose_name_plural = "السيارات"
        ordering = ['plate_number']

    def __str__(self):
        return f"{self.plate_number} - {self.brand} {self.model}"

# ===== نماذج العملاء المحدثة =====

class Customer(models.Model):
    """نموذج العملاء"""
    CUSTOMER_TYPES = [
        ('wholesale', 'جملة'),
        ('retail', 'تجزئة'),
        ('credit', 'أجل'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم العميل")
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPES, default='retail', verbose_name="نوع العميل")
    email = models.EmailField(blank=True, null=True, verbose_name="البريد الإلكتروني")
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    address = models.TextField(blank=True, null=True, verbose_name="العنوان")
    tax_number = models.CharField(max_length=50, blank=True, null=True, verbose_name="الرقم الضريبي")
    credit_limit = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="حد الائتمان")
    credit_days = models.IntegerField(default=30, verbose_name="أيام الائتمان")
    assigned_representative = models.ForeignKey(
        SalesRepresentative,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="المندوب المخصص"
    )
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "عميل"
        verbose_name_plural = "العملاء"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_customer_type_display()})"

    current_balance = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="الرصيد الحالي")

    def calculate_balance(self):
        """حساب الرصيد الحالي للعميل"""
        from django.db.models import Sum
        invoices_total = self.salesinvoice_set.filter(status__in=['sent', 'overdue']).aggregate(
            total=Sum('total_amount'))['total'] or 0
        payments_total = self.payments.aggregate(total=Sum('amount'))['total'] or 0
        self.current_balance = invoices_total - payments_total
        self.save()
        return self.current_balance

# ===== نماذج المنتجات المحدثة =====

class Product(models.Model):
    """نموذج المنتجات"""
    name = models.CharField(max_length=200, verbose_name="اسم المنتج")
    code = models.CharField(max_length=50, unique=True, verbose_name="كود المنتج")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    unit_price_wholesale = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الجملة")
    unit_price_retail = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر التجزئة")
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر التكلفة")
    stock_quantity = models.IntegerField(default=0, verbose_name="الكمية المتاحة")
    min_stock_level = models.IntegerField(default=10, verbose_name="الحد الأدنى للمخزون")
    max_stock_level = models.IntegerField(default=1000, verbose_name="الحد الأقصى للمخزون")
    unit = models.CharField(max_length=20, default="قطعة", verbose_name="الوحدة")
    barcode = models.CharField(max_length=50, blank=True, null=True, verbose_name="الباركود")
    category = models.CharField(max_length=100, blank=True, null=True, verbose_name="الفئة")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "منتج"
        verbose_name_plural = "المنتجات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    def get_price_for_customer_type(self, customer_type):
        """الحصول على السعر حسب نوع العميل"""
        if customer_type == 'wholesale':
            return self.unit_price_wholesale
        return self.unit_price_retail

# ===== نماذج تحميل السيارات =====

class VehicleLoading(models.Model):
    """نموذج تحميل السيارات"""
    loading_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التحميل")
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, verbose_name="السيارة")
    representative = models.ForeignKey(SalesRepresentative, on_delete=models.CASCADE, verbose_name="المندوب")
    loading_date = models.DateField(verbose_name="تاريخ التحميل")
    warehouse = models.CharField(max_length=100, verbose_name="المخزن")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    total_weight = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الوزن الإجمالي")
    total_value = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="القيمة الإجمالية")
    status = models.CharField(max_length=20, choices=[
        ('pending', 'في الانتظار'),
        ('loaded', 'تم التحميل'),
        ('delivered', 'تم التسليم'),
        ('returned', 'مرتجع'),
    ], default='pending', verbose_name="الحالة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تحميل سيارة"
        verbose_name_plural = "تحميل السيارات"
        ordering = ['-created_at']

    def __str__(self):
        return f"تحميل {self.loading_number} - {self.vehicle.plate_number}"

    def save(self, *args, **kwargs):
        if not self.loading_number:
            self.loading_number = f"VL-{datetime.now().strftime('%Y%m%d')}-{VehicleLoading.objects.count() + 1:04d}"
        super().save(*args, **kwargs)

class VehicleLoadingItem(models.Model):
    """نموذج عناصر تحميل السيارة"""
    loading = models.ForeignKey(VehicleLoading, related_name='items', on_delete=models.CASCADE, verbose_name="التحميل")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    total_weight = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الوزن الإجمالي")

    class Meta:
        verbose_name = "عنصر تحميل"
        verbose_name_plural = "عناصر التحميل"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_value(self):
        return self.quantity * self.unit_price

# ===== نماذج الحركة اليومية للمناديب =====

class DailyMovement(models.Model):
    """نموذج الحركة اليومية للمندوب"""
    representative = models.ForeignKey(SalesRepresentative, on_delete=models.CASCADE, verbose_name="المندوب")
    movement_date = models.DateField(verbose_name="تاريخ الحركة")
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, verbose_name="السيارة")
    opening_cash = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="رصيد افتتاحي")
    total_sales = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="إجمالي المبيعات")
    total_collections = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="إجمالي التحصيلات")
    total_returns = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="إجمالي المرتجعات")
    expenses = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="المصروفات")
    closing_cash = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="رصيد ختامي")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    is_closed = models.BooleanField(default=False, verbose_name="مغلقة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "حركة يومية"
        verbose_name_plural = "الحركات اليومية"
        ordering = ['-movement_date']
        unique_together = ['representative', 'movement_date']

    def __str__(self):
        return f"حركة {self.representative.full_name} - {self.movement_date}"

    @property
    def net_movement(self):
        """صافي الحركة اليومية"""
        return self.opening_cash + self.total_sales + self.total_collections - self.total_returns - self.expenses

# ===== نماذج المرتجعات =====

class SalesReturn(models.Model):
    """نموذج مرتجعات المبيعات"""
    RETURN_REASONS = [
        ('damaged', 'تالف'),
        ('expired', 'منتهي الصلاحية'),
        ('wrong_item', 'صنف خاطئ'),
        ('customer_request', 'طلب العميل'),
        ('quality_issue', 'مشكلة في الجودة'),
        ('other', 'أخرى'),
    ]

    return_number = models.CharField(max_length=50, unique=True, verbose_name="رقم المرتجع")
    original_invoice = models.ForeignKey('SalesInvoice', on_delete=models.CASCADE, verbose_name="الفاتورة الأصلية")
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name="العميل")
    representative = models.ForeignKey(SalesRepresentative, on_delete=models.CASCADE, verbose_name="المندوب")
    return_date = models.DateField(verbose_name="تاريخ المرتجع")
    reason = models.CharField(max_length=20, choices=RETURN_REASONS, verbose_name="سبب المرتجع")
    reason_details = models.TextField(blank=True, null=True, verbose_name="تفاصيل السبب")
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="إجمالي المبلغ")
    is_approved = models.BooleanField(default=False, verbose_name="معتمد")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="اعتمد بواسطة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_returns', verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مرتجع مبيعات"
        verbose_name_plural = "مرتجعات المبيعات"
        ordering = ['-created_at']

    def __str__(self):
        return f"مرتجع {self.return_number} - {self.customer.name}"

    def save(self, *args, **kwargs):
        if not self.return_number:
            self.return_number = f"SR-{datetime.now().strftime('%Y%m%d')}-{SalesReturn.objects.count() + 1:04d}"
        super().save(*args, **kwargs)

class SalesReturnItem(models.Model):
    """نموذج عناصر مرتجع المبيعات"""
    return_record = models.ForeignKey(SalesReturn, related_name='items', on_delete=models.CASCADE, verbose_name="المرتجع")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية المرتجعة")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    condition = models.CharField(max_length=20, choices=[
        ('good', 'جيد'),
        ('damaged', 'تالف'),
        ('expired', 'منتهي الصلاحية'),
    ], default='good', verbose_name="حالة المنتج")

    class Meta:
        verbose_name = "عنصر مرتجع"
        verbose_name_plural = "عناصر المرتجعات"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_amount(self):
        return self.quantity * self.unit_price

# ===== نماذج أوامر البيع المحدثة =====

class SalesOrder(models.Model):
    """نموذج أوامر البيع"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('confirmed', 'مؤكد'),
        ('shipped', 'تم الشحن'),
        ('delivered', 'تم التسليم'),
        ('cancelled', 'ملغي'),
    ]

    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الطلب")
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name="العميل")
    representative = models.ForeignKey(SalesRepresentative, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المندوب")
    order_date = models.DateField(verbose_name="تاريخ الطلب")
    delivery_date = models.DateField(blank=True, null=True, verbose_name="تاريخ التسليم المتوقع")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=15, verbose_name="نسبة الضريبة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "أمر بيع"
        verbose_name_plural = "أوامر البيع"
        ordering = ['-created_at']

    def __str__(self):
        return f"أمر بيع {self.order_number} - {self.customer.name}"

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def discount_amount(self):
        return self.subtotal * (self.discount_percentage / 100)

    @property
    def tax_amount(self):
        return (self.subtotal - self.discount_amount) * (self.tax_percentage / 100)

    @property
    def total_amount(self):
        return self.subtotal - self.discount_amount + self.tax_amount

class SalesOrderItem(models.Model):
    """نموذج عناصر أمر البيع"""
    order = models.ForeignKey(SalesOrder, related_name='items', on_delete=models.CASCADE, verbose_name="أمر البيع")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")

    class Meta:
        verbose_name = "عنصر أمر بيع"
        verbose_name_plural = "عناصر أوامر البيع"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_price(self):
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100)
        return subtotal - discount

# ===== نماذج الفواتير المحدثة =====

class SalesInvoice(models.Model):
    """نموذج فواتير البيع"""
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('sent', 'مرسلة'),
        ('paid', 'مدفوعة'),
        ('overdue', 'متأخرة'),
        ('cancelled', 'ملغية'),
    ]

    INVOICE_TYPES = [
        ('wholesale', 'جملة'),
        ('retail', 'تجزئة'),
        ('credit', 'أجل'),
    ]

    invoice_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الفاتورة")
    invoice_type = models.CharField(max_length=20, choices=INVOICE_TYPES, default='retail', verbose_name="نوع الفاتورة")
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name="العميل")
    representative = models.ForeignKey(SalesRepresentative, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المندوب")
    order = models.ForeignKey(SalesOrder, blank=True, null=True, on_delete=models.SET_NULL, verbose_name="أمر البيع")
    vehicle = models.ForeignKey(Vehicle, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="السيارة")
    invoice_date = models.DateField(verbose_name="تاريخ الفاتورة")
    due_date = models.DateField(verbose_name="تاريخ الاستحقاق")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="الحالة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")
    discount_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="مبلغ الخصم")
    tax_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=15, verbose_name="نسبة الضريبة")
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="إجمالي المبلغ")
    payment_method = models.CharField(max_length=20, choices=[
        ('cash', 'نقدي'),
        ('credit', 'آجل'),
        ('card', 'بطاقة'),
        ('transfer', 'تحويل'),
    ], default='cash', verbose_name="طريقة الدفع")
    is_printed = models.BooleanField(default=False, verbose_name="تم الطباعة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "فاتورة بيع"
        verbose_name_plural = "فواتير البيع"
        ordering = ['-created_at']

    def __str__(self):
        return f"فاتورة {self.invoice_number} - {self.customer.name}"

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            prefix = {
                'wholesale': 'WS',
                'retail': 'RT',
                'credit': 'CR'
            }.get(self.invoice_type, 'INV')
            self.invoice_number = f"{prefix}-{datetime.now().strftime('%Y%m%d')}-{SalesInvoice.objects.count() + 1:04d}"
        super().save(*args, **kwargs)

    @property
    def subtotal(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def calculated_discount_amount(self):
        if self.discount_amount > 0:
            return self.discount_amount
        return self.subtotal * (self.discount_percentage / 100)

    @property
    def tax_amount(self):
        return (self.subtotal - self.calculated_discount_amount) * (self.tax_percentage / 100)

    @property
    def calculated_total_amount(self):
        return self.subtotal - self.calculated_discount_amount + self.tax_amount

    def calculate_totals(self):
        """حساب وحفظ الإجماليات"""
        self.total_amount = self.calculated_total_amount
        self.save()

    @property
    def paid_amount(self):
        """إجمالي المبلغ المدفوع"""
        # للبساطة، نعيد 0 حالياً
        # يمكن تطوير هذا لاحقاً لربط المدفوعات بالفواتير
        return 0

    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return max(0, self.total_amount - self.paid_amount)

# ===== نماذج أذونات الصرف =====

class DispensePermission(models.Model):
    """نموذج أذونات الصرف"""
    permission_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الإذن")
    warehouse = models.CharField(max_length=100, verbose_name="المخزن")
    representative = models.ForeignKey(SalesRepresentative, on_delete=models.CASCADE, verbose_name="المندوب")
    vehicle = models.ForeignKey(Vehicle, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="السيارة")
    dispense_date = models.DateField(verbose_name="تاريخ الصرف")
    purpose = models.CharField(max_length=200, verbose_name="الغرض من الصرف")
    total_value = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="القيمة الإجمالية")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    is_approved = models.BooleanField(default=False, verbose_name="معتمد")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="اعتمد بواسطة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_dispenses', verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "إذن صرف"
        verbose_name_plural = "أذونات الصرف"
        ordering = ['-created_at']

    def __str__(self):
        return f"إذن صرف {self.permission_number} - {self.representative.full_name}"

    def save(self, *args, **kwargs):
        if not self.permission_number:
            self.permission_number = f"DP-{datetime.now().strftime('%Y%m%d')}-{DispensePermission.objects.count() + 1:04d}"
        super().save(*args, **kwargs)

class DispensePermissionItem(models.Model):
    """نموذج عناصر إذن الصرف"""
    permission = models.ForeignKey(DispensePermission, related_name='items', on_delete=models.CASCADE, verbose_name="إذن الصرف")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="تكلفة الوحدة")

    class Meta:
        verbose_name = "عنصر إذن صرف"
        verbose_name_plural = "عناصر أذونات الصرف"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_cost(self):
        return self.quantity * self.unit_cost

# ===== نماذج الجرد =====

class Inventory(models.Model):
    """نموذج الجرد"""
    INVENTORY_TYPES = [
        ('daily', 'يومي'),
        ('monthly', 'شهري'),
        ('annual', 'سنوي'),
    ]

    inventory_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الجرد")
    inventory_type = models.CharField(max_length=20, choices=INVENTORY_TYPES, verbose_name="نوع الجرد")
    inventory_date = models.DateField(verbose_name="تاريخ الجرد")
    warehouse = models.CharField(max_length=100, blank=True, null=True, verbose_name="المخزن")
    vehicle = models.ForeignKey(Vehicle, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="السيارة")
    representative = models.ForeignKey(SalesRepresentative, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المندوب")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    total_value = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="القيمة الإجمالية")
    is_approved = models.BooleanField(default=False, verbose_name="معتمد")
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="اعتمد بواسطة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_inventories', verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "جرد"
        verbose_name_plural = "الجرد"
        ordering = ['-created_at']

    def __str__(self):
        return f"جرد {self.inventory_number} - {self.inventory_date}"

    def save(self, *args, **kwargs):
        if not self.inventory_number:
            prefix = {
                'daily': 'DI',
                'monthly': 'MI',
                'annual': 'AI'
            }.get(self.inventory_type, 'INV')
            self.inventory_number = f"{prefix}-{datetime.now().strftime('%Y%m%d')}-{Inventory.objects.count() + 1:04d}"
        super().save(*args, **kwargs)

class InventoryItem(models.Model):
    """نموذج عناصر الجرد"""
    inventory = models.ForeignKey(Inventory, related_name='items', on_delete=models.CASCADE, verbose_name="الجرد")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    system_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الكمية النظامية")
    actual_quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الكمية الفعلية")
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="تكلفة الوحدة")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "عنصر جرد"
        verbose_name_plural = "عناصر الجرد"

    def __str__(self):
        return f"{self.product.name} - {self.actual_quantity} {self.product.unit}"

    @property
    def variance_quantity(self):
        """فرق الكمية"""
        return self.actual_quantity - self.system_quantity

    @property
    def variance_value(self):
        """فرق القيمة"""
        return self.variance_quantity * self.unit_cost

    @property
    def total_value(self):
        return self.actual_quantity * self.unit_cost

# ===== نماذج حركة الأصناف =====

class ProductMovement(models.Model):
    """نموذج حركة الأصناف"""
    MOVEMENT_TYPES = [
        ('sale', 'بيع'),
        ('loading', 'تحميل'),
        ('dispense', 'صرف'),
        ('return', 'مرتجع'),
        ('adjustment', 'تسوية'),
    ]

    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPES, verbose_name="نوع الحركة")
    movement_date = models.DateField(verbose_name="تاريخ الحركة")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    reference_number = models.CharField(max_length=50, verbose_name="رقم المرجع")
    reference_type = models.CharField(max_length=50, verbose_name="نوع المرجع")
    warehouse = models.CharField(max_length=100, blank=True, null=True, verbose_name="المخزن")
    representative = models.ForeignKey(SalesRepresentative, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المندوب")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "حركة صنف"
        verbose_name_plural = "حركة الأصناف"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.product.name} - {self.get_movement_type_display()} - {self.quantity}"

    @property
    def total_value(self):
        return self.quantity * self.unit_price

# ===== نماذج المدفوعات =====

class Payment(models.Model):
    """نموذج المدفوعات"""
    PAYMENT_TYPES = [
        ('collection', 'تحصيل'),
        ('payment', 'دفع'),
    ]

    PAYMENT_METHODS = [
        ('cash', 'نقدي'),
        ('card', 'بطاقة'),
        ('transfer', 'تحويل'),
        ('check', 'شيك'),
    ]

    payment_number = models.CharField(max_length=50, unique=True, verbose_name="رقم المدفوعة")
    payment_type = models.CharField(max_length=20, choices=PAYMENT_TYPES, verbose_name="نوع المدفوعة")
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='payments', verbose_name="العميل")
    representative = models.ForeignKey(SalesRepresentative, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المندوب")
    amount = models.DecimalField(max_digits=12, decimal_places=2, verbose_name="المبلغ")
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHODS, verbose_name="طريقة الدفع")
    payment_date = models.DateField(verbose_name="تاريخ المدفوعة")
    reference_number = models.CharField(max_length=50, blank=True, null=True, verbose_name="رقم المرجع")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مدفوعة"
        verbose_name_plural = "المدفوعات"
        ordering = ['-created_at']

    def __str__(self):
        return f"مدفوعة {self.payment_number} - {self.customer.name} - {self.amount}"

    def save(self, *args, **kwargs):
        if not self.payment_number:
            prefix = 'COL' if self.payment_type == 'collection' else 'PAY'
            self.payment_number = f"{prefix}-{datetime.now().strftime('%Y%m%d')}-{Payment.objects.count() + 1:04d}"
        super().save(*args, **kwargs)

# ===== نماذج عناصر الفواتير والطلبات =====

class SalesOrderItem(models.Model):
    """نموذج عناصر أمر البيع"""
    order = models.ForeignKey(SalesOrder, related_name='items', on_delete=models.CASCADE, verbose_name="أمر البيع")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")

    class Meta:
        verbose_name = "عنصر أمر بيع"
        verbose_name_plural = "عناصر أوامر البيع"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_price(self):
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100)
        return subtotal - discount

class SalesInvoiceItem(models.Model):
    """نموذج عناصر فاتورة البيع"""
    invoice = models.ForeignKey(SalesInvoice, related_name='items', on_delete=models.CASCADE, verbose_name="فاتورة البيع")
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name="المنتج")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="الكمية")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="سعر الوحدة")
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, verbose_name="نسبة الخصم")

    class Meta:
        verbose_name = "عنصر فاتورة بيع"
        verbose_name_plural = "عناصر فواتير البيع"

    def __str__(self):
        return f"{self.product.name} - {self.quantity} {self.product.unit}"

    @property
    def total_price(self):
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100)
        return subtotal - discount


# ========== نماذج نظام الصلاحيات ==========

class Role(models.Model):
    """نموذج الأدوار"""
    ROLE_TYPES = [
        ('admin', 'مدير عام'),
        ('manager', 'مدير'),
        ('supervisor', 'مشرف'),
        ('representative', 'مندوب'),
        ('accountant', 'محاسب'),
        ('warehouse_keeper', 'أمين مخزن'),
    ]

    name = models.CharField(max_length=100, verbose_name="اسم الدور")
    role_type = models.CharField(max_length=20, choices=ROLE_TYPES, verbose_name="نوع الدور")
    description = models.TextField(blank=True, verbose_name="وصف الدور")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "دور"
        verbose_name_plural = "الأدوار"


class Permission(models.Model):
    """نموذج الصلاحيات"""
    PERMISSION_TYPES = [
        ('view', 'عرض'),
        ('add', 'إضافة'),
        ('change', 'تعديل'),
        ('delete', 'حذف'),
        ('approve', 'اعتماد'),
        ('print', 'طباعة'),
        ('export', 'تصدير'),
    ]

    MODULE_CHOICES = [
        ('customers', 'العملاء'),
        ('products', 'المنتجات'),
        ('sales', 'المبيعات'),
        ('invoices', 'الفواتير'),
        ('orders', 'الطلبات'),
        ('returns', 'المرتجعات'),
        ('payments', 'المدفوعات'),
        ('inventory', 'المخزون'),
        ('reports', 'التقارير'),
        ('settings', 'الإعدادات'),
        ('users', 'المستخدمين'),
        ('vehicles', 'السيارات'),
        ('representatives', 'المناديب'),
    ]

    name = models.CharField(max_length=100, verbose_name="اسم الصلاحية")
    codename = models.CharField(max_length=100, unique=True, verbose_name="الكود")
    permission_type = models.CharField(max_length=20, choices=PERMISSION_TYPES, verbose_name="نوع الصلاحية")
    module = models.CharField(max_length=50, choices=MODULE_CHOICES, verbose_name="الوحدة")
    description = models.TextField(blank=True, verbose_name="وصف الصلاحية")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    def __str__(self):
        return f"{self.name} ({self.get_module_display()})"

    class Meta:
        verbose_name = "صلاحية"
        verbose_name_plural = "الصلاحيات"
        unique_together = ['permission_type', 'module']


class UserRole(models.Model):
    """نموذج ربط المستخدمين بالأدوار"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    role = models.ForeignKey(Role, on_delete=models.CASCADE, verbose_name="الدور")
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='assigned_roles', verbose_name="عُين بواسطة")
    assigned_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التعيين")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - {self.role.name}"

    class Meta:
        verbose_name = "دور المستخدم"
        verbose_name_plural = "أدوار المستخدمين"
        unique_together = ['user', 'role']


class AccessLog(models.Model):
    """نموذج سجل الوصول"""
    ACTION_TYPES = [
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('view', 'عرض'),
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('approve', 'اعتماد'),
        ('print', 'طباعة'),
        ('export', 'تصدير'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    action = models.CharField(max_length=20, choices=ACTION_TYPES, verbose_name="الإجراء")
    module = models.CharField(max_length=50, verbose_name="الوحدة")
    object_id = models.CharField(max_length=100, blank=True, verbose_name="معرف الكائن")
    object_name = models.CharField(max_length=200, blank=True, verbose_name="اسم الكائن")
    ip_address = models.GenericIPAddressField(verbose_name="عنوان IP")
    user_agent = models.TextField(blank=True, verbose_name="معلومات المتصفح")
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name="الوقت")
    success = models.BooleanField(default=True, verbose_name="نجح")
    error_message = models.TextField(blank=True, verbose_name="رسالة الخطأ")

    def __str__(self):
        return f"{self.user.username} - {self.action} - {self.module} - {self.timestamp}"

    class Meta:
        verbose_name = "سجل الوصول"
        verbose_name_plural = "سجلات الوصول"
        ordering = ['-timestamp']
