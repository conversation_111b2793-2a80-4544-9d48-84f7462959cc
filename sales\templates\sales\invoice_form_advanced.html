{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .invoice-form {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin: 20px 0;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: #f8f9fa;
    }
    
    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #007bff;
    }
    
    .items-table {
        margin-top: 20px;
    }
    
    .btn-add-item {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        transition: all 0.3s;
    }
    
    .btn-add-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }
    
    .total-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-top: 20px;
    }
    
    .customer-info {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-top: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="invoice-form">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="text-primary">
                        <i class="bi bi-receipt-cutoff"></i>
                        {{ title }}
                    </h2>
                    <a href="{% url 'sales:invoice_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>

                <form method="post" id="invoice-form">
                    {% csrf_token %}
                    
                    <!-- معلومات الفاتورة الأساسية -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            معلومات الفاتورة
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">نوع الفاتورة</label>
                                    {{ form.invoice_type }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الفاتورة</label>
                                    {{ form.invoice_date }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    {{ form.due_date }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">العميل</label>
                                    {{ form.customer }}
                                    <div id="customer-info" class="customer-info" style="display: none;">
                                        <small class="text-muted">معلومات العميل ستظهر هنا</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">المندوب</label>
                                    {{ form.representative }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">السيارة</label>
                                    {{ form.vehicle }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">طريقة الدفع</label>
                                    {{ form.payment_method }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">حالة الفاتورة</label>
                                    {{ form.status }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">أمر البيع (اختياري)</label>
                                    {{ form.order }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- عناصر الفاتورة -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-list-ul"></i>
                            عناصر الفاتورة
                        </h4>
                        
                        {{ formset.management_form }}
                        
                        <div class="table-responsive items-table">
                            <table class="table table-bordered" id="items-table">
                                <thead class="table-primary">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>سعر الوحدة</th>
                                        <th>نسبة الخصم %</th>
                                        <th>الإجمالي</th>
                                        <th>حذف</th>
                                    </tr>
                                </thead>
                                <tbody id="items-tbody">
                                    {% for form in formset %}
                                        <tr class="item-row">
                                            <td>
                                                {{ form.product }}
                                                {{ form.id }}
                                            </td>
                                            <td>{{ form.quantity }}</td>
                                            <td>{{ form.unit_price }}</td>
                                            <td>{{ form.discount_percentage }}</td>
                                            <td class="item-total">0.00</td>
                                            <td>
                                                {% if not forloop.first %}
                                                    {{ form.DELETE }}
                                                    <label for="{{ form.DELETE.id_for_label }}" class="btn btn-sm btn-outline-danger">
                                                        <i class="bi bi-trash"></i>
                                                    </label>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <button type="button" class="btn btn-add-item" id="add-item">
                            <i class="bi bi-plus-circle"></i>
                            إضافة عنصر جديد
                        </button>
                    </div>

                    <!-- الخصومات والضرائب -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-calculator"></i>
                            الخصومات والضرائب
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">نسبة الخصم %</label>
                                    {{ form.discount_percentage }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">مبلغ الخصم</label>
                                    {{ form.discount_amount }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">نسبة الضريبة %</label>
                                    {{ form.tax_percentage }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            {{ form.notes }}
                        </div>
                    </div>

                    <!-- إجمالي الفاتورة -->
                    <div class="total-section">
                        <div class="row">
                            <div class="col-md-8">
                                <h5>ملخص الفاتورة</h5>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex justify-content-between">
                                    <span>المجموع الفرعي:</span>
                                    <span id="subtotal">0.00</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>الخصم:</span>
                                    <span id="discount">0.00</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>الضريبة:</span>
                                    <span id="tax">0.00</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>الإجمالي النهائي:</strong>
                                    <strong id="total">0.00</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-save"></i>
                            حفظ الفاتورة
                        </button>
                        <a href="{% url 'sales:invoice_list' %}" class="btn btn-secondary btn-lg ms-2">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث معلومات العميل عند التغيير
    $('#id_customer').change(function() {
        var customerId = $(this).val();
        if (customerId) {
            $.get('/sales/api/customer/' + customerId + '/', function(data) {
                var info = `
                    <strong>نوع العميل:</strong> ${data.customer_type}<br>
                    <strong>حد الائتمان:</strong> ${data.credit_limit}<br>
                    <strong>الرصيد الحالي:</strong> ${data.current_balance}
                `;
                $('#customer-info').html(info).show();
                
                // تحديث نوع الفاتورة
                $('#id_invoice_type').val(data.customer_type);
                
                // تحديث المندوب المخصص
                if (data.assigned_representative) {
                    $('#id_representative').val(data.assigned_representative);
                }
            });
        } else {
            $('#customer-info').hide();
        }
    });

    // تحديث سعر المنتج عند التغيير
    $(document).on('change', '[id*="product"]', function() {
        var productId = $(this).val();
        var customerType = $('#id_invoice_type').val() || 'retail';
        var row = $(this).closest('tr');
        
        if (productId) {
            $.get('/sales/api/product-price/' + productId + '/', {customer_type: customerType}, function(data) {
                row.find('[id*="unit_price"]').val(data.unit_price);
                calculateRowTotal(row);
            });
        }
    });

    // حساب إجمالي الصف
    function calculateRowTotal(row) {
        var quantity = parseFloat(row.find('[id*="quantity"]').val()) || 0;
        var unitPrice = parseFloat(row.find('[id*="unit_price"]').val()) || 0;
        var discountPercent = parseFloat(row.find('[id*="discount_percentage"]').val()) || 0;
        
        var subtotal = quantity * unitPrice;
        var discount = subtotal * (discountPercent / 100);
        var total = subtotal - discount;
        
        row.find('.item-total').text(total.toFixed(2));
        calculateInvoiceTotal();
    }

    // حساب إجمالي الفاتورة
    function calculateInvoiceTotal() {
        var subtotal = 0;
        $('.item-total').each(function() {
            subtotal += parseFloat($(this).text()) || 0;
        });
        
        var discountPercent = parseFloat($('#id_discount_percentage').val()) || 0;
        var discountAmount = parseFloat($('#id_discount_amount').val()) || 0;
        var taxPercent = parseFloat($('#id_tax_percentage').val()) || 0;
        
        var discount = discountAmount > 0 ? discountAmount : (subtotal * discountPercent / 100);
        var taxableAmount = subtotal - discount;
        var tax = taxableAmount * (taxPercent / 100);
        var total = subtotal - discount + tax;
        
        $('#subtotal').text(subtotal.toFixed(2));
        $('#discount').text(discount.toFixed(2));
        $('#tax').text(tax.toFixed(2));
        $('#total').text(total.toFixed(2));
    }

    // تحديث الحسابات عند تغيير القيم
    $(document).on('input', '[id*="quantity"], [id*="unit_price"], [id*="discount_percentage"]', function() {
        calculateRowTotal($(this).closest('tr'));
    });

    $(document).on('input', '#id_discount_percentage, #id_discount_amount, #id_tax_percentage', function() {
        calculateInvoiceTotal();
    });

    // إضافة صف جديد
    $('#add-item').click(function() {
        var totalForms = $('#id_form-TOTAL_FORMS');
        var formNum = parseInt(totalForms.val());
        
        var newRow = $('.item-row:first').clone();
        newRow.find('input, select').each(function() {
            var name = $(this).attr('name');
            if (name) {
                name = name.replace('-0-', '-' + formNum + '-');
                $(this).attr('name', name);
                $(this).attr('id', 'id_' + name);
                $(this).val('');
            }
        });
        
        newRow.find('.item-total').text('0.00');
        $('#items-tbody').append(newRow);
        
        totalForms.val(formNum + 1);
    });

    // حساب أولي
    calculateInvoiceTotal();
});
</script>
{% endblock %}
