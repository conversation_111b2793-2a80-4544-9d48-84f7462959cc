# Generated by Django 5.2.4 on 2025-07-13 14:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0004_alter_productlocation_unique_together_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code_type', models.CharField(choices=[('supplier', 'كود المورد'), ('manufacturer', 'كود الشركة المصنعة'), ('customer', 'كود العميل'), ('internal', 'كود داخلي'), ('old_system', 'كود النظام القديم'), ('barcode', 'باركود إضافي'), ('sku', 'رقم تعريف المنتج')], max_length=20, verbose_name='نوع الكود')),
                ('code', models.CharField(max_length=100, verbose_name='الكود')),
                ('description', models.CharField(blank=True, max_length=200, verbose_name='الوصف')),
                ('supplier_name', models.CharField(blank=True, max_length=100, verbose_name='اسم المورد/الجهة')),
                ('reference_number', models.CharField(blank=True, max_length=50, verbose_name='رقم المرجع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_primary', models.BooleanField(default=False, verbose_name='كود أساسي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alternative_codes', to='definitions.productdefinition', verbose_name='الصنف')),
            ],
            options={
                'verbose_name': 'كود صنف بديل',
                'verbose_name_plural': 'أكواد الأصناف البديلة',
                'ordering': ['product__code', 'code_type', 'code'],
                'unique_together': {('code_type', 'code')},
            },
        ),
    ]
