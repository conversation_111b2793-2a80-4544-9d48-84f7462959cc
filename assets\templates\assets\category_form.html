{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 2rem auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .form-header h2 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 600;
    }
    
    .form-body {
        padding: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .required-field::after {
        content: " *";
        color: #ef4444;
    }
    
    .form-control, .form-select {
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.3s ease;
        font-size: 0.95rem;
        width: 100%;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }
    
    .form-check {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .form-check-input {
        width: 1.2rem;
        height: 1.2rem;
    }
    
    .form-check-label {
        font-weight: 500;
        color: #374151;
        margin: 0;
    }
    
    .help-text {
        font-size: 0.85rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }
    
    .error-message {
        color: #ef4444;
        font-size: 0.85rem;
        margin-top: 0.25rem;
    }
    
    .btn-container {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid #e5e7eb;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .btn-secondary {
        background: #6b7280;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        color: white;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }
    
    .info-box {
        background: #f0f9ff;
        border: 2px solid #0ea5e9;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .info-box h6 {
        color: #0369a1;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }
    
    .info-box p {
        color: #0891b2;
        margin: 0;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="form-container">
        <div class="form-header">
            <h2>
                <i class="bi bi-folder-plus"></i>
                {{ title }}
            </h2>
        </div>
        
        <div class="form-body">
            <div class="info-box">
                <h6>
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات مهمة
                </h6>
                <p>فئات الأصول تساعد في تنظيم وتصنيف الأصول الثابتة. يمكن تحديد معدل استهلاك افتراضي لكل فئة.</p>
            </div>

            <form method="post" novalidate>
                {% csrf_token %}
                
                <div class="form-group">
                    <label for="{{ form.name.id_for_label }}" class="form-label required-field">{{ form.name.label }}</label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <div class="error-message">{{ form.name.errors.0 }}</div>
                    {% endif %}
                    <div class="help-text">اسم واضح ومميز لفئة الأصول</div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="error-message">{{ form.description.errors.0 }}</div>
                    {% endif %}
                    <div class="help-text">وصف تفصيلي لنوع الأصول في هذه الفئة</div>
                </div>
                
                <div class="form-group">
                    <label for="{{ form.depreciation_rate.id_for_label }}" class="form-label">{{ form.depreciation_rate.label }}</label>
                    {{ form.depreciation_rate }}
                    {% if form.depreciation_rate.errors %}
                        <div class="error-message">{{ form.depreciation_rate.errors.0 }}</div>
                    {% endif %}
                    <div class="help-text">معدل الاستهلاك السنوي كنسبة مئوية (مثال: 20 تعني 20% سنوياً)</div>
                </div>
                
                <div class="form-group">
                    <div class="form-check">
                        {{ form.is_active }}
                        <label for="{{ form.is_active.id_for_label }}" class="form-check-label">{{ form.is_active.label }}</label>
                    </div>
                    {% if form.is_active.errors %}
                        <div class="error-message">{{ form.is_active.errors.0 }}</div>
                    {% endif %}
                    <div class="help-text">الفئات غير النشطة لن تظهر في قوائم الاختيار</div>
                </div>

                <div class="btn-container">
                    <button type="submit" class="btn-primary">
                        <i class="bi bi-check-circle me-2"></i>
                        {% if category %}تحديث الفئة{% else %}إضافة الفئة{% endif %}
                    </button>
                    <a href="{% url 'assets:category_list' %}" class="btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
    });
    
    // التحقق من صحة معدل الاستهلاك
    const depreciationField = document.getElementById('{{ form.depreciation_rate.id_for_label }}');
    if (depreciationField) {
        depreciationField.addEventListener('input', function() {
            const value = parseFloat(this.value);
            if (value < 0 || value > 100) {
                this.style.borderColor = '#ef4444';
                if (!this.nextElementSibling || !this.nextElementSibling.classList.contains('validation-warning')) {
                    const warning = document.createElement('div');
                    warning.className = 'validation-warning error-message';
                    warning.textContent = 'معدل الاستهلاك يجب أن يكون بين 0 و 100';
                    this.parentNode.insertBefore(warning, this.nextElementSibling);
                }
            } else {
                this.style.borderColor = '#e5e7eb';
                const warning = this.parentNode.querySelector('.validation-warning');
                if (warning) {
                    warning.remove();
                }
            }
        });
    }
    
    console.log('✅ تم تحميل نموذج فئات الأصول');
});
</script>
{% endblock %}
