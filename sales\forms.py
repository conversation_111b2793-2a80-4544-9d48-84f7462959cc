from django import forms
from django.forms import inlineformset_factory
from .models import (
    Customer, Product, SalesOrder, SalesOrderItem, SalesInvoice, SalesInvoiceItem,
    SalesRepresentative, Vehicle, VehicleLoading, VehicleLoadingItem,
    DailyMovement, SalesReturn, SalesReturnItem, DispensePermission, DispensePermissionItem,
    Inventory, InventoryItem, ProductMovement, Payment
)

# ===== نماذج العملاء =====

class CustomerForm(forms.ModelForm):
    """نموذج إضافة/تعديل العملاء"""
    class Meta:
        model = Customer
        fields = ['name', 'customer_type', 'email', 'phone', 'address', 'tax_number', 'credit_limit', 'credit_days', 'assigned_representative', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم العميل'}),
            'customer_type': forms.Select(attrs={'class': 'form-select'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'العنوان'}),
            'tax_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الرقم الضريبي'}),
            'credit_limit': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'credit_days': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'assigned_representative': forms.Select(attrs={'class': 'form-select'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

# ===== نماذج المنتجات =====

class ProductForm(forms.ModelForm):
    """نموذج إضافة/تعديل المنتجات"""
    class Meta:
        model = Product
        fields = ['name', 'code', 'description', 'unit_price_wholesale', 'unit_price_retail', 'cost_price',
                 'stock_quantity', 'min_stock_level', 'max_stock_level', 'unit', 'barcode', 'category', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المنتج'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود المنتج'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف المنتج'}),
            'unit_price_wholesale': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'unit_price_retail': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'cost_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'stock_quantity': forms.NumberInput(attrs={'class': 'form-control'}),
            'min_stock_level': forms.NumberInput(attrs={'class': 'form-control'}),
            'max_stock_level': forms.NumberInput(attrs={'class': 'form-control'}),
            'unit': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الوحدة'}),
            'barcode': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الباركود'}),
            'category': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الفئة'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

# ===== نماذج المناديب =====

class SalesRepresentativeForm(forms.ModelForm):
    """نموذج إضافة/تعديل المناديب"""
    class Meta:
        model = SalesRepresentative
        fields = ['user', 'employee_id', 'phone', 'address', 'hire_date', 'commission_rate', 'target_monthly', 'is_active']
        widgets = {
            'user': forms.Select(attrs={'class': 'form-select'}),
            'employee_id': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الموظف'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'العنوان'}),
            'hire_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'commission_rate': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'target_monthly': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

# ===== نماذج السيارات =====

class VehicleForm(forms.ModelForm):
    """نموذج إضافة/تعديل السيارات"""
    class Meta:
        model = Vehicle
        fields = ['plate_number', 'vehicle_type', 'brand', 'model', 'year', 'capacity', 'assigned_representative', 'is_active']
        widgets = {
            'plate_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم اللوحة'}),
            'vehicle_type': forms.Select(attrs={'class': 'form-select'}),
            'brand': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الماركة'}),
            'model': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الموديل'}),
            'year': forms.NumberInput(attrs={'class': 'form-control', 'min': '1990', 'max': '2030'}),
            'capacity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'assigned_representative': forms.Select(attrs={'class': 'form-select'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

# ===== نماذج أوامر البيع =====

class SalesOrderForm(forms.ModelForm):
    """نموذج إضافة/تعديل أوامر البيع"""
    class Meta:
        model = SalesOrder
        fields = ['customer', 'representative', 'order_date', 'delivery_date', 'status', 'notes', 'discount_percentage', 'tax_percentage']
        widgets = {
            'customer': forms.Select(attrs={'class': 'form-select'}),
            'representative': forms.Select(attrs={'class': 'form-select'}),
            'order_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'delivery_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'tax_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
        }

class SalesOrderItemForm(forms.ModelForm):
    """نموذج عناصر أمر البيع"""
    class Meta:
        model = SalesOrderItem
        fields = ['product', 'quantity', 'unit_price', 'discount_percentage']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
        }

# ===== نماذج الفواتير =====

class SalesInvoiceForm(forms.ModelForm):
    """نموذج إضافة/تعديل فواتير البيع"""
    class Meta:
        model = SalesInvoice
        fields = ['invoice_type', 'customer', 'representative', 'order', 'vehicle', 'invoice_date', 'due_date',
                 'status', 'notes', 'discount_percentage', 'discount_amount', 'tax_percentage', 'payment_method']
        widgets = {
            'invoice_type': forms.Select(attrs={'class': 'form-select'}),
            'customer': forms.Select(attrs={'class': 'form-select'}),
            'representative': forms.Select(attrs={'class': 'form-select'}),
            'order': forms.Select(attrs={'class': 'form-select'}),
            'vehicle': forms.Select(attrs={'class': 'form-select'}),
            'invoice_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'due_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'discount_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'tax_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'payment_method': forms.Select(attrs={'class': 'form-select'}),
        }

# ===== نماذج تحميل السيارات =====

class VehicleLoadingForm(forms.ModelForm):
    """نموذج تحميل السيارات"""
    class Meta:
        model = VehicleLoading
        fields = ['vehicle', 'representative', 'loading_date', 'warehouse', 'notes', 'status']
        widgets = {
            'vehicle': forms.Select(attrs={'class': 'form-select'}),
            'representative': forms.Select(attrs={'class': 'form-select'}),
            'loading_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'warehouse': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'المخزن'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

class VehicleLoadingItemForm(forms.ModelForm):
    """نموذج عناصر تحميل السيارة"""
    class Meta:
        model = VehicleLoadingItem
        fields = ['product', 'quantity', 'unit_price', 'total_weight']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'total_weight': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
        }

# ===== نماذج الحركة اليومية =====

class DailyMovementForm(forms.ModelForm):
    """نموذج الحركة اليومية"""
    class Meta:
        model = DailyMovement
        fields = ['representative', 'movement_date', 'vehicle', 'opening_cash', 'total_sales',
                 'total_collections', 'total_returns', 'expenses', 'closing_cash', 'notes']
        widgets = {
            'representative': forms.Select(attrs={'class': 'form-select'}),
            'movement_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'vehicle': forms.Select(attrs={'class': 'form-select'}),
            'opening_cash': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'total_sales': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'total_collections': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'total_returns': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'expenses': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'closing_cash': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

# ===== نماذج المرتجعات =====

class SalesReturnForm(forms.ModelForm):
    """نموذج مرتجعات المبيعات"""
    class Meta:
        model = SalesReturn
        fields = ['original_invoice', 'customer', 'representative', 'return_date', 'reason', 'reason_details']
        widgets = {
            'original_invoice': forms.Select(attrs={'class': 'form-select'}),
            'customer': forms.Select(attrs={'class': 'form-select'}),
            'representative': forms.Select(attrs={'class': 'form-select'}),
            'return_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'reason': forms.Select(attrs={'class': 'form-select'}),
            'reason_details': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'تفاصيل السبب'}),
        }

class SalesReturnItemForm(forms.ModelForm):
    """نموذج عناصر المرتجع"""
    class Meta:
        model = SalesReturnItem
        fields = ['product', 'quantity', 'unit_price', 'condition']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'condition': forms.Select(attrs={'class': 'form-select'}),
        }

class SalesInvoiceItemForm(forms.ModelForm):
    """نموذج عناصر فاتورة البيع"""
    class Meta:
        model = SalesInvoiceItem
        fields = ['product', 'quantity', 'unit_price', 'discount_percentage']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
        }

# ===== نماذج أذونات الصرف =====

class DispensePermissionForm(forms.ModelForm):
    """نموذج أذونات الصرف"""
    class Meta:
        model = DispensePermission
        fields = ['warehouse', 'representative', 'vehicle', 'dispense_date', 'purpose', 'notes']
        widgets = {
            'warehouse': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'المخزن'}),
            'representative': forms.Select(attrs={'class': 'form-select'}),
            'vehicle': forms.Select(attrs={'class': 'form-select'}),
            'dispense_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'purpose': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الغرض من الصرف'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

class DispensePermissionItemForm(forms.ModelForm):
    """نموذج عناصر إذن الصرف"""
    class Meta:
        model = DispensePermissionItem
        fields = ['product', 'quantity', 'unit_cost']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'unit_cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
        }

# ===== نماذج الجرد =====

class InventoryForm(forms.ModelForm):
    """نموذج الجرد"""
    class Meta:
        model = Inventory
        fields = ['inventory_type', 'inventory_date', 'warehouse', 'vehicle', 'representative', 'notes']
        widgets = {
            'inventory_type': forms.Select(attrs={'class': 'form-select'}),
            'inventory_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'warehouse': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'المخزن'}),
            'vehicle': forms.Select(attrs={'class': 'form-select'}),
            'representative': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

class InventoryItemForm(forms.ModelForm):
    """نموذج عناصر الجرد"""
    class Meta:
        model = InventoryItem
        fields = ['product', 'system_quantity', 'actual_quantity', 'unit_cost', 'notes']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'system_quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'actual_quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'unit_cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'ملاحظات'}),
        }

# ===== نماذج المدفوعات =====

class PaymentForm(forms.ModelForm):
    """نموذج المدفوعات"""
    class Meta:
        model = Payment
        fields = ['payment_type', 'customer', 'representative', 'amount', 'payment_method', 'payment_date', 'reference_number', 'notes']
        widgets = {
            'payment_type': forms.Select(attrs={'class': 'form-select'}),
            'customer': forms.Select(attrs={'class': 'form-select'}),
            'representative': forms.Select(attrs={'class': 'form-select'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'payment_method': forms.Select(attrs={'class': 'form-select'}),
            'payment_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم المرجع'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

# ===== إنشاء Formsets للعناصر =====

SalesOrderItemFormSet = inlineformset_factory(
    SalesOrder,
    SalesOrderItem,
    form=SalesOrderItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)

SalesInvoiceItemFormSet = inlineformset_factory(
    SalesInvoice,
    SalesInvoiceItem,
    form=SalesInvoiceItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)

VehicleLoadingItemFormSet = inlineformset_factory(
    VehicleLoading,
    VehicleLoadingItem,
    form=VehicleLoadingItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)

SalesReturnItemFormSet = inlineformset_factory(
    SalesReturn,
    SalesReturnItem,
    form=SalesReturnItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)

DispensePermissionItemFormSet = inlineformset_factory(
    DispensePermission,
    DispensePermissionItem,
    form=DispensePermissionItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)

InventoryItemFormSet = inlineformset_factory(
    Inventory,
    InventoryItem,
    form=InventoryItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)
