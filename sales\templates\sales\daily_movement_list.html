{% extends 'base.html' %}
{% load static %}

{% block title %}الحركة اليومية للمناديب{% endblock %}

{% block extra_css %}
<style>
    .movement-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .movement-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
    }
    
    .stats-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    .search-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    .stat-card {
        text-align: center;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 15px;
    }
    
    .stat-card.total {
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
    }
    
    .stat-card.today {
        background: linear-gradient(45deg, #28a745, #1e7e34);
        color: white;
    }
    
    .stat-card.open {
        background: linear-gradient(45deg, #ffc107, #e0a800);
        color: #212529;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .movement-header {
        background: linear-gradient(45deg, #17a2b8, #138496);
        color: white;
        padding: 20px;
    }
    
    .movement-date {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .movement-rep {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .movement-body {
        padding: 20px;
    }
    
    .movement-details {
        margin-bottom: 15px;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 5px 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .detail-value {
        font-weight: 600;
        color: #333;
    }
    
    .net-movement {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        margin-top: 15px;
    }
    
    .net-positive {
        background: #d4edda;
        color: #155724;
    }
    
    .net-negative {
        background: #f8d7da;
        color: #721c24;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-open {
        background: #ffc107;
        color: #212529;
    }
    
    .status-closed {
        background: #28a745;
        color: white;
    }
    
    .btn-create {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-calendar-check"></i>
                    الحركة اليومية للمناديب
                </h1>
                <p class="mb-0">متابعة وإدارة الحركة اليومية للمبيعات والتحصيلات</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'sales:daily_movement_create' %}" class="btn btn-create">
                    <i class="bi bi-plus-circle"></i>
                    حركة جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="stats-section">
        <div class="row">
            <div class="col-md-4">
                <div class="stat-card total">
                    <div class="stat-value">{{ total_movements }}</div>
                    <div class="stat-label">إجمالي الحركات</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card today">
                    <div class="stat-value">{{ today_movements }}</div>
                    <div class="stat-label">حركات اليوم</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card open">
                    <div class="stat-value">{{ open_movements }}</div>
                    <div class="stat-label">حركات مفتوحة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم البحث والفلترة -->
    <div class="search-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="اسم المندوب أو رقم السيارة" 
                       value="{{ search }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">المندوب</label>
                <select name="representative" class="form-select">
                    <option value="">جميع المناديب</option>
                    {% for rep in representatives %}
                        <option value="{{ rep.id }}" {% if representative == rep.id|stringformat:"s" %}selected{% endif %}>
                            {{ rep.full_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search"></i>
                    بحث
                </button>
                <a href="{% url 'sales:daily_movement_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- قائمة الحركات -->
    {% if movements %}
        <div class="row">
            {% for movement in movements %}
                <div class="col-lg-6 col-xl-4">
                    <div class="movement-card">
                        <div class="movement-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="movement-date">{{ movement.movement_date }}</div>
                                    <div class="movement-rep">
                                        <i class="bi bi-person"></i>
                                        {{ movement.representative.full_name }}
                                    </div>
                                </div>
                                <span class="status-badge {% if movement.is_closed %}status-closed{% else %}status-open{% endif %}">
                                    {% if movement.is_closed %}مغلقة{% else %}مفتوحة{% endif %}
                                </span>
                            </div>
                        </div>
                        
                        <div class="movement-body">
                            <div class="movement-details">
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-truck text-info"></i>
                                        السيارة:
                                    </span>
                                    <span class="detail-value">{{ movement.vehicle.plate_number }}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-cash text-success"></i>
                                        رصيد افتتاحي:
                                    </span>
                                    <span class="detail-value">{{ movement.opening_cash }} ج.م</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-receipt text-primary"></i>
                                        المبيعات:
                                    </span>
                                    <span class="detail-value">{{ movement.total_sales }} ج.م</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-arrow-down-circle text-success"></i>
                                        التحصيلات:
                                    </span>
                                    <span class="detail-value">{{ movement.total_collections }} ج.م</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-arrow-return-left text-warning"></i>
                                        المرتجعات:
                                    </span>
                                    <span class="detail-value">{{ movement.total_returns }} ج.م</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-credit-card text-danger"></i>
                                        المصروفات:
                                    </span>
                                    <span class="detail-value">{{ movement.expenses }} ج.م</span>
                                </div>
                            </div>
                            
                            <div class="net-movement {% if movement.net_movement >= 0 %}net-positive{% else %}net-negative{% endif %}">
                                <strong>صافي الحركة: {{ movement.net_movement }} ج.م</strong>
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="{% url 'sales:daily_movement_detail' movement.pk %}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    عرض التفاصيل
                                </a>
                                {% if not movement.is_closed %}
                                    <a href="{% url 'sales:daily_movement_edit' movement.pk %}" 
                                       class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-pencil"></i>
                                        تعديل
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- الترقيم -->
        {% if movements.has_other_pages %}
            <nav aria-label="ترقيم الصفحات" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if movements.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ movements.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if representative %}&representative={{ representative }}{% endif %}">
                                السابق
                            </a>
                        </li>
                    {% endif %}

                    {% for num in movements.paginator.page_range %}
                        {% if movements.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > movements.number|add:'-3' and num < movements.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if representative %}&representative={{ representative }}{% endif %}">
                                    {{ num }}
                                </a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if movements.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ movements.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if representative %}&representative={{ representative }}{% endif %}">
                                التالي
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="empty-state">
            <i class="bi bi-calendar-x"></i>
            <h3>لا توجد حركات يومية</h3>
            <p>لم يتم العثور على أي حركات تطابق معايير البحث</p>
            <a href="{% url 'sales:daily_movement_create' %}" class="btn btn-create">
                <i class="bi bi-plus-circle"></i>
                إنشاء أول حركة يومية
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحديث تلقائي للصفحة كل دقيقة
    setTimeout(function() {
        location.reload();
    }, 60000);
    
    // تأثيرات بصرية للبطاقات
    $('.movement-card').hover(
        function() {
            $(this).find('.movement-date').addClass('text-warning');
        },
        function() {
            $(this).find('.movement-date').removeClass('text-warning');
        }
    );
});
</script>
{% endblock %}
