{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 20px;
        padding: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    .stats-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid rgba(0,0,0,0.1);
    }
    .stage-row {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .stage-row:hover {
        background-color: rgba(0, 123, 255, 0.05);
        border-left-color: #0056b3;
    }
    .stage-row.critical {
        border-left-color: #dc3545;
    }
    .stage-row.optional {
        border-left-color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-list-ol me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:production_stage_create' %}" class="btn btn-light btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>إنشاء مرحلة جديدة
                </a>
                <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-primary">إجمالي المراحل</h4>
                <h2 class="text-dark">{{ total_stages }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-success">المراحل النشطة</h4>
                <h2 class="text-dark">{{ active_stages }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-danger">المراحل الحرجة</h4>
                <h2 class="text-dark">{{ critical_stages }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h4 class="text-warning">قيد المراجعة</h4>
                <h2 class="text-dark">{{ total_stages|add:"-"|add:active_stages }}</h2>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="content-card">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" value="{{ search_query }}" placeholder="البحث في الكود أو الاسم">
            </div>
            <div class="col-md-2">
                <label class="form-label">نوع المرحلة</label>
                <select name="stage_type" class="form-select">
                    <option value="">الكل</option>
                    {% for value, label in stage_types %}
                        <option value="{{ value }}" {% if stage_type == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select name="status" class="form-select">
                    <option value="">الكل</option>
                    {% for value, label in stage_statuses %}
                        <option value="{{ value }}" {% if status == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">مرحلة حرجة</label>
                <select name="is_critical" class="form-select">
                    <option value="">الكل</option>
                    <option value="true" {% if is_critical == 'true' %}selected{% endif %}>نعم</option>
                    <option value="false" {% if is_critical == 'false' %}selected{% endif %}>لا</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary d-block w-100">
                    <i class="bi bi-search"></i> بحث
                </button>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <a href="{% url 'definitions:production_stage_list' %}" class="btn btn-secondary d-block w-100">
                    <i class="bi bi-arrow-clockwise"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- قائمة المراحل -->
    <div class="content-card">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>التسلسل</th>
                        <th>الكود</th>
                        <th>اسم المرحلة</th>
                        <th>نوع المرحلة</th>
                        <th>المدة المعيارية</th>
                        <th>التكلفة/ساعة</th>
                        <th>الحالة</th>
                        <th>الخصائص</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for stage in stages %}
                    <tr class="stage-row {% if stage.is_critical %}critical{% elif stage.is_optional %}optional{% endif %}">
                        <td>
                            <span class="badge bg-primary">{{ stage.sequence_number }}</span>
                        </td>
                        <td><strong>{{ stage.code }}</strong></td>
                        <td>
                            <a href="{% url 'definitions:production_stage_detail' stage.id %}" class="text-decoration-none">
                                {{ stage.name }}
                            </a>
                            {% if stage.name_en %}
                                <br><small class="text-muted">{{ stage.name_en }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ stage.get_stage_type_display }}</span>
                        </td>
                        <td>{{ stage.standard_duration_hours }} ساعة</td>
                        <td>{{ stage.total_cost_per_hour|floatformat:2 }} جنيه</td>
                        <td>
                            {% if stage.status == 'active' %}
                                <span class="badge bg-success">نشط</span>
                            {% elif stage.status == 'inactive' %}
                                <span class="badge bg-danger">غير نشط</span>
                            {% elif stage.status == 'under_review' %}
                                <span class="badge bg-warning">قيد المراجعة</span>
                            {% else %}
                                <span class="badge bg-secondary">ملغي</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if stage.is_critical %}
                                <span class="badge bg-danger" title="مرحلة حرجة">حرجة</span>
                            {% endif %}
                            {% if stage.is_optional %}
                                <span class="badge bg-secondary" title="مرحلة اختيارية">اختيارية</span>
                            {% endif %}
                            {% if stage.can_run_parallel %}
                                <span class="badge bg-info" title="يمكن تشغيلها بالتوازي">متوازية</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'definitions:production_stage_detail' stage.id %}" class="btn btn-outline-primary" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'definitions:production_stage_edit' stage.id %}" class="btn btn-outline-warning" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'definitions:production_stage_delete' stage.id %}" class="btn btn-outline-danger" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <i class="bi bi-list-ol" style="font-size: 3rem; color: #6c757d;"></i>
                            <p class="mt-2 text-muted">لا توجد مراحل إنتاج متاحة</p>
                            <a href="{% url 'definitions:production_stage_create' %}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>إنشاء مرحلة جديدة
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="content-card">
        <h4><i class="bi bi-info-circle me-2"></i>معلومات المراحل</h4>
        <div class="row">
            <div class="col-md-3">
                <div class="alert alert-primary">
                    <strong>المراحل العادية:</strong> مراحل أساسية في عملية الإنتاج
                </div>
            </div>
            <div class="col-md-3">
                <div class="alert alert-danger">
                    <strong>المراحل الحرجة:</strong> مراحل مهمة جداً ولا يمكن تجاوزها
                </div>
            </div>
            <div class="col-md-3">
                <div class="alert alert-secondary">
                    <strong>المراحل الاختيارية:</strong> مراحل يمكن تخطيها حسب الحاجة
                </div>
            </div>
            <div class="col-md-3">
                <div class="alert alert-info">
                    <strong>المراحل المتوازية:</strong> يمكن تنفيذها مع مراحل أخرى
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
