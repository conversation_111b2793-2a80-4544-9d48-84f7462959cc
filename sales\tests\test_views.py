from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.exceptions import PermissionDenied
from decimal import Decimal
from datetime import date

from sales.models import (
    Customer, Product, SalesRepresentative, SalesInvoice, SalesInvoiceItem,
    SalesOrder, VehicleLoading, DailyMovement, SalesReturn, DispensePermission,
    Inventory
)


class SalesDashboardViewTest(TestCase):
    """اختبارات عرض لوحة تحكم المبيعات"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # إنشاء بيانات تجريبية
        self.customer = Customer.objects.create(
            name='عميل تجريبي',
            customer_type='retail'
        )
        
        self.product = Product.objects.create(
            name='منتج تجريبي',
            code='PROD001',
            unit_price_retail=Decimal('100.00')
        )
    
    def test_dashboard_requires_login(self):
        """اختبار أن لوحة التحكم تتطلب تسجيل الدخول"""
        response = self.client.get(reverse('sales:dashboard'))
        self.assertEqual(response.status_code, 302)  # إعادة توجيه لصفحة تسجيل الدخول
    
    def test_dashboard_with_login(self):
        """اختبار لوحة التحكم مع تسجيل الدخول"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'لوحة تحكم المبيعات')
    
    def test_dashboard_context_data(self):
        """اختبار بيانات السياق في لوحة التحكم"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:dashboard'))
        
        self.assertIn('total_customers', response.context)
        self.assertIn('total_products', response.context)
        self.assertIn('total_sales', response.context)
        self.assertEqual(response.context['total_customers'], 1)
        self.assertEqual(response.context['total_products'], 1)


class CustomerViewsTest(TestCase):
    """اختبارات عروض العملاء"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.customer = Customer.objects.create(
            name='عميل تجريبي',
            customer_type='retail',
            phone='01234567890',
            email='<EMAIL>'
        )
    
    def test_customer_list_view(self):
        """اختبار عرض قائمة العملاء"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:customer_list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'عميل تجريبي')
        self.assertContains(response, 'إدارة العملاء')
    
    def test_customer_create_view_get(self):
        """اختبار عرض إنشاء عميل جديد (GET)"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:customer_create'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'إضافة عميل جديد')
    
    def test_customer_create_view_post(self):
        """اختبار إنشاء عميل جديد (POST)"""
        self.client.login(username='testuser', password='testpass123')
        
        data = {
            'name': 'عميل جديد',
            'customer_type': 'wholesale',
            'phone': '01987654321',
            'email': '<EMAIL>',
            'address': 'عنوان جديد',
            'credit_limit': '5000.00',
            'credit_days': '15'
        }
        
        response = self.client.post(reverse('sales:customer_create'), data)
        self.assertEqual(response.status_code, 302)  # إعادة توجيه بعد النجاح
        
        # التحقق من إنشاء العميل
        self.assertTrue(Customer.objects.filter(name='عميل جديد').exists())
    
    def test_customer_edit_view(self):
        """اختبار تعديل عميل"""
        self.client.login(username='testuser', password='testpass123')
        
        data = {
            'name': 'عميل محدث',
            'customer_type': 'credit',
            'phone': '01234567890',
            'email': '<EMAIL>',
            'address': 'عنوان محدث',
            'credit_limit': '10000.00',
            'credit_days': '30'
        }
        
        response = self.client.post(
            reverse('sales:customer_edit', kwargs={'pk': self.customer.pk}),
            data
        )
        
        self.assertEqual(response.status_code, 302)
        
        # التحقق من التحديث
        self.customer.refresh_from_db()
        self.assertEqual(self.customer.name, 'عميل محدث')
        self.assertEqual(self.customer.customer_type, 'credit')
    
    def test_customer_delete_view(self):
        """اختبار حذف عميل"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.post(
            reverse('sales:customer_delete', kwargs={'pk': self.customer.pk})
        )
        
        self.assertEqual(response.status_code, 302)
        
        # التحقق من الحذف (التعطيل)
        self.customer.refresh_from_db()
        self.assertFalse(self.customer.is_active)


class ProductViewsTest(TestCase):
    """اختبارات عروض المنتجات"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.product = Product.objects.create(
            name='منتج تجريبي',
            code='PROD001',
            category='فئة تجريبية',
            unit='قطعة',
            cost_price=Decimal('50.00'),
            unit_price_retail=Decimal('75.00'),
            unit_price_wholesale=Decimal('65.00'),
            stock_quantity=Decimal('100.00')
        )
    
    def test_product_list_view(self):
        """اختبار عرض قائمة المنتجات"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:product_list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'منتج تجريبي')
        self.assertContains(response, 'PROD001')
    
    def test_product_create_view(self):
        """اختبار إنشاء منتج جديد"""
        self.client.login(username='testuser', password='testpass123')
        
        data = {
            'name': 'منتج جديد',
            'code': 'PROD002',
            'category': 'فئة جديدة',
            'unit': 'كيلو',
            'cost_price': '30.00',
            'unit_price_retail': '45.00',
            'unit_price_wholesale': '40.00',
            'stock_quantity': '50.00',
            'min_stock_level': '5.00'
        }
        
        response = self.client.post(reverse('sales:product_create'), data)
        self.assertEqual(response.status_code, 302)
        
        # التحقق من إنشاء المنتج
        self.assertTrue(Product.objects.filter(code='PROD002').exists())
    
    def test_product_api_view(self):
        """اختبار API للحصول على بيانات المنتج"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.get(
            reverse('sales:get_product_data', kwargs={'product_id': self.product.pk})
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['unit_price_retail'], 75.0)
        self.assertEqual(data['stock_quantity'], 100.0)


class SalesInvoiceViewsTest(TestCase):
    """اختبارات عروض فواتير البيع"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.customer = Customer.objects.create(
            name='عميل تجريبي',
            customer_type='retail'
        )
        
        self.rep_user = User.objects.create_user(
            username='rep1',
            first_name='أحمد',
            last_name='محمد'
        )
        
        self.representative = SalesRepresentative.objects.create(
            user=self.rep_user,
            employee_id='EMP001'
        )
        
        self.product = Product.objects.create(
            name='منتج تجريبي',
            code='PROD001',
            unit_price_retail=Decimal('100.00'),
            stock_quantity=Decimal('50.00')
        )
        
        self.invoice = SalesInvoice.objects.create(
            customer=self.customer,
            representative=self.representative,
            invoice_date=date.today(),
            invoice_type='retail',
            payment_method='cash',
            status='draft',
            created_by=self.user
        )
    
    def test_invoice_list_view(self):
        """اختبار عرض قائمة الفواتير"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:invoice_list'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.invoice.invoice_number)
    
    def test_invoice_detail_view(self):
        """اختبار عرض تفاصيل الفاتورة"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('sales:invoice_detail', kwargs={'pk': self.invoice.pk})
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.invoice.invoice_number)
        self.assertContains(response, self.customer.name)
    
    def test_invoice_create_view(self):
        """اختبار إنشاء فاتورة جديدة"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:invoice_create'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'إنشاء فاتورة جديدة')


class ReportsViewsTest(TestCase):
    """اختبارات عروض التقارير"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # إنشاء بيانات تجريبية للتقارير
        self.customer = Customer.objects.create(
            name='عميل تجريبي',
            customer_type='retail'
        )
        
        self.product = Product.objects.create(
            name='منتج تجريبي',
            code='PROD001',
            unit_price_retail=Decimal('100.00'),
            stock_quantity=Decimal('50.00')
        )
        
        self.rep_user = User.objects.create_user(username='rep1')
        self.representative = SalesRepresentative.objects.create(
            user=self.rep_user,
            employee_id='EMP001'
        )
        
        self.invoice = SalesInvoice.objects.create(
            customer=self.customer,
            representative=self.representative,
            invoice_date=date.today(),
            invoice_type='retail',
            payment_method='cash',
            status='paid',
            total_amount=Decimal('500.00'),
            created_by=self.user
        )
    
    def test_reports_dashboard_view(self):
        """اختبار لوحة تحكم التقارير"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:reports_dashboard'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'لوحة تحكم التقارير')
    
    def test_sales_report_view(self):
        """اختبار تقرير المبيعات"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:sales_report'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'تقرير المبيعات')
        self.assertIn('total_amount', response.context)
        self.assertIn('total_invoices', response.context)
    
    def test_inventory_report_view(self):
        """اختبار تقرير المخزون"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:inventory_report'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'تقرير المخزون')
        self.assertContains(response, 'منتج تجريبي')
    
    def test_representative_performance_report_view(self):
        """اختبار تقرير أداء المناديب"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('sales:representative_performance_report'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'تقرير أداء المناديب')
        self.assertIn('performance_data', response.context)


class SearchAndFilterTest(TestCase):
    """اختبارات البحث والفلترة"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # إنشاء عملاء متعددين للبحث
        Customer.objects.create(name='أحمد محمد', customer_type='retail')
        Customer.objects.create(name='فاطمة علي', customer_type='wholesale')
        Customer.objects.create(name='محمد أحمد', customer_type='credit')
    
    def test_customer_search(self):
        """اختبار البحث في العملاء"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.get(reverse('sales:customer_list'), {'search': 'أحمد'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'أحمد محمد')
        self.assertContains(response, 'محمد أحمد')
        self.assertNotContains(response, 'فاطمة علي')
    
    def test_customer_filter_by_type(self):
        """اختبار فلترة العملاء حسب النوع"""
        self.client.login(username='testuser', password='testpass123')
        
        response = self.client.get(reverse('sales:customer_list'), {'customer_type': 'wholesale'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'فاطمة علي')
        self.assertNotContains(response, 'أحمد محمد')
