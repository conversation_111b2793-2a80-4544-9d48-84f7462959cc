{% extends 'base.html' %}

{% block title %}إنشاء مخزن جديد{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4>إنشاء مخزن جديد</h4>
                </div>
                
                <div class="card-body">
                    <!-- عرض الرسائل -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }}">{{ message|linebreaks }}</div>
                        {% endfor %}
                    {% endif %}

                    <!-- عرض معلومات التشخيص في حالة الخطأ -->
                    {% if debug_info %}
                        <div class="alert alert-warning">
                            <h6>معلومات التشخيص:</h6>
                            <p><strong>نوع الخطأ:</strong> {{ debug_info.error_type }}</p>
                            <p><strong>رسالة الخطأ:</strong> {{ debug_info.error_message }}</p>
                            <details>
                                <summary>تفاصيل تقنية</summary>
                                <pre>{{ debug_info.traceback }}</pre>
                            </details>
                        </div>
                    {% endif %}

                    <!-- النموذج البسيط -->
                    <form method="post" action="{% url 'definitions:warehouse_create_basic' %}">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label class="form-label">كود المخزن *</label>
                            <input type="text" name="code" class="form-control" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">اسم المخزن *</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">نوع المخزن *</label>
                            <select name="warehouse_type" class="form-select" required>
                                <option value="">اختر نوع المخزن</option>
                                <option value="main">مخزن رئيسي</option>
                                <option value="branch">مخزن فرعي</option>
                                <option value="raw_materials">مخزن مواد خام</option>
                                <option value="finished_goods">مخزن منتجات تامة</option>
                                <option value="damaged">مخزن تالف</option>
                                <option value="quarantine">مخزن حجر صحي</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea name="address" class="form-control" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="text" name="phone" class="form-control">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">مدير المخزن</label>
                            <input type="text" name="manager_name" class="form-control">
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="is_active" class="form-check-input" checked>
                                <label class="form-check-label">المخزن نشط</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="allow_negative_stock" class="form-check-input">
                                <label class="form-check-label">السماح بالمخزون السالب</label>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-success btn-lg" id="submitBtn">حفظ المخزن</button>
                            <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = document.getElementById('submitBtn');

    form.addEventListener('submit', function(e) {
        console.log('Form submission started');

        // تغيير نص الزر
        submitBtn.innerHTML = 'جاري الحفظ...';
        submitBtn.disabled = true;

        // جمع البيانات
        const formData = new FormData(form);
        console.log('Form data:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ':', value);
        }

        // التحقق من الحقول المطلوبة
        const code = document.querySelector('input[name="code"]').value.trim();
        const name = document.querySelector('input[name="name"]').value.trim();
        const warehouse_type = document.querySelector('select[name="warehouse_type"]').value;

        console.log('Required fields check:');
        console.log('Code:', code);
        console.log('Name:', name);
        console.log('Warehouse Type:', warehouse_type);

        if (!code || !name || !warehouse_type) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            submitBtn.innerHTML = 'حفظ المخزن';
            submitBtn.disabled = false;
            return false;
        }

        console.log('Form validation passed, submitting...');
    });

    // إضافة معالج للأخطاء
    window.addEventListener('error', function(e) {
        console.error('JavaScript Error:', e.error);
        alert('حدث خطأ في JavaScript: ' + e.error.message);
    });
});
</script>
{% endblock %}
