#!/usr/bin/env python
"""
اختبار نموذج إضافة الأصول الثابتة
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from assets.models import Asset, AssetCategory
from assets.forms import AssetForm
from django.contrib.auth.models import User
from datetime import date

def test_asset_form():
    """اختبار نموذج الأصول"""
    print("🔄 بدء اختبار نموذج إضافة الأصول...")
    
    try:
        # إنشاء فئة أصول للاختبار
        category, created = AssetCategory.objects.get_or_create(
            name='أجهزة كمبيوتر',
            defaults={
                'description': 'أجهزة الكمبيوتر والمعدات الإلكترونية',
                'depreciation_rate': 20.00
            }
        )
        print(f"✅ فئة الأصول: {category.name}")
        
        # إنشاء مستخدم للاختبار
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={
                'first_name': 'مستخدم',
                'last_name': 'تجريبي',
                'email': '<EMAIL>'
            }
        )
        print(f"✅ المستخدم: {user.get_full_name() or user.username}")
        
        # بيانات الاختبار
        test_data = {
            'name': 'جهاز كمبيوتر محمول',
            'asset_code': 'LAPTOP-001',
            'category': category.id,
            'description': 'جهاز كمبيوتر محمول للعمل',
            'purchase_date': date.today(),
            'purchase_price': 15000.00,
            'supplier': 'شركة التكنولوجيا المتقدمة',
            'useful_life_years': 5,
            'salvage_value': 1000.00,
            'status': 'active',
            'condition': 'excellent',
            'location': 'مكتب الإدارة',
            'serial_number': 'SN123456789',
            'model': 'Dell Latitude 5520',
            'manufacturer': 'Dell',
            'responsible_person': user.id
        }
        
        # اختبار النموذج
        print("\n📝 اختبار صحة النموذج...")
        form = AssetForm(data=test_data)
        
        if form.is_valid():
            print("✅ النموذج صحيح")
            
            # حفظ الأصل
            asset = form.save()
            print(f"✅ تم حفظ الأصل: {asset.name} ({asset.asset_code})")
            print(f"   القيمة الدفترية: {asset.current_book_value:.2f} ج.م")
            print(f"   الاستهلاك السنوي: {asset.annual_depreciation:.2f} ج.م")
            
            return True
            
        else:
            print("❌ النموذج غير صحيح:")
            for field, errors in form.errors.items():
                print(f"   {field}: {', '.join(errors)}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_form():
    """اختبار نموذج مبسط"""
    print("\n🔄 اختبار نموذج مبسط...")
    
    try:
        # إنشاء فئة بسيطة
        category, created = AssetCategory.objects.get_or_create(
            name='معدات مكتبية',
            defaults={'depreciation_rate': 10.00}
        )
        
        # بيانات مبسطة
        simple_data = {
            'name': 'طاولة مكتب',
            'asset_code': 'DESK-001',
            'category': category.id,
            'purchase_date': date.today(),
            'purchase_price': 2500.00,
            'useful_life_years': 10,
            'salvage_value': 250.00,
            'status': 'active',
            'condition': 'excellent'
        }
        
        form = AssetForm(data=simple_data)
        
        if form.is_valid():
            asset = form.save()
            print(f"✅ تم حفظ الأصل المبسط: {asset.name}")
            return True
        else:
            print("❌ النموذج المبسط غير صحيح:")
            for field, errors in form.errors.items():
                print(f"   {field}: {', '.join(errors)}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار المبسط: {e}")
        return False

def test_form_validation():
    """اختبار التحقق من صحة النموذج"""
    print("\n🔄 اختبار التحقق من صحة النموذج...")
    
    # بيانات غير صحيحة
    invalid_data = {
        'name': '',  # اسم فارغ
        'asset_code': '',  # كود فارغ
        'purchase_price': -100,  # سعر سالب
        'useful_life_years': 0  # عمر افتراضي صفر
    }
    
    form = AssetForm(data=invalid_data)
    
    if not form.is_valid():
        print("✅ النموذج رفض البيانات غير الصحيحة بنجاح")
        print("   الأخطاء المكتشفة:")
        for field, errors in form.errors.items():
            print(f"   - {field}: {', '.join(errors)}")
        return True
    else:
        print("❌ النموذج قبل بيانات غير صحيحة!")
        return False

if __name__ == '__main__':
    print("🚀 بدء اختبار نموذج الأصول الثابتة")
    print("=" * 50)
    
    # تشغيل الاختبارات
    test1 = test_asset_form()
    test2 = test_simple_form()
    test3 = test_form_validation()
    
    print("\n" + "=" * 50)
    if test1 and test2 and test3:
        print("🎉 جميع الاختبارات نجحت! النموذج يعمل بشكل مثالي")
        print("✅ الزر مبسط ويعمل بكفاءة")
        print("✅ النموذج احترافي ومتكامل")
    else:
        print("⚠️ بعض الاختبارات فشلت، يرجى مراجعة الأخطاء")
