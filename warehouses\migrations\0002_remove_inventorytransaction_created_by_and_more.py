# Generated by Django 5.2.4 on 2025-07-13 12:21

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('warehouses', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='inventorytransaction',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='inventorytransaction',
            name='product',
        ),
        migrations.RemoveField(
            model_name='inventorytransaction',
            name='to_warehouse',
        ),
        migrations.RemoveField(
            model_name='inventorytransaction',
            name='warehouse',
        ),
        migrations.RemoveField(
            model_name='stockadjustment',
            name='approved_by',
        ),
        migrations.RemoveField(
            model_name='stockadjustment',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='stockadjustment',
            name='warehouse',
        ),
        migrations.RemoveField(
            model_name='stockadjustmentitem',
            name='adjustment',
        ),
        migrations.RemoveField(
            model_name='stockadjustmentitem',
            name='product',
        ),
        migrations.DeleteModel(
            name='InventoryBalance',
        ),
        migrations.DeleteModel(
            name='InventoryTransaction',
        ),
        migrations.DeleteModel(
            name='StockAdjustment',
        ),
        migrations.DeleteModel(
            name='StockAdjustmentItem',
        ),
    ]
