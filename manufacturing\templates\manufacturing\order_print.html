<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { background: white !important; }
            .container { max-width: 100% !important; }
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        
        .print-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .company-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .company-name {
            font-size: 2rem;
            font-weight: 900;
            margin-bottom: 0.5rem;
        }
        
        .document-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-top: 1rem;
        }
        
        .info-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #667eea;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .info-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            background: #f8f9fa;
        }
        
        .info-label {
            font-size: 0.9rem;
            color: #636e72;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .info-value {
            font-size: 1.1rem;
            font-weight: 700;
            color: #2d3436;
        }
        
        .materials-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .table thead th {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem;
            font-weight: 700;
            text-align: center;
        }
        
        .table tbody td {
            padding: 1rem;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }
        
        .status-draft { background: #e9ecef; color: #495057; }
        .status-approved { background: #d1ecf1; color: #0c5460; }
        .status-in_progress { background: #fff3cd; color: #856404; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .priority-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 10px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .priority-low { background: #d1f2eb; color: #0e6655; }
        .priority-normal { background: #d6eaf8; color: #1b4f72; }
        .priority-high { background: #fadbd8; color: #943126; }
        .priority-urgent { background: #f1948a; color: #ffffff; }
        
        .cost-summary {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
        }
        
        .cost-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .cost-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 1.5rem;
        }
        
        .cost-label {
            font-size: 1rem;
            color: #1565c0;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .cost-value {
            font-size: 1.8rem;
            font-weight: 900;
            color: #0d47a1;
        }
        
        .print-footer {
            margin-top: 3rem;
            padding: 2rem;
            border-top: 2px solid #e9ecef;
            text-align: center;
            color: #636e72;
        }
        
        .signature-section {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 3rem;
            margin-top: 3rem;
        }
        
        .signature-box {
            text-align: center;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            min-height: 100px;
        }
        
        .signature-title {
            font-weight: 700;
            color: #495057;
            margin-bottom: 1rem;
        }
        
        .print-buttons {
            text-align: center;
            margin: 2rem 0;
        }
        
        .btn-print {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .cost-grid {
                grid-template-columns: 1fr;
            }
            
            .signature-section {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container my-4">
        <!-- Print Buttons -->
        <div class="print-buttons no-print">
            <button onclick="window.print()" class="btn-print">
                <i class="bi bi-printer me-2"></i>طباعة
            </button>
            <button onclick="window.close()" class="btn-print" style="background: linear-gradient(135deg, #636e72 0%, #**********%);">
                <i class="bi bi-x-circle me-2"></i>إغلاق
            </button>
        </div>

        <!-- Header -->
        <div class="print-header">
            <div class="company-logo">
                <i class="bi bi-gear-wide-connected"></i>
            </div>
            <div class="company-name">شركة أوساريك للتصنيع</div>
            <div class="document-title">أمر تصنيع رقم: {{ order.order_number }}</div>
            <div class="mt-2">
                <span class="status-badge status-{{ order.status }}">{{ order.get_status_display }}</span>
                <span class="priority-badge priority-{{ order.priority }} ms-3">{{ order.get_priority_display }}</span>
            </div>
        </div>

        <!-- معلومات أساسية -->
        <div class="info-section">
            <h3 class="section-title">المعلومات الأساسية</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">رقم أمر التصنيع</div>
                    <div class="info-value">{{ order.order_number }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">تاريخ الأمر</div>
                    <div class="info-value">{{ order.order_date|date:"d/m/Y H:i" }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">تاريخ الإنجاز المتوقع</div>
                    <div class="info-value">{{ order.expected_completion_date|date:"d/m/Y H:i" }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">المنتج النهائي</div>
                    <div class="info-value">{{ order.final_product.name }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">الكمية المطلوبة</div>
                    <div class="info-value">{{ order.quantity_to_produce }} {{ order.unit_of_measure.name }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">أنشئ بواسطة</div>
                    <div class="info-value">{{ order.created_by.get_full_name|default:order.created_by.username }}</div>
                </div>
            </div>
        </div>

        <!-- معلومات المخازن -->
        <div class="info-section">
            <h3 class="section-title">معلومات المخازن</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">مخزن المواد الخام</div>
                    <div class="info-value">{{ order.raw_materials_warehouse.name }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">مخزن المنتجات التامة</div>
                    <div class="info-value">{{ order.finished_goods_warehouse.name }}</div>
                </div>
            </div>
        </div>

        <!-- المواد الخام -->
        <div class="info-section">
            <h3 class="section-title">المواد الخام المطلوبة</h3>
            <div class="materials-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>المادة الخام</th>
                            <th>الكود</th>
                            <th>الكمية المطلوبة</th>
                            <th>وحدة القياس</th>
                            <th>تكلفة الوحدة</th>
                            <th>إجمالي التكلفة</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for material in order.raw_materials.all %}
                        <tr>
                            <td>
                                <strong>{{ material.raw_material.name }}</strong>
                                {% if material.is_critical %}
                                    <span class="badge bg-danger ms-2">حرجة</span>
                                {% endif %}
                            </td>
                            <td>{{ material.raw_material.code }}</td>
                            <td class="text-center"><strong>{{ material.required_quantity }}</strong></td>
                            <td class="text-center">{{ material.unit_of_measure.name }}</td>
                            <td class="text-end">{{ material.unit_cost|floatformat:2 }} جنيه</td>
                            <td class="text-end"><strong>{{ material.total_cost|floatformat:2 }} جنيه</strong></td>
                            <td>{{ material.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center py-4">لم يتم تحديد مواد خام لهذا الأمر</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- تحليل التكاليف -->
        <div class="info-section">
            <h3 class="section-title">تحليل التكاليف</h3>
            <div class="cost-summary">
                <h4 class="mb-4">التكاليف المقدرة</h4>
                <div class="cost-grid">
                    <div class="cost-item">
                        <div class="cost-label">تكلفة المواد الخام</div>
                        <div class="cost-value">{{ order.estimated_raw_material_cost|floatformat:2 }}</div>
                        <small>جنيه</small>
                    </div>
                    <div class="cost-item">
                        <div class="cost-label">تكلفة العمالة</div>
                        <div class="cost-value">{{ order.estimated_labor_cost|floatformat:2 }}</div>
                        <small>جنيه</small>
                    </div>
                    <div class="cost-item">
                        <div class="cost-label">التكاليف الإضافية</div>
                        <div class="cost-value">{{ order.estimated_overhead_cost|floatformat:2 }}</div>
                        <small>جنيه</small>
                    </div>
                    <div class="cost-item">
                        <div class="cost-label">إجمالي التكلفة</div>
                        <div class="cost-value">{{ order.total_estimated_cost|floatformat:2 }}</div>
                        <small>جنيه</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        {% if order.notes or order.special_instructions or order.quality_requirements %}
        <div class="info-section">
            <h3 class="section-title">معلومات إضافية</h3>
            {% if order.notes %}
            <div class="mb-3">
                <strong>ملاحظات:</strong>
                <p>{{ order.notes|linebreaks }}</p>
            </div>
            {% endif %}
            {% if order.special_instructions %}
            <div class="mb-3">
                <strong>تعليمات خاصة:</strong>
                <p>{{ order.special_instructions|linebreaks }}</p>
            </div>
            {% endif %}
            {% if order.quality_requirements %}
            <div class="mb-3">
                <strong>متطلبات الجودة:</strong>
                <p>{{ order.quality_requirements|linebreaks }}</p>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- التوقيعات -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-title">مدير الإنتاج</div>
                <div style="margin-top: 2rem;">التوقيع: _______________</div>
                <div>التاريخ: _______________</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">مراقب الجودة</div>
                <div style="margin-top: 2rem;">التوقيع: _______________</div>
                <div>التاريخ: _______________</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">مدير المخزون</div>
                <div style="margin-top: 2rem;">التوقيع: _______________</div>
                <div>التاريخ: _______________</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="print-footer">
            <p>تم طباعة هذا المستند في: {{ print_date|date:"d/m/Y H:i" }}</p>
            <p>شركة أوساريك للتصنيع - نظام إدارة التصنيع المتكامل</p>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
