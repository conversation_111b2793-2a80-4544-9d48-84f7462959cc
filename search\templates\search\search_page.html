<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث الذكي - {% if query %}{{ query }}{% else %}البحث في النظام{% endif %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .search-page-container {
            padding-top: 2rem;
        }
        
        .search-header {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .search-box-large {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .search-input-large {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e5e7eb;
            border-radius: 15px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .search-input-large:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .search-icon-large {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-size: 1.2rem;
        }
        
        .search-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .search-results-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .results-sidebar {
            background: #f8fafc;
            padding: 1.5rem;
            border-left: 1px solid #e5e7eb;
        }
        
        .results-main {
            padding: 1.5rem;
        }
        
        .result-item {
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .result-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .result-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3rem;
            flex-shrink: 0;
        }
        
        .result-content {
            flex: 1;
        }
        
        .result-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .result-title a {
            color: inherit;
            text-decoration: none;
        }
        
        .result-title a:hover {
            color: #667eea;
        }
        
        .result-description {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 0.5rem;
        }
        
        .result-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.85rem;
            color: #9ca3af;
        }
        
        .result-category {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .sidebar-section {
            margin-bottom: 2rem;
        }
        
        .sidebar-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1rem;
        }
        
        .category-filter {
            display: block;
            width: 100%;
            text-align: right;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: none;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .category-filter:hover, .category-filter.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
            text-decoration: none;
        }
        
        .popular-search-item {
            display: block;
            padding: 0.5rem 0;
            color: #6b7280;
            text-decoration: none;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.3s ease;
        }
        
        .popular-search-item:hover {
            color: #667eea;
            text-decoration: none;
        }
        
        .search-count {
            font-size: 0.8rem;
            color: #9ca3af;
        }
        
        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            color: #6b7280;
        }
        
        .no-results i {
            font-size: 4rem;
            margin-bottom: 1rem;
            display: block;
            color: #d1d5db;
        }
    </style>
</head>
<body>
    <div class="container search-page-container">
        <!-- رأس البحث -->
        <div class="search-header">
            <div class="search-box-large">
                <i class="bi bi-search search-icon-large"></i>
                <input type="text" class="search-input-large" 
                       value="{{ query }}" 
                       placeholder="{% if user_language == 'en' %}Search in system...{% else %}ابحث في النظام...{% endif %}"
                       onkeyup="if(event.key==='Enter') searchPage(this.value)">
            </div>
            
            <div class="search-stats">
                <div>
                    {% if query %}
                        {% if user_language == 'en' %}
                            <strong>{{ total_count }}</strong> results for "<strong>{{ query }}</strong>"
                            {% if execution_time %}in {{ execution_time }} seconds{% endif %}
                        {% else %}
                            <strong>{{ total_count }}</strong> نتيجة للبحث عن "<strong>{{ query }}</strong>"
                            {% if execution_time %}في {{ execution_time }} ثانية{% endif %}
                        {% endif %}
                    {% else %}
                        {% if user_language == 'en' %}Search in all parts of the system{% else %}ابحث في جميع أجزاء النظام{% endif %}
                    {% endif %}
                </div>
                <div>
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-house"></i> {% if user_language == 'en' %}Back to Home{% else %}العودة للرئيسية{% endif %}
                    </a>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- الشريط الجانبي -->
            <div class="col-md-3">
                <div class="search-results-container">
                    <div class="results-sidebar">
                        <!-- فلترة حسب الفئة -->
                        <div class="sidebar-section">
                            <h6 class="sidebar-title">الفئات</h6>
                            <a href="?q={{ query }}" class="category-filter {% if not current_category %}active{% endif %}">
                                <i class="bi bi-grid me-2"></i>
                                جميع النتائج
                            </a>
                            {% for category, items in categories.items %}
                            <a href="?q={{ query }}&category={{ category }}" 
                               class="category-filter {% if current_category == category %}active{% endif %}">
                                <i class="bi bi-folder me-2"></i>
                                {{ category }} ({{ items|length }})
                            </a>
                            {% endfor %}
                        </div>
                        
                        <!-- البحثات الشائعة -->
                        <div class="sidebar-section">
                            <h6 class="sidebar-title">البحثات الشائعة</h6>
                            {% for popular in popular_searches %}
                            <a href="?q={{ popular.query }}" class="popular-search-item">
                                {{ popular.query }}
                                <span class="search-count">({{ popular.search_count }})</span>
                            </a>
                            {% endfor %}
                        </div>
                        
                        <!-- تاريخ البحث -->
                        {% if recent_searches %}
                        <div class="sidebar-section">
                            <h6 class="sidebar-title">البحثات الأخيرة</h6>
                            {% for recent in recent_searches %}
                            <a href="?q={{ recent.query }}" class="popular-search-item">
                                {{ recent.query }}
                                <span class="search-count">({{ recent.results_count }})</span>
                            </a>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- النتائج الرئيسية -->
            <div class="col-md-9">
                <div class="search-results-container">
                    <div class="results-main">
                        {% if results %}
                            {% for result in results %}
                            <div class="result-item">
                                <div class="result-icon" style="background: {% if result.icon == 'bi-box' %}#f59e0b{% elif result.icon == 'bi-people' %}#10b981{% elif result.icon == 'bi-receipt' %}#3b82f6{% elif result.icon == 'bi-person' %}#8b5cf6{% elif result.icon == 'bi-envelope' %}#06b6d4{% else %}#6b7280{% endif %};">
                                    <i class="{{ result.icon }}"></i>
                                </div>
                                <div class="result-content">
                                    <div class="result-title">
                                        <a href="{{ result.url }}">{{ result.title }}</a>
                                    </div>
                                    <div class="result-description">{{ result.description }}</div>
                                    <div class="result-meta">
                                        <span class="result-category">{{ result.category }}</span>
                                        <span>درجة الصلة: {{ result.relevance_score }}</span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                            
                            <!-- التصفح -->
                            {% if results.has_other_pages %}
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if results.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?q={{ query }}&page={{ results.previous_page_number }}{% if current_category %}&category={{ current_category }}{% endif %}">السابق</a>
                                    </li>
                                    {% endif %}
                                    
                                    {% for num in results.paginator.page_range %}
                                    {% if num == results.number %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="?q={{ query }}&page={{ num }}{% if current_category %}&category={{ current_category }}{% endif %}">{{ num }}</a>
                                    </li>
                                    {% endif %}
                                    {% endfor %}
                                    
                                    {% if results.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?q={{ query }}&page={{ results.next_page_number }}{% if current_category %}&category={{ current_category }}{% endif %}">{% if user_language == 'en' %}Next{% else %}التالي{% endif %}</a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% endif %}
                        {% else %}
                            <div class="no-results">
                                {% if query %}
                                    <i class="bi bi-search"></i>
                                    <h4>{% if user_language == 'en' %}No Results{% else %}لا توجد نتائج{% endif %}</h4>
                                    <p>{% if user_language == 'en' %}We couldn't find any results for "{{ query }}"{% else %}لم نجد أي نتائج للبحث عن "{{ query }}"{% endif %}</p>
                                    <p>{% if user_language == 'en' %}Try:{% else %}جرب:{% endif %}</p>
                                    <ul style="list-style: none; padding: 0;">
                                        {% if user_language == 'en' %}
                                            <li>• Check spelling</li>
                                            <li>• Use different keywords</li>
                                            <li>• Search in specific category</li>
                                        {% else %}
                                            <li>• التأكد من الإملاء</li>
                                            <li>• استخدام كلمات أخرى</li>
                                            <li>• البحث في فئة محددة</li>
                                        {% endif %}
                                    </ul>
                                {% else %}
                                    <i class="bi bi-search"></i>
                                    <h4>{% if user_language == 'en' %}Start Searching{% else %}ابدأ البحث{% endif %}</h4>
                                    <p>{% if user_language == 'en' %}Type in the search box above to find what you're looking for{% else %}اكتب في مربع البحث أعلاه للعثور على ما تريد{% endif %}</p>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function searchPage(query) {
            if (query.trim()) {
                window.location.href = `?q=${encodeURIComponent(query.trim())}`;
            }
        }
    </script>
</body>
</html>
