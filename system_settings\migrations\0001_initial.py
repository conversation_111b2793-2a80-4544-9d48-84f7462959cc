# Generated manually for system_settings

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(default='شركة أوساريك', max_length=200, verbose_name='اسم الشركة')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='company/', verbose_name='شعار الشركة')),
                ('company_address', models.TextField(blank=True, verbose_name='عنوان الشركة')),
                ('company_phone', models.CharField(blank=True, max_length=20, verbose_name='هاتف الشركة')),
                ('company_email', models.EmailField(blank=True, max_length=254, verbose_name='بريد الشركة')),
                ('company_website', models.URLField(blank=True, verbose_name='موقع الشركة')),
                ('company_tax_number', models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي')),
                ('system_language', models.CharField(choices=[('ar', 'العربية'), ('en', 'English')], default='ar', max_length=10, verbose_name='لغة النظام')),
                ('system_timezone', models.CharField(default='Asia/Riyadh', max_length=50, verbose_name='المنطقة الزمنية')),
                ('currency_code', models.CharField(default='SAR', max_length=3, verbose_name='رمز العملة')),
                ('currency_symbol', models.CharField(default='ريال', max_length=10, verbose_name='رمز العملة')),
                ('session_timeout', models.IntegerField(default=30, validators=[django.core.validators.MinValueValidator(5), django.core.validators.MaxValueValidator(480)], verbose_name='انتهاء الجلسة (دقيقة)')),
                ('password_min_length', models.IntegerField(default=8, validators=[django.core.validators.MinValueValidator(6), django.core.validators.MaxValueValidator(20)], verbose_name='الحد الأدنى لطول كلمة المرور')),
                ('require_strong_password', models.BooleanField(default=True, verbose_name='كلمة مرور قوية مطلوبة')),
                ('max_login_attempts', models.IntegerField(default=5, validators=[django.core.validators.MinValueValidator(3), django.core.validators.MaxValueValidator(10)], verbose_name='عدد محاولات تسجيل الدخول')),
                ('email_host', models.CharField(blank=True, max_length=100, verbose_name='خادم البريد')),
                ('email_port', models.IntegerField(default=587, verbose_name='منفذ البريد')),
                ('email_use_tls', models.BooleanField(default=True, verbose_name='استخدام TLS')),
                ('backup_enabled', models.BooleanField(default=True, verbose_name='تفعيل النسخ الاحتياطي')),
                ('backup_frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري')], default='daily', max_length=20, verbose_name='تكرار النسخ الاحتياطي')),
                ('theme_color', models.CharField(default='#667eea', max_length=7, verbose_name='لون الواجهة الأساسي')),
                ('sidebar_collapsed', models.BooleanField(default=False, verbose_name='الشريط الجانبي مطوي افتراضياً')),
                ('items_per_page', models.IntegerField(default=20, validators=[django.core.validators.MinValueValidator(10), django.core.validators.MaxValueValidator(100)], verbose_name='عدد العناصر في الصفحة')),
                ('notifications_enabled', models.BooleanField(default=True, verbose_name='تفعيل الإشعارات')),
                ('email_notifications', models.BooleanField(default=True, verbose_name='إشعارات البريد الإلكتروني')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='محدث بواسطة')),
            ],
            options={
                'verbose_name': 'إعدادات النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('mobile', models.CharField(blank=True, max_length=20, verbose_name='رقم الجوال')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('birth_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('national_id', models.CharField(blank=True, max_length=20, verbose_name='رقم الهوية')),
                ('employee_id', models.CharField(blank=True, max_length=20, unique=True, verbose_name='رقم الموظف')),
                ('department', models.CharField(blank=True, choices=[('admin', 'الإدارة'), ('sales', 'المبيعات'), ('purchases', 'المشتريات'), ('warehouse', 'المخازن'), ('accounting', 'المحاسبة'), ('hr', 'الموارد البشرية'), ('it', 'تقنية المعلومات'), ('customer_service', 'خدمة العملاء')], max_length=20, verbose_name='القسم')),
                ('position', models.CharField(blank=True, choices=[('ceo', 'المدير التنفيذي'), ('manager', 'مدير'), ('supervisor', 'مشرف'), ('senior_employee', 'موظف أول'), ('employee', 'موظف'), ('intern', 'متدرب')], max_length=20, verbose_name='المنصب')),
                ('hire_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التوظيف')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='الصورة الشخصية')),
                ('theme_preference', models.CharField(choices=[('light', 'فاتح'), ('dark', 'داكن'), ('auto', 'تلقائي')], default='light', max_length=20, verbose_name='تفضيل الواجهة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='آخر IP تسجيل دخول')),
                ('failed_login_attempts', models.IntegerField(default=0, verbose_name='محاولات تسجيل الدخول الفاشلة')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subordinates', to=settings.AUTH_USER_MODEL, verbose_name='المدير المباشر')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف المستخدم',
                'verbose_name_plural': 'ملفات المستخدمين',
            },
        ),
    ]
