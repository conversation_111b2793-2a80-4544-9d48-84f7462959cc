#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from django.contrib.auth.models import User
from notifications.models import Notification

def create_sample_notifications():
    """إنشاء إشعارات تجريبية"""
    
    # الحصول على المستخدم الأول
    try:
        user = User.objects.first()
        if not user:
            print("لا يوجد مستخدمين في النظام")
            return
    except Exception as e:
        print(f"خطأ في الحصول على المستخدم: {e}")
        return
    
    # إشعارات تجريبية
    sample_notifications = [
        {
            'title': 'مرحباً بك في النظام',
            'message': 'تم تسجيل دخولك بنجاح إلى نظام أوساريك',
            'notification_type': 'success',
            'priority': 'normal',
            'icon': 'bi-check-circle'
        },
        {
            'title': 'تنبيه مخزون',
            'message': 'منتج "لابتوب ديل" تحت الحد الأدنى (5 قطع متبقية)',
            'notification_type': 'warning',
            'priority': 'high',
            'icon': 'bi-exclamation-triangle'
        },
        {
            'title': 'طلب جديد',
            'message': 'تم استلام طلب شراء جديد رقم #1234 بقيمة 15,000 ريال',
            'notification_type': 'order',
            'priority': 'normal',
            'icon': 'bi-cart'
        },
        {
            'title': 'دفعة جديدة',
            'message': 'تم استلام دفعة من العميل أحمد محمد بقيمة 8,500 ريال',
            'notification_type': 'payment',
            'priority': 'normal',
            'icon': 'bi-cash-coin'
        },
        {
            'title': 'تحديث النظام',
            'message': 'تم تحديث النظام إلى الإصدار 2.1.0 بنجاح',
            'notification_type': 'system',
            'priority': 'low',
            'icon': 'bi-gear'
        },
        {
            'title': 'رسالة جديدة',
            'message': 'لديك رسالة جديدة من سارة أحمد',
            'notification_type': 'message',
            'priority': 'normal',
            'icon': 'bi-chat-dots'
        },
        {
            'title': 'تنبيه أمني',
            'message': 'محاولة دخول من عنوان IP غير معروف',
            'notification_type': 'error',
            'priority': 'urgent',
            'icon': 'bi-shield-exclamation'
        },
        {
            'title': 'مستخدم جديد',
            'message': 'تم تسجيل مستخدم جديد: محمد علي',
            'notification_type': 'user',
            'priority': 'low',
            'icon': 'bi-person-plus'
        },
        {
            'title': 'نسخة احتياطية',
            'message': 'تم إنشاء النسخة الاحتياطية اليومية بنجاح',
            'notification_type': 'system',
            'priority': 'low',
            'icon': 'bi-cloud-check'
        },
        {
            'title': 'انتهاء صلاحية',
            'message': 'منتج "دواء الصداع" سينتهي خلال 30 يوم',
            'notification_type': 'warning',
            'priority': 'high',
            'icon': 'bi-calendar-x'
        }
    ]
    
    created_count = 0
    
    try:
        for notification_data in sample_notifications:
            notification = Notification.objects.create(
                recipient=user,
                title=notification_data['title'],
                message=notification_data['message'],
                notification_type=notification_data['notification_type'],
                priority=notification_data['priority'],
                icon=notification_data['icon']
            )
            created_count += 1
            print(f"تم إنشاء إشعار: {notification.title}")
        
        print(f"\n✅ تم إنشاء {created_count} إشعار بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الإشعارات: {e}")

if __name__ == '__main__':
    create_sample_notifications()
