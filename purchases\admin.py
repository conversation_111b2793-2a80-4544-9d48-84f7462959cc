from django.contrib import admin
from .models import (Supplier, Product, PurchaseOrder, PurchaseOrderItem,
                    PurchaseInvoice, PurchaseInvoiceItem, Warehouse)

@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ['name', 'phone', 'email', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'phone', 'email']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('name', 'code', 'supplier_type', 'is_active')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone', 'mobile', 'email', 'website')
        }),
        ('العنوان', {
            'fields': ('address', 'city', 'state', 'postal_code')
        }),
        ('المعلومات المالية', {
            'fields': ('payment_terms', 'credit_limit', 'discount_percentage')
        }),
        ('معلومات إضافية', {
            'fields': ('tax_number', 'commercial_register', 'notes')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'stock_quantity', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'code']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('name', 'code', 'barcode', 'category', 'description', 'is_active')
        }),
        ('معلومات المخزون', {
            'fields': ('stock_quantity', 'min_stock_level', 'max_stock_level', 'unit')
        }),
        ('معلومات التكلفة', {
            'fields': ('cost_price', 'selling_price')
        }),
        ('تواريخ النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

class PurchaseOrderItemInline(admin.TabularInline):
    model = PurchaseOrderItem
    extra = 1
    fields = ['product', 'quantity', 'unit_price', 'discount_percentage', 'total_price']
    readonly_fields = ['total_price']

@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'supplier', 'order_date', 'status', 'total_amount', 'created_by']
    list_filter = ['status', 'order_date', 'created_at']
    search_fields = ['order_number', 'supplier__name']
    readonly_fields = ['order_number', 'total_amount', 'created_at', 'updated_at']
    inlines = [PurchaseOrderItemInline]
    fieldsets = (
        ('معلومات أمر الشراء', {
            'fields': ('order_number', 'supplier', 'order_date', 'expected_delivery_date', 'reference_number')
        }),
        ('حالة وأولوية الطلب', {
            'fields': ('status', 'priority')
        }),
        ('تفاصيل التسليم', {
            'fields': ('delivery_address', 'special_instructions')
        }),
        ('الإجماليات', {
            'fields': ('total_amount',),
            'classes': ('collapse',)
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

class PurchaseInvoiceItemInline(admin.TabularInline):
    model = PurchaseInvoiceItem
    extra = 1
    fields = ['product', 'quantity', 'unit_price', 'discount_percentage', 'total_price']
    readonly_fields = ['total_price']

@admin.register(PurchaseInvoice)
class PurchaseInvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'supplier', 'invoice_date', 'due_date', 'status', 'total_amount', 'created_by']
    list_filter = ['status', 'invoice_date', 'due_date', 'created_at']
    search_fields = ['invoice_number', 'supplier__name', 'reference_number']
    readonly_fields = ['invoice_number', 'total_amount', 'created_at', 'updated_at']
    inlines = [PurchaseInvoiceItemInline]
    fieldsets = (
        ('معلومات فاتورة الشراء', {
            'fields': ('invoice_number', 'supplier', 'invoice_date', 'due_date', 'reference_number')
        }),
        ('حالة الفاتورة', {
            'fields': ('status',)
        }),
        ('الإجماليات', {
            'fields': ('total_amount',),
            'classes': ('collapse',)
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


# تم نقل إدارة المخازن والمخزون إلى تطبيق warehouses
# يمكن الوصول إليها من خلال:
# - WarehouseDefinition في تطبيق definitions
# - InventoryItem و InventoryTransaction في تطبيق warehouses
