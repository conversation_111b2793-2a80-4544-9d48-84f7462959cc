<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل العميل: {{ customer.name }} - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .customer-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .customer-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            margin: 0 auto 20px;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .customer-name {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .customer-type {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .balance-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-bottom: 25px;
        }
        
        .balance-card.negative {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        
        .balance-title {
            font-size: 1.2rem;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .balance-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .info-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            display: flex;
            align-items: center;
        }
        
        .info-label i {
            margin-left: 8px;
            color: #667eea;
        }
        
        .info-value {
            font-weight: 600;
            color: #333;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto 15px;
            color: white;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #333;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .icon-invoices { background: linear-gradient(45deg, #28a745, #20c997); }
        .icon-orders { background: linear-gradient(45deg, #17a2b8, #138496); }
        .icon-payments { background: linear-gradient(45deg, #ffc107, #e0a800); }
        .icon-returns { background: linear-gradient(45deg, #dc3545, #c82333); }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn-action {
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-edit {
            background: linear-gradient(45deg, #ffc107, #ff8f00);
            color: white;
        }
        
        .btn-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 193, 7, 0.3);
            color: white;
        }
        
        .btn-invoice {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .btn-invoice:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .btn-payment {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
        }
        
        .btn-payment:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(23, 162, 184, 0.3);
            color: white;
        }
        
        .btn-back {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
        }
        
        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(108, 117, 125, 0.3);
            color: white;
        }
        
        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .recent-transactions {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .transaction-item:last-child {
            border-bottom: none;
        }
        
        .transaction-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .transaction-details h6 {
            margin: 0;
            font-weight: 600;
            color: #333;
        }
        
        .transaction-details small {
            color: #6c757d;
        }
        
        .transaction-amount {
            font-weight: 700;
            font-size: 1.1rem;
        }
        
        .amount-positive { color: #28a745; }
        .amount-negative { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-person"></i>
                        تفاصيل العميل
                    </h1>
                    <p class="mb-0">عرض تفصيلي لبيانات العميل والمعاملات</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- بطاقة العميل الرئيسية -->
        <div class="customer-card">
            <div class="row">
                <div class="col-md-4 text-center">
                    <div class="customer-avatar">
                        <i class="bi bi-person"></i>
                    </div>
                    <div class="customer-name">{{ customer.name }}</div>
                    <div class="customer-type">
                        {% if customer.customer_type == 'retail' %}
                            عميل تجزئة
                        {% elif customer.customer_type == 'wholesale' %}
                            عميل جملة
                        {% elif customer.customer_type == 'credit' %}
                            عميل آجل
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="balance-card {% if customer.current_balance < 0 %}negative{% endif %}">
                        <div class="balance-title">الرصيد الحالي</div>
                        <div class="balance-value">{{ customer.current_balance|floatformat:2 }}</div>
                        <div>جنيه مصري</div>
                    </div>
                    
                    {% if customer.credit_limit %}
                        <div class="balance-card" style="background: linear-gradient(135deg, #6f42c1, #5a32a3);">
                            <div class="balance-title">حد الائتمان</div>
                            <div class="balance-value">{{ customer.credit_limit|floatformat:2 }}</div>
                            <div>جنيه مصري</div>
                        </div>
                    {% endif %}
                </div>
                
                <div class="col-md-4">
                    <div class="info-section">
                        <h5 class="section-title">معلومات الاتصال</h5>
                        
                        {% if customer.phone %}
                            <div class="info-row">
                                <div class="info-label">
                                    <i class="bi bi-telephone"></i>
                                    الهاتف:
                                </div>
                                <div class="info-value">{{ customer.phone }}</div>
                            </div>
                        {% endif %}
                        
                        {% if customer.email %}
                            <div class="info-row">
                                <div class="info-label">
                                    <i class="bi bi-envelope"></i>
                                    البريد الإلكتروني:
                                </div>
                                <div class="info-value">{{ customer.email }}</div>
                            </div>
                        {% endif %}
                        
                        {% if customer.address %}
                            <div class="info-row">
                                <div class="info-label">
                                    <i class="bi bi-geo-alt"></i>
                                    العنوان:
                                </div>
                                <div class="info-value">{{ customer.address }}</div>
                            </div>
                        {% endif %}
                        
                        <div class="info-row">
                            <div class="info-label">
                                <i class="bi bi-calendar-plus"></i>
                                تاريخ الإضافة:
                            </div>
                            <div class="info-value">{{ customer.created_at|date:"Y/m/d" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات العميل -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon icon-invoices">
                    <i class="bi bi-receipt"></i>
                </div>
                <div class="stat-number">{{ customer_stats.total_invoices }}</div>
                <div class="stat-label">إجمالي الفواتير</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-orders">
                    <i class="bi bi-cart"></i>
                </div>
                <div class="stat-number">{{ customer_stats.total_orders }}</div>
                <div class="stat-label">إجمالي الطلبات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-payments">
                    <i class="bi bi-cash"></i>
                </div>
                <div class="stat-number">{{ customer_stats.total_payments|floatformat:0 }}</div>
                <div class="stat-label">إجمالي المدفوعات (ج.م)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-returns">
                    <i class="bi bi-arrow-return-left"></i>
                </div>
                <div class="stat-number">{{ customer_stats.total_returns }}</div>
                <div class="stat-label">إجمالي المرتجعات</div>
            </div>
        </div>

        <!-- رسم بياني للمعاملات -->
        <div class="chart-container">
            <h3 class="section-title">تطور المعاملات (آخر 12 شهر)</h3>
            <canvas id="transactionsChart" width="400" height="200"></canvas>
        </div>

        <!-- المعاملات الأخيرة -->
        <div class="recent-transactions">
            <h3 class="section-title">المعاملات الأخيرة</h3>
            
            {% if recent_transactions %}
                {% for transaction in recent_transactions %}
                    <div class="transaction-item">
                        <div class="transaction-info">
                            <div class="transaction-icon" style="background: {% if transaction.type == 'invoice' %}#28a745{% elif transaction.type == 'payment' %}#17a2b8{% elif transaction.type == 'return' %}#dc3545{% endif %};">
                                <i class="bi bi-{% if transaction.type == 'invoice' %}receipt{% elif transaction.type == 'payment' %}cash{% elif transaction.type == 'return' %}arrow-return-left{% endif %}"></i>
                            </div>
                            <div class="transaction-details">
                                <h6>{{ transaction.description }}</h6>
                                <small>{{ transaction.date|date:"Y/m/d H:i" }}</small>
                            </div>
                        </div>
                        <div class="transaction-amount {% if transaction.amount > 0 %}amount-positive{% else %}amount-negative{% endif %}">
                            {{ transaction.amount|floatformat:2 }} ج.م
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center text-muted py-4">
                    <i class="bi bi-inbox" style="font-size: 3rem; opacity: 0.5;"></i>
                    <p class="mt-2">لا توجد معاملات حديثة</p>
                </div>
            {% endif %}
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <a href="{% url 'sales:customer_edit' customer.pk %}" class="btn-action btn-edit">
                <i class="bi bi-pencil"></i>
                تعديل العميل
            </a>
            
            <a href="{% url 'sales:invoice_create' %}?customer={{ customer.pk }}" class="btn-action btn-invoice">
                <i class="bi bi-receipt"></i>
                إنشاء فاتورة
            </a>
            
            {% if customer.customer_type == 'credit' %}
                <a href="{% url 'sales:payment_create' %}?customer={{ customer.pk }}" class="btn-action btn-payment">
                    <i class="bi bi-cash"></i>
                    تسجيل دفعة
                </a>
            {% endif %}
            
            <a href="{% url 'sales:customer_list' %}" class="btn-action btn-back">
                <i class="bi bi-arrow-left"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .info-section');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });

            // رسم بياني للمعاملات
            const ctx = document.getElementById('transactionsChart').getContext('2d');
            const transactionsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                    datasets: [
                        {
                            label: 'المبيعات (ج.م)',
                            data: {{ monthly_sales|safe }},
                            borderColor: 'rgb(102, 126, 234)',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'المدفوعات (ج.م)',
                            data: {{ monthly_payments|safe }},
                            borderColor: 'rgb(23, 162, 184)',
                            backgroundColor: 'rgba(23, 162, 184, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ج.م';
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
