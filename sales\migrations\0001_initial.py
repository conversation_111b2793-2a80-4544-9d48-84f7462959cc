# Generated by Django 5.2.4 on 2025-07-12 22:37

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم العميل')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='حد الائتمان')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'العملاء',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود المنتج')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر التكلفة')),
                ('stock_quantity', models.IntegerField(default=0, verbose_name='الكمية المتاحة')),
                ('unit', models.CharField(default='قطعة', max_length=20, verbose_name='الوحدة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SalesInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الفاتورة')),
                ('invoice_date', models.DateField(verbose_name='تاريخ الفاتورة')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسلة'), ('paid', 'مدفوعة'), ('overdue', 'متأخرة'), ('cancelled', 'ملغية')], default='draft', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=15, max_digits=5, verbose_name='نسبة الضريبة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.customer', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'فاتورة بيع',
                'verbose_name_plural': 'فواتير البيع',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SalesInvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.salesinvoice', verbose_name='فاتورة البيع')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر فاتورة بيع',
                'verbose_name_plural': 'عناصر فواتير البيع',
            },
        ),
        migrations.CreateModel(
            name='SalesOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الطلب')),
                ('order_date', models.DateField(verbose_name='تاريخ الطلب')),
                ('delivery_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('confirmed', 'مؤكد'), ('shipped', 'تم الشحن'), ('delivered', 'تم التسليم'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=15, max_digits=5, verbose_name='نسبة الضريبة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.customer', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'أمر بيع',
                'verbose_name_plural': 'أوامر البيع',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='salesinvoice',
            name='order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.salesorder', verbose_name='أمر البيع'),
        ),
        migrations.CreateModel(
            name='SalesOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.salesorder', verbose_name='أمر البيع')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='sales.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر أمر بيع',
                'verbose_name_plural': 'عناصر أوامر البيع',
            },
        ),
    ]
