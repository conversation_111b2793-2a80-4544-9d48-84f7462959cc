#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from django.contrib.auth.models import User
from search.models import PopularSearch, SearchSuggestion

def create_sample_search_data():
    """إنشاء بيانات تجريبية للبحث"""
    
    print("إنشاء البحثات الشائعة...")
    
    # البحثات الشائعة
    popular_searches = [
        {'query': 'المنتجات', 'count': 45},
        {'query': 'العملاء', 'count': 38},
        {'query': 'الفواتير', 'count': 32},
        {'query': 'لابتوب', 'count': 28},
        {'query': 'أحمد محمد', 'count': 25},
        {'query': 'طابعة', 'count': 22},
        {'query': 'فاتورة 2024', 'count': 20},
        {'query': 'مخزون', 'count': 18},
        {'query': 'تقارير', 'count': 15},
        {'query': 'إعدادات', 'count': 12}
    ]
    
    for search_data in popular_searches:
        popular, created = PopularSearch.objects.get_or_create(
            query=search_data['query'],
            defaults={'search_count': search_data['count']}
        )
        if created:
            print(f"✅ تم إنشاء بحث شائع: {popular.query}")
        else:
            popular.search_count = search_data['count']
            popular.save()
            print(f"🔄 تم تحديث بحث شائع: {popular.query}")
    
    print("\nإنشاء اقتراحات البحث...")
    
    # اقتراحات البحث
    search_suggestions = [
        {'keyword': 'منتج', 'suggestion': 'المنتجات', 'category': 'products', 'priority': 10},
        {'keyword': 'عميل', 'suggestion': 'العملاء', 'category': 'customers', 'priority': 9},
        {'keyword': 'فاتورة', 'suggestion': 'الفواتير', 'category': 'invoices', 'priority': 8},
        {'keyword': 'مستخدم', 'suggestion': 'المستخدمين', 'category': 'users', 'priority': 7},
        {'keyword': 'رسالة', 'suggestion': 'الرسائل', 'category': 'messages', 'priority': 6},
        {'keyword': 'إشعار', 'suggestion': 'الإشعارات', 'category': 'notifications', 'priority': 5},
        {'keyword': 'تقرير', 'suggestion': 'التقارير', 'category': 'reports', 'priority': 4},
        {'keyword': 'إعداد', 'suggestion': 'الإعدادات', 'category': 'settings', 'priority': 3},
        {'keyword': 'مخزن', 'suggestion': 'إدارة المخازن', 'category': 'warehouses', 'priority': 2},
        {'keyword': 'حساب', 'suggestion': 'الحسابات العامة', 'category': 'accounting', 'priority': 1}
    ]
    
    for suggestion_data in search_suggestions:
        suggestion, created = SearchSuggestion.objects.get_or_create(
            keyword=suggestion_data['keyword'],
            defaults={
                'suggestion': suggestion_data['suggestion'],
                'category': suggestion_data['category'],
                'priority': suggestion_data['priority']
            }
        )
        if created:
            print(f"✅ تم إنشاء اقتراح: {suggestion.keyword} -> {suggestion.suggestion}")
        else:
            print(f"🔄 اقتراح موجود: {suggestion.keyword} -> {suggestion.suggestion}")
    
    print(f"\n🎉 تم إنشاء {len(popular_searches)} بحث شائع و {len(search_suggestions)} اقتراح بحث!")

if __name__ == '__main__':
    create_sample_search_data()
