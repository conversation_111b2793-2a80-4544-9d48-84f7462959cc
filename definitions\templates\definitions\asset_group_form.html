{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if action == 'edit' %}
تعديل مجموعة الأصول - {{ asset_group.name }}
{% else %}
إضافة مجموعة أصول جديدة
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    body {
        background: #ffffff;
        min-height: 100vh;
    }

    .form-container {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        color: #333;
    }

    .form-title {
        font-size: 2rem;
        font-weight: 800;
        color: #333;
        margin-bottom: 2rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .form-section {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-label {
        color: #333;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .required::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .form-control, .form-select {
        background: #ffffff;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 0.75rem 1rem;
        color: #333;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .form-control:focus, .form-select:focus {
        background: #ffffff;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        color: #333;
        outline: none;
    }

    .btn {
        padding: 0.75rem 2rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .btn-primary {
        background: #007bff;
        color: white;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        color: white;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e0e0e0;
    }

    .form-check {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .form-check-input {
        background: #ffffff;
        border: 1px solid #ddd;
        margin-right: 0.5rem;
    }

    .form-check-input:checked {
        background: #007bff;
        border-color: #007bff;
    }

    .form-check-label {
        color: #333;
        font-weight: 600;
    }

    .form-text {
        color: #666;
        font-size: 0.8rem;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-container">
                <h1 class="form-title">
                    <i class="bi bi-collection"></i>
                    {% if action == 'edit' %}
                    تعديل مجموعة الأصول
                    {% else %}
                    إضافة مجموعة أصول جديدة
                    {% endif %}
                </h1>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" id="assetGroupForm">
                    {% csrf_token %}
                    
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            المعلومات الأساسية
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="code" class="form-label required">
                                    <i class="bi bi-hash"></i>كود مجموعة الأصول
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="code" 
                                       name="code" 
                                       value="{% if action == 'edit' %}{{ asset_group.code }}{% else %}{{ form_data.code|default:'' }}{% endif %}"
                                       placeholder="مثال: COMP، FURN، VEHI"
                                       maxlength="10"
                                       style="text-transform: uppercase;"
                                       required>
                                <div class="form-text">كود مختصر لمجموعة الأصول</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="name" class="form-label required">
                                    <i class="bi bi-collection"></i>اسم مجموعة الأصول
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       value="{% if action == 'edit' %}{{ asset_group.name }}{% else %}{{ form_data.name|default:'' }}{% endif %}"
                                       placeholder="الاسم بالعربية"
                                       required>
                                <div class="form-text">اسم مجموعة الأصول بالعربية</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="name_en" class="form-label">
                                    <i class="bi bi-globe"></i>الاسم بالإنجليزية
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name_en" 
                                       name="name_en" 
                                       value="{% if action == 'edit' %}{{ asset_group.name_en }}{% else %}{{ form_data.name_en|default:'' }}{% endif %}"
                                       placeholder="Name in English">
                                <div class="form-text">اسم مجموعة الأصول بالإنجليزية (اختياري)</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="parent" class="form-label">
                                    <i class="bi bi-diagram-3"></i>المجموعة الأب
                                </label>
                                <select class="form-select" id="parent" name="parent">
                                    <option value="">-- اختر المجموعة الأب (اختياري) --</option>
                                    {% for group in asset_groups %}
                                        {% if action != 'edit' or group.id != asset_group.id %}
                                        <option value="{{ group.id }}"
                                                {% if action == 'edit' and asset_group.parent and asset_group.parent.id == group.id %}selected
                                                {% elif action != 'edit' and form_data.parent == group.id|stringformat:"s" %}selected{% endif %}>
                                            {{ group.code }} - {{ group.name }}
                                        </option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                                <div class="form-text">المجموعة الرئيسية التي تنتمي إليها هذه المجموعة</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="asset_type" class="form-label required">
                                    <i class="bi bi-tags"></i>نوع الأصل
                                </label>
                                <select class="form-select" id="asset_type" name="asset_type" required>
                                    <option value="">-- اختر نوع الأصل --</option>
                                    <option value="fixed" {% if action == 'edit' and asset_group.asset_type == 'fixed' %}selected
                                                          {% elif action != 'edit' and form_data.asset_type == 'fixed' %}selected{% endif %}>
                                        أصول ثابتة
                                    </option>
                                    <option value="current" {% if action == 'edit' and asset_group.asset_type == 'current' %}selected
                                                            {% elif action != 'edit' and form_data.asset_type == 'current' %}selected{% endif %}>
                                        أصول متداولة
                                    </option>
                                    <option value="intangible" {% if action == 'edit' and asset_group.asset_type == 'intangible' %}selected
                                                               {% elif action != 'edit' and form_data.asset_type == 'intangible' %}selected{% endif %}>
                                        أصول غير ملموسة
                                    </option>
                                    <option value="investment" {% if action == 'edit' and asset_group.asset_type == 'investment' %}selected
                                                               {% elif action != 'edit' and form_data.asset_type == 'investment' %}selected{% endif %}>
                                        استثمارات
                                    </option>
                                </select>
                                <div class="form-text">تصنيف نوع الأصل</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="description" class="form-label">
                                    <i class="bi bi-file-text"></i>وصف مجموعة الأصول
                                </label>
                                <textarea class="form-control"
                                          id="description"
                                          name="description"
                                          rows="3"
                                          placeholder="وصف تفصيلي لمجموعة الأصول...">{% if action == 'edit' %}{{ asset_group.description }}{% else %}{{ form_data.description|default:'' }}{% endif %}</textarea>
                                <div class="form-text">وصف تفصيلي لمجموعة الأصول (اختياري)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Depreciation Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-graph-down"></i>
                            معلومات الإهلاك
                        </h3>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="depreciation_method" class="form-label required">
                                    <i class="bi bi-calculator"></i>طريقة الإهلاك
                                </label>
                                <select class="form-select" id="depreciation_method" name="depreciation_method" required>
                                    <option value="">-- اختر طريقة الإهلاك --</option>
                                    <option value="straight_line" {% if action == 'edit' and asset_group.depreciation_method == 'straight_line' %}selected
                                                                  {% elif action != 'edit' and form_data.depreciation_method == 'straight_line' %}selected
                                                                  {% elif action != 'edit' %}selected{% endif %}>
                                        القسط الثابت
                                    </option>
                                    <option value="declining_balance" {% if action == 'edit' and asset_group.depreciation_method == 'declining_balance' %}selected
                                                                      {% elif action != 'edit' and form_data.depreciation_method == 'declining_balance' %}selected{% endif %}>
                                        الرصيد المتناقص
                                    </option>
                                    <option value="sum_of_years" {% if action == 'edit' and asset_group.depreciation_method == 'sum_of_years' %}selected
                                                                 {% elif action != 'edit' and form_data.depreciation_method == 'sum_of_years' %}selected{% endif %}>
                                        مجموع سنوات الخدمة
                                    </option>
                                </select>
                                <div class="form-text">طريقة حساب الإهلاك</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="depreciation_rate" class="form-label required">
                                    <i class="bi bi-percent"></i>معدل الإهلاك السنوي (%)
                                </label>
                                <input type="number"
                                       class="form-control"
                                       id="depreciation_rate"
                                       name="depreciation_rate"
                                       value="{% if action == 'edit' %}{{ asset_group.depreciation_rate }}{% else %}{{ form_data.depreciation_rate|default:'10.00' }}{% endif %}"
                                       step="0.01"
                                       min="0"
                                       max="100"
                                       placeholder="10.00"
                                       required>
                                <div class="form-text">معدل الإهلاك السنوي كنسبة مئوية</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="useful_life_years" class="form-label required">
                                    <i class="bi bi-calendar-range"></i>العمر الافتراضي (سنوات)
                                </label>
                                <input type="number"
                                       class="form-control"
                                       id="useful_life_years"
                                       name="useful_life_years"
                                       value="{% if action == 'edit' %}{{ asset_group.useful_life_years }}{% else %}{{ form_data.useful_life_years|default:'5' }}{% endif %}"
                                       min="1"
                                       max="100"
                                       placeholder="5"
                                       required>
                                <div class="form-text">العمر الافتراضي للأصل بالسنوات</div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-gear"></i>
                            معلومات إضافية
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active"
                                           {% if action == 'edit' and asset_group.is_active %}checked
                                           {% elif action != 'edit' %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        <i class="bi bi-toggle-on me-1"></i>مجموعة نشطة
                                    </label>
                                    <div class="form-text">تفعيل أو إلغاء تفعيل مجموعة الأصول</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="notes" class="form-label">
                                    <i class="bi bi-journal-text"></i>ملاحظات
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3"
                                          placeholder="ملاحظات إضافية...">{% if action == 'edit' %}{{ asset_group.notes }}{% else %}{{ form_data.notes|default:'' }}{% endif %}</textarea>
                                <div class="form-text">ملاحظات إضافية (اختياري)</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <div>
                            <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary me-2">
                                <i class="bi bi-house me-2"></i>التعريفات
                            </a>
                            <a href="{% url 'definitions:asset_group_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-2"></i>قائمة المجموعات
                            </a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" onclick="this.disabled=true; this.innerHTML='<span class=\'spinner-border spinner-border-sm me-2\'></span>جاري الحفظ...'; this.form.submit();">
                            <i class="bi bi-check-circle me-2"></i>
                            {% if action == 'edit' %}
                            حفظ التعديلات
                            {% else %}
                            إضافة مجموعة الأصول
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل صفحة مجموعة الأصول');

    // تحويل الكود إلى أحرف كبيرة تلقائياً
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }

    // حساب معدل الإهلاك تلقائياً بناءً على العمر الافتراضي
    const usefulLifeInput = document.getElementById('useful_life_years');
    const depreciationRateInput = document.getElementById('depreciation_rate');
    const depreciationMethodSelect = document.getElementById('depreciation_method');

    function calculateDepreciationRate() {
        const usefulLife = parseInt(usefulLifeInput.value);
        const method = depreciationMethodSelect.value;

        if (usefulLife && usefulLife > 0 && method === 'straight_line') {
            const rate = (100 / usefulLife).toFixed(2);
            depreciationRateInput.value = rate;
        }
    }

    if (usefulLifeInput && depreciationRateInput && depreciationMethodSelect) {
        usefulLifeInput.addEventListener('input', calculateDepreciationRate);
        depreciationMethodSelect.addEventListener('change', function() {
            if (this.value === 'straight_line') {
                calculateDepreciationRate();
            }
        });
    }

    // تحديث معدل الإهلاك عند تحميل الصفحة إذا كانت طريقة القسط الثابت محددة
    if (depreciationMethodSelect && depreciationMethodSelect.value === 'straight_line') {
        calculateDepreciationRate();
    }
});
</script>
{% endblock %}
