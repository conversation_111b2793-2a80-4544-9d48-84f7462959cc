{% extends 'base.html' %}
{% load static %}

{% block title %}جرد المخزون{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .count-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .count-form {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .form-control, .form-select {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .current-stock {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        border-left: 4px solid #667eea;
    }

    .stock-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }

    .stock-item:last-child {
        margin-bottom: 0;
    }

    .info-section {
        background: #e3f2fd;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #2196f3;
    }

    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="count-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="bi bi-clipboard-check me-2"></i>جرد المخزون</h1>
                <p class="mb-0">نظام جرد المخزون الاحترافي</p>
            </div>
            <div>
                <a href="{% url 'warehouses:count_history' %}" class="btn btn-light">
                    <i class="bi bi-clock-history me-2"></i>سجل الجرد
                </a>
                <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-light">
                    <i class="bi bi-arrow-left me-2"></i>قائمة المخزون
                </a>
            </div>
        </div>
    </div>

    <!-- Info Section -->
    <div class="info-section">
        <h6><i class="bi bi-info-circle me-2"></i>تعليمات الجرد</h6>
        <p class="mb-0">اختر المخزن والمنتج، ثم أدخل الكمية الفعلية الموجودة واضغط تأكيد الجرد</p>
    </div>

    <!-- جرد مبسط -->
    <div class="count-form">
        <h5 class="mb-4"><i class="bi bi-clipboard-check me-2"></i>جرد سريع</h5>

        <form method="post" action="{% url 'warehouses:stock_count' %}" onsubmit="console.log('Form submitting...')">
            {% csrf_token %}

            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">المخزن</label>
                    <select name="warehouse" class="form-select" required>
                        <option value="">اختر المخزن</option>
                        {% for warehouse in warehouses %}
                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-4">
                    <label class="form-label">المنتج</label>
                    <select name="product" class="form-select" required>
                        <option value="">اختر المنتج</option>
                        {% for product in products %}
                        <option value="{{ product.id }}">{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-4">
                    <label class="form-label">الكمية الفعلية</label>
                    <input type="number" name="counted_quantity" class="form-control"
                           step="0.001" min="0" placeholder="الكمية المعدودة" required>
                </div>

                <div class="col-12">
                    <textarea name="notes" class="form-control" rows="2"
                              placeholder="ملاحظات (اختياري)"></textarea>
                </div>
            </div>

            <div class="text-center mt-4">
                <input type="submit" class="btn btn-primary" value="تأكيد الجرد">
                <a href="{% url 'warehouses:count_history' %}" class="btn btn-info ms-2">
                    <i class="bi bi-clock-history me-1"></i>سجل الجرد
                </a>
                <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-secondary ms-2">
                    <i class="bi bi-arrow-left me-1"></i>عودة
                </a>
            </div>
        </form>
    </div>
</div>


{% endblock %}
