from django.core.management.base import BaseCommand
from django.utils import timezone
from warehouses.views import check_and_create_inventory_alerts


class Command(BaseCommand):
    help = 'فحص تنبيهات المخزون وإنشاء الإشعارات'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='عرض تفاصيل أكثر',
        )

    def handle(self, *args, **options):
        verbose = options['verbose']
        
        if verbose:
            self.stdout.write(
                self.style.SUCCESS(f'بدء فحص تنبيهات المخزون في {timezone.now()}')
            )
        
        try:
            results = check_and_create_inventory_alerts()
            
            if results:
                if verbose:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'تم فحص التنبيهات بنجاح:\n'
                            f'- مخزون نافد: {results["out_of_stock"]}\n'
                            f'- مخزون منخفض: {results["low_stock"]}\n'
                            f'- مخزون زائد: {results["overstock"]}\n'
                            f'- إشعارات مرسلة لـ: {results["notifications_sent"]} مستخدم'
                        )
                    )
                else:
                    total_alerts = results["out_of_stock"] + results["low_stock"] + results["overstock"]
                    if total_alerts > 0:
                        self.stdout.write(
                            self.style.WARNING(f'تم العثور على {total_alerts} تنبيه مخزون')
                        )
                    else:
                        self.stdout.write(
                            self.style.SUCCESS('لا توجد تنبيهات مخزون')
                        )
            else:
                self.stdout.write(
                    self.style.ERROR('فشل في فحص تنبيهات المخزون')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في فحص تنبيهات المخزون: {e}')
            )
