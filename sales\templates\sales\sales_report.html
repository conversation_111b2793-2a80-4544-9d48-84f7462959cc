{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير المبيعات{% endblock %}

{% block extra_css %}
<style>
    .report-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .filter-section {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border-left: 5px solid;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }
    
    .stats-card.total { border-left-color: #007bff; }
    .stats-card.paid { border-left-color: #28a745; }
    .stats-card.pending { border-left-color: #ffc107; }
    .stats-card.count { border-left-color: #6f42c1; }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 1rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .chart-container {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        height: 400px;
    }
    
    .data-table {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .table-responsive {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .table thead th {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        border: none;
        font-weight: 600;
        text-align: center;
        padding: 15px;
    }
    
    .table tbody td {
        padding: 12px 15px;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;
    }
    
    .table tbody tr:hover {
        background: #f8f9fa;
    }
    
    .btn-filter {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .section-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 25px;
        color: #333;
        border-bottom: 3px solid #28a745;
        padding-bottom: 10px;
    }
    
    .export-buttons {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .btn-export {
        background: linear-gradient(45deg, #6f42c1, #5a2d91);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-weight: 600;
        margin: 5px;
        transition: all 0.3s;
    }
    
    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
        color: white;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-paid { background: #d4edda; color: #155724; }
    .status-sent { background: #d1ecf1; color: #0c5460; }
    .status-draft { background: #e2e3e5; color: #383d41; }
    .status-overdue { background: #f8d7da; color: #721c24; }
    
    .period-display {
        background: rgba(255, 255, 255, 0.2);
        padding: 15px 20px;
        border-radius: 15px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس التقرير -->
    <div class="report-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-graph-up-arrow"></i>
                    تقرير المبيعات التفصيلي
                </h1>
                <p class="mb-0">تحليل شامل للمبيعات والأداء خلال الفترة المحددة</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="period-display">
                    <h6 class="mb-1">فترة التقرير</h6>
                    <strong>{{ date_from }} إلى {{ date_to }}</strong>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم الفلترة -->
    <div class="filter-section">
        <h4 class="section-title">فلترة البيانات</h4>
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from|date:'Y-m-d' }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to|date:'Y-m-d' }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">المندوب</label>
                <select name="representative" class="form-select">
                    <option value="">جميع المناديب</option>
                    {% for rep in representatives %}
                        <option value="{{ rep.id }}" {% if representative == rep.id|stringformat:"s" %}selected{% endif %}>
                            {{ rep.full_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">نوع العميل</label>
                <select name="customer_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in customer_types %}
                        <option value="{{ value }}" {% if customer_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-filter w-100">
                    <i class="bi bi-funnel"></i>
                    تطبيق الفلتر
                </button>
            </div>
        </form>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card total">
                <div class="stats-number text-primary">{{ total_amount|floatformat:0 }}</div>
                <div class="stats-label">إجمالي المبيعات (ج.م)</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card paid">
                <div class="stats-number text-success">{{ paid_amount|floatformat:0 }}</div>
                <div class="stats-label">المبيعات المحصلة (ج.م)</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card pending">
                <div class="stats-number text-warning">{{ pending_amount|floatformat:0 }}</div>
                <div class="stats-label">المبيعات المعلقة (ج.م)</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card count">
                <div class="stats-number text-purple">{{ total_invoices }}</div>
                <div class="stats-label">عدد الفواتير</div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <h5 class="text-center mb-4">المبيعات حسب المندوب</h5>
                <canvas id="representativeChart"></canvas>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <h5 class="text-center mb-4">المبيعات حسب نوع العميل</h5>
                <canvas id="customerTypeChart"></canvas>
            </div>
        </div>
    </div>

    <!-- أزرار التصدير -->
    <div class="export-buttons">
        <button class="btn btn-export" onclick="exportToPDF()">
            <i class="bi bi-file-earmark-pdf"></i>
            تصدير PDF
        </button>
        <button class="btn btn-export" onclick="exportToExcel()">
            <i class="bi bi-file-earmark-excel"></i>
            تصدير Excel
        </button>
        <button class="btn btn-export" onclick="window.print()">
            <i class="bi bi-printer"></i>
            طباعة
        </button>
    </div>

    <!-- جدول البيانات -->
    <div class="data-table">
        <h4 class="section-title">تفاصيل الفواتير</h4>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>المندوب</th>
                        <th>النوع</th>
                        <th>المبلغ</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                        <tr>
                            <td><strong>{{ invoice.invoice_number }}</strong></td>
                            <td>{{ invoice.invoice_date }}</td>
                            <td>{{ invoice.customer.name }}</td>
                            <td>{{ invoice.representative.full_name|default:"-" }}</td>
                            <td>{{ invoice.get_invoice_type_display }}</td>
                            <td><strong>{{ invoice.total_amount }} ج.م</strong></td>
                            <td>
                                <span class="status-badge status-{{ invoice.status }}">
                                    {{ invoice.get_status_display }}
                                </span>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="7" class="text-center text-muted">لا توجد فواتير في هذه الفترة</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // رسم بياني للمناديب
    const repCtx = document.getElementById('representativeChart').getContext('2d');
    const repData = {
        labels: [{% for stat in rep_stats %}'{{ stat.representative__user__first_name }} {{ stat.representative__user__last_name }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'المبيعات (ج.م)',
            data: [{% for stat in rep_stats %}{{ stat.total_sales }}{% if not forloop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
            ]
        }]
    };
    
    new Chart(repCtx, {
        type: 'doughnut',
        data: repData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // رسم بياني لأنواع العملاء
    const customerCtx = document.getElementById('customerTypeChart').getContext('2d');
    const customerData = {
        labels: [{% for stat in customer_type_stats %}'{{ stat.customer__customer_type }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'المبيعات (ج.م)',
            data: [{% for stat in customer_type_stats %}{{ stat.total_sales }}{% if not forloop.last %},{% endif %}{% endfor %}],
            backgroundColor: ['#28a745', '#007bff', '#ffc107', '#dc3545']
        }]
    };
    
    new Chart(customerCtx, {
        type: 'bar',
        data: customerData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});

function exportToPDF() {
    alert('سيتم تطوير وظيفة تصدير PDF قريباً');
}

function exportToExcel() {
    alert('سيتم تطوير وظيفة تصدير Excel قريباً');
}
</script>
{% endblock %}
