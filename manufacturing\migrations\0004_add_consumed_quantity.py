# Generated manually to add consumed_quantity field

from django.db import migrations, models
from decimal import Decimal


class Migration(migrations.Migration):

    dependencies = [
        ('manufacturing', '0003_manufacturingorderrawmaterial_warehouse'),
    ]

    operations = [
        migrations.AddField(
            model_name='manufacturingorderrawmaterial',
            name='consumed_quantity',
            field=models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='الكمية المستهلكة'),
        ),
    ]
