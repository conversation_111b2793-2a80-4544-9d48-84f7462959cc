from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, timedelta

from sales.models import (
    Customer, Product, SalesRepresentative, Vehicle, SalesOrder, SalesOrderItem,
    SalesInvoice, SalesInvoiceItem, VehicleLoading, VehicleLoadingItem,
    DailyMovement, SalesReturn, SalesReturnItem, DispensePermission, DispensePermissionItem,
    Inventory, InventoryItem, ProductMovement, Payment
)


class CustomerModelTest(TestCase):
    """اختبارات نموذج العميل"""
    
    def setUp(self):
        self.customer_data = {
            'name': 'عميل تجريبي',
            'customer_type': 'retail',
            'phone': '01234567890',
            'email': '<EMAIL>',
            'address': 'عنوان تجريبي',
            'credit_limit': Decimal('10000.00'),
            'credit_days': 30,
        }
    
    def test_customer_creation(self):
        """اختبار إنشاء عميل جديد"""
        customer = Customer.objects.create(**self.customer_data)
        self.assertEqual(customer.name, 'عميل تجريبي')
        self.assertEqual(customer.customer_type, 'retail')
        self.assertEqual(customer.current_balance, Decimal('0.00'))
        self.assertTrue(customer.is_active)
    
    def test_customer_str_method(self):
        """اختبار طريقة __str__ للعميل"""
        customer = Customer.objects.create(**self.customer_data)
        self.assertEqual(str(customer), 'عميل تجريبي')
    
    def test_customer_credit_limit_validation(self):
        """اختبار التحقق من حد الائتمان"""
        customer = Customer.objects.create(**self.customer_data)
        customer.current_balance = Decimal('15000.00')  # أكبر من حد الائتمان
        
        with self.assertRaises(ValidationError):
            customer.clean()
    
    def test_customer_phone_validation(self):
        """اختبار التحقق من رقم الهاتف"""
        invalid_data = self.customer_data.copy()
        invalid_data['phone'] = '123'  # رقم قصير
        
        customer = Customer(**invalid_data)
        with self.assertRaises(ValidationError):
            customer.clean()


class ProductModelTest(TestCase):
    """اختبارات نموذج المنتج"""
    
    def setUp(self):
        self.product_data = {
            'name': 'منتج تجريبي',
            'code': 'PROD001',
            'category': 'فئة تجريبية',
            'unit': 'قطعة',
            'cost_price': Decimal('50.00'),
            'unit_price_retail': Decimal('75.00'),
            'unit_price_wholesale': Decimal('65.00'),
            'stock_quantity': Decimal('100.00'),
            'min_stock_level': Decimal('10.00'),
        }
    
    def test_product_creation(self):
        """اختبار إنشاء منتج جديد"""
        product = Product.objects.create(**self.product_data)
        self.assertEqual(product.name, 'منتج تجريبي')
        self.assertEqual(product.code, 'PROD001')
        self.assertTrue(product.is_active)
    
    def test_product_str_method(self):
        """اختبار طريقة __str__ للمنتج"""
        product = Product.objects.create(**self.product_data)
        self.assertEqual(str(product), 'منتج تجريبي (PROD001)')
    
    def test_get_price_for_customer_type(self):
        """اختبار الحصول على السعر حسب نوع العميل"""
        product = Product.objects.create(**self.product_data)
        
        self.assertEqual(product.get_price_for_customer_type('retail'), Decimal('75.00'))
        self.assertEqual(product.get_price_for_customer_type('wholesale'), Decimal('65.00'))
        self.assertEqual(product.get_price_for_customer_type('credit'), Decimal('75.00'))
    
    def test_product_code_uniqueness(self):
        """اختبار تفرد كود المنتج"""
        Product.objects.create(**self.product_data)
        
        with self.assertRaises(Exception):  # IntegrityError
            Product.objects.create(**self.product_data)


class SalesRepresentativeModelTest(TestCase):
    """اختبارات نموذج المندوب"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='rep1',
            first_name='أحمد',
            last_name='محمد',
            email='<EMAIL>'
        )
        
        self.rep_data = {
            'user': self.user,
            'employee_id': 'EMP001',
            'phone': '01234567890',
            'address': 'عنوان المندوب',
            'hire_date': date.today(),
            'target_monthly': Decimal('50000.00'),
            'commission_rate': Decimal('2.5'),
        }
    
    def test_representative_creation(self):
        """اختبار إنشاء مندوب جديد"""
        rep = SalesRepresentative.objects.create(**self.rep_data)
        self.assertEqual(rep.employee_id, 'EMP001')
        self.assertTrue(rep.is_active)
    
    def test_representative_full_name(self):
        """اختبار الحصول على الاسم الكامل"""
        rep = SalesRepresentative.objects.create(**self.rep_data)
        self.assertEqual(rep.full_name, 'أحمد محمد')
    
    def test_representative_str_method(self):
        """اختبار طريقة __str__ للمندوب"""
        rep = SalesRepresentative.objects.create(**self.rep_data)
        self.assertEqual(str(rep), 'أحمد محمد (EMP001)')


class SalesInvoiceModelTest(TestCase):
    """اختبارات نموذج فاتورة البيع"""
    
    def setUp(self):
        self.user = User.objects.create_user(username='testuser')
        
        self.customer = Customer.objects.create(
            name='عميل تجريبي',
            customer_type='retail'
        )
        
        self.rep_user = User.objects.create_user(
            username='rep1',
            first_name='أحمد',
            last_name='محمد'
        )
        
        self.representative = SalesRepresentative.objects.create(
            user=self.rep_user,
            employee_id='EMP001'
        )
        
        self.product = Product.objects.create(
            name='منتج تجريبي',
            code='PROD001',
            unit_price_retail=Decimal('100.00'),
            stock_quantity=Decimal('50.00')
        )
        
        self.invoice_data = {
            'customer': self.customer,
            'representative': self.representative,
            'invoice_date': date.today(),
            'due_date': date.today() + timedelta(days=30),
            'invoice_type': 'retail',
            'payment_method': 'cash',
            'status': 'draft',
            'created_by': self.user,
        }
    
    def test_invoice_creation(self):
        """اختبار إنشاء فاتورة جديدة"""
        invoice = SalesInvoice.objects.create(**self.invoice_data)
        self.assertTrue(invoice.invoice_number.startswith('INV-'))
        self.assertEqual(invoice.status, 'draft')
        self.assertEqual(invoice.total_amount, Decimal('0.00'))
    
    def test_invoice_number_generation(self):
        """اختبار توليد رقم الفاتورة"""
        invoice1 = SalesInvoice.objects.create(**self.invoice_data)
        invoice2 = SalesInvoice.objects.create(**self.invoice_data)
        
        self.assertNotEqual(invoice1.invoice_number, invoice2.invoice_number)
        self.assertTrue(invoice1.invoice_number.startswith('INV-'))
        self.assertTrue(invoice2.invoice_number.startswith('INV-'))
    
    def test_invoice_with_items(self):
        """اختبار فاتورة مع عناصر"""
        invoice = SalesInvoice.objects.create(**self.invoice_data)
        
        item = SalesInvoiceItem.objects.create(
            invoice=invoice,
            product=self.product,
            quantity=Decimal('5.00'),
            unit_price=Decimal('100.00'),
            discount_percentage=Decimal('10.00')
        )
        
        # حساب الإجمالي
        expected_total = Decimal('5.00') * Decimal('100.00') * (1 - Decimal('10.00') / 100)
        self.assertEqual(item.total_price, expected_total)


class ProductMovementModelTest(TestCase):
    """اختبارات نموذج حركة المنتج"""
    
    def setUp(self):
        self.product = Product.objects.create(
            name='منتج تجريبي',
            code='PROD001',
            stock_quantity=Decimal('100.00')
        )
        
        self.rep_user = User.objects.create_user(username='rep1')
        self.representative = SalesRepresentative.objects.create(
            user=self.rep_user,
            employee_id='EMP001'
        )
    
    def test_product_movement_creation(self):
        """اختبار إنشاء حركة منتج"""
        movement = ProductMovement.objects.create(
            product=self.product,
            movement_type='sale',
            movement_date=date.today(),
            quantity=Decimal('10.00'),
            unit_price=Decimal('50.00'),
            reference_number='INV-001',
            reference_type='فاتورة بيع',
            representative=self.representative
        )
        
        self.assertEqual(movement.movement_type, 'sale')
        self.assertEqual(movement.quantity, Decimal('10.00'))
        self.assertEqual(movement.total_value, Decimal('500.00'))
    
    def test_movement_str_method(self):
        """اختبار طريقة __str__ لحركة المنتج"""
        movement = ProductMovement.objects.create(
            product=self.product,
            movement_type='sale',
            movement_date=date.today(),
            quantity=Decimal('10.00'),
            unit_price=Decimal('50.00'),
            reference_number='INV-001',
            reference_type='فاتورة بيع'
        )
        
        expected_str = f"منتج تجريبي - sale - {date.today()}"
        self.assertEqual(str(movement), expected_str)


class PaymentModelTest(TestCase):
    """اختبارات نموذج الدفع"""
    
    def setUp(self):
        self.user = User.objects.create_user(username='testuser')
        
        self.customer = Customer.objects.create(
            name='عميل تجريبي',
            customer_type='credit'
        )
        
        self.rep_user = User.objects.create_user(username='rep1')
        self.representative = SalesRepresentative.objects.create(
            user=self.rep_user,
            employee_id='EMP001'
        )
    
    def test_payment_creation(self):
        """اختبار إنشاء دفعة جديدة"""
        payment = Payment.objects.create(
            customer=self.customer,
            representative=self.representative,
            payment_date=date.today(),
            amount=Decimal('1000.00'),
            payment_type='collection',
            payment_method='cash',
            created_by=self.user
        )
        
        self.assertTrue(payment.payment_number.startswith('PAY-'))
        self.assertEqual(payment.amount, Decimal('1000.00'))
        self.assertEqual(payment.payment_type, 'collection')
    
    def test_payment_number_generation(self):
        """اختبار توليد رقم الدفعة"""
        payment1 = Payment.objects.create(
            customer=self.customer,
            representative=self.representative,
            payment_date=date.today(),
            amount=Decimal('1000.00'),
            payment_type='collection',
            payment_method='cash',
            created_by=self.user
        )
        
        payment2 = Payment.objects.create(
            customer=self.customer,
            representative=self.representative,
            payment_date=date.today(),
            amount=Decimal('500.00'),
            payment_type='collection',
            payment_method='bank',
            created_by=self.user
        )
        
        self.assertNotEqual(payment1.payment_number, payment2.payment_number)
        self.assertTrue(payment1.payment_number.startswith('PAY-'))
        self.assertTrue(payment2.payment_number.startswith('PAY-'))
