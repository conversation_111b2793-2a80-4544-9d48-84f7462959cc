from django import template
from decimal import Decimal, DivisionByZero

register = template.Library()

@register.filter
def div(value, arg):
    """قسمة رقمين مع التعامل مع القسمة على صفر"""
    try:
        if not value or not arg:
            return 0
        return float(value) / float(arg)
    except (ValueError, ZeroDivisionError, DivisionByZero):
        return 0

@register.filter
def multiply(value, arg):
    """ضرب رقمين"""
    try:
        if not value or not arg:
            return 0
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def percentage(value, total):
    """حساب النسبة المئوية"""
    try:
        if not value or not total:
            return 0
        return round((float(value) / float(total)) * 100, 1)
    except (ValueError, ZeroDivisionError, TypeError):
        return 0

@register.filter
def currency_format(value):
    """تنسيق العملة بالجنيه المصري"""
    try:
        if not value:
            return "0.00 ج.م"
        return f"{float(value):,.2f} ج.م"
    except (ValueError, TypeError):
        return "0.00 ج.م"

@register.filter
def number_format(value):
    """تنسيق الأرقام بالفواصل"""
    try:
        if not value:
            return "0"
        return f"{float(value):,.0f}"
    except (ValueError, TypeError):
        return "0"


@register.filter
def subtract(value, arg):
    """طرح رقم من آخر"""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0


@register.filter
def add_filter(value, arg):
    """جمع رقمين"""
    try:
        return float(value) + float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def to_degrees(value, max_value=100):
    """تحويل النسبة المئوية إلى درجات للدوائر التقدمية"""
    try:
        if not value or not max_value:
            return 0
        percentage = (float(value) / float(max_value)) * 100
        # تحويل النسبة المئوية إلى درجات (360 درجة = 100%)
        degrees = min(percentage * 3.6, 360)
        return int(degrees)
    except (ValueError, ZeroDivisionError, TypeError):
        return 0

@register.filter
def safe_percentage(value, total):
    """حساب النسبة المئوية مع حد أقصى 100%"""
    try:
        if not value or not total:
            return 0
        percentage = (float(value) / float(total)) * 100
        return min(round(percentage, 1), 100)
    except (ValueError, ZeroDivisionError, TypeError):
        return 0
