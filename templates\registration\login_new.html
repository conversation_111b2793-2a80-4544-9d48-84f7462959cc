<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            padding: 3rem 2.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            position: relative;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-container {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-8px); }
        }

        .logo-text {
            color: white;
            font-weight: 900;
            text-align: center;
            line-height: 1.1;
        }

        .logo-text .main-text {
            display: block;
            font-size: 2rem;
            margin-bottom: -2px;
        }

        .logo-text .sub-text {
            display: block;
            font-size: 0.9rem;
            font-weight: 700;
            opacity: 0.95;
        }

        .logo-text .ultra-power {
            display: block;
            font-size: 0.6rem;
            font-weight: 600;
            opacity: 0.9;
            letter-spacing: 1px;
        }

        .login-title {
            text-align: center;
            color: #2d3748;
            font-size: 2.2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-subtitle {
            text-align: center;
            color: #718096;
            font-size: 1rem;
            margin-bottom: 2.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 0.8rem;
            font-size: 1.1rem;
            display: block;
        }

        .form-label i {
            margin-left: 8px;
            color: #667eea;
        }

        .form-control {
            width: 100%;
            height: 60px;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 1rem 1rem 1rem 3.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            background: #f8fafc;
            color: #2d3748;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            font-size: 1.3rem;
            z-index: 5;
        }

        .form-control:focus + .input-icon {
            color: #667eea;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #a0aec0;
            cursor: pointer;
            font-size: 1.2rem;
            z-index: 10;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 2rem 0;
        }

        .form-check {
            display: flex;
            align-items: center;
        }

        .form-check-input {
            width: 18px;
            height: 18px;
            margin-left: 0.5rem;
            border: 2px solid #667eea;
            border-radius: 4px;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .form-check-label {
            color: #2d3748;
            font-weight: 600;
            cursor: pointer;
        }

        .forgot-password {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .forgot-password:hover {
            background: rgba(102, 126, 234, 0.1);
            text-decoration: none;
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            width: 100%;
            height: 65px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .test-credentials {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            color: white;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-credentials:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .test-credentials h6 {
            color: white;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .credentials-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .credential-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.8rem;
            border-radius: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .credential-label {
            display: block;
            font-size: 0.8rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
        }

        .credential-value {
            font-size: 1.1rem;
            font-weight: 900;
        }

        .click-hint {
            background: rgba(16, 185, 129, 0.9);
            padding: 0.8rem;
            border-radius: 10px;
            margin-top: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .alert-danger {
            background: linear-gradient(135deg, #fed7d7, #feb2b2);
            color: #c53030;
            border-left: 4px solid #e53e3e;
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                padding: 2rem 1.5rem;
            }
            
            .logo-container {
                width: 100px;
                height: 100px;
            }
            
            .login-title {
                font-size: 1.8rem;
            }
            
            .remember-forgot {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="text-center">
            <div class="logo-container">
                <div class="logo-text">
                    <span class="main-text">OSARIC</span>
                    <span class="sub-text">أوساريك</span>
                    <span class="ultra-power">ULTRA POWER</span>
                    <span class="ultra-power">قوة فائقة</span>
                </div>
            </div>
            <h1 class="login-title">تسجيل الدخول</h1>
            <p class="login-subtitle">مرحباً بك في نظام أوساريك للإدارة المتكاملة</p>
        </div>

        {% if form.errors %}
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong>خطأ في تسجيل الدخول!</strong>
                {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                        <br>{{ error }}
                    {% endfor %}
                {% endfor %}
            </div>
        {% endif %}

        <form method="post" id="loginForm">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="{{ form.username.id_for_label }}" class="form-label">
                    <i class="bi bi-person-circle"></i>
                    اسم المستخدم
                </label>
                <div style="position: relative;">
                    <input type="text" 
                           class="form-control" 
                           id="{{ form.username.id_for_label }}" 
                           name="{{ form.username.name }}" 
                           value="{{ form.username.value|default:'' }}"
                           placeholder="أدخل اسم المستخدم"
                           required
                           autocomplete="username">
                    <i class="bi bi-person-circle input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="{{ form.password.id_for_label }}" class="form-label">
                    <i class="bi bi-shield-lock"></i>
                    كلمة المرور
                </label>
                <div style="position: relative;">
                    <input type="password" 
                           class="form-control" 
                           id="{{ form.password.id_for_label }}" 
                           name="{{ form.password.name }}"
                           placeholder="أدخل كلمة المرور"
                           required
                           autocomplete="current-password">
                    <i class="bi bi-shield-lock input-icon"></i>
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        <i class="bi bi-eye" id="passwordToggleIcon"></i>
                    </button>
                </div>
            </div>

            <div class="remember-forgot">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="rememberMe" name="remember_me">
                    <label class="form-check-label" for="rememberMe">
                        <i class="bi bi-bookmark-heart me-1"></i>
                        تذكرني
                    </label>
                </div>
                <a href="#" class="forgot-password" onclick="showForgotPassword(event)">
                    <i class="bi bi-key me-1"></i>
                    نسيت كلمة المرور؟
                </a>
            </div>

            <button type="submit" class="btn btn-login" id="loginButton">
                <i class="bi bi-box-arrow-in-right me-2"></i>
                دخول إلى النظام
            </button>
        </form>

        <div class="test-credentials" onclick="fillTestData()" title="انقر لملء البيانات تلقائياً">
            <h6><i class="bi bi-info-circle me-2"></i>بيانات تجريبية للاختبار</h6>
            <div class="credentials-grid">
                <div class="credential-item">
                    <span class="credential-label">اسم المستخدم</span>
                    <div class="credential-value">admin</div>
                </div>
                <div class="credential-item">
                    <span class="credential-label">كلمة المرور</span>
                    <div class="credential-value">admin123</div>
                </div>
            </div>
            <div class="click-hint">
                <i class="bi bi-hand-index me-2"></i>
                <strong>انقر هنا لملء البيانات تلقائياً</strong>
            </div>
        </div>

        <div class="text-center mt-4">
            <small class="text-muted">
                © 2024 نظام أوساريك - جميع الحقوق محفوظة
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('{{ form.password.id_for_label }}');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        function fillTestData() {
            document.getElementById('{{ form.username.id_for_label }}').value = 'admin';
            document.getElementById('{{ form.password.id_for_label }}').value = 'admin123';
        }

        function showForgotPassword(event) {
            event.preventDefault();
            alert('سيتم إضافة وظيفة استعادة كلمة المرور قريباً');
        }

        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                document.getElementById('{{ form.username.id_for_label }}').focus();
            }, 500);
        });

        {% if form.errors %}
            document.addEventListener('DOMContentLoaded', function() {
                const container = document.querySelector('.login-container');
                container.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    container.style.animation = '';
                }, 500);
            });
        {% endif %}

        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-10px); }
                75% { transform: translateX(10px); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
