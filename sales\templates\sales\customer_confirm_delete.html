{% extends 'base.html' %}
{% load static %}

{% block title %}حذف العميل{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">حذف العميل</h1>
            <p class="text-muted">تأكيد حذف العميل من قاعدة البيانات</p>
        </div>
        <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            العودة للقائمة
        </a>
    </div>

    <!-- Delete Confirmation -->
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <!-- Customer Information -->
                    <div class="mb-4">
                        <h6 class="text-muted mb-3">معلومات العميل المراد حذفه:</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-sm-4">
                                        <strong>اسم العميل:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        {{ customer.name }}
                                    </div>
                                </div>
                                {% if customer.email %}
                                <div class="row mt-2">
                                    <div class="col-sm-4">
                                        <strong>البريد الإلكتروني:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        {{ customer.email }}
                                    </div>
                                </div>
                                {% endif %}
                                {% if customer.phone %}
                                <div class="row mt-2">
                                    <div class="col-sm-4">
                                        <strong>رقم الهاتف:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        {{ customer.phone }}
                                    </div>
                                </div>
                                {% endif %}
                                <div class="row mt-2">
                                    <div class="col-sm-4">
                                        <strong>تاريخ الإنشاء:</strong>
                                    </div>
                                    <div class="col-sm-8">
                                        {{ customer.created_at|date:"Y/m/d H:i" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <p class="text-muted mb-4">
                        هل أنت متأكد من رغبتك في حذف هذا العميل؟ سيتم حذف جميع البيانات المرتبطة به نهائياً.
                    </p>

                    <!-- Action Buttons -->
                    <form method="post" class="d-inline">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash me-2"></i>
                                تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card-header.bg-danger {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.alert-warning {
    border-left: 4px solid #ffc107;
}

.card.bg-light {
    background-color: #f8f9fa !important;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation dialog
    const deleteForm = document.querySelector('form');
    const deleteButton = deleteForm.querySelector('button[type="submit"]');
    
    deleteButton.addEventListener('click', function(e) {
        e.preventDefault();
        
        if (confirm('هل أنت متأكد من رغبتك في حذف هذا العميل؟ لا يمكن التراجع عن هذا الإجراء.')) {
            deleteForm.submit();
        }
    });
});
</script>
{% endblock %}
