# Osaric - نظام أوساريك لإدارة الحسابات والمخزون

هذا المشروع هو نظام متكامل لإدارة الحسابات المالية والمخزون باستخدام Django وPostgreSQL.

## المميزات الرئيسية
- إدارة المشتريات والمبيعات والفواتير
- إدارة العملاء والموردين
- إدارة المخزون والمخازن
- تقارير مالية ورسوم بيانية
- دعم تعدد المستخدمين
- واجهة احترافية باستخدام Bootstrap

## بدء التشغيل
1. تأكد من تفعيل البيئة الافتراضية:
   ```powershell
   .\venv\Scripts\Activate.ps1
   ```
2. شغل الخادم:
   ```powershell
   python manage.py runserver
   ```

## تخصيص قاعدة البيانات
- الإعدادات الافتراضية تستخدم SQLite، ويمكنك تعديلها لاستخدام PostgreSQL من خلال ملف settings.py.

## ملاحظات
- جميع الأكواد قابلة للتطوير والتخصيص حسب احتياجك.
