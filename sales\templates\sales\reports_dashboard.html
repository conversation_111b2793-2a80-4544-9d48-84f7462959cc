{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم التقارير{% endblock %}

{% block extra_css %}
<style>
    .reports-header {
        background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .report-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
        position: relative;
        cursor: pointer;
    }
    
    .report-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }
    
    .report-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #6f42c1, #5a2d91);
    }
    
    .report-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        margin-bottom: 20px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    .report-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 15px;
        color: #333;
    }
    
    .report-description {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 20px;
        line-height: 1.6;
    }
    
    .report-btn {
        background: linear-gradient(45deg, #6f42c1, #5a2d91);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
        text-decoration: none;
        display: inline-block;
    }
    
    .report-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
        color: white;
    }
    
    .icon-sales { background: linear-gradient(45deg, #28a745, #20c997); color: white; }
    .icon-inventory { background: linear-gradient(45deg, #fd7e14, #e55a00); color: white; }
    .icon-representatives { background: linear-gradient(45deg, #007bff, #0056b3); color: white; }
    .icon-customers { background: linear-gradient(45deg, #dc3545, #c82333); color: white; }
    .icon-financial { background: linear-gradient(45deg, #ffc107, #e0a800); color: #333; }
    .icon-analytics { background: linear-gradient(45deg, #17a2b8, #138496); color: white; }
    
    .quick-stats {
        background: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
    
    .stat-item {
        text-align: center;
        padding: 20px;
        border-radius: 15px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        margin-bottom: 15px;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #6f42c1;
        margin-bottom: 5px;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    .section-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 25px;
        color: #333;
        border-bottom: 3px solid #6f42c1;
        padding-bottom: 10px;
    }
    
    .export-section {
        background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .export-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
        text-decoration: none;
        display: inline-block;
        margin: 5px;
        backdrop-filter: blur(10px);
    }
    
    .export-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس التقارير -->
    <div class="reports-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-graph-up"></i>
                    لوحة تحكم التقارير والتحليلات
                </h1>
                <p class="mb-0">تقارير شاملة ومفصلة لجميع عمليات المبيعات والمخزون والأداء</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <h5>{{ today|date:"l, j F Y" }}</h5>
                    <p class="mb-0">آخر تحديث: {{ "now"|date:"H:i" }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="quick-stats">
        <h3 class="section-title">إحصائيات اليوم</h3>
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ total_sales_today|floatformat:0 }}</div>
                    <div class="stat-label">مبيعات اليوم (ج.م)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ total_orders_today }}</div>
                    <div class="stat-label">طلبات اليوم</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ total_returns_today|floatformat:0 }}</div>
                    <div class="stat-label">مرتجعات اليوم (ج.م)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ active_representatives }}</div>
                    <div class="stat-label">مناديب نشطين</div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة التقارير -->
    <div class="row">
        <div class="col-12">
            <h3 class="section-title">التقارير المتاحة</h3>
        </div>
    </div>

    <div class="row">
        <!-- تقرير المبيعات -->
        <div class="col-lg-4 col-md-6">
            <div class="report-card" onclick="location.href='{% url 'sales:sales_report' %}'">
                <div class="report-icon icon-sales">
                    <i class="bi bi-graph-up-arrow"></i>
                </div>
                <div class="report-title">تقرير المبيعات</div>
                <div class="report-description">
                    تقرير شامل للمبيعات حسب الفترة والمندوب ونوع العميل مع إحصائيات مفصلة
                </div>
                <a href="{% url 'sales:sales_report' %}" class="report-btn">
                    <i class="bi bi-eye"></i>
                    عرض التقرير
                </a>
            </div>
        </div>

        <!-- تقرير المخزون -->
        <div class="col-lg-4 col-md-6">
            <div class="report-card" onclick="location.href='{% url 'sales:inventory_report' %}'">
                <div class="report-icon icon-inventory">
                    <i class="bi bi-boxes"></i>
                </div>
                <div class="report-title">تقرير المخزون</div>
                <div class="report-description">
                    تقرير حالة المخزون والمنتجات منخفضة المخزون والقيم الإجمالية
                </div>
                <a href="{% url 'sales:inventory_report' %}" class="report-btn">
                    <i class="bi bi-eye"></i>
                    عرض التقرير
                </a>
            </div>
        </div>

        <!-- تقرير أداء المناديب -->
        <div class="col-lg-4 col-md-6">
            <div class="report-card" onclick="location.href='{% url 'sales:representative_performance_report' %}'">
                <div class="report-icon icon-representatives">
                    <i class="bi bi-person-check"></i>
                </div>
                <div class="report-title">تقرير أداء المناديب</div>
                <div class="report-description">
                    تقرير مفصل لأداء المناديب والمبيعات والتحصيلات والعمولات
                </div>
                <a href="{% url 'sales:representative_performance_report' %}" class="report-btn">
                    <i class="bi bi-eye"></i>
                    عرض التقرير
                </a>
            </div>
        </div>

        <!-- تقرير العملاء -->
        <div class="col-lg-4 col-md-6">
            <div class="report-card">
                <div class="report-icon icon-customers">
                    <i class="bi bi-people"></i>
                </div>
                <div class="report-title">تقرير العملاء</div>
                <div class="report-description">
                    تقرير شامل للعملاء وأرصدتهم ومشترياتهم وحالة الحسابات
                </div>
                <a href="#" class="report-btn">
                    <i class="bi bi-clock"></i>
                    قريباً
                </a>
            </div>
        </div>

        <!-- التقرير المالي -->
        <div class="col-lg-4 col-md-6">
            <div class="report-card">
                <div class="report-icon icon-financial">
                    <i class="bi bi-cash-stack"></i>
                </div>
                <div class="report-title">التقرير المالي</div>
                <div class="report-description">
                    تقرير الأرباح والخسائر والتدفقات النقدية والمؤشرات المالية
                </div>
                <a href="#" class="report-btn">
                    <i class="bi bi-clock"></i>
                    قريباً
                </a>
            </div>
        </div>

        <!-- تقرير التحليلات -->
        <div class="col-lg-4 col-md-6">
            <div class="report-card">
                <div class="report-icon icon-analytics">
                    <i class="bi bi-bar-chart"></i>
                </div>
                <div class="report-title">تحليلات متقدمة</div>
                <div class="report-description">
                    رسوم بيانية وتحليلات متقدمة للاتجاهات والتوقعات
                </div>
                <a href="#" class="report-btn">
                    <i class="bi bi-clock"></i>
                    قريباً
                </a>
            </div>
        </div>
    </div>

    <!-- قسم التصدير -->
    <div class="export-section">
        <h4 class="mb-3">تصدير التقارير</h4>
        <p class="mb-4">يمكنك تصدير جميع التقارير بصيغ مختلفة</p>
        <a href="#" class="export-btn">
            <i class="bi bi-file-earmark-pdf"></i>
            تصدير PDF
        </a>
        <a href="#" class="export-btn">
            <i class="bi bi-file-earmark-excel"></i>
            تصدير Excel
        </a>
        <a href="#" class="export-btn">
            <i class="bi bi-printer"></i>
            طباعة
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تأثيرات بصرية للبطاقات
    $('.report-card').hover(
        function() {
            $(this).find('.report-title').addClass('text-primary');
        },
        function() {
            $(this).find('.report-title').removeClass('text-primary');
        }
    );
    
    // تحديث الوقت كل دقيقة
    setInterval(function() {
        var now = new Date();
        var time = now.getHours().toString().padStart(2, '0') + ':' + 
                   now.getMinutes().toString().padStart(2, '0');
        $('p:contains("آخر تحديث:")').text('آخر تحديث: ' + time);
    }, 60000);
});
</script>
{% endblock %}
