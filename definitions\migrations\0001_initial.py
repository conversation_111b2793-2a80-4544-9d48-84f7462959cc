# Generated manually for product definitions

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الفئة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('image', models.ImageField(blank=True, upload_to='categories/', verbose_name='صورة الفئة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='ترتيب العرض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='definitions.productcategory', verbose_name='الفئة الأب')),
            ],
            options={
                'verbose_name': 'فئة الصنف',
                'verbose_name_plural': 'فئات الأصناف',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود الصنف')),
                ('barcode', models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='الباركود')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الصنف')),
                ('name_en', models.CharField(blank=True, max_length=200, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('specifications', models.TextField(blank=True, verbose_name='المواصفات')),
                ('product_type', models.CharField(max_length=20, verbose_name='نوع الصنف')),
                ('main_unit', models.CharField(max_length=20, verbose_name='الوحدة الأساسية')),
                ('sub_unit', models.CharField(blank=True, max_length=20, verbose_name='الوحدة الفرعية')),
                ('conversion_factor', models.DecimalField(decimal_places=4, default=1, max_digits=10, verbose_name='معامل التحويل')),
                ('cost_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر التكلفة')),
                ('selling_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر البيع')),
                ('wholesale_price', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='سعر الجملة')),
                ('minimum_stock', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأدنى للمخزون')),
                ('maximum_stock', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الحد الأقصى للمخزون')),
                ('reorder_point', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='نقطة إعادة الطلب')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الضريبة %')),
                ('discount_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الخصم %')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_sellable', models.BooleanField(default=True, verbose_name='قابل للبيع')),
                ('is_purchasable', models.BooleanField(default=True, verbose_name='قابل للشراء')),
                ('track_inventory', models.BooleanField(default=True, verbose_name='تتبع المخزون')),
                ('image', models.ImageField(blank=True, upload_to='products/', verbose_name='صورة الصنف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productcategory', verbose_name='الفئة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تعريف الصنف',
                'verbose_name_plural': 'تعريفات الأصناف',
                'ordering': ['name'],
            },
        ),
    ]
