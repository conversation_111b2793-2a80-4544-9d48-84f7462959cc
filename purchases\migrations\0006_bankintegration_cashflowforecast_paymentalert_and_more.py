# Generated by Django 5.2.4 on 2025-07-26 08:25

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchases', '0005_remove_supplierpayment_amount_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BankIntegration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bank_name', models.CharField(choices=[('nbe', 'البنك الأهلي المصري'), ('cib', 'البنك التجاري الدولي'), ('banque_misr', 'بنك مصر'), ('hsbc', 'بنك HSBC'), ('other', 'بنك آخر')], max_length=50, verbose_name='اسم البنك')),
                ('account_number', models.CharField(max_length=50, verbose_name='رقم الحساب')),
                ('account_name', models.CharField(max_length=100, verbose_name='اسم الحساب')),
                ('api_endpoint', models.URLField(blank=True, null=True, verbose_name='نقطة الاتصال API')),
                ('api_key', models.CharField(blank=True, max_length=200, null=True, verbose_name='مفتاح API')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('auto_sync', models.BooleanField(default=False, verbose_name='مزامنة تلقائية')),
                ('last_sync', models.DateTimeField(blank=True, null=True, verbose_name='آخر مزامنة')),
                ('sync_frequency', models.IntegerField(default=60, verbose_name='تكرار المزامنة (دقائق)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'ربط بنكي',
                'verbose_name_plural': 'الربط البنكي',
            },
        ),
        migrations.CreateModel(
            name='CashFlowForecast',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period_type', models.CharField(choices=[('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي')], max_length=20, verbose_name='نوع الفترة')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('expected_purchases', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المشتريات المتوقعة')),
                ('expected_payments', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المدفوعات المتوقعة')),
                ('outstanding_invoices', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الفواتير المعلقة')),
                ('cash_flow_impact', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تأثير التدفق النقدي')),
                ('confidence_level', models.IntegerField(default=80, verbose_name='مستوى الثقة %')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'توقع التدفق النقدي',
                'verbose_name_plural': 'توقعات التدفق النقدي',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='PaymentAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('due_soon', 'مستحقة قريباً'), ('overdue', 'متأخرة'), ('payment_reminder', 'تذكير دفع'), ('credit_limit', 'تجاوز حد الائتمان'), ('large_payment', 'دفعة كبيرة تحتاج موافقة')], max_length=20, verbose_name='نوع التنبيه')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('critical', 'حرجة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التنبيه')),
                ('message', models.TextField(verbose_name='رسالة التنبيه')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True, verbose_name='المبلغ')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('is_resolved', models.BooleanField(default=False, verbose_name='تم الحل')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_alerts', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='purchases.purchaseinvoice', verbose_name='الفاتورة')),
                ('payment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='purchases.supplierpayment', verbose_name='الدفعة')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_alerts', to=settings.AUTH_USER_MODEL, verbose_name='حُل بواسطة')),
                ('supplier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='purchases.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'تنبيه دفع',
                'verbose_name_plural': 'تنبيهات الدفع',
                'ordering': ['-priority', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentApproval',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_threshold', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='حد المبلغ المطلوب للموافقة')),
                ('requested_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('justification', models.TextField(verbose_name='مبرر الدفعة')),
                ('status', models.CharField(choices=[('pending', 'في انتظار الموافقة'), ('approved', 'موافق عليها'), ('rejected', 'مرفوضة'), ('cancelled', 'ملغية')], default='pending', max_length=20, verbose_name='حالة الموافقة')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('rejection_reason', models.TextField(blank=True, null=True, verbose_name='سبب الرفض')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('critical', 'حرجة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات إضافية')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_payments', to=settings.AUTH_USER_MODEL, verbose_name='وافق بواسطة')),
                ('payment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='approval', to='purchases.supplierpayment', verbose_name='الدفعة')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_requests', to=settings.AUTH_USER_MODEL, verbose_name='طلب بواسطة')),
            ],
            options={
                'verbose_name': 'موافقة دفعة',
                'verbose_name_plural': 'موافقات الدفعات',
                'ordering': ['-requested_at'],
            },
        ),
        migrations.CreateModel(
            name='SupplierRiskAssessment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_history_score', models.IntegerField(default=0, verbose_name='درجة تاريخ الدفع')),
                ('credit_utilization', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة استخدام الائتمان')),
                ('average_payment_delay', models.IntegerField(default=0, verbose_name='متوسط تأخير الدفع (أيام)')),
                ('order_fulfillment_rate', models.DecimalField(decimal_places=2, default=100, max_digits=5, verbose_name='معدل تنفيذ الطلبات')),
                ('quality_score', models.IntegerField(default=100, verbose_name='درجة الجودة')),
                ('delivery_performance', models.IntegerField(default=100, verbose_name='أداء التسليم')),
                ('overall_risk_level', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('critical', 'حرجة')], default='medium', max_length=10, verbose_name='مستوى المخاطر العام')),
                ('risk_score', models.IntegerField(default=50, verbose_name='درجة المخاطر')),
                ('recommended_credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='حد الائتمان المقترح')),
                ('payment_terms_recommendation', models.CharField(blank=True, max_length=100, null=True, verbose_name='شروط الدفع المقترحة')),
                ('last_assessment_date', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تقييم')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات التقييم')),
                ('assessed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='قُيم بواسطة')),
                ('supplier', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='risk_assessment', to='purchases.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'تقييم مخاطر مورد',
                'verbose_name_plural': 'تقييمات مخاطر الموردين',
            },
        ),
    ]
