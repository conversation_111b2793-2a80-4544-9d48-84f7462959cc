# Generated by Django 5.2.4 on 2025-07-13 22:26

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0006_currencydefinition_notes'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UnitDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='كود وحدة القياس')),
                ('name', models.CharField(max_length=100, verbose_name='اسم وحدة القياس')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('unit_type', models.CharField(choices=[('weight', 'وزن'), ('length', 'طول'), ('area', 'مساحة'), ('volume', 'حجم'), ('quantity', 'كمية'), ('time', 'وقت'), ('other', 'أخرى')], max_length=20, verbose_name='نوع وحدة القياس')),
                ('decimal_places', models.PositiveIntegerField(default=2, validators=[django.core.validators.MaxValueValidator(6)], verbose_name='عدد الخانات العشرية')),
                ('conversion_factor', models.DecimalField(decimal_places=6, default=1, max_digits=15, verbose_name='معامل التحويل')),
                ('is_base_unit', models.BooleanField(default=False, verbose_name='وحدة أساسية')),
                ('description', models.TextField(blank=True, verbose_name='وصف وحدة القياس')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('base_unit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='definitions.unitdefinition', verbose_name='الوحدة الأساسية')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'تعريف وحدة قياس',
                'verbose_name_plural': 'تعريفات وحدات القياس',
                'ordering': ['unit_type', 'name'],
            },
        ),
    ]
