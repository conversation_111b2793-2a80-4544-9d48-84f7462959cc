# Generated by Django 5.2.4 on 2025-07-12 23:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الفرع')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز الفرع')),
                ('branch_type', models.CharField(choices=[('headquarters', 'المركز الرئيسي'), ('branch', 'فرع'), ('warehouse', 'مستودع'), ('office', 'مكتب'), ('factory', 'مصنع'), ('showroom', 'معرض')], max_length=20, verbose_name='نوع الفرع')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('city', models.CharField(max_length=100, verbose_name='المدينة')),
                ('region', models.CharField(blank=True, max_length=100, null=True, verbose_name='المنطقة')),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='الرمز البريدي')),
                ('country', models.CharField(default='السعودية', max_length=100, verbose_name='الدولة')),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True, verbose_name='خط العرض')),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True, verbose_name='خط الطول')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('mobile', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الجوال')),
                ('fax', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الفاكس')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, null=True, verbose_name='الموقع الإلكتروني')),
                ('manager_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم المدير')),
                ('manager_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='هاتف المدير')),
                ('opening_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الافتتاح')),
                ('working_hours', models.CharField(blank=True, max_length=200, null=True, verbose_name='ساعات العمل')),
                ('employee_count', models.PositiveIntegerField(default=0, verbose_name='عدد الموظفين')),
                ('area_size', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='المساحة (متر مربع)')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('under_construction', 'تحت الإنشاء'), ('closed', 'مغلق')], default='active', max_length=20, verbose_name='الحالة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_branches', to=settings.AUTH_USER_MODEL, verbose_name='المدير')),
                ('parent_branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sub_branches', to='branches.branch', verbose_name='الفرع الأب')),
            ],
            options={
                'verbose_name': 'فرع',
                'verbose_name_plural': 'الفروع',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BranchAsset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الأصل')),
                ('asset_type', models.CharField(choices=[('equipment', 'معدات'), ('furniture', 'أثاث'), ('vehicle', 'مركبة'), ('computer', 'حاسوب'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الأصل')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('serial_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='الرقم التسلسلي')),
                ('purchase_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الشراء')),
                ('purchase_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر الشراء')),
                ('current_value', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='القيمة الحالية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assets', to='branches.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'أصل فرع',
                'verbose_name_plural': 'أصول الفروع',
                'ordering': ['branch', 'name'],
            },
        ),
        migrations.CreateModel(
            name='BranchEmployee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('position', models.CharField(choices=[('manager', 'مدير'), ('assistant_manager', 'مساعد مدير'), ('supervisor', 'مشرف'), ('employee', 'موظف'), ('security', 'أمن'), ('cleaner', 'عامل نظافة')], max_length=20, verbose_name='المنصب')),
                ('hire_date', models.DateField(verbose_name='تاريخ التوظيف')),
                ('salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الراتب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='branches.branch', verbose_name='الفرع')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'موظف فرع',
                'verbose_name_plural': 'موظفي الفروع',
                'ordering': ['branch', 'position'],
                'unique_together': {('branch', 'user')},
            },
        ),
    ]
