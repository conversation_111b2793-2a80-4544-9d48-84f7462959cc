{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        padding: 2rem;
        margin: 2rem 0;
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 15px 15px 0 0;
        margin: -2rem -2rem 2rem -2rem;
        text-align: center;
    }
    
    .form-header h2 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 600;
    }
    
    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }
    
    .form-section h4 {
        color: #667eea;
        margin-bottom: 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .required-field::after {
        content: " *";
        color: #ef4444;
    }
    
    .form-control, .form-select {
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }
    
    .btn-container {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid #e5e7eb;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .btn-secondary {
        background: #6b7280;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        color: white;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }
    
    .help-text {
        font-size: 0.85rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }
    
    .error-message {
        color: #ef4444;
        font-size: 0.85rem;
        margin-top: 0.25rem;
    }
    
    .row {
        margin-left: -0.5rem;
        margin-right: -0.5rem;
    }
    
    .col-md-6 {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-container">
                <div class="form-header">
                    <h2>
                        <i class="bi bi-box"></i>
                        {{ title }}
                    </h2>
                </div>

                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <!-- معلومات أساسية -->
                    <div class="form-section">
                        <h4>
                            <i class="bi bi-info-circle"></i>
                            المعلومات الأساسية
                        </h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.name.id_for_label }}" class="form-label required-field">{{ form.name.label }}</label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="error-message">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.asset_code.id_for_label }}" class="form-label required-field">{{ form.asset_code.label }}</label>
                                    {{ form.asset_code }}
                                    {% if form.asset_code.errors %}
                                        <div class="error-message">{{ form.asset_code.errors.0 }}</div>
                                    {% endif %}
                                    <div class="help-text">كود فريد للأصل</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.category.id_for_label }}" class="form-label required-field">{{ form.category.label }}</label>
                                    {{ form.category }}
                                    {% if form.category.errors %}
                                        <div class="error-message">{{ form.category.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.responsible_person.id_for_label }}" class="form-label">{{ form.responsible_person.label }}</label>
                                    {{ form.responsible_person }}
                                    {% if form.responsible_person.errors %}
                                        <div class="error-message">{{ form.responsible_person.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="error-message">{{ form.description.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- معلومات الشراء -->
                    <div class="form-section">
                        <h4>
                            <i class="bi bi-cart"></i>
                            معلومات الشراء
                        </h4>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.purchase_date.id_for_label }}" class="form-label required-field">{{ form.purchase_date.label }}</label>
                                    {{ form.purchase_date }}
                                    {% if form.purchase_date.errors %}
                                        <div class="error-message">{{ form.purchase_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.purchase_price.id_for_label }}" class="form-label required-field">{{ form.purchase_price.label }}</label>
                                    {{ form.purchase_price }}
                                    {% if form.purchase_price.errors %}
                                        <div class="error-message">{{ form.purchase_price.errors.0 }}</div>
                                    {% endif %}
                                    <div class="help-text">بالجنيه المصري</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.supplier.id_for_label }}" class="form-label">{{ form.supplier.label }}</label>
                                    {{ form.supplier }}
                                    {% if form.supplier.errors %}
                                        <div class="error-message">{{ form.supplier.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الاستهلاك -->
                    <div class="form-section">
                        <h4>
                            <i class="bi bi-graph-down"></i>
                            معلومات الاستهلاك
                        </h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.useful_life_years.id_for_label }}" class="form-label required-field">{{ form.useful_life_years.label }}</label>
                                    {{ form.useful_life_years }}
                                    {% if form.useful_life_years.errors %}
                                        <div class="error-message">{{ form.useful_life_years.errors.0 }}</div>
                                    {% endif %}
                                    <div class="help-text">العمر الافتراضي للأصل</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.salvage_value.id_for_label }}" class="form-label">{{ form.salvage_value.label }}</label>
                                    {{ form.salvage_value }}
                                    {% if form.salvage_value.errors %}
                                        <div class="error-message">{{ form.salvage_value.errors.0 }}</div>
                                    {% endif %}
                                    <div class="help-text">القيمة المتوقعة في نهاية العمر الافتراضي</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الحالة والموقع -->
                    <div class="form-section">
                        <h4>
                            <i class="bi bi-geo-alt"></i>
                            الحالة والموقع
                        </h4>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.status.id_for_label }}" class="form-label required-field">{{ form.status.label }}</label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="error-message">{{ form.status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.condition.id_for_label }}" class="form-label required-field">{{ form.condition.label }}</label>
                                    {{ form.condition }}
                                    {% if form.condition.errors %}
                                        <div class="error-message">{{ form.condition.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.location.id_for_label }}" class="form-label">{{ form.location.label }}</label>
                                    {{ form.location }}
                                    {% if form.location.errors %}
                                        <div class="error-message">{{ form.location.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="form-section">
                        <h4>
                            <i class="bi bi-plus-circle"></i>
                            معلومات إضافية
                        </h4>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.serial_number.id_for_label }}" class="form-label">{{ form.serial_number.label }}</label>
                                    {{ form.serial_number }}
                                    {% if form.serial_number.errors %}
                                        <div class="error-message">{{ form.serial_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.model.id_for_label }}" class="form-label">{{ form.model.label }}</label>
                                    {{ form.model }}
                                    {% if form.model.errors %}
                                        <div class="error-message">{{ form.model.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.manufacturer.id_for_label }}" class="form-label">{{ form.manufacturer.label }}</label>
                                    {{ form.manufacturer }}
                                    {% if form.manufacturer.errors %}
                                        <div class="error-message">{{ form.manufacturer.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="btn-container">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            {% if asset %}تحديث الأصل{% else %}إضافة الأصل{% endif %}
                        </button>
                        <a href="{% url 'assets:asset_list' %}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
    });
    
    // تحديد تلقائي لكود الأصل إذا كان فارغاً
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    const codeField = document.getElementById('{{ form.asset_code.id_for_label }}');
    
    if (nameField && codeField && !codeField.value) {
        nameField.addEventListener('blur', function() {
            if (!codeField.value && this.value) {
                // إنشاء كود تلقائي بناءً على الاسم
                const code = 'AST-' + this.value.substring(0, 3).toUpperCase() + '-' + Date.now().toString().slice(-4);
                codeField.value = code;
            }
        });
    }
    
    console.log('✅ تم تحميل نموذج الأصول الثابتة');
});
</script>
{% endblock %}
