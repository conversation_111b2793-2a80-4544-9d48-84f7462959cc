{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم الحسابات العامة{% endblock %}

{% block extra_css %}
<style>
    .accounting-header {
        background: linear-gradient(135deg, #6f42c1 0%, #495057 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
    }

    .stats-card {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-bottom: 2rem;
    }

    .quick-action-btn {
        background: linear-gradient(135deg, #6f42c1 0%, #495057 100%);
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(111,66,193,0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Accounting Header -->
<div class="accounting-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-calculator me-2"></i>
                    لوحة تحكم الحسابات العامة
                </h1>
                <p class="mb-0 opacity-75">إدارة شاملة لدليل الحسابات والقيود المحاسبية</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <i class="bi bi-calendar me-2"></i>
                    {{ "now"|date:"Y/m/d" }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container-fluid">
    <div class="quick-actions">
        <a href="{% url 'accounting:account_type_create' %}" class="quick-action-btn">
            <i class="bi bi-plus-circle"></i>
            إضافة نوع حساب
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-folder-plus"></i>
            إضافة حساب جديد
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-journal-plus"></i>
            إنشاء قيد محاسبي
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-file-earmark-text"></i>
            التقارير المالية
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(111, 66, 193, 0.1); color: #6f42c1;">
                    <i class="bi bi-list-ul"></i>
                </div>
                <div class="stats-number" style="color: #6f42c1;">{{ total_account_types }}</div>
                <p class="stats-label">أنواع الحسابات</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(40, 167, 69, 0.1); color: #28a745;">
                    <i class="bi bi-folder"></i>
                </div>
                <div class="stats-number text-success">{{ total_accounts }}</div>
                <p class="stats-label">إجمالي الحسابات</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(0, 123, 255, 0.1); color: #007bff;">
                    <i class="bi bi-folder2-open"></i>
                </div>
                <div class="stats-number text-primary">{{ main_accounts }}</div>
                <p class="stats-label">الحسابات الرئيسية</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-files"></i>
                </div>
                <div class="stats-number text-warning">{{ sub_accounts }}</div>
                <p class="stats-label">الحسابات الفرعية</p>
            </div>
        </div>
    </div>

    <!-- Journal Entry Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(32, 201, 151, 0.1); color: #20c997;">
                    <i class="bi bi-journal-text"></i>
                </div>
                <div class="stats-number text-info">{{ total_entries }}</div>
                <p class="stats-label">إجمالي القيود</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(40, 167, 69, 0.1); color: #28a745;">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="stats-number text-success">{{ posted_entries }}</div>
                <p class="stats-label">قيود مرحلة</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-pencil-square"></i>
                </div>
                <div class="stats-number text-warning">{{ draft_entries }}</div>
                <p class="stats-label">قيود مسودة</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-calendar-month"></i>
                </div>
                <div class="stats-number text-danger">{{ monthly_entries }}</div>
                <p class="stats-label">قيود هذا الشهر</p>
            </div>
        </div>
    </div>

    <!-- Financial Totals -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(40, 167, 69, 0.1); color: #28a745;">
                    <i class="bi bi-arrow-up-circle"></i>
                </div>
                <div class="stats-number text-success">{{ total_debits|floatformat:2 }}</div>
                <p class="stats-label">إجمالي المدين (ريال)</p>
            </div>
        </div>
        
        <div class="col-lg-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-arrow-down-circle"></i>
                </div>
                <div class="stats-number text-danger">{{ total_credits|floatformat:2 }}</div>
                <p class="stats-label">إجمالي الدائن (ريال)</p>
            </div>
        </div>
    </div>

    <!-- Recent Journal Entries -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث القيود المحاسبية</h5>
                    <a href="#" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_entries %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم القيد</th>
                                    <th>تاريخ القيد</th>
                                    <th>الوصف</th>
                                    <th>المدين</th>
                                    <th>الدائن</th>
                                    <th>الحالة</th>
                                    <th>أنشئ بواسطة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entry in recent_entries %}
                                <tr>
                                    <td>{{ entry.entry_number }}</td>
                                    <td>{{ entry.entry_date }}</td>
                                    <td>{{ entry.description|truncatechars:50 }}</td>
                                    <td>{{ entry.total_debit|floatformat:2 }} ريال</td>
                                    <td>{{ entry.total_credit|floatformat:2 }} ريال</td>
                                    <td>
                                        {% if entry.status == 'posted' %}
                                            <span class="badge bg-success">مرحل</span>
                                        {% elif entry.status == 'draft' %}
                                            <span class="badge bg-warning">مسودة</span>
                                        {% else %}
                                            <span class="badge bg-danger">ملغي</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ entry.created_by.get_full_name|default:entry.created_by.username }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-journal-text text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد قيود محاسبية حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
