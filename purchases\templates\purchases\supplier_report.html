{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير الموردين - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-left: 5px solid #28a745;
    }

    .stat-card h3 {
        margin: 0 0 10px 0;
        font-size: 2.5rem;
        font-weight: bold;
        color: #28a745;
    }

    .stat-card p {
        margin: 0;
        font-size: 1.1rem;
        color: #6c757d;
    }

    .filters-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .suppliers-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table {
        margin: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table tbody td {
        padding: 15px;
        vertical-align: middle;
        border-bottom: 1px solid #f0f0f0;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .supplier-type-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .type-local {
        background: #e3f2fd;
        color: #1565c0;
    }

    .type-international {
        background: #f3e5f5;
        color: #7b1fa2;
    }

    .type-manufacturer {
        background: #e8f5e8;
        color: #2e7d32;
    }

    .type-distributor {
        background: #fff3e0;
        color: #ef6c00;
    }

    .search-bar {
        position: relative;
        margin-bottom: 20px;
    }

    .search-bar input {
        padding-right: 45px;
    }

    .search-bar .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }

    .btn-export {
        background: linear-gradient(45deg, #6f42c1, #5a32a3);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-people"></i>
                    تقرير الموردين
                </h1>
                <p class="mb-0">تقرير شامل عن جميع الموردين وحالاتهم</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'purchases:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-house"></i>
                        لوحة التحكم
                    </a>
                    <button class="btn btn-export" onclick="window.print()">
                        <i class="bi bi-printer"></i>
                        طباعة التقرير
                    </button>
                    <a href="{% url 'purchases:reports' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-right"></i>
                        العودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="stats-cards">
        <div class="stat-card">
            <h3>{{ total_suppliers }}</h3>
            <p>إجمالي الموردين</p>
        </div>
        <div class="stat-card">
            <h3>{{ active_suppliers }}</h3>
            <p>الموردين النشطين</p>
        </div>
        <div class="stat-card">
            <h3>{{ inactive_suppliers }}</h3>
            <p>الموردين غير النشطين</p>
        </div>
    </div>

    <!-- الفلاتر -->
    <div class="filters-section">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <div class="search-bar">
                    <input type="text" name="search" class="form-control" placeholder="البحث عن مورد..." value="{{ search }}">
                    <i class="bi bi-search search-icon"></i>
                </div>
            </div>
            
            <div class="col-md-4">
                <select name="supplier_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="local" {% if supplier_type == 'local' %}selected{% endif %}>محلي</option>
                    <option value="international" {% if supplier_type == 'international' %}selected{% endif %}>دولي</option>
                    <option value="manufacturer" {% if supplier_type == 'manufacturer' %}selected{% endif %}>مصنع</option>
                    <option value="distributor" {% if supplier_type == 'distributor' %}selected{% endif %}>موزع</option>
                </select>
            </div>
            
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-funnel"></i>
                    تطبيق
                </button>
            </div>
        </form>
    </div>

    <!-- جدول الموردين -->
    <div class="suppliers-table">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>اسم المورد</th>
                    <th>الكود</th>
                    <th>النوع</th>
                    <th>الهاتف</th>
                    <th>البريد الإلكتروني</th>
                    <th>المدينة</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                </tr>
            </thead>
            <tbody>
                {% for supplier in suppliers %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ supplier.name }}</strong>
                                {% if supplier.commercial_register %}
                                    <br>
                                    <small class="text-muted">س.ت: {{ supplier.commercial_register }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <strong>{{ supplier.code }}</strong>
                        </td>
                        <td>
                            <span class="supplier-type-badge type-{{ supplier.supplier_type }}">
                                {{ supplier.get_supplier_type_display }}
                            </span>
                        </td>
                        <td>
                            {% if supplier.phone %}
                                {{ supplier.phone }}
                                {% if supplier.mobile %}
                                    <br>
                                    <small class="text-muted">{{ supplier.mobile }}</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.email %}
                                <a href="mailto:{{ supplier.email }}">{{ supplier.email }}</a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.city %}
                                {{ supplier.city }}
                                {% if supplier.state %}
                                    <br>
                                    <small class="text-muted">{{ supplier.state }}</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="status-badge {% if supplier.is_active %}status-active{% else %}status-inactive{% endif %}">
                                {% if supplier.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                        </td>
                        <td>
                            <strong>{{ supplier.created_at|date:"d/m/Y" }}</strong>
                            <br>
                            <small class="text-muted">{{ supplier.created_at|time:"H:i" }}</small>
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد موردين</p>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين الطباعة
    window.addEventListener('beforeprint', function() {
        document.body.classList.add('printing');
    });
    
    window.addEventListener('afterprint', function() {
        document.body.classList.remove('printing');
    });
</script>

<style>
    @media print {
        .btn, .filters-section {
            display: none !important;
        }
        
        .page-header {
            background: #28a745 !important;
            -webkit-print-color-adjust: exact;
        }
        
        .table thead th {
            background: #28a745 !important;
            -webkit-print-color-adjust: exact;
        }
    }
</style>
{% endblock %}
