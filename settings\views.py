from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import datetime, timedelta, date
from .models import SystemSetting, UserPreference, BackupSchedule, BackupHistory, SystemLog
from .forms import (SystemSettingForm, UserPreferenceForm, BackupScheduleForm, CompanySettingsForm,
                   FinancialSettingsForm, SecuritySettingsForm, NotificationSettingsForm, SettingsSearchForm)

def is_admin(user):
    """التحقق من أن المستخدم مدير"""
    return user.is_superuser or user.is_staff

@login_required
@user_passes_test(is_admin)
def settings_dashboard(request):
    """لوحة تحكم الإعدادات والخدمات"""
    # إحصائيات عامة
    total_settings = SystemSetting.objects.count()
    editable_settings = SystemSetting.objects.filter(is_editable=True).count()
    required_settings = SystemSetting.objects.filter(is_required=True).count()

    # إحصائيات النسخ الاحتياطية
    total_backup_schedules = BackupSchedule.objects.count()
    active_schedules = BackupSchedule.objects.filter(status='active').count()
    recent_backups = BackupHistory.objects.filter(
        backup_date__gte=timezone.now() - timedelta(days=7)
    ).count()

    # إحصائيات السجلات
    total_logs = SystemLog.objects.count()
    error_logs = SystemLog.objects.filter(level='error').count()
    warning_logs = SystemLog.objects.filter(level='warning').count()

    # الإعدادات حسب الفئة
    settings_by_category = SystemSetting.objects.values('category').annotate(count=Count('id'))

    # أحدث السجلات
    recent_logs = SystemLog.objects.order_by('-created_at')[:10]

    # أحدث النسخ الاحتياطية
    recent_backup_history = BackupHistory.objects.select_related('schedule').order_by('-backup_date')[:5]

    context = {
        'total_settings': total_settings,
        'editable_settings': editable_settings,
        'required_settings': required_settings,
        'total_backup_schedules': total_backup_schedules,
        'active_schedules': active_schedules,
        'recent_backups': recent_backups,
        'total_logs': total_logs,
        'error_logs': error_logs,
        'warning_logs': warning_logs,
        'settings_by_category': settings_by_category,
        'recent_logs': recent_logs,
        'recent_backup_history': recent_backup_history,
    }
    return render(request, 'settings/dashboard.html', context)

# ========== إدارة إعدادات النظام ==========
@login_required
@user_passes_test(is_admin)
def system_settings_list(request):
    """قائمة إعدادات النظام"""
    form = SettingsSearchForm(request.GET)
    settings = SystemSetting.objects.all()

    if form.is_valid():
        search = form.cleaned_data.get('search')
        category = form.cleaned_data.get('category')
        setting_type = form.cleaned_data.get('setting_type')
        is_editable = form.cleaned_data.get('is_editable')

        if search:
            settings = settings.filter(
                Q(name__icontains=search) |
                Q(key__icontains=search) |
                Q(description__icontains=search)
            )

        if category:
            settings = settings.filter(category=category)

        if setting_type:
            settings = settings.filter(setting_type=setting_type)

        if is_editable:
            settings = settings.filter(is_editable=is_editable == 'True')

    paginator = Paginator(settings, 20)
    page_number = request.GET.get('page')
    settings = paginator.get_page(page_number)

    context = {
        'settings': settings,
        'form': form,
    }
    return render(request, 'settings/system_settings_list.html', context)

@login_required
@user_passes_test(is_admin)
def system_setting_create(request):
    """إضافة إعداد نظام جديد"""
    if request.method == 'POST':
        form = SystemSettingForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة الإعداد بنجاح')
            return redirect('settings:system_settings_list')
    else:
        form = SystemSettingForm()

    context = {'form': form, 'title': 'إضافة إعداد جديد'}
    return render(request, 'settings/system_setting_form.html', context)

@login_required
@user_passes_test(is_admin)
def system_setting_edit(request, pk):
    """تعديل إعداد نظام"""
    setting = get_object_or_404(SystemSetting, pk=pk)
    if not setting.is_editable:
        messages.error(request, 'هذا الإعداد غير قابل للتعديل')
        return redirect('settings:system_settings_list')

    if request.method == 'POST':
        form = SystemSettingForm(request.POST, instance=setting)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث الإعداد بنجاح')
            return redirect('settings:system_settings_list')
    else:
        form = SystemSettingForm(instance=setting)

    context = {'form': form, 'title': 'تعديل الإعداد', 'setting': setting}
    return render(request, 'settings/system_setting_form.html', context)

@login_required
@user_passes_test(is_admin)
def system_setting_delete(request, pk):
    """حذف إعداد نظام"""
    setting = get_object_or_404(SystemSetting, pk=pk)
    if setting.is_required:
        messages.error(request, 'لا يمكن حذف هذا الإعداد لأنه مطلوب')
        return redirect('settings:system_settings_list')

    if request.method == 'POST':
        setting.delete()
        messages.success(request, 'تم حذف الإعداد بنجاح')
        return redirect('settings:system_settings_list')

    context = {'setting': setting}
    return render(request, 'settings/system_setting_confirm_delete.html', context)
