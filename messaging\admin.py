from django.contrib import admin
from .models import Message, MessageThread, MessageAttachment, UserMessageSettings


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ['subject', 'sender', 'recipient', 'priority', 'status', 'is_read', 'created_at']
    list_filter = ['priority', 'status', 'is_read', 'created_at']
    search_fields = ['subject', 'content', 'sender__username', 'recipient__username']
    readonly_fields = ['created_at', 'read_at']
    
    fieldsets = (
        ('معلومات الرسالة', {
            'fields': ('sender', 'recipient', 'subject', 'content')
        }),
        ('الإعدادات', {
            'fields': ('priority', 'status', 'is_read', 'is_starred')
        }),
        ('التواريخ', {
            'fields': ('created_at', 'read_at'),
            'classes': ('collapse',)
        }),
        ('خيارات متقدمة', {
            'fields': ('reply_to', 'attachment', 'is_deleted_by_sender', 'is_deleted_by_recipient'),
            'classes': ('collapse',)
        })
    )


@admin.register(MessageThread)
class MessageThreadAdmin(admin.ModelAdmin):
    list_display = ['subject', 'created_at', 'updated_at', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['subject']
    filter_horizontal = ['participants']


@admin.register(MessageAttachment)
class MessageAttachmentAdmin(admin.ModelAdmin):
    list_display = ['filename', 'message', 'file_size', 'uploaded_at']
    list_filter = ['uploaded_at']
    search_fields = ['filename', 'message__subject']


@admin.register(UserMessageSettings)
class UserMessageSettingsAdmin(admin.ModelAdmin):
    list_display = ['user', 'email_notifications', 'sound_notifications', 'auto_read_receipts']
    list_filter = ['email_notifications', 'sound_notifications', 'auto_read_receipts']
    search_fields = ['user__username']
