{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء فاتورة شراء{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-file-earmark-plus"></i>
                        إنشاء فاتورة شراء جديدة
                    </h4>
                </div>
                
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المورد <span class="text-danger">*</span></label>
                                    <select name="supplier" class="form-select" required>
                                        <option value="">اختر المورد</option>
                                        {% for supplier in suppliers %}
                                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الفاتورة</label>
                                    <input type="date" name="invoice_date" class="form-control" 
                                           value="{{ today|date:'Y-m-d' }}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">إجمالي الفاتورة</label>
                                    <input type="number" name="total_amount" class="form-control" 
                                           step="0.01" min="0" placeholder="0.00">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="2" 
                                              placeholder="ملاحظات إضافية (اختياري)"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- زر الحفظ البسيط -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="bi bi-check-circle"></i>
                                حفظ الفاتورة
                            </button>
                            <a href="{% url 'purchases:purchase_invoice_list' %}" class="btn btn-outline-secondary btn-lg ms-3">
                                <i class="bi bi-arrow-left"></i>
                                العودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
