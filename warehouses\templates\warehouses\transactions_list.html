{% extends 'base.html' %}
{% load static %}

{% block title %}حركات المخزون{% endblock %}

{% block extra_css %}
<style>
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .stat-card h3 {
        margin: 0 0 10px 0;
        font-size: 2.5rem;
        font-weight: bold;
    }

    .stat-card p {
        margin: 0;
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .filters-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .transactions-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table {
        margin: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table tbody td {
        padding: 15px;
        vertical-align: middle;
        border-bottom: 1px solid #f0f0f0;
    }

    .transaction-type-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .transaction-receipt {
        background: #d4edda;
        color: #155724;
    }

    .transaction-issue {
        background: #f8d7da;
        color: #721c24;
    }

    .transaction-transfer_in {
        background: #d1ecf1;
        color: #0c5460;
    }

    .transaction-transfer_out {
        background: #ffeaa7;
        color: #6c5ce7;
    }

    .transaction-adjustment_increase {
        background: #d4edda;
        color: #155724;
    }

    .transaction-adjustment_decrease {
        background: #f8d7da;
        color: #721c24;
    }

    .quantity-positive {
        color: #28a745;
        font-weight: bold;
    }

    .quantity-negative {
        color: #dc3545;
        font-weight: bold;
    }

    .search-bar {
        position: relative;
        margin-bottom: 20px;
    }

    .search-bar input {
        padding-right: 45px;
    }

    .search-bar .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }

    .pagination {
        justify-content: center;
        margin-top: 30px;
    }

    .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: none;
        color: #667eea;
    }

    .page-link:hover {
        background: #667eea;
        color: white;
    }

    .page-item.active .page-link {
        background: #667eea;
        border-color: #667eea;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="bi bi-arrow-left-right"></i>
            حركات المخزون
        </h1>
        <a href="{% url 'warehouses:dashboard' %}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right"></i>
            العودة إلى لوحة المخازن
        </a>
    </div>

    <!-- الإحصائيات -->
    <div class="stats-cards">
        <div class="stat-card">
            <h3>{{ total_transactions }}</h3>
            <p>إجمالي الحركات</p>
        </div>
        <div class="stat-card">
            <h3>{{ today_transactions }}</h3>
            <p>حركات اليوم</p>
        </div>
        <div class="stat-card">
            <h3>{{ transactions.paginator.count }}</h3>
            <p>نتائج البحث</p>
        </div>
    </div>

    <!-- الفلاتر -->
    <div class="filters-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">المخزن</label>
                <select name="warehouse" class="form-select">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                        <option value="{{ warehouse.id }}" {% if warehouse.id|stringformat:"s" == selected_warehouse %}selected{% endif %}>
                            {{ warehouse.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">المنتج</label>
                <select name="product" class="form-select">
                    <option value="">جميع المنتجات</option>
                    {% for product in products %}
                        <option value="{{ product.id }}" {% if product.id|stringformat:"s" == selected_product %}selected{% endif %}>
                            {{ product.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">نوع الحركة</label>
                <select name="transaction_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for type_code, type_name in transaction_types %}
                        <option value="{{ type_code }}" {% if type_code == selected_transaction_type %}selected{% endif %}>
                            {{ type_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            
            <div class="col-md-2">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            
            <div class="col-12">
                <div class="search-bar">
                    <input type="text" name="search" class="form-control" placeholder="البحث في الحركات..." value="{{ search_query }}">
                    <i class="bi bi-search search-icon"></i>
                </div>
            </div>
            
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-funnel"></i>
                    تطبيق الفلاتر
                </button>
                <a href="{% url 'warehouses:transactions_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i>
                    إعادة تعيين
                </a>
                <a href="{% url 'warehouses:dashboard' %}" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-right"></i>
                    العودة
                </a>
            </div>
        </form>
    </div>

    <!-- جدول الحركات -->
    <div class="transactions-table">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>رقم الحركة</th>
                    <th>نوع الحركة</th>
                    <th>المنتج</th>
                    <th>المخزن</th>
                    <th>الكمية</th>
                    <th>التكلفة</th>
                    <th>الرصيد</th>
                    <th>المرجع</th>
                    <th>التاريخ</th>
                    <th>المستخدم</th>
                </tr>
            </thead>
            <tbody>
                {% for transaction in transactions %}
                    <tr>
                        <td>
                            <strong>{{ transaction.transaction_number }}</strong>
                        </td>
                        <td>
                            <span class="transaction-type-badge transaction-{{ transaction.transaction_type }}">
                                {{ transaction.get_transaction_type_display }}
                            </span>
                        </td>
                        <td>
                            <div>
                                <strong>{{ transaction.inventory_item.product.name }}</strong>
                                <br>
                                <small class="text-muted">{{ transaction.inventory_item.product.code }}</small>
                            </div>
                        </td>
                        <td>
                            <strong>{{ transaction.inventory_item.warehouse.name }}</strong>
                        </td>
                        <td>
                            <span class="{% if transaction.transaction_type == 'receipt' or transaction.transaction_type == 'adjustment_increase' %}quantity-positive{% else %}quantity-negative{% endif %}">
                                {% if transaction.transaction_type == 'receipt' or transaction.transaction_type == 'adjustment_increase' %}+{% else %}-{% endif %}{{ transaction.quantity }}
                            </span>
                        </td>
                        <td>
                            {% if transaction.unit_cost %}
                                {{ transaction.unit_cost|floatformat:2 }} ج.م
                                <br>
                                <small class="text-muted">إجمالي: {{ transaction.total_cost|floatformat:2 }} ج.م</small>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">قبل: {{ transaction.balance_before }}</small>
                            <br>
                            <strong>بعد: {{ transaction.balance_after }}</strong>
                        </td>
                        <td>
                            {% if transaction.reference_number %}
                                <strong>{{ transaction.reference_number }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                            {% if transaction.notes %}
                                <br>
                                <small class="text-muted">{{ transaction.notes|truncatechars:50 }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ transaction.transaction_date|date:"d/m/Y" }}</strong>
                            <br>
                            <small class="text-muted">{{ transaction.transaction_date|time:"H:i" }}</small>
                        </td>
                        <td>
                            {% if transaction.created_by %}
                                {{ transaction.created_by.get_full_name|default:transaction.created_by.username }}
                            {% else %}
                                <span class="text-muted">النظام</span>
                            {% endif %}
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد حركات مخزون</p>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- الترقيم -->
    {% if transactions.has_other_pages %}
        <nav aria-label="ترقيم الصفحات">
            <ul class="pagination">
                {% if transactions.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}{% if request.GET.product %}&product={{ request.GET.product }}{% endif %}{% if request.GET.transaction_type %}&transaction_type={{ request.GET.transaction_type }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأولى</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transactions.previous_page_number }}{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}{% if request.GET.product %}&product={{ request.GET.product }}{% endif %}{% if request.GET.transaction_type %}&transaction_type={{ request.GET.transaction_type }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">السابقة</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">{{ transactions.number }} من {{ transactions.paginator.num_pages }}</span>
                </li>

                {% if transactions.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transactions.next_page_number }}{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}{% if request.GET.product %}&product={{ request.GET.product }}{% endif %}{% if request.GET.transaction_type %}&transaction_type={{ request.GET.transaction_type }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">التالية</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transactions.paginator.num_pages }}{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}{% if request.GET.product %}&product={{ request.GET.product }}{% endif %}{% if request.GET.transaction_type %}&transaction_type={{ request.GET.transaction_type }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأخيرة</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}
</div>
{% endblock %}
