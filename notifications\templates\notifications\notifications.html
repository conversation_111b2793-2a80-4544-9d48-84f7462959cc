<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            padding-top: 2rem;
        }
        
        .notifications-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .notifications-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .notifications-body {
            padding: 2rem;
        }
        
        .notification-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .notification-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .notification-unread {
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        
        .notification-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .notification-message {
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        
        .notification-time {
            font-size: 0.8rem;
            color: #adb5bd;
        }
        
        .priority-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .priority-low { background: #6c757d; color: white; }
        .priority-normal { background: #007bff; color: white; }
        .priority-high { background: #ffc107; color: black; }
        .priority-urgent { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="notifications-card">
                    <div class="notifications-header">
                        <h1><i class="bi bi-bell me-3"></i>الإشعارات</h1>
                        <p class="mb-0">جميع إشعاراتك في مكان واحد</p>
                    </div>
                    
                    <div class="notifications-body">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-primary">{{ stats.total|default:0 }}</h4>
                                        <p class="mb-0">إجمالي الإشعارات</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-warning">{{ stats.unread|default:0 }}</h4>
                                        <p class="mb-0">غير مقروءة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-success">{{ stats.today|default:0 }}</h4>
                                        <p class="mb-0">اليوم</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="notifications-list">
                            {% for notification in notifications %}
                            <div class="notification-item {% if not notification.is_read %}notification-unread{% endif %}">
                                <div class="notification-icon" style="background: {{ notification.get_type_color }};">
                                    <i class="{{ notification.get_type_icon }}"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title">{{ notification.title }}</div>
                                    <div class="notification-message">{{ notification.message }}</div>
                                    <div class="notification-time">{{ notification.time_since_created }}</div>
                                </div>
                                <div>
                                    <span class="priority-badge priority-{{ notification.priority }}">
                                        {% if notification.priority == 'low' %}منخفضة
                                        {% elif notification.priority == 'normal' %}عادية
                                        {% elif notification.priority == 'high' %}عالية
                                        {% elif notification.priority == 'urgent' %}عاجلة
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-center py-5">
                                <i class="bi bi-bell" style="font-size: 4rem; color: #dee2e6;"></i>
                                <h4 class="mt-3 text-muted">لا توجد إشعارات</h4>
                                <p class="text-muted">ستظهر إشعاراتك هنا</p>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <div class="mt-4 text-center">
                            <a href="/" class="btn btn-secondary">
                                <i class="bi bi-house"></i> العودة للرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
