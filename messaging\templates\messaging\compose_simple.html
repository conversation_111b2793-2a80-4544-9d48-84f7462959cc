<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رسالة جديدة - الرسائل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            padding-top: 2rem;
        }
        
        .compose-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .compose-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .compose-body {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 1px solid #dee2e6;
            padding: 0.75rem;
            margin-bottom: 1rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-send {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-send:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-cancel {
            background: #6c757d;
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-cancel:hover {
            background: #5a6268;
            color: white;
            text-decoration: none;
        }
        
        .user-search-results {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            max-height: 200px;
            overflow-y: auto;
            background: white;
            position: absolute;
            width: 100%;
            z-index: 1000;
            display: none;
        }
        
        .user-option {
            padding: 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .user-option:hover {
            background: #f8f9fa;
        }
        
        .selected-user {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="compose-card">
                    <div class="compose-header">
                        <h1><i class="bi bi-envelope-plus me-3"></i>رسالة جديدة</h1>
                        <p class="mb-0">إرسال رسالة إلى مستخدم آخر</p>
                    </div>
                    
                    <div class="compose-body">
                        <form method="post" id="composeForm">
                            {% csrf_token %}
                            
                            <!-- المستقبل -->
                            <div class="mb-3">
                                <label class="form-label fw-bold">إلى:</label>
                                {% if selected_recipient %}
                                <div class="selected-user">
                                    <div>
                                        <strong>{{ selected_recipient.get_full_name|default:selected_recipient.username }}</strong>
                                        <br>
                                        <small class="text-muted">{{ selected_recipient.email }}</small>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSelectedUser()">
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>
                                <input type="hidden" name="recipient" value="{{ selected_recipient.id }}" id="recipientId">
                                {% else %}
                                <div style="position: relative;">
                                    <input type="text" class="form-control" placeholder="ابحث عن مستخدم..." 
                                           id="userSearch" onkeyup="searchUsers(this.value)">
                                    <div class="user-search-results" id="userResults"></div>
                                </div>
                                <input type="hidden" name="recipient" id="recipientId" required>
                                {% endif %}
                            </div>
                            
                            <!-- الموضوع -->
                            <div class="mb-3">
                                <label class="form-label fw-bold">الموضوع:</label>
                                <input type="text" name="subject" class="form-control" placeholder="موضوع الرسالة..." required>
                            </div>
                            
                            <!-- الأولوية -->
                            <div class="mb-3">
                                <label class="form-label fw-bold">الأولوية:</label>
                                <select name="priority" class="form-control">
                                    <option value="low">منخفضة</option>
                                    <option value="normal" selected>عادية</option>
                                    <option value="high">عالية</option>
                                    <option value="urgent">عاجلة</option>
                                </select>
                            </div>
                            
                            <!-- المحتوى -->
                            <div class="mb-3">
                                <label class="form-label fw-bold">المحتوى:</label>
                                <textarea name="content" class="form-control" rows="6" placeholder="اكتب رسالتك هنا..." required></textarea>
                            </div>
                            
                            <!-- أزرار الإجراءات -->
                            <div class="d-flex gap-3 justify-content-end">
                                <a href="/messages/" class="btn-cancel">
                                    <i class="bi bi-x-circle"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn-send">
                                    <i class="bi bi-send"></i>
                                    إرسال الرسالة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let searchTimeout;
        
        function searchUsers(query) {
            clearTimeout(searchTimeout);
            const results = document.getElementById('userResults');
            
            if (query.length < 2) {
                results.style.display = 'none';
                return;
            }
            
            searchTimeout = setTimeout(() => {
                fetch(`/messages/api/search-users/?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    results.innerHTML = '';
                    
                    if (data.users && data.users.length > 0) {
                        data.users.forEach(user => {
                            const option = document.createElement('div');
                            option.className = 'user-option';
                            option.innerHTML = `
                                <strong>${user.full_name}</strong><br>
                                <small class="text-muted">${user.email}</small>
                            `;
                            option.onclick = () => selectUser(user);
                            results.appendChild(option);
                        });
                        results.style.display = 'block';
                    } else {
                        {% if user_language == 'en' %}
                            results.innerHTML = '<div class="user-option">No results found</div>';
                        {% else %}
                            results.innerHTML = '<div class="user-option">لا توجد نتائج</div>';
                        {% endif %}
                        results.style.display = 'block';
                    }
                })
                .catch(error => {
                    {% if user_language == 'en' %}
                        console.error('Search error:', error);
                    {% else %}
                        console.error('خطأ في البحث:', error);
                    {% endif %}
                });
            }, 300);
        }
        
        function selectUser(user) {
            document.getElementById('recipientId').value = user.id;
            document.getElementById('userSearch').value = user.full_name;
            document.getElementById('userResults').style.display = 'none';
        }
        
        function removeSelectedUser() {
            // هذه الوظيفة للمستقبل المحدد مسبقاً
            location.href = '/messages/compose/';
        }
        
        // إغلاق نتائج البحث عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!event.target.closest('#userSearch') && !event.target.closest('#userResults')) {
                document.getElementById('userResults').style.display = 'none';
            }
        });
        
        // التحقق من صحة النموذج
        document.getElementById('composeForm').addEventListener('submit', function(e) {
            const recipientId = document.getElementById('recipientId').value;
            if (!recipientId) {
                e.preventDefault();
                alert('يرجى اختيار مستقبل للرسالة');
                return false;
            }
        });
    </script>
</body>
</html>
