#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from definitions.models import ProductDefinition, ProductCategory
from django.contrib.auth.models import User

def create_sample_products():
    """إنشاء أصناف تجريبية"""
    
    # الحصول على المستخدم الأول
    user = User.objects.first()
    if not user:
        print("❌ لا يوجد مستخدمين في النظام")
        return
    
    # الحصول على الفئات
    electronics = ProductCategory.objects.filter(code='ELEC').first()
    clothing = ProductCategory.objects.filter(code='CLOTH').first()
    
    if not electronics:
        print("❌ لا توجد فئة الإلكترونيات")
        return
    
    print(f"✅ المستخدم: {user.username}")
    print(f"✅ فئة الإلكترونيات: {electronics.name}")
    if clothing:
        print(f"✅ فئة الملابس: {clothing.name}")
    
    # إنشاء أصناف إلكترونية
    products_data = [
        {
            'code': 'LAP001',
            'name': 'لابتوب ديل انسبايرون 15',
            'name_en': 'Dell Inspiron 15 Laptop',
            'barcode': '1234567890123',
            'category': electronics,
            'product_type': 'product',
            'main_unit': 'piece',
            'description': 'لابتوب ديل انسبايرون 15 بوصة، معالج Intel Core i5، ذاكرة 8GB، قرص صلب 512GB SSD',
            'cost_price': 15000.00,
            'selling_price': 18000.00,
            'minimum_stock': 5,
            'maximum_stock': 50,
            'is_active': True,
            'track_inventory': True,
            'created_by': user
        },
        {
            'code': 'PHN001',
            'name': 'هاتف سامسونج جالاكسي A54',
            'name_en': 'Samsung Galaxy A54',
            'barcode': '9876543210987',
            'category': electronics,
            'product_type': 'product',
            'main_unit': 'piece',
            'description': 'هاتف سامسونج جالاكسي A54، شاشة 6.4 بوصة، كاميرا 50 ميجابكسل، ذاكرة 128GB',
            'cost_price': 8000.00,
            'selling_price': 10000.00,
            'minimum_stock': 10,
            'maximum_stock': 100,
            'is_active': True,
            'track_inventory': True,
            'created_by': user
        },
        {
            'code': 'TAB001',
            'name': 'تابلت آيباد',
            'name_en': 'iPad Tablet',
            'barcode': '5555666677778',
            'category': electronics,
            'product_type': 'product',
            'main_unit': 'piece',
            'description': 'تابلت آيباد 10.9 بوصة، ذاكرة 64GB، واي فاي',
            'cost_price': 12000.00,
            'selling_price': 15000.00,
            'minimum_stock': 3,
            'maximum_stock': 30,
            'is_active': True,
            'track_inventory': True,
            'created_by': user
        },
        {
            'code': 'MSE001',
            'name': 'ماوس لاسلكي',
            'name_en': 'Wireless Mouse',
            'barcode': '1111222233334',
            'category': electronics,
            'product_type': 'product',
            'main_unit': 'piece',
            'description': 'ماوس لاسلكي بتقنية البلوتوث، دقة عالية، بطارية طويلة المدى',
            'cost_price': 150.00,
            'selling_price': 250.00,
            'minimum_stock': 20,
            'maximum_stock': 200,
            'is_active': True,
            'track_inventory': True,
            'created_by': user
        },
        {
            'code': 'KBD001',
            'name': 'لوحة مفاتيح ميكانيكية',
            'name_en': 'Mechanical Keyboard',
            'barcode': '4444555566667',
            'category': electronics,
            'product_type': 'product',
            'main_unit': 'piece',
            'description': 'لوحة مفاتيح ميكانيكية مع إضاءة RGB، مفاتيح زرقاء',
            'cost_price': 800.00,
            'selling_price': 1200.00,
            'minimum_stock': 10,
            'maximum_stock': 50,
            'is_active': True,
            'track_inventory': True,
            'created_by': user
        }
    ]
    
    # إضافة أصناف ملابس إذا كانت الفئة موجودة
    if clothing:
        products_data.extend([
            {
                'code': 'SHT001',
                'name': 'قميص قطني رجالي',
                'name_en': 'Men Cotton Shirt',
                'barcode': '7777888899990',
                'category': clothing,
                'product_type': 'product',
                'main_unit': 'piece',
                'description': 'قميص قطني رجالي، مقاس متوسط، لون أبيض',
                'cost_price': 80.00,
                'selling_price': 150.00,
                'minimum_stock': 15,
                'maximum_stock': 100,
                'is_active': True,
                'track_inventory': True,
                'created_by': user
            },
            {
                'code': 'PNT001',
                'name': 'بنطلون جينز',
                'name_en': 'Jeans Pants',
                'barcode': '2222333344445',
                'category': clothing,
                'product_type': 'product',
                'main_unit': 'piece',
                'description': 'بنطلون جينز أزرق، مقاس 32، قطن 100%',
                'cost_price': 120.00,
                'selling_price': 200.00,
                'minimum_stock': 12,
                'maximum_stock': 80,
                'is_active': True,
                'track_inventory': True,
                'created_by': user
            }
        ])
    
    # إنشاء الأصناف
    created_count = 0
    for product_data in products_data:
        product, created = ProductDefinition.objects.get_or_create(
            code=product_data['code'],
            defaults=product_data
        )
        
        if created:
            created_count += 1
            print(f"✅ تم إنشاء الصنف: {product.name}")
        else:
            print(f"⚠️  الصنف موجود بالفعل: {product.name}")
    
    print(f"\n🎉 تم إنشاء {created_count} صنف جديد")
    print(f"📊 إجمالي الأصناف في النظام: {ProductDefinition.objects.count()}")

if __name__ == '__main__':
    create_sample_products()
