<!DOCTYPE html>
<html>
<head>
    <title>رسالة جديدة - الرسائل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>

{% block extra_css %}
<style>
    .compose-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
        max-width: 800px;
        margin: 0 auto;
    }

    .compose-header {
        border-bottom: 2px solid #f3f4f6;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }

    .compose-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        border: 1px solid #d1d5db;
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    .user-search {
        position: relative;
    }

    .user-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }

    .user-option {
        padding: 0.75rem;
        cursor: pointer;
        border-bottom: 1px solid #f3f4f6;
        transition: all 0.3s ease;
    }

    .user-option:hover {
        background: #f8fafc;
    }

    .user-option:last-child {
        border-bottom: none;
    }

    .user-name {
        font-weight: 600;
        color: #1f2937;
    }

    .user-email {
        font-size: 0.85rem;
        color: #6b7280;
    }

    .priority-selector {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .priority-option {
        padding: 0.5rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .priority-option.selected {
        border-color: #667eea;
        background: #667eea;
        color: white;
    }

    .priority-option.low { border-color: #6b7280; }
    .priority-option.low.selected { background: #6b7280; }

    .priority-option.normal { border-color: #3b82f6; }
    .priority-option.normal.selected { background: #3b82f6; }

    .priority-option.high { border-color: #f59e0b; }
    .priority-option.high.selected { background: #f59e0b; }

    .priority-option.urgent { border-color: #ef4444; }
    .priority-option.urgent.selected { background: #ef4444; }

    .compose-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
    }

    .character-count {
        font-size: 0.8rem;
        color: #6b7280;
        text-align: left;
        margin-top: 0.25rem;
    }

    .selected-user {
        background: #f0f9ff;
        border: 1px solid #3b82f6;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .selected-user-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .selected-user-avatar {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .remove-user {
        background: none;
        border: none;
        color: #ef4444;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
    }

    .remove-user:hover {
        background: #fee2e2;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="compose-container">
                <div class="compose-header">
                    <h1 class="compose-title">
                        <i class="bi bi-envelope-plus"></i>
                        رسالة جديدة
                    </h1>
                </div>

                <form method="post" id="composeForm">
                    {% csrf_token %}
                    
                    <!-- المستقبل -->
                    <div class="form-group">
                        <label class="form-label">إلى:</label>
                        {% if selected_recipient %}
                        <div class="selected-user">
                            <div class="selected-user-info">
                                <div class="selected-user-avatar">
                                    {{ selected_recipient.first_name.0|default:selected_recipient.username.0|upper }}
                                </div>
                                <div>
                                    <div class="user-name">{{ selected_recipient.get_full_name|default:selected_recipient.username }}</div>
                                    <div class="user-email">{{ selected_recipient.email }}</div>
                                </div>
                            </div>
                            <button type="button" class="remove-user" onclick="removeSelectedUser()">
                                <i class="bi bi-x-lg"></i>
                            </button>
                        </div>
                        <input type="hidden" name="recipient" value="{{ selected_recipient.id }}" id="recipientId">
                        {% else %}
                        <div class="user-search">
                            <input type="text" class="form-control" placeholder="ابحث عن مستخدم..." 
                                   id="userSearch" onkeyup="searchUsers(this.value)">
                            <div class="user-dropdown" id="userDropdown"></div>
                        </div>
                        <input type="hidden" name="recipient" id="recipientId">
                        {% endif %}
                    </div>

                    <!-- الموضوع -->
                    <div class="form-group">
                        <label class="form-label">الموضوع:</label>
                        <input type="text" name="subject" class="form-control" placeholder="موضوع الرسالة..." required>
                    </div>

                    <!-- الأولوية -->
                    <div class="form-group">
                        <label class="form-label">الأولوية:</label>
                        <div class="priority-selector">
                            <div class="priority-option low" data-priority="low">
                                <i class="bi bi-arrow-down me-1"></i>
                                منخفضة
                            </div>
                            <div class="priority-option normal selected" data-priority="normal">
                                <i class="bi bi-dash me-1"></i>
                                عادية
                            </div>
                            <div class="priority-option high" data-priority="high">
                                <i class="bi bi-arrow-up me-1"></i>
                                عالية
                            </div>
                            <div class="priority-option urgent" data-priority="urgent">
                                <i class="bi bi-exclamation-triangle me-1"></i>
                                عاجلة
                            </div>
                        </div>
                        <input type="hidden" name="priority" value="normal" id="priorityInput">
                    </div>

                    <!-- المحتوى -->
                    <div class="form-group">
                        <label class="form-label">المحتوى:</label>
                        <textarea name="content" class="form-control" rows="8" placeholder="اكتب رسالتك هنا..." 
                                  required onkeyup="updateCharacterCount(this)"></textarea>
                        <div class="character-count" id="charCount">0 حرف</div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="compose-actions">
                        <a href="{% url 'messaging:inbox' %}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-send"></i>
                            إرسال الرسالة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let searchTimeout;
let selectedUser = null;

// البحث عن المستخدمين
function searchUsers(query) {
    clearTimeout(searchTimeout);
    
    if (query.length < 2) {
        document.getElementById('userDropdown').style.display = 'none';
        return;
    }
    
    searchTimeout = setTimeout(() => {
        fetch(`/messages/api/search-users/?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            const dropdown = document.getElementById('userDropdown');
            dropdown.innerHTML = '';
            
            if (data.users.length > 0) {
                data.users.forEach(user => {
                    const option = document.createElement('div');
                    option.className = 'user-option';
                    option.innerHTML = `
                        <div class="user-name">${user.full_name}</div>
                        <div class="user-email">${user.email}</div>
                    `;
                    option.onclick = () => selectUser(user);
                    dropdown.appendChild(option);
                });
                dropdown.style.display = 'block';
            } else {
                dropdown.style.display = 'none';
            }
        });
    }, 300);
}

// اختيار مستخدم
function selectUser(user) {
    selectedUser = user;
    document.getElementById('recipientId').value = user.id;
    document.getElementById('userSearch').value = user.full_name;
    document.getElementById('userDropdown').style.display = 'none';
}

// إزالة المستخدم المحدد
function removeSelectedUser() {
    document.querySelector('.selected-user').remove();
    const searchHtml = `
        <div class="user-search">
            <input type="text" class="form-control" placeholder="ابحث عن مستخدم..." 
                   id="userSearch" onkeyup="searchUsers(this.value)">
            <div class="user-dropdown" id="userDropdown"></div>
        </div>
    `;
    document.querySelector('.form-group').insertAdjacentHTML('beforeend', searchHtml);
    document.getElementById('recipientId').value = '';
}

// اختيار الأولوية
document.querySelectorAll('.priority-option').forEach(option => {
    option.addEventListener('click', function() {
        document.querySelectorAll('.priority-option').forEach(opt => opt.classList.remove('selected'));
        this.classList.add('selected');
        document.getElementById('priorityInput').value = this.dataset.priority;
    });
});

// عداد الأحرف
function updateCharacterCount(textarea) {
    const count = textarea.value.length;
    document.getElementById('charCount').textContent = `${count} حرف`;
}

// إغلاق القائمة المنسدلة عند النقر خارجها
document.addEventListener('click', function(event) {
    if (!event.target.closest('.user-search')) {
        document.getElementById('userDropdown').style.display = 'none';
    }
});

// التحقق من صحة النموذج
document.getElementById('composeForm').addEventListener('submit', function(e) {
    const recipientId = document.getElementById('recipientId').value;
    if (!recipientId) {
        e.preventDefault();
        alert('يرجى اختيار مستقبل للرسالة');
        return false;
    }
});
</script>

</body>
</html>
