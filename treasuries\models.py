from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal

class Treasury(models.Model):
    """نموذج الخزائن"""
    name = models.CharField(max_length=200, verbose_name="اسم الخزينة")
    code = models.CharField(max_length=20, unique=True, verbose_name="كود الخزينة")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    location = models.CharField(max_length=200, blank=True, null=True, verbose_name="الموقع")
    responsible_person = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المسؤول")
    opening_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد الافتتاحي")
    current_balance = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الرصيد الحالي")
    max_limit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الحد الأقصى للرصيد")
    min_limit = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="الحد الأدنى للرصيد")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "خزينة"
        verbose_name_plural = "الخزائن"
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def is_over_limit(self):
        """هل الرصيد يتجاوز الحد الأقصى"""
        return self.current_balance > self.max_limit if self.max_limit > 0 else False

    @property
    def is_under_limit(self):
        """هل الرصيد أقل من الحد الأدنى"""
        return self.current_balance < self.min_limit

class TreasuryTransaction(models.Model):
    """نموذج معاملات الخزينة"""
    TRANSACTION_TYPE_CHOICES = [
        ('receipt', 'قبض'),
        ('payment', 'دفع'),
        ('transfer_in', 'تحويل وارد'),
        ('transfer_out', 'تحويل صادر'),
        ('adjustment', 'تسوية'),
        ('opening_balance', 'رصيد افتتاحي'),
    ]

    STATUS_CHOICES = [
        ('pending', 'معلق'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]

    treasury = models.ForeignKey(Treasury, related_name='transactions', on_delete=models.CASCADE, verbose_name="الخزينة")
    transaction_date = models.DateField(verbose_name="تاريخ المعاملة")
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES, verbose_name="نوع المعاملة")
    amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="المبلغ")
    description = models.TextField(verbose_name="وصف المعاملة")
    reference_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="رقم المرجع")
    from_treasury = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True,
                                     related_name='transfers_out', verbose_name="من الخزينة")
    to_treasury = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='transfers_in', verbose_name="إلى الخزينة")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="الحالة")
    balance_before = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="الرصيد قبل المعاملة")
    balance_after = models.DecimalField(max_digits=15, decimal_places=2, verbose_name="الرصيد بعد المعاملة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "معاملة خزينة"
        verbose_name_plural = "معاملات الخزائن"
        ordering = ['-transaction_date', '-created_at']

    def __str__(self):
        return f"{self.get_transaction_type_display()} - {self.amount} - {self.treasury.name}"

    def save(self, *args, **kwargs):
        """تحديث رصيد الخزينة عند حفظ المعاملة"""
        if not self.pk and self.status == 'completed':  # معاملة جديدة ومكتملة
            self.balance_before = self.treasury.current_balance

            if self.transaction_type in ['receipt', 'transfer_in', 'opening_balance']:
                self.balance_after = self.balance_before + self.amount
            elif self.transaction_type in ['payment', 'transfer_out']:
                self.balance_after = self.balance_before - self.amount
            else:  # adjustment
                self.balance_after = self.balance_before + self.amount

            # تحديث رصيد الخزينة
            self.treasury.current_balance = self.balance_after
            self.treasury.save()

        super().save(*args, **kwargs)

class TreasuryTransfer(models.Model):
    """نموذج التحويلات بين الخزائن"""
    STATUS_CHOICES = [
        ('pending', 'معلق'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]

    from_treasury = models.ForeignKey(Treasury, related_name='outgoing_transfers', on_delete=models.CASCADE, verbose_name="من الخزينة")
    to_treasury = models.ForeignKey(Treasury, related_name='incoming_transfers', on_delete=models.CASCADE, verbose_name="إلى الخزينة")
    amount = models.DecimalField(max_digits=15, decimal_places=2, validators=[MinValueValidator(0.01)], verbose_name="المبلغ")
    transfer_date = models.DateField(verbose_name="تاريخ التحويل")
    description = models.TextField(verbose_name="وصف التحويل")
    reference_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="رقم المرجع")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="الحالة")
    approved_by = models.ForeignKey(User, related_name='approved_transfers', on_delete=models.SET_NULL, null=True, blank=True, verbose_name="تمت الموافقة بواسطة")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "تحويل بين الخزائن"
        verbose_name_plural = "التحويلات بين الخزائن"
        ordering = ['-transfer_date', '-created_at']

    def __str__(self):
        return f"تحويل {self.amount} من {self.from_treasury.name} إلى {self.to_treasury.name}"

    def approve_transfer(self, approved_by_user):
        """الموافقة على التحويل وتنفيذه"""
        if self.status == 'pending':
            # إنشاء معاملة خروج من الخزينة المصدر
            TreasuryTransaction.objects.create(
                treasury=self.from_treasury,
                transaction_date=self.transfer_date,
                transaction_type='transfer_out',
                amount=self.amount,
                description=f"تحويل إلى {self.to_treasury.name}: {self.description}",
                reference_number=self.reference_number,
                status='completed',
                created_by=approved_by_user
            )

            # إنشاء معاملة دخول للخزينة المستقبلة
            TreasuryTransaction.objects.create(
                treasury=self.to_treasury,
                transaction_date=self.transfer_date,
                transaction_type='transfer_in',
                amount=self.amount,
                description=f"تحويل من {self.from_treasury.name}: {self.description}",
                reference_number=self.reference_number,
                status='completed',
                created_by=approved_by_user
            )

            self.status = 'completed'
            self.approved_by = approved_by_user
            self.save()
