{% extends 'base.html' %}
{% load static %}

{% block title %}الملخص المالي للموردين - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .summary-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-left: 5px solid #6f42c1;
    }

    .summary-card h3 {
        margin: 0 0 10px 0;
        font-size: 2.5rem;
        font-weight: bold;
        color: #6f42c1;
    }

    .summary-card p {
        margin: 0;
        font-size: 1.1rem;
        color: #6c757d;
    }

    .summary-card.debt {
        border-left-color: #dc3545;
    }

    .summary-card.debt h3 {
        color: #dc3545;
    }

    .summary-card.paid {
        border-left-color: #28a745;
    }

    .summary-card.paid h3 {
        color: #28a745;
    }

    .summary-card.total {
        border-left-color: #17a2b8;
    }

    .summary-card.total h3 {
        color: #17a2b8;
    }

    .suppliers-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table {
        margin: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table tbody td {
        padding: 15px;
        vertical-align: middle;
        border-bottom: 1px solid #f0f0f0;
    }

    .progress-bar-custom {
        height: 8px;
        border-radius: 4px;
        background: #e9ecef;
        overflow: hidden;
        margin-top: 5px;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(45deg, #28a745, #20c997);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .amount-highlight {
        font-size: 1.1rem;
        font-weight: bold;
    }

    .amount-debt {
        color: #dc3545;
    }

    .amount-paid {
        color: #28a745;
    }

    .amount-total {
        color: #17a2b8;
    }

    .status-summary {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .status-item {
        background: #f8f9fa;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        border: 1px solid #dee2e6;
    }

    .status-paid {
        background: #d4edda;
        color: #155724;
        border-color: #c3e6cb;
    }

    .status-partial {
        background: #fff3cd;
        color: #856404;
        border-color: #ffeaa7;
    }

    .status-overdue {
        background: #f8d7da;
        color: #721c24;
        border-color: #f5c6cb;
    }

    .btn-export {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .supplier-name {
        font-weight: bold;
        color: #2c3e50;
    }

    .supplier-name:hover {
        color: #6f42c1;
        text-decoration: none;
    }

    .overall-progress {
        background: white;
        padding: 20px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .progress-large {
        height: 30px;
        border-radius: 15px;
        background: #e9ecef;
        overflow: hidden;
    }

    .progress-large .progress-bar {
        background: linear-gradient(45deg, #28a745, #20c997);
        height: 100%;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-people-fill"></i>
                    الملخص المالي للموردين
                </h1>
                <p class="mb-0">تقرير شامل عن الديون والمدفوعات لجميع الموردين</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-export me-2" onclick="window.print()">
                    <i class="bi bi-printer"></i>
                    طباعة التقرير
                </button>
                <a href="{% url 'purchases:reports' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right"></i>
                    العودة
                </a>
            </div>
        </div>
    </div>

    <!-- الملخص العام -->
    <div class="summary-cards">
        <div class="summary-card debt">
            <h3>{{ total_debt|floatformat:0 }}</h3>
            <p>إجمالي الديون (ج.م)</p>
        </div>
        <div class="summary-card paid">
            <h3>{{ total_paid|floatformat:0 }}</h3>
            <p>إجمالي المدفوع (ج.م)</p>
        </div>
        <div class="summary-card total">
            <h3>{{ total_purchases|floatformat:0 }}</h3>
            <p>إجمالي المشتريات (ج.م)</p>
        </div>
        <div class="summary-card">
            <h3>{{ suppliers_count }}</h3>
            <p>عدد الموردين النشطين</p>
        </div>
    </div>

    <!-- شريط التقدم العام -->
    <div class="overall-progress">
        <h4 class="mb-3">
            <i class="bi bi-graph-up"></i>
            نسبة السداد الإجمالية
        </h4>
        <div class="progress-large">
            <div class="progress-bar" style="width: {{ overall_payment_percentage|floatformat:1 }}%;">
                {{ overall_payment_percentage|floatformat:1 }}% مدفوع
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-4">
                <small class="text-muted">المدفوع: {{ total_paid|floatformat:0 }} ج.م</small>
            </div>
            <div class="col-md-4 text-center">
                <small class="text-muted">المتبقي: {{ total_debt|floatformat:0 }} ج.م</small>
            </div>
            <div class="col-md-4 text-end">
                <small class="text-muted">الإجمالي: {{ total_purchases|floatformat:0 }} ج.م</small>
            </div>
        </div>
    </div>

    <!-- جدول الموردين -->
    <div class="suppliers-table">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>المورد</th>
                    <th>إجمالي المشتريات</th>
                    <th>المدفوع</th>
                    <th>المتبقي</th>
                    <th>نسبة السداد</th>
                    <th>حالة الفواتير</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for data in suppliers_data %}
                    <tr>
                        <td>
                            <a href="{% url 'purchases:supplier_detail' data.supplier.pk %}" class="supplier-name">
                                {{ data.supplier.name }}
                            </a>
                            <br>
                            <small class="text-muted">{{ data.invoices_count }} فاتورة</small>
                        </td>
                        <td>
                            <span class="amount-highlight amount-total">
                                {{ data.total_purchases|floatformat:2 }} ج.م
                            </span>
                        </td>
                        <td>
                            <span class="amount-highlight amount-paid">
                                {{ data.total_paid|floatformat:2 }} ج.م
                            </span>
                        </td>
                        <td>
                            <span class="amount-highlight amount-debt">
                                {{ data.total_remaining|floatformat:2 }} ج.م
                            </span>
                        </td>
                        <td>
                            <div>
                                <strong>{{ data.payment_percentage|floatformat:1 }}%</strong>
                                <div class="progress-bar-custom">
                                    <div class="progress-fill" style="width: {{ data.payment_percentage|floatformat:1 }}%;"></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="status-summary">
                                {% if data.paid_invoices > 0 %}
                                    <span class="status-item status-paid">
                                        {{ data.paid_invoices }} مدفوعة
                                    </span>
                                {% endif %}
                                {% if data.partially_paid_invoices > 0 %}
                                    <span class="status-item status-partial">
                                        {{ data.partially_paid_invoices }} جزئية
                                    </span>
                                {% endif %}
                                {% if data.overdue_invoices > 0 %}
                                    <span class="status-item status-overdue">
                                        {{ data.overdue_invoices }} متأخرة
                                    </span>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <a href="{% url 'purchases:supplier_detail' data.supplier.pk %}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-eye"></i>
                                عرض
                            </a>
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد بيانات موردين</p>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- ملخص إضافي -->
    {% if suppliers_data %}
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header" style="background: #6f42c1; color: white;">
                        <h5 class="mb-0">تحليل الديون</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>أعلى دين:</strong>
                            </div>
                            <div class="col-6">
                                {% with highest_debt=suppliers_data.0 %}
                                    {{ highest_debt.total_remaining|floatformat:2 }} ج.م
                                    <br>
                                    <small class="text-muted">{{ highest_debt.supplier.name }}</small>
                                {% endwith %}
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>متوسط الدين:</strong>
                            </div>
                            <div class="col-6">
                                {{ average_debt|floatformat:2 }} ج.م
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>نسبة السداد العامة:</strong>
                            </div>
                            <div class="col-6">
                                <span class="text-success">{{ overall_payment_percentage|floatformat:1 }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين الطباعة
    window.addEventListener('beforeprint', function() {
        document.body.classList.add('printing');
    });
    
    window.addEventListener('afterprint', function() {
        document.body.classList.remove('printing');
    });
</script>

<style>
    @media print {
        .btn, .card-header {
            display: none !important;
        }
        
        .page-header {
            background: #6f42c1 !important;
            -webkit-print-color-adjust: exact;
        }
        
        .table thead th {
            background: #6f42c1 !important;
            -webkit-print-color-adjust: exact;
        }
    }
</style>
{% endblock %}
