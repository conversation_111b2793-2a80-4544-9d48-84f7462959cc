#!/usr/bin/env python
import os
import sys
import django
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
    
    # تشغيل migrations أولاً
    try:
        print("🔧 تطبيق migrations...")
        execute_from_command_line(['manage.py', 'migrate', '--fake-initial'])
        print("✅ تم تطبيق migrations بنجاح")
    except Exception as e:
        print(f"⚠️ خطأ في migrations: {e}")
    
    # تشغيل الخادم
    try:
        print("🚀 تشغيل الخادم...")
        execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8000'])
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
