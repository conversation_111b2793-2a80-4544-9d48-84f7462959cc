<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير العملاء - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .filter-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #ffecd2;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 20px;
            color: white;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 500;
        }
        
        .icon-total { background: linear-gradient(45deg, #28a745, #20c997); }
        .icon-active { background: linear-gradient(45deg, #17a2b8, #138496); }
        .icon-credit { background: linear-gradient(45deg, #ffc107, #e0a800); }
        .icon-balance { background: linear-gradient(45deg, #6f42c1, #5a32a3); }
        
        .customers-table {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            text-align: center;
            border-bottom: 3px solid #ffecd2;
            padding-bottom: 15px;
        }
        
        .table th {
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
            color: white;
            border: none;
            padding: 15px;
            font-weight: 600;
        }
        
        .table td {
            padding: 15px;
            vertical-align: middle;
            border-color: #e9ecef;
        }
        
        .customer-row:hover {
            background-color: #f8f9fa;
        }
        
        .customer-type {
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: 600;
            font-size: 0.8rem;
            text-align: center;
        }
        
        .type-retail { background: #e3f2fd; color: #1976d2; }
        .type-wholesale { background: #f3e5f5; color: #7b1fa2; }
        .type-credit { background: #fff3e0; color: #f57c00; }
        
        .balance-display {
            font-weight: 700;
            text-align: center;
        }
        
        .balance-positive { color: #28a745; }
        .balance-negative { color: #dc3545; }
        .balance-zero { color: #6c757d; }
        
        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .btn-export {
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 236, 210, 0.3);
            color: white;
        }
        
        .top-customers {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .customer-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .customer-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
        }
        
        .customer-info h6 {
            margin: 0;
            font-weight: 600;
            color: #333;
        }
        
        .customer-info small {
            color: #6c757d;
        }
        
        .customer-sales {
            text-align: left;
            font-weight: 700;
            color: #ffecd2;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-people"></i>
                        تقرير العملاء
                    </h1>
                    <p class="mb-0">تقرير شامل عن العملاء وأنشطتهم التجارية</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-export me-2" onclick="exportReport()">
                        <i class="bi bi-download"></i>
                        تصدير التقرير
                    </button>
                    <a href="{% url 'sales:reports_dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
        </div>

        <!-- قسم الفلترة -->
        <div class="filter-section">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">نوع التقرير</label>
                    <select name="report_type" class="form-select">
                        <option value="all" {% if report_type == 'all' %}selected{% endif %}>جميع العملاء</option>
                        <option value="balances" {% if report_type == 'balances' %}selected{% endif %}>أرصدة العملاء</option>
                        <option value="top_customers" {% if report_type == 'top_customers' %}selected{% endif %}>أفضل العملاء</option>
                        <option value="inactive" {% if report_type == 'inactive' %}selected{% endif %}>عملاء غير نشطين</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع العميل</label>
                    <select name="customer_type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="retail" {% if customer_type == 'retail' %}selected{% endif %}>تجزئة</option>
                        <option value="wholesale" {% if customer_type == 'wholesale' %}selected{% endif %}>جملة</option>
                        <option value="credit" {% if customer_type == 'credit' %}selected{% endif %}>آجل</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="اسم العميل أو رقم الهاتف" value="{{ search }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                        تطبيق الفلتر
                    </button>
                </div>
            </form>
        </div>

        <!-- الإحصائيات العامة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon icon-total">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stat-number">{{ total_customers }}</div>
                <div class="stat-label">إجمالي العملاء</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-active">
                    <i class="bi bi-person-check"></i>
                </div>
                <div class="stat-number">{{ active_customers }}</div>
                <div class="stat-label">عملاء نشطين</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-credit">
                    <i class="bi bi-credit-card"></i>
                </div>
                <div class="stat-number">{{ credit_customers }}</div>
                <div class="stat-label">عملاء آجل</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-balance">
                    <i class="bi bi-currency-dollar"></i>
                </div>
                <div class="stat-number">{{ total_balance|floatformat:0 }}</div>
                <div class="stat-label">إجمالي الأرصدة (ج.م)</div>
            </div>
        </div>

        <!-- أفضل العملاء -->
        <div class="top-customers">
            <h3 class="section-title">أفضل العملاء (حسب المبيعات)</h3>
            
            {% for customer in top_customers %}
                <div class="customer-item">
                    <div class="customer-info">
                        <h6>{{ customer.name }}</h6>
                        <small>
                            {% if customer.customer_type == 'retail' %}
                                تجزئة
                            {% elif customer.customer_type == 'wholesale' %}
                                جملة
                            {% elif customer.customer_type == 'credit' %}
                                آجل
                            {% endif %}
                            | عدد الفواتير: {{ customer.invoice_count }}
                        </small>
                    </div>
                    <div class="customer-sales">
                        {{ customer.total_sales|floatformat:2 }} ج.م
                    </div>
                </div>
            {% empty %}
                <div class="text-center text-muted py-4">
                    <i class="bi bi-person" style="font-size: 3rem; opacity: 0.5;"></i>
                    <p class="mt-2">لا توجد بيانات عملاء</p>
                </div>
            {% endfor %}
        </div>

        <!-- جدول العملاء التفصيلي -->
        {% if customers %}
            <div class="customers-table">
                <h3 class="section-title">تفاصيل العملاء</h3>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>النوع</th>
                                <th>الهاتف</th>
                                <th>الرصيد الحالي</th>
                                <th>حد الائتمان</th>
                                <th>إجمالي المبيعات</th>
                                <th>آخر فاتورة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers %}
                                <tr class="customer-row">
                                    <td>
                                        <a href="{% url 'sales:customer_detail' customer.pk %}" class="text-decoration-none">
                                            <strong>{{ customer.name }}</strong>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="customer-type type-{{ customer.customer_type }}">
                                            {% if customer.customer_type == 'retail' %}
                                                تجزئة
                                            {% elif customer.customer_type == 'wholesale' %}
                                                جملة
                                            {% elif customer.customer_type == 'credit' %}
                                                آجل
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>{{ customer.phone|default:"غير محدد" }}</td>
                                    <td>
                                        <span class="balance-display {% if customer.current_balance > 0 %}balance-positive{% elif customer.current_balance < 0 %}balance-negative{% else %}balance-zero{% endif %}">
                                            {{ customer.current_balance|floatformat:2 }} ج.م
                                        </span>
                                    </td>
                                    <td>{{ customer.credit_limit|default:"غير محدد"|floatformat:2 }} ج.م</td>
                                    <td>{{ customer.total_sales|floatformat:2 }} ج.م</td>
                                    <td>{{ customer.last_invoice_date|date:"Y/m/d"|default:"لا توجد" }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- الترقيم -->
                {% if customers.has_other_pages %}
                    <nav aria-label="ترقيم الصفحات" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if customers.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ customers.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if customer_type %}&customer_type={{ customer_type }}{% endif %}{% if report_type %}&report_type={{ report_type }}{% endif %}">
                                        السابق
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in customers.paginator.page_range %}
                                {% if customers.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > customers.number|add:'-3' and num < customers.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if customer_type %}&customer_type={{ customer_type }}{% endif %}{% if report_type %}&report_type={{ report_type }}{% endif %}">
                                            {{ num }}
                                        </a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if customers.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ customers.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if customer_type %}&customer_type={{ customer_type }}{% endif %}{% if report_type %}&report_type={{ report_type }}{% endif %}">
                                        التالي
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            </div>
        {% else %}
            <div class="empty-state">
                <i class="bi bi-people"></i>
                <h3>لا توجد عملاء</h3>
                <p>لم يتم العثور على عملاء يطابقون معايير البحث</p>
            </div>
        {% endif %}

        <!-- رسوم بيانية -->
        <div class="row">
            <div class="col-md-6">
                <div class="chart-container">
                    <h3 class="section-title">توزيع العملاء حسب النوع</h3>
                    <canvas id="customerTypeChart" width="400" height="300"></canvas>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="chart-container">
                    <h3 class="section-title">توزيع الأرصدة</h3>
                    <canvas id="balanceChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .customers-table, .top-customers');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });

            // رسم بياني لتوزيع العملاء
            const typeCtx = document.getElementById('customerTypeChart').getContext('2d');
            const typeChart = new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['تجزئة', 'جملة', 'آجل'],
                    datasets: [{
                        data: {{ customer_type_distribution|safe }},
                        backgroundColor: [
                            '#ffecd2',
                            '#fcb69f',
                            '#a8edea'
                        ],
                        borderWidth: 3,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });

            // رسم بياني للأرصدة
            const balanceCtx = document.getElementById('balanceChart').getContext('2d');
            const balanceChart = new Chart(balanceCtx, {
                type: 'bar',
                data: {
                    labels: ['أرصدة موجبة', 'أرصدة سالبة', 'أرصدة صفر'],
                    datasets: [{
                        label: 'عدد العملاء',
                        data: {{ balance_distribution|safe }},
                        backgroundColor: [
                            '#28a745',
                            '#dc3545',
                            '#6c757d'
                        ],
                        borderWidth: 2,
                        borderRadius: 10
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });

        function exportReport() {
            // تصدير التقرير إلى PDF أو Excel
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'pdf');
            window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
        }
    </script>
</body>
</html>
