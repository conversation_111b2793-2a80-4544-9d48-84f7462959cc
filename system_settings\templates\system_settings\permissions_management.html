{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .permissions-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .permissions-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .search-container {
        background: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .groups-section {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .group-card {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 15px;
        overflow: hidden;
    }
    
    .group-header {
        background: #f8f9fa;
        padding: 15px;
        border-bottom: 1px solid #e9ecef;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .group-header:hover {
        background: #e9ecef;
    }
    
    .group-content {
        padding: 15px;
        display: none;
    }
    
    .group-content.show {
        display: block;
    }
    
    .permission-item {
        padding: 8px 12px;
        margin: 5px 0;
        background: #f8f9fa;
        border-radius: 5px;
        border-left: 3px solid #667eea;
        font-size: 14px;
    }
    
    .btn-create {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    .search-input {
        border: 2px solid #e9ecef;
        border-radius: 25px;
        padding: 10px 20px;
        font-size: 14px;
        transition: all 0.3s ease;
    }
    
    .search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .no-groups {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    
    .no-groups i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #dee2e6;
    }
    
    .group-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .permission-count {
        background: #667eea;
        color: white;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="permissions-header">
        <h2><i class="fas fa-shield-alt me-2"></i>{{ title }}</h2>
        <p class="mb-0">إدارة الأدوار والصلاحيات في النظام</p>
    </div>

    <!-- Search and Actions -->
    <div class="search-container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text bg-transparent border-end-0">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" class="form-control search-input border-start-0" 
                           placeholder="البحث في الأدوار..." id="searchInput">
                </div>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-create" onclick="setupPermissions()">
                    <i class="fas fa-cog me-2"></i>إعداد الصلاحيات
                </button>
            </div>
        </div>
    </div>

    <!-- Groups/Roles Section -->
    <div class="groups-section">
        {% if groups %}
            {% for group in groups %}
            <div class="group-card">
                <div class="group-header" onclick="toggleGroup({{ group.id }})">
                    <div class="group-stats">
                        <div>
                            <h5 class="mb-1">
                                <i class="fas fa-users me-2"></i>{{ group.name }}
                            </h5>
                            <small class="text-muted">{{ group.user_set.count }} مستخدم</small>
                        </div>
                        <div>
                            <span class="permission-count">{{ group.permissions.count }} صلاحية</span>
                            <i class="fas fa-chevron-down ms-2" id="icon-{{ group.id }}"></i>
                        </div>
                    </div>
                </div>
                <div class="group-content" id="content-{{ group.id }}">
                    <h6 class="mb-3">الصلاحيات المخصصة:</h6>
                    {% if group.permissions.all %}
                        <div class="row">
                            {% for permission in group.permissions.all %}
                            <div class="col-md-6 col-lg-4">
                                <div class="permission-item">
                                    <i class="fas fa-check text-success me-2"></i>
                                    {{ permission.name }}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">لا توجد صلاحيات مخصصة لهذا الدور</p>
                    {% endif %}
                    
                    <div class="mt-3">
                        <a href="{% url 'system_settings:edit_group_permissions' group.id %}"
                           class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-edit me-1"></i>تعديل الصلاحيات
                        </a>
                        <a href="{% url 'system_settings:group_users' group.id %}"
                           class="btn btn-sm btn-outline-info">
                            <i class="fas fa-users me-1"></i>إدارة المستخدمين
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
        <div class="no-groups">
            <i class="fas fa-users"></i>
            <h4>لا توجد أدوار</h4>
            <p>لم يتم إنشاء أي أدوار في النظام بعد</p>
            <button class="btn btn-create" onclick="setupPermissions()">
                <i class="fas fa-plus me-2"></i>إعداد الأدوار والصلاحيات
            </button>
        </div>
        {% endif %}
    </div>
</div>

<script>
function toggleGroup(groupId) {
    const content = document.getElementById('content-' + groupId);
    const icon = document.getElementById('icon-' + groupId);
    
    if (content.classList.contains('show')) {
        content.classList.remove('show');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    } else {
        content.classList.add('show');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    }
}

function setupPermissions() {
    if (confirm('هل تريد إعادة إعداد نظام الصلاحيات؟')) {
        fetch('{% url "system_settings:setup_permissions" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إعداد نظام الصلاحيات بنجاح!');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function showGroupUsers(groupId, groupName) {
    // يمكن إضافة modal أو توجيه لصفحة منفصلة
    alert(`عرض مستخدمي مجموعة "${groupName}"\nسيتم إضافة هذه الوظيفة قريباً`);
}

document.addEventListener('DOMContentLoaded', function() {
    // البحث في الأدوار
    const searchInput = document.getElementById('searchInput');
    const groupCards = document.querySelectorAll('.group-card');
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        groupCards.forEach(card => {
            const text = card.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });
    });
});
</script>
{% endblock %}
