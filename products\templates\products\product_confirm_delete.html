{% extends 'base.html' %}

{% block title %}حذف المنتج {{ product.name }} - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{% url 'products:product_list' %}">المنتجات</a></li>
                <li class="breadcrumb-item"><a href="{% url 'products:product_detail' product.pk %}">{{ product.name }}</a></li>
                <li class="breadcrumb-item active">حذف المنتج</li>
            </ol>
        </nav>
        <h1 class="page-title text-danger">حذف المنتج</h1>
        <p class="page-subtitle">تأكيد حذف المنتج من النظام</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>تحذير: حذف المنتج
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="product-icon-lg bg-danger text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                            <i class="bi bi-box"></i>
                        </div>
                        <h4 class="mb-2">{{ product.name }}</h4>
                        <p class="text-muted">SKU: {{ product.sku }}</p>
                    </div>

                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>تنبيه مهم:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">معلومات المنتج المراد حذفه:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="bi bi-tag me-2 text-muted"></i>
                                <strong>اسم المنتج:</strong> {{ product.name }}
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-upc me-2 text-muted"></i>
                                <strong>رمز المنتج:</strong> {{ product.sku }}
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-boxes me-2 text-muted"></i>
                                <strong>الكمية المتوفرة:</strong> {{ product.quantity }} وحدة
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-currency-dollar me-2 text-muted"></i>
                                <strong>سعر الوحدة:</strong> {{ product.price }} ر.س
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-calculator me-2 text-muted"></i>
                                <strong>القيمة الإجمالية:</strong> {{ product.quantity|mul:product.price|floatformat:2 }} ر.س
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-calendar me-2 text-muted"></i>
                                <strong>تاريخ الإضافة:</strong> {{ product.created_at|date:"Y/m/d H:i" }}
                            </li>
                        </ul>
                    </div>

                    {% if product.quantity > 0 %}
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-circle me-2"></i>
                            <strong>تحذير:</strong> هذا المنتج يحتوي على {{ product.quantity }} وحدة في المخزون!
                            حذف المنتج سيؤدي إلى فقدان هذه الكمية من السجلات.
                        </div>
                    {% endif %}

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم حذف جميع البيانات المرتبطة بهذا المنتج بما في ذلك:
                        <ul class="mt-2 mb-0">
                            <li>سجل المبيعات والمشتريات</li>
                            <li>تاريخ حركة المخزون</li>
                            <li>أي تقارير مرتبطة بالمنتج</li>
                        </ul>
                    </div>

                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'products:product_detail' product.pk %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-right me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger" id="deleteBtn">
                                <i class="bi bi-trash me-2"></i>تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Alternative Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightbulb me-2"></i>بدائل أخرى
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">بدلاً من حذف المنتج، يمكنك:</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'products:product_edit' product.pk %}" class="btn btn-outline-primary">
                            <i class="bi bi-pencil me-2"></i>تعديل بيانات المنتج
                        </a>
                        {% if product.quantity > 0 %}
                            <button class="btn btn-outline-warning" onclick="setQuantityToZero()">
                                <i class="bi bi-dash-circle me-2"></i>تصفير الكمية بدلاً من الحذف
                            </button>
                        {% endif %}
                        <a href="{% url 'products:product_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-list me-2"></i>العودة إلى قائمة المنتجات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .product-icon-lg {
        width: 80px;
        height: 80px;
        font-size: 2rem;
        font-weight: 700;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .card-header {
        border-bottom: 1px solid #dee2e6;
    }
    
    .list-unstyled li {
        padding: 0.25rem 0;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const deleteBtn = document.getElementById('deleteBtn');
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show confirmation dialog
            if (confirm('هل أنت متأكد تماماً من حذف هذا المنتج؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                deleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحذف...';
                deleteBtn.disabled = true;
                
                // Submit the form
                form.submit();
            }
        });
    });
    
    function setQuantityToZero() {
        if (confirm('هل تريد تصفير كمية المنتج بدلاً من حذفه؟')) {
            // Redirect to edit page with a parameter to set quantity to zero
            window.location.href = '{% url "products:product_edit" product.pk %}?set_zero=1';
        }
    }
</script>
{% endblock %}
