<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المبيعات المتكامل - أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            position: relative;
        }
        
        .stats-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }
        
        .stats-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-bottom: 20px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .icon-customers { background: linear-gradient(45deg, #28a745, #20c997); color: white; }
        .icon-products { background: linear-gradient(45deg, #f093fb, #f5576c); color: white; }
        .icon-orders { background: linear-gradient(45deg, #4facfe, #00f2fe); color: white; }
        .icon-sales { background: linear-gradient(45deg, #43e97b, #38f9d7); color: white; }
        
        .quick-actions {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .action-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 15px 25px;
            border-radius: 15px;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            border-bottom: 3px solid #28a745;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس لوحة التحكم -->
        <div class="dashboard-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-speedometer2"></i>
                        نظام المبيعات المتكامل
                    </h1>
                    <p class="mb-0">مرحباً بك في نظام أوساريك لإدارة المبيعات</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="text-white">
                        <h5>{{ "now"|date:"l, j F Y" }}</h5>
                        <p class="mb-0">آخر تحديث: {{ "now"|date:"H:i" }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإحصائيات الرئيسية -->
        <div class="row">
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon icon-customers">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stats-number">{{ total_customers }}</div>
                    <div class="stats-label">إجمالي العملاء</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon icon-products">
                        <i class="bi bi-box-seam"></i>
                    </div>
                    <div class="stats-number">{{ total_products }}</div>
                    <div class="stats-label">إجمالي المنتجات</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon icon-orders">
                        <i class="bi bi-receipt"></i>
                    </div>
                    <div class="stats-number">{{ total_orders }}</div>
                    <div class="stats-label">إجمالي الطلبات</div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-icon icon-sales">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    <div class="stats-number">{{ total_sales|floatformat:0 }}</div>
                    <div class="stats-label">إجمالي المبيعات (ج.م)</div>
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="quick-actions">
            <h3 class="section-title">الإجراءات السريعة</h3>
            <div class="text-center">
                <a href="{% url 'sales:customer_list' %}" class="action-btn">
                    <i class="bi bi-people"></i>
                    إدارة العملاء
                </a>
                <a href="{% url 'sales:product_list' %}" class="action-btn">
                    <i class="bi bi-box-seam"></i>
                    إدارة المنتجات
                </a>
                <a href="{% url 'sales:invoice_list' %}" class="action-btn">
                    <i class="bi bi-receipt"></i>
                    إدارة الفواتير
                </a>
                <a href="{% url 'sales:order_list' %}" class="action-btn">
                    <i class="bi bi-cart"></i>
                    إدارة الطلبات
                </a>
                <a href="{% url 'sales:reports_dashboard' %}" class="action-btn">
                    <i class="bi bi-graph-up"></i>
                    التقارير
                </a>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="row">
            <div class="col-md-12">
                <div class="stats-card">
                    <h3 class="section-title">مرحباً بك في نظام المبيعات المتكامل</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>المميزات المتاحة:</h5>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success me-2"></i>إدارة العملاء والمنتجات</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>نظام الفواتير المتقدم</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>تحميل السيارات</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>نظام المرتجعات</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>أذونات الصرف</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>المميزات الإضافية:</h5>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success me-2"></i>نظام الجرد المتقدم</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>التقارير والتحليلات</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>نظام الصلاحيات</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>واجهات احترافية</li>
                                <li><i class="bi bi-check-circle text-success me-2"></i>دعم كامل للعربية</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
