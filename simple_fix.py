import os
import subprocess

# تغيير المجلد
os.chdir(r"d:\osaric try")

print("🔧 إصلاح مشاكل Django...")

# تشغيل الأوامر
commands = [
    "python manage.py makemigrations",
    "python manage.py migrate --fake-initial",
    "python manage.py runserver 127.0.0.1:8000"
]

for cmd in commands:
    print(f"\n▶️ تشغيل: {cmd}")
    try:
        if "runserver" in cmd:
            print("🚀 تشغيل الخادم...")
            subprocess.run(cmd, shell=True)
        else:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            print(result.stdout)
            if result.stderr:
                print("خطأ:", result.stderr)
    except Exception as e:
        print(f"خطأ: {e}")
        continue
