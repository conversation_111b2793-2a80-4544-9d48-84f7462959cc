# Generated by Django 5.2.4 on 2025-07-18 11:10

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0014_auto_20250714_0411'),
        ('warehouses', '0002_remove_inventorytransaction_created_by_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='InventoryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_on_hand', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المتاحة')),
                ('quantity_reserved', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المحجوزة')),
                ('quantity_ordered', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المطلوبة')),
                ('minimum_stock', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الحد الأدنى للمخزون')),
                ('maximum_stock', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الحد الأقصى للمخزون')),
                ('reorder_point', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='نقطة إعادة الطلب')),
                ('reorder_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='كمية إعادة الطلب')),
                ('average_cost', models.DecimalField(decimal_places=4, default=0, max_digits=15, verbose_name='متوسط التكلفة')),
                ('last_cost', models.DecimalField(decimal_places=4, default=0, max_digits=15, verbose_name='آخر تكلفة')),
                ('total_value', models.DecimalField(decimal_places=4, default=0, max_digits=20, verbose_name='إجمالي القيمة')),
                ('last_received_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر استلام')),
                ('last_issued_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر صرف')),
                ('last_counted_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر جرد')),
                ('abc_classification', models.CharField(choices=[('A', 'فئة A - عالية القيمة'), ('B', 'فئة B - متوسطة القيمة'), ('C', 'فئة C - منخفضة القيمة')], default='C', max_length=1, verbose_name='تصنيف ABC')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='definitions.warehouselocation', verbose_name='الموقع')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='المنتج')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'عنصر مخزون',
                'verbose_name_plural': 'عناصر المخزون',
                'ordering': ['warehouse', 'product'],
                'unique_together': {('warehouse', 'product')},
            },
        ),
        migrations.CreateModel(
            name='InventoryTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الحركة')),
                ('transaction_type', models.CharField(choices=[('receipt', 'استلام'), ('issue', 'صرف'), ('transfer_in', 'تحويل وارد'), ('transfer_out', 'تحويل صادر'), ('adjustment_in', 'تسوية زيادة'), ('adjustment_out', 'تسوية نقص'), ('return_in', 'مرتجع وارد'), ('return_out', 'مرتجع صادر'), ('opening_balance', 'رصيد افتتاحي'), ('physical_count', 'جرد فعلي')], max_length=20, verbose_name='نوع الحركة')),
                ('transaction_date', models.DateTimeField(default=datetime.datetime.now, verbose_name='تاريخ الحركة')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=4, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=4, default=0, max_digits=20, verbose_name='إجمالي التكلفة')),
                ('balance_before', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الرصيد قبل الحركة')),
                ('balance_after', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الرصيد بعد الحركة')),
                ('reference_number', models.CharField(blank=True, max_length=100, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_inventory_transactions', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('inventory_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='warehouses.inventoryitem', verbose_name='عنصر المخزون')),
                ('transfer_to_warehouse', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfer_in_transactions', to='definitions.warehousedefinition', verbose_name='المخزن المحول إليه')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-transaction_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjustment_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التسوية')),
                ('adjustment_date', models.DateTimeField(default=datetime.datetime.now, verbose_name='تاريخ التسوية')),
                ('adjustment_type', models.CharField(choices=[('increase', 'زيادة'), ('decrease', 'نقص'), ('correction', 'تصحيح'), ('damage', 'تالف'), ('expired', 'منتهي الصلاحية'), ('lost', 'مفقود'), ('found', 'موجود')], max_length=20, verbose_name='نوع التسوية')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=4, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_value', models.DecimalField(decimal_places=4, default=0, max_digits=20, verbose_name='إجمالي القيمة')),
                ('reason', models.TextField(verbose_name='السبب')),
                ('reference_number', models.CharField(blank=True, max_length=100, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_stock_adjustments', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('inventory_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='warehouses.inventoryitem', verbose_name='عنصر المخزون')),
            ],
            options={
                'verbose_name': 'تسوية مخزون',
                'verbose_name_plural': 'تسويات المخزون',
                'ordering': ['-adjustment_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('low_stock', 'مخزون منخفض'), ('out_of_stock', 'مخزون نافد'), ('overstock', 'مخزون زائد'), ('expiry_warning', 'تحذير انتهاء صلاحية'), ('reorder_point', 'نقطة إعادة الطلب')], max_length=20, verbose_name='نوع التنبيه')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('critical', 'حرجة')], default='medium', max_length=10, verbose_name='الأولوية')),
                ('message', models.TextField(verbose_name='رسالة التنبيه')),
                ('threshold_value', models.DecimalField(blank=True, decimal_places=3, max_digits=15, null=True, verbose_name='قيمة العتبة')),
                ('current_value', models.DecimalField(blank=True, decimal_places=3, max_digits=15, null=True, verbose_name='القيمة الحالية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_acknowledged', models.BooleanField(default=False, verbose_name='تم الاطلاع')),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاطلاع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='acknowledged_stock_alerts', to=settings.AUTH_USER_MODEL, verbose_name='اطلع عليه')),
                ('inventory_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='warehouses.inventoryitem', verbose_name='عنصر المخزون')),
            ],
            options={
                'verbose_name': 'تنبيه مخزون',
                'verbose_name_plural': 'تنبيهات المخزون',
                'ordering': ['-created_at', 'priority'],
            },
        ),
        migrations.CreateModel(
            name='StockCount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('count_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الجرد')),
                ('count_date', models.DateTimeField(default=datetime.datetime.now, verbose_name='تاريخ الجرد')),
                ('count_type', models.CharField(choices=[('full', 'جرد شامل'), ('partial', 'جرد جزئي'), ('cycle', 'جرد دوري'), ('spot', 'جرد عشوائي')], max_length=20, verbose_name='نوع الجرد')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('approved', 'معتمد'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('start_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البداية')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_stock_counts', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='definitions.warehouselocation', verbose_name='الموقع')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'جرد مخزون',
                'verbose_name_plural': 'جرد المخزون',
                'ordering': ['-count_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التحويل')),
                ('transfer_date', models.DateTimeField(default=datetime.datetime.now, verbose_name='تاريخ التحويل')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('pending', 'في الانتظار'), ('in_transit', 'في الطريق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('shipped_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الشحن')),
                ('received_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاستلام')),
                ('expected_date', models.DateTimeField(blank=True, null=True, verbose_name='التاريخ المتوقع')),
                ('reason', models.TextField(blank=True, verbose_name='سبب التحويل')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_stock_transfers', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_out', to='definitions.warehousedefinition', verbose_name='من المخزن')),
                ('to_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers_in', to='definitions.warehousedefinition', verbose_name='إلى المخزن')),
            ],
            options={
                'verbose_name': 'تحويل مخزون',
                'verbose_name_plural': 'تحويلات المخزون',
                'ordering': ['-transfer_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockCountItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('system_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية النظامية')),
                ('physical_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية الفعلية')),
                ('variance_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='فرق الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=4, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('variance_value', models.DecimalField(decimal_places=4, default=0, max_digits=20, verbose_name='قيمة الفرق')),
                ('counted_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الجرد')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('count', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='warehouses.stockcount', verbose_name='الجرد')),
                ('counted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='جرد بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'صنف جرد',
                'verbose_name_plural': 'أصناف الجرد',
                'unique_together': {('count', 'product')},
            },
        ),
        migrations.CreateModel(
            name='StockTransferItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_requested', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المطلوبة')),
                ('quantity_shipped', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المشحونة')),
                ('quantity_received', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية المستلمة')),
                ('unit_cost', models.DecimalField(decimal_places=4, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=4, default=0, max_digits=20, verbose_name='إجمالي التكلفة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='المنتج')),
                ('transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='warehouses.stocktransfer', verbose_name='التحويل')),
            ],
            options={
                'verbose_name': 'صنف تحويل',
                'verbose_name_plural': 'أصناف التحويل',
                'unique_together': {('transfer', 'product')},
            },
        ),
    ]
