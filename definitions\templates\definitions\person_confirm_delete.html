{% extends 'base.html' %}
{% load static %}

{% block title %}تأكيد حذف الشخص{% endblock %}

{% block extra_css %}
<style>
    body {
        background: #ffffff;
        min-height: 100vh;
    }

    .delete-container {
        max-width: 600px;
        margin: 3rem auto;
        padding: 2rem;
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .delete-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #dc3545;
    }

    .delete-title {
        color: #dc3545;
        font-size: 2rem;
        font-weight: 800;
        margin: 0;
    }

    .delete-subtitle {
        color: #666;
        font-size: 1.1rem;
        margin: 0.5rem 0 0 0;
    }

    .person-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 15px;
        padding: 2rem;
        margin: 2rem 0;
    }

    .person-detail {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .person-detail:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-value {
        color: #212529;
        font-weight: 500;
    }

    .warning-message {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 1px solid #ffeaa7;
        border-radius: 15px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: center;
    }

    .warning-icon {
        font-size: 3rem;
        color: #856404;
        margin-bottom: 1rem;
    }

    .warning-text {
        color: #856404;
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn {
        padding: 0.75rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }

    .btn-danger:hover {
        background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
        color: white;
    }

    .btn-secondary:hover {
        background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
    }

    .badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .badge-customer {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .badge-supplier {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        color: white;
    }

    .badge-employee {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
    }

    .badge-both {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
    }

    .badge-other {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }

    @media (max-width: 768px) {
        .delete-container {
            margin: 1rem;
            padding: 1.5rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .person-detail {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="delete-container">
    <!-- Header -->
    <div class="delete-header">
        <h1 class="delete-title">
            <i class="bi bi-exclamation-triangle me-2"></i>
            تأكيد حذف الشخص
        </h1>
        <p class="delete-subtitle">
            هل أنت متأكد من رغبتك في حذف هذا الشخص؟
        </p>
    </div>

    <!-- Person Information -->
    <div class="person-info">
        <div class="person-detail">
            <span class="detail-label">
                <i class="bi bi-hash"></i>
                الكود
            </span>
            <span class="detail-value">{{ person.code }}</span>
        </div>

        <div class="person-detail">
            <span class="detail-label">
                <i class="bi bi-person"></i>
                الاسم
            </span>
            <span class="detail-value">{{ person.name }}</span>
        </div>

        {% if person.name_en %}
        <div class="person-detail">
            <span class="detail-label">
                <i class="bi bi-translate"></i>
                الاسم بالإنجليزية
            </span>
            <span class="detail-value">{{ person.name_en }}</span>
        </div>
        {% endif %}

        <div class="person-detail">
            <span class="detail-label">
                <i class="bi bi-tags"></i>
                نوع الشخص
            </span>
            <span class="badge badge-{{ person.person_type }}">
                {{ person.get_person_type_display }}
            </span>
        </div>

        {% if person.phone %}
        <div class="person-detail">
            <span class="detail-label">
                <i class="bi bi-telephone"></i>
                الهاتف
            </span>
            <span class="detail-value">{{ person.phone }}</span>
        </div>
        {% endif %}

        {% if person.email %}
        <div class="person-detail">
            <span class="detail-label">
                <i class="bi bi-envelope"></i>
                البريد الإلكتروني
            </span>
            <span class="detail-value">{{ person.email }}</span>
        </div>
        {% endif %}

        {% if person.city %}
        <div class="person-detail">
            <span class="detail-label">
                <i class="bi bi-geo-alt"></i>
                المدينة
            </span>
            <span class="detail-value">{{ person.city }}</span>
        </div>
        {% endif %}

        <div class="person-detail">
            <span class="detail-label">
                <i class="bi bi-calendar"></i>
                تاريخ الإنشاء
            </span>
            <span class="detail-value">{{ person.created_at|date:"d/m/Y H:i" }}</span>
        </div>
    </div>

    <!-- Warning Message -->
    <div class="warning-message">
        <div class="warning-icon">
            <i class="bi bi-exclamation-triangle-fill"></i>
        </div>
        <p class="warning-text">
            تحذير: هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع البيانات المرتبطة بهذا الشخص نهائياً.
        </p>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{% url 'definitions:dashboard' %}" class="btn btn-info">
            <i class="bi bi-house"></i>
            العودة للتعريفات
        </a>

        <a href="{% url 'definitions:person_list' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للقائمة
        </a>

        <form method="post" style="display: inline;">
            {% csrf_token %}
            <input type="submit" class="btn btn-danger" value="تأكيد الحذف">
        </form>
    </div>
</div>
{% endblock %}
