from django import forms
from .models import (
    InventoryTransaction, InventoryBalance, StockAdjustment, StockAdjustmentItem
)
from definitions.models import WarehouseDefinition, ProductDefinition

class InventoryTransactionForm(forms.ModelForm):
    class Meta:
        model = InventoryTransaction
        fields = ['warehouse', 'product', 'transaction_type', 'transaction_reason',
                 'quantity', 'unit_cost', 'reference_number', 'notes', 'transaction_date']
        widgets = {
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'product': forms.Select(attrs={'class': 'form-select'}),
            'transaction_type': forms.Select(attrs={'class': 'form-select'}),
            'transaction_reason': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.001', 'min': '0'}),
            'unit_cost': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'reference_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم المرجع'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'transaction_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
        }
        labels = {
            'warehouse': 'المخزن',
            'product': 'الصنف',
            'transaction_type': 'نوع الحركة',
            'transaction_reason': 'سبب الحركة',
            'quantity': 'الكمية',
            'unit_cost': 'تكلفة الوحدة',
            'reference_number': 'رقم المرجع',
            'notes': 'ملاحظات',
            'transaction_date': 'تاريخ الحركة',
        }

class StockAdjustmentForm(forms.ModelForm):
    class Meta:
        model = StockAdjustment
        fields = ['warehouse', 'adjustment_type', 'adjustment_date', 'reason']
        widgets = {
            'warehouse': forms.Select(attrs={'class': 'form-select'}),
            'adjustment_type': forms.Select(attrs={'class': 'form-select'}),
            'adjustment_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'سبب التسوية'}),
        }
        labels = {
            'warehouse': 'المخزن',
            'adjustment_type': 'نوع التسوية',
            'adjustment_date': 'تاريخ التسوية',
            'reason': 'سبب التسوية',
        }

# نماذج إضافية يمكن إضافتها لاحقاً
