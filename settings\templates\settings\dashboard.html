{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم الإعدادات والخدمات{% endblock %}

{% block extra_css %}
<style>
    .settings-header {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 1rem 1rem;
    }

    .stats-card {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0;
    }

    .quick-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-bottom: 2rem;
    }

    .quick-action-btn {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .quick-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108,117,125,0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Settings Header -->
<div class="settings-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-gear me-2"></i>
                    لوحة تحكم الإعدادات والخدمات
                </h1>
                <p class="mb-0 opacity-75">إدارة شاملة لإعدادات النظام والخدمات المساعدة</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="text-white">
                    <i class="bi bi-calendar me-2"></i>
                    {{ "now"|date:"Y/m/d" }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container-fluid">
    <div class="quick-actions">
        <a href="{% url 'settings:system_settings_list' %}" class="quick-action-btn">
            <i class="bi bi-sliders"></i>
            إعدادات النظام
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-shield-check"></i>
            إعدادات الأمان
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-hdd"></i>
            النسخ الاحتياطية
        </a>
        <a href="#" class="quick-action-btn">
            <i class="bi bi-file-earmark-text"></i>
            سجلات النظام
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(108, 117, 125, 0.1); color: #6c757d;">
                    <i class="bi bi-sliders"></i>
                </div>
                <div class="stats-number" style="color: #6c757d;">{{ total_settings }}</div>
                <p class="stats-label">إجمالي الإعدادات</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(40, 167, 69, 0.1); color: #28a745;">
                    <i class="bi bi-pencil-square"></i>
                </div>
                <div class="stats-number text-success">{{ editable_settings }}</div>
                <p class="stats-label">إعدادات قابلة للتعديل</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <div class="stats-number text-danger">{{ required_settings }}</div>
                <p class="stats-label">إعدادات مطلوبة</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(0, 123, 255, 0.1); color: #007bff;">
                    <i class="bi bi-hdd"></i>
                </div>
                <div class="stats-number text-primary">{{ active_schedules }}</div>
                <p class="stats-label">جدولة نسخ احتياطية نشطة</p>
            </div>
        </div>
    </div>

    <!-- Backup and Logs Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(32, 201, 151, 0.1); color: #20c997;">
                    <i class="bi bi-archive"></i>
                </div>
                <div class="stats-number text-info">{{ recent_backups }}</div>
                <p class="stats-label">نسخ احتياطية هذا الأسبوع</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-file-earmark-text"></i>
                </div>
                <div class="stats-number text-warning">{{ total_logs }}</div>
                <p class="stats-label">إجمالي السجلات</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                    <i class="bi bi-x-circle"></i>
                </div>
                <div class="stats-number text-danger">{{ error_logs }}</div>
                <p class="stats-label">سجلات الأخطاء</p>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="stats-icon" style="background: rgba(255, 193, 7, 0.1); color: #ffc107;">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <div class="stats-number text-warning">{{ warning_logs }}</div>
                <p class="stats-label">سجلات التحذيرات</p>
            </div>
        </div>
    </div>

    <!-- Settings Categories and Recent Logs -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث سجلات النظام</h5>
                    <a href="#" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_logs %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المستوى</th>
                                    <th>الرسالة</th>
                                    <th>المستخدم</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_logs %}
                                <tr>
                                    <td>
                                        {% if log.level == 'error' %}
                                            <span class="badge bg-danger">خطأ</span>
                                        {% elif log.level == 'warning' %}
                                            <span class="badge bg-warning">تحذير</span>
                                        {% elif log.level == 'info' %}
                                            <span class="badge bg-info">معلومات</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ log.get_level_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ log.message|truncatechars:50 }}</td>
                                    <td>{{ log.user.username|default:"النظام" }}</td>
                                    <td>{{ log.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-file-earmark-text text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد سجلات حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">أحدث النسخ الاحتياطية</h5>
                    <a href="#" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                </div>
                <div class="card-body">
                    {% if recent_backup_history %}
                    <div class="list-group list-group-flush">
                        {% for backup in recent_backup_history %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ backup.schedule.name|default:"نسخة يدوية" }}</h6>
                                    <p class="mb-1 small">{{ backup.backup_date|date:"Y-m-d H:i" }}</p>
                                    <small class="text-muted">{{ backup.file_size|filesizeformat }}</small>
                                </div>
                                <div>
                                    {% if backup.status == 'completed' %}
                                        <span class="badge bg-success">مكتمل</span>
                                    {% elif backup.status == 'running' %}
                                        <span class="badge bg-primary">قيد التنفيذ</span>
                                    {% else %}
                                        <span class="badge bg-danger">فشل</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-hdd text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">لا توجد نسخ احتياطية حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
