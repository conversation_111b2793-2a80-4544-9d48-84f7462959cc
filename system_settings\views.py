from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.models import User, Permission
from django.http import JsonResponse
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.utils.translation import activate, get_language
from django.conf import settings as django_settings
from .models import SystemSettings, UserProfile
from .forms import UserCreateForm, UserEditForm
from notifications.views import create_notification
from .decorators import permission_required, has_user_permission


def is_admin(user):
    """التحقق من صلاحيات الإدارة"""
    return user.is_superuser or user.is_staff


@login_required
@user_passes_test(is_admin)
def settings_dashboard(request):
    """لوحة تحكم الإعدادات المتقدمة"""
    
    # إحصائيات سريعة
    stats = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'inactive_users': User.objects.filter(is_active=False).count(),
        'staff_users': User.objects.filter(is_staff=True).count(),
        'total_permissions': Permission.objects.count(),
    }
    
    # المستخدمين النشطين
    active_users = User.objects.filter(
        is_active=True,
        last_login__isnull=False
    ).order_by('-last_login')[:10]
    
    context = {
        'stats': stats,
        'recent_logs': [],  # فارغة مؤقتاً
        'active_users': active_users,
        'enabled_modules': [],
        'department_stats': [],
        'position_stats': [],
    }
    
    return render(request, 'system_settings/dashboard.html', context)


@login_required
@user_passes_test(is_admin)
def system_settings_view(request):
    """إعدادات النظام المتقدمة"""

    settings = SystemSettings.get_settings()

    if request.method == 'POST':
        # تحديث الإعدادات
        boolean_fields = [
            'backup_enabled', 'require_strong_password', 'email_use_tls', 'email_use_ssl',
            'notifications_enabled', 'email_notifications', 'sms_notifications',
            'push_notifications', 'notification_sound', 'sidebar_collapsed',
            'require_2fa', 'auto_logout_inactive', 'backup_compress', 'backup_encrypt',
            'show_breadcrumbs', 'enable_animations', 'compact_mode', 'auto_generate_reports',
            'enable_caching', 'enable_compression'
        ]

        integer_fields = [
            'session_timeout', 'password_min_length', 'max_login_attempts', 'lockout_duration',
            'password_expiry_days', 'email_port', 'items_per_page', 'backup_retention_days',
            'report_retention_days', 'cache_timeout', 'max_file_upload_size', 'decimal_places'
        ]

        for field_name in request.POST:
            if hasattr(settings, field_name) and field_name != 'csrfmiddlewaretoken':
                field_value = request.POST.get(field_name)

                # معالجة الحقول المختلفة
                if field_name in boolean_fields:
                    field_value = field_value == 'on'
                elif field_name in integer_fields:
                    try:
                        field_value = int(field_value)
                    except (ValueError, TypeError):
                        continue

                setattr(settings, field_name, field_value)

        # تطبيق اللغة المختارة
        if 'system_language' in request.POST:
            language = request.POST.get('system_language')
            activate(language)
            request.session['django_language'] = language

        settings.updated_by = request.user
        settings.save()

        messages.success(request, 'تم حفظ إعدادات النظام بنجاح')
        return redirect('system_settings:system_settings')

    # إعداد البيانات للقوائم المنسدلة
    context = {
        'settings': settings,
        'timezones': [
            ('Africa/Cairo', 'القاهرة - مصر'),
            ('Asia/Riyadh', 'الرياض - السعودية'),
            ('Asia/Dubai', 'دبي - الإمارات'),
            ('Asia/Kuwait', 'الكويت'),
            ('Asia/Qatar', 'قطر'),
            ('Asia/Bahrain', 'البحرين'),
            ('Asia/Baghdad', 'بغداد - العراق'),
            ('Asia/Amman', 'عمان - الأردن'),
            ('Asia/Beirut', 'بيروت - لبنان'),
            ('Asia/Damascus', 'دمشق - سوريا'),
            ('UTC', 'التوقيت العالمي'),
        ],
        'languages': [
            ('ar', 'العربية'),
            ('en', 'English'),
        ],
        'currencies': [
            ('EGP', 'جنيه مصري'),
            ('SAR', 'ريال سعودي'),
            ('AED', 'درهم إماراتي'),
            ('KWD', 'دينار كويتي'),
            ('QAR', 'ريال قطري'),
            ('BHD', 'دينار بحريني'),
            ('IQD', 'دينار عراقي'),
            ('JOD', 'دينار أردني'),
            ('LBP', 'ليرة لبنانية'),
            ('SYP', 'ليرة سورية'),
            ('USD', 'دولار أمريكي'),
            ('EUR', 'يورو'),
        ],
        'countries': [
            ('Egypt', 'مصر'),
            ('Saudi Arabia', 'السعودية'),
            ('UAE', 'الإمارات'),
            ('Kuwait', 'الكويت'),
            ('Qatar', 'قطر'),
            ('Bahrain', 'البحرين'),
            ('Iraq', 'العراق'),
            ('Jordan', 'الأردن'),
            ('Lebanon', 'لبنان'),
            ('Syria', 'سوريا'),
        ],
    }

    return render(request, 'system_settings/system_settings.html', context)


@login_required
@user_passes_test(is_admin)
def users_management(request):
    """إدارة المستخدمين"""

    # إذا كان طلب AJAX لتحديث حالة الاتصال
    if request.GET.get('ajax') == 'online_status':
        users = User.objects.select_related('profile').all()
        users_data = []

        for user in users:
            if hasattr(user, 'profile') and user.profile:
                if user.profile.is_user_online():
                    status_html = '''
                    <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.25rem 0.75rem; background: #dcfce7; color: #166534; border-radius: 12px; font-size: 0.8rem; font-weight: 600;">
                        <div style="width: 8px; height: 8px; background: #22c55e; border-radius: 50%; animation: pulse 2s infinite;"></div>
                        متصل الآن
                    </div>
                    '''
                else:
                    status_text = user.profile.get_online_status_display()
                    status_html = f'''
                    <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.25rem 0.75rem; background: #f3f4f6; color: #6b7280; border-radius: 12px; font-size: 0.8rem; font-weight: 600;">
                        <div style="width: 8px; height: 8px; background: #9ca3af; border-radius: 50%;"></div>
                        {status_text}
                    </div>
                    '''
            else:
                status_html = '''
                <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.25rem 0.75rem; background: #fef2f2; color: #dc2626; border-radius: 12px; font-size: 0.8rem; font-weight: 600;">
                    <div style="width: 8px; height: 8px; background: #ef4444; border-radius: 50%;"></div>
                    غير متصل
                </div>
                '''

            users_data.append({
                'id': user.id,
                'status_html': status_html
            })

        return JsonResponse({'users': users_data})

    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')

    users = User.objects.select_related('profile').all()
    
    # تطبيق الفلاتر
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )
    
    if status_filter == 'active':
        users = users.filter(is_active=True)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)
    elif status_filter == 'staff':
        users = users.filter(is_staff=True)
    elif status_filter == 'superuser':
        users = users.filter(is_superuser=True)
    
    # ترتيب النتائج
    users = users.order_by('username')
    
    # التصفح
    paginator = Paginator(users, 25)
    page_number = request.GET.get('page')
    users_page = paginator.get_page(page_number)

    # حساب الإحصائيات
    all_users = User.objects.all()
    stats = {
        'total_users': all_users.count(),
        'active_users': all_users.filter(is_active=True).count(),
        'inactive_users': all_users.filter(is_active=False).count(),
        'staff_users': all_users.filter(is_staff=True).count(),
    }

    context = {
        'users': users_page,
        'stats': stats,
        'search_query': search_query,
        'status_filter': status_filter,
        'departments': UserProfile.DEPARTMENTS,
        'positions': UserProfile.POSITIONS,
        'is_paginated': paginator.num_pages > 1,
        'page_obj': users_page,
    }
    
    return render(request, 'system_settings/users_management.html', context)


@login_required
@user_passes_test(is_admin)
def user_detail(request, user_id):
    """تفاصيل المستخدم"""
    
    user = get_object_or_404(User, id=user_id)
    profile, created = UserProfile.objects.get_or_create(user=user)
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'update_profile':
            # تحديث الملف الشخصي
            for field in ['phone', 'mobile', 'address', 'department', 'position', 'employee_id']:
                if field in request.POST:
                    setattr(profile, field, request.POST.get(field))
            
            profile.save()
            
        elif action == 'update_account':
            # تحديث معلومات الحساب
            user.first_name = request.POST.get('first_name', '')
            user.last_name = request.POST.get('last_name', '')
            user.email = request.POST.get('email', '')
            user.is_active = request.POST.get('is_active') == 'on'
            user.is_staff = request.POST.get('is_staff') == 'on'
            user.is_superuser = request.POST.get('is_superuser') == 'on'
            user.save()
        
        elif action == 'reset_password':
            # إعادة تعيين كلمة المرور
            new_password = request.POST.get('new_password')
            if new_password:
                user.set_password(new_password)
                user.save()
        
        messages.success(request, 'تم تحديث ملف المستخدم بنجاح')
        return redirect('system_settings:user_detail', user_id=user.id)
    
    # إحصائيات المستخدم
    user_stats = {
        'last_login': user.last_login,
        'date_joined': user.date_joined,
        'failed_attempts': profile.failed_login_attempts,
    }
    
    context = {
        'user': user,
        'profile': profile,
        'user_stats': user_stats,
        'departments': UserProfile.DEPARTMENTS,
        'positions': UserProfile.POSITIONS,
    }
    
    return render(request, 'system_settings/user_detail.html', context)


@login_required
@user_passes_test(is_admin)
def user_create(request):
    """إضافة مستخدم جديد"""

    print(f"Request method: {request.method}")  # للتشخيص

    if request.method == 'POST':
        print("POST request received!")  # للتشخيص
        print(f"POST data: {request.POST}")  # للتشخيص

        try:
            form = UserCreateForm(request.POST, request.FILES)
            print(f"Form created, is_valid: {form.is_valid()}")  # للتشخيص

            if form.is_valid():
                print("Form is valid, saving user...")  # للتشخيص
                # حفظ المستخدم
                user = form.save()
                print(f"User saved: {user.username}")  # للتشخيص
                from dashboard.context_processors import user_settings
                user_context = user_settings(request)
                user_language = user_context.get('user_language', 'ar')

                if user_language == 'en':
                    messages.success(request, f'User {user.username} created successfully')
                else:
                    messages.success(request, f'تم إنشاء المستخدم {user.username} بنجاح')
                return redirect('system_settings:users_management')
            else:
                print(f"Form errors: {form.errors}")  # للتشخيص
                # عرض أخطاء النموذج
                if user_language == 'en':
                    messages.error(request, 'Please correct the errors in the form')
                else:
                    messages.error(request, 'يرجى تصحيح الأخطاء في النموذج')

        except Exception as e:
            print(f"Exception occurred: {e}")  # للتشخيص
            import traceback
            traceback.print_exc()
            messages.error(request, f'خطأ في إنشاء المستخدم: {str(e)}')
            form = UserCreateForm(request.POST, request.FILES)
    else:
        print("GET request, creating empty form")  # للتشخيص
        form = UserCreateForm()

    context = {
        'form': form,
        'title': 'إضافة مستخدم جديد',
        'departments': UserProfile.DEPARTMENTS,
        'positions': UserProfile.POSITIONS,
    }

    return render(request, 'system_settings/user_create.html', context)


# تم حذف دالة permissions_management - استخدم النظام الجديد في /permissions/


# تم حذف دالة setup_permissions - استخدم النظام الجديد


@login_required
def edit_group_permissions(request, group_id):
    """تعديل صلاحيات المجموعة"""
    from django.contrib.auth.models import Group, Permission
    from django.contrib.contenttypes.models import ContentType

    group = get_object_or_404(Group, id=group_id)

    if request.method == 'POST':
        try:
            # الحصول على الصلاحيات المحددة من الطلب
            selected_permissions = request.POST.getlist('permissions')
            print(f"الصلاحيات المحددة: {selected_permissions}")  # للتشخيص

            # تحديث صلاحيات المجموعة
            group.permissions.clear()
            added_count = 0
            for perm_id in selected_permissions:
                try:
                    permission = Permission.objects.get(id=perm_id)
                    group.permissions.add(permission)
                    added_count += 1
                except Permission.DoesNotExist:
                    print(f"صلاحية غير موجودة: {perm_id}")
                    continue

            print(f"تم إضافة {added_count} صلاحية للمجموعة {group.name}")
            messages.success(request, f'تم تحديث صلاحيات مجموعة "{group.name}" بنجاح. تم إضافة {added_count} صلاحية.')

            # إنشاء إشعار نجاح
            create_notification(
                recipient=request.user,
                title='تم تحديث الصلاحيات',
                message=f'تم تحديث صلاحيات مجموعة "{group.name}" بنجاح. تم إضافة {added_count} صلاحية.',
                notification_type='success',
                action_url='/settings/permissions/',
                icon='bi-shield-check'
            )

            return redirect('system_settings:permissions_management')

        except Exception as e:
            print(f"خطأ في تحديث الصلاحيات: {e}")
            messages.error(request, f'حدث خطأ في تحديث الصلاحيات: {str(e)}')
            return redirect('system_settings:edit_group_permissions', group_id=group_id)

    # الحصول على جميع الصلاحيات مجمعة حسب النوع
    permissions_by_content_type = {}
    for permission in Permission.objects.select_related('content_type').all():
        content_type = permission.content_type.name
        if content_type not in permissions_by_content_type:
            permissions_by_content_type[content_type] = []
        permissions_by_content_type[content_type].append(permission)

    # الحصول على صلاحيات المجموعة الحالية
    current_permissions = set(group.permissions.values_list('id', flat=True))

    context = {
        'group': group,
        'permissions_by_content_type': permissions_by_content_type,
        'current_permissions': current_permissions,
        'title': f'تعديل صلاحيات مجموعة "{group.name}"'
    }

    return render(request, 'system_settings/edit_group_permissions.html', context)


@login_required
@user_passes_test(is_admin)
def group_users(request, group_id):
    """عرض مستخدمي المجموعة"""
    from django.contrib.auth.models import Group

    group = get_object_or_404(Group, id=group_id)
    users = group.user_set.all()

    if request.method == 'POST':
        # إضافة أو إزالة مستخدم من المجموعة
        action = request.POST.get('action')
        user_id = request.POST.get('user_id')

        if action and user_id:
            try:
                user = User.objects.get(id=user_id)
                if action == 'add':
                    group.user_set.add(user)
                    messages.success(request, f'تم إضافة {user.username} إلى مجموعة {group.name}')

                    # إشعار للمستخدم المضاف
                    create_notification(
                        recipient=user,
                        title='تم إضافتك إلى مجموعة جديدة',
                        message=f'تم إضافتك إلى مجموعة "{group.name}". تحقق من صلاحياتك الجديدة.',
                        notification_type='system',
                        action_url='/settings/permissions/',
                        icon='bi-person-plus'
                    )

                elif action == 'remove':
                    group.user_set.remove(user)
                    messages.success(request, f'تم إزالة {user.username} من مجموعة {group.name}')

                    # إشعار للمستخدم المحذوف
                    create_notification(
                        recipient=user,
                        title='تم إزالتك من مجموعة',
                        message=f'تم إزالتك من مجموعة "{group.name}". قد تتغير صلاحياتك.',
                        notification_type='warning',
                        action_url='/settings/permissions/',
                        icon='bi-person-dash'
                    )
            except User.DoesNotExist:
                messages.error(request, 'المستخدم غير موجود')

        return redirect('system_settings:group_users', group_id=group_id)

    # الحصول على المستخدمين غير المنتمين للمجموعة
    available_users = User.objects.exclude(groups=group)

    context = {
        'group': group,
        'users': users,
        'available_users': available_users,
        'title': f'مستخدمو مجموعة "{group.name}"'
    }

    return render(request, 'system_settings/group_users.html', context)


@login_required
def notifications_view(request):
    """عرض الإشعارات"""
    from notifications.models import Notification

    notifications = Notification.objects.filter(
        recipient=request.user
    ).order_by('-created_at')[:50]

    unread_count = Notification.objects.filter(
        recipient=request.user,
        is_read=False
    ).count()

    context = {
        'notifications': notifications,
        'unread_count': unread_count,
        'title': 'الإشعارات'
    }

    return render(request, 'notifications/notifications.html', context)


@login_required
def mark_notification_read(request, notification_id):
    """تحديد إشعار كمقروء"""
    if request.method == 'POST':
        success = NotificationManager.mark_as_read(notification_id, request.user)
        if success:
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'error': 'فشل في تحديث الإشعار'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مسموحة'})


@login_required
def mark_all_notifications_read(request):
    """تحديد جميع الإشعارات كمقروءة"""
    if request.method == 'POST':
        success = NotificationManager.mark_all_as_read(request.user)
        if success:
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'error': 'فشل في تحديث الإشعارات'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مسموحة'})


@login_required
def delete_notification(request, notification_id):
    """حذف إشعار"""
    if request.method == 'POST':
        success = NotificationManager.delete_notification(notification_id, request.user)
        if success:
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'error': 'فشل في حذف الإشعار'})

    return JsonResponse({'success': False, 'error': 'طريقة غير مسموحة'})


@login_required
def get_notifications_ajax(request):
    """الحصول على الإشعارات عبر AJAX"""
    notifications = NotificationManager.get_user_notifications(request.user, limit=10)
    unread_count = NotificationManager.get_unread_count(request.user)

    return JsonResponse({
        'notifications': notifications,
        'unread_count': unread_count,
        'has_notifications': len(notifications) > 0
    })


@login_required
@user_passes_test(is_admin)
def roles_management(request):
    """إدارة الأدوار"""
    from django.contrib.auth.models import Group

    # الحصول على جميع المجموعات مع عدد المستخدمين والصلاحيات
    groups = Group.objects.all().annotate(
        user_count=Count('user'),
        permission_count=Count('permissions')
    ).order_by('name')

    # إضافة معلومات إضافية لكل مجموعة
    roles_data = []
    for group in groups:
        roles_data.append({
            'id': group.id,
            'name': group.name,
            'user_count': group.user_set.count(),
            'permission_count': group.permissions.count(),
            'users': list(group.user_set.values('id', 'username', 'first_name', 'last_name')),
            'permissions': list(group.permissions.values('id', 'name', 'codename'))
        })

    context = {
        'groups': groups,
        'roles_data': roles_data,
        'title': 'إدارة الأدوار والمجموعات'
    }

    return render(request, 'system_settings/roles_management.html', context)


@login_required
@user_passes_test(is_admin)
def user_permissions(request, user_id):
    """إدارة صلاحيات المستخدم"""
    from django.db import connection

    user = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        selected_roles = request.POST.getlist('roles')

        cursor = connection.cursor()

        # حذف الأدوار الحالية
        cursor.execute("""
            UPDATE system_settings_userrole
            SET is_active = 0
            WHERE user_id = %s
        """, [user_id])

        # إضافة الأدوار الجديدة
        for role_id in selected_roles:
            cursor.execute("""
                INSERT OR REPLACE INTO system_settings_userrole
                (user_id, role_id, assigned_by_id, is_active)
                VALUES (%s, %s, %s, 1)
            """, [user_id, role_id, request.user.id])

        messages.success(request, f'تم تحديث صلاحيات المستخدم {user.username} بنجاح')
        return redirect('system_settings:users_management')

    # الحصول على جميع الأدوار
    cursor = connection.cursor()
    cursor.execute("""
        SELECT id, name, description
        FROM system_settings_role
        WHERE is_active = 1
        ORDER BY name
    """)

    all_roles = []
    for row in cursor.fetchall():
        all_roles.append({
            'id': row[0],
            'name': row[1],
            'description': row[2]
        })

    # الحصول على أدوار المستخدم الحالية
    cursor.execute("""
        SELECT role_id
        FROM system_settings_userrole
        WHERE user_id = %s AND is_active = 1
    """, [user_id])

    user_role_ids = [row[0] for row in cursor.fetchall()]

    return render(request, 'system_settings/user_permissions.html', {
        'user': user,
        'all_roles': all_roles,
        'user_role_ids': user_role_ids,
        'title': f'صلاحيات المستخدم: {user.username}'
    })


@login_required
def permissions_summary(request):
    """ملخص نظام الصلاحيات"""
    from django.db import connection

    cursor = connection.cursor()

    # إحصائيات عامة
    cursor.execute("SELECT COUNT(*) FROM system_settings_permission")
    total_permissions = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM system_settings_permission WHERE is_active = 1")
    active_permissions = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM system_settings_role WHERE is_active = 1")
    total_roles = cursor.fetchone()[0]

    cursor.execute("""
        SELECT COUNT(DISTINCT user_id)
        FROM system_settings_userrole
        WHERE is_active = 1
    """)
    total_users_with_roles = cursor.fetchone()[0]

    return render(request, 'system_settings/permissions_summary.html', {
        'total_permissions': total_permissions,
        'active_permissions': active_permissions,
        'total_roles': total_roles,
        'total_users_with_roles': total_users_with_roles,
        'title': 'ملخص نظام الصلاحيات'
    })


@login_required
@user_passes_test(is_admin)
def user_edit(request, user_id):
    """تعديل المستخدم"""

    user = get_object_or_404(User, id=user_id)

    if request.method == 'POST':
        form = UserEditForm(request.POST, request.FILES, instance=user, user=user)
        if form.is_valid():
            user = form.save()
            messages.success(request, f'تم تحديث المستخدم {user.username} بنجاح')
            return redirect('system_settings:user_detail', user_id=user.id)
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء أدناه')
    else:
        form = UserEditForm(instance=user, user=user)

    context = {
        'form': form,
        'user': user,
        'title': f'تعديل المستخدم {user.username}',
        'departments': UserProfile.DEPARTMENTS,
        'positions': UserProfile.POSITIONS,
    }

    return render(request, 'system_settings/user_edit.html', context)


@login_required
@user_passes_test(is_admin)
def user_delete(request, user_id):
    """حذف المستخدم"""

    user = get_object_or_404(User, id=user_id)

    # منع حذف المستخدم الحالي أو المدير العام
    if user == request.user:
        messages.error(request, 'لا يمكنك حذف حسابك الخاص')
        return redirect('system_settings:users_management')

    if user.is_superuser:
        messages.error(request, 'لا يمكن حذف المدير العام')
        return redirect('system_settings:users_management')

    if request.method == 'POST':
        username = user.username
        user.delete()
        messages.success(request, f'تم حذف المستخدم {username} بنجاح')
        return redirect('system_settings:users_management')

    context = {
        'user': user,
        'title': f'حذف المستخدم {user.username}',
    }

    return render(request, 'system_settings/user_delete.html', context)


@login_required
def permissions_management(request):
    """إدارة الصلاحيات"""
    context = {
        'title': 'إدارة الصلاحيات',
        'message': 'نظام الصلاحيات متاح في تطبيق المبيعات الجديد'
    }

    return render(request, 'system_settings/permissions_management.html', context)
