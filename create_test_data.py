#!/usr/bin/env python
"""
سكريبت لإنشاء بيانات تجريبية للاختبار
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from definitions.models import ProductDefinition, UnitDefinition
from django.contrib.auth.models import User

def create_test_data():
    """إنشاء بيانات تجريبية"""
    try:
        print("إنشاء بيانات تجريبية...")
        
        # إنشاء وحدات قياس
        units_data = [
            {'code': 'KG', 'name': 'كيلوجرام', 'unit_type': 'weight'},
            {'code': 'PC', 'name': 'قطعة', 'unit_type': 'quantity'},
            {'code': 'LT', 'name': 'لتر', 'unit_type': 'volume'},
            {'code': 'MT', 'name': 'متر', 'unit_type': 'length'},
        ]
        
        for unit_data in units_data:
            unit, created = UnitDefinition.objects.get_or_create(
                code=unit_data['code'],
                defaults={
                    'name': unit_data['name'],
                    'unit_type': unit_data['unit_type'],
                    'is_active': True,
                    'created_by': User.objects.first() or User.objects.create_user('admin', '<EMAIL>', 'admin123')
                }
            )
            if created:
                print(f"✅ تم إنشاء وحدة القياس: {unit.name}")
            else:
                print(f"⚠️ وحدة القياس موجودة: {unit.name}")
        
        # إنشاء منتجات تجريبية
        kg_unit = UnitDefinition.objects.get(code='KG')
        pc_unit = UnitDefinition.objects.get(code='PC')
        
        products_data = [
            {
                'code': 'RAW001',
                'name': 'دقيق أبيض',
                'product_type': 'raw_material',
                'cost_price': 15.50,
                'selling_price': 18.00,
                'main_unit': kg_unit
            },
            {
                'code': 'RAW002', 
                'name': 'سكر أبيض',
                'product_type': 'raw_material',
                'cost_price': 12.75,
                'selling_price': 15.00,
                'main_unit': kg_unit
            },
            {
                'code': 'RAW003',
                'name': 'زيت نباتي',
                'product_type': 'raw_material', 
                'cost_price': 25.00,
                'selling_price': 30.00,
                'main_unit': kg_unit
            },
            {
                'code': 'RAW004',
                'name': 'بيض',
                'product_type': 'raw_material',
                'cost_price': 2.50,
                'selling_price': 3.00,
                'main_unit': pc_unit
            },
        ]
        
        for product_data in products_data:
            product, created = ProductDefinition.objects.get_or_create(
                code=product_data['code'],
                defaults={
                    'name': product_data['name'],
                    'product_type': product_data['product_type'],
                    'cost_price': product_data['cost_price'],
                    'selling_price': product_data['selling_price'],
                    'main_unit': product_data['main_unit'],
                    'is_active': True,
                    'track_inventory': True,
                    'created_by': User.objects.first()
                }
            )
            if created:
                print(f"✅ تم إنشاء المنتج: {product.name} - {product.cost_price} ج.م")
            else:
                print(f"⚠️ المنتج موجود: {product.name} - {product.cost_price} ج.م")
        
        print("\n" + "="*60)
        print("البيانات التجريبية:")
        print("="*60)
        
        products = ProductDefinition.objects.filter(product_type='raw_material', is_active=True)
        for product in products:
            print(f"ID: {product.id} | {product.name} | {product.cost_price} ج.م | {product.main_unit.name}")
        
        print("="*60)
        print(f"إجمالي المنتجات: {products.count()}")
        print("يمكنك الآن اختبار API باستخدام هذه المعرفات")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    create_test_data()
