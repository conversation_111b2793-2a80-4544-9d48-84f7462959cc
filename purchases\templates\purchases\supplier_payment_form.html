{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة دفعة للمورد - {{ supplier.name }} - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .section-title {
        color: #28a745;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }

    .invoice-card {
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .invoice-card:hover {
        border-color: #28a745;
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
    }

    .invoice-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
    }

    .invoice-number {
        font-weight: bold;
        color: #2c3e50;
        font-size: 1.1rem;
    }

    .invoice-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .status-received {
        background: #fff3cd;
        color: #856404;
    }

    .status-partially-paid {
        background: #d1ecf1;
        color: #0c5460;
    }

    .status-overdue {
        background: #f8d7da;
        color: #721c24;
    }

    .invoice-amounts {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
    }

    .amount-item {
        text-align: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .amount-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .amount-value {
        font-size: 1.1rem;
        font-weight: bold;
    }

    .amount-total {
        color: #17a2b8;
    }

    .amount-paid {
        color: #28a745;
    }

    .amount-remaining {
        color: #dc3545;
    }

    .allocation-input {
        max-width: 150px;
        margin: 0 auto;
    }

    .summary-card {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 20px;
    }

    .summary-card h3 {
        margin: 0 0 10px 0;
        font-size: 2rem;
    }

    .btn-submit {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .btn-cancel {
        background: #6c757d;
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }

    .alert-info {
        background: linear-gradient(45deg, #d1ecf1, #bee5eb);
        border: none;
        border-radius: 10px;
        color: #0c5460;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-cash-stack"></i>
                    إضافة دفعة للمورد
                </h1>
                <p class="mb-0">{{ supplier.name }} - إجمالي المديونية: {{ total_debt|floatformat:2 }} ج.م</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'purchases:supplier_detail' supplier.pk %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right"></i>
                    العودة للمورد
                </a>
            </div>
        </div>
    </div>

    <form method="post" id="paymentForm">
        {% csrf_token %}
        
        <!-- معلومات الدفعة -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="bi bi-credit-card"></i>
                معلومات الدفعة
            </h3>
            
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">تاريخ الدفع <span class="text-danger">*</span></label>
                    <input type="date" name="payment_date" class="form-control" value="{{ today }}" required>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">إجمالي المبلغ المدفوع <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="number" name="total_amount" id="totalAmount" class="form-control" 
                               placeholder="0.00" step="0.01" min="0" required onchange="updateAllocations()">
                        <span class="input-group-text">ج.م</span>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                    <select name="payment_method" class="form-select" required>
                        <option value="">اختر طريقة الدفع</option>
                        <option value="cash">نقدي</option>
                        <option value="bank_transfer">تحويل بنكي</option>
                        <option value="check">شيك</option>
                        <option value="credit_card">بطاقة ائتمان</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">رقم المرجع</label>
                    <input type="text" name="reference_number" class="form-control" 
                           placeholder="رقم الشيك، التحويل، إلخ">
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-12">
                    <label class="form-label">ملاحظات</label>
                    <textarea name="notes" class="form-control" rows="3" 
                              placeholder="ملاحظات إضافية عن الدفعة"></textarea>
                </div>
            </div>
        </div>

        <!-- توزيع الدفعة على الفواتير -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="bi bi-distribute-horizontal"></i>
                توزيع الدفعة على الفواتير
            </h3>
            
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                <strong>تعليمات:</strong> يمكنك توزيع المبلغ المدفوع على الفواتير المختلفة. سيتم تطبيق الدفعة على الفواتير الأقدم أولاً إذا لم تحدد توزيعاً مخصصاً.
            </div>

            {% if unpaid_invoices %}
                {% for invoice in unpaid_invoices %}
                    <div class="invoice-card">
                        <div class="invoice-header">
                            <div class="invoice-number">فاتورة {{ invoice.invoice_number }}</div>
                            <div class="invoice-status status-{{ invoice.status }}">
                                {{ invoice.get_status_display }}
                            </div>
                        </div>
                        
                        <div class="invoice-amounts">
                            <div class="amount-item">
                                <div class="amount-label">إجمالي الفاتورة</div>
                                <div class="amount-value amount-total">{{ invoice.total_amount|floatformat:2 }} ج.م</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">المدفوع</div>
                                <div class="amount-value amount-paid">{{ invoice.paid_amount|floatformat:2 }} ج.م</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">المتبقي</div>
                                <div class="amount-value amount-remaining">{{ invoice.remaining_amount|floatformat:2 }} ج.م</div>
                            </div>
                            <div class="amount-item">
                                <div class="amount-label">تاريخ الاستحقاق</div>
                                <div class="amount-value">{{ invoice.due_date }}</div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <label class="form-label">المبلغ المخصص لهذه الفاتورة</label>
                            <div class="input-group allocation-input">
                                <input type="number" name="allocation_{{ invoice.id }}" 
                                       class="form-control allocation-input-field" 
                                       placeholder="0.00" step="0.01" min="0" 
                                       max="{{ invoice.remaining_amount }}"
                                       data-max="{{ invoice.remaining_amount }}"
                                       onchange="validateAllocation(this)">
                                <span class="input-group-text">ج.م</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-check-circle display-4 text-success"></i>
                    <h4 class="mt-3">جميع الفواتير مدفوعة!</h4>
                    <p class="text-muted">لا توجد فواتير غير مدفوعة لهذا المورد</p>
                </div>
            {% endif %}
        </div>

        <!-- ملخص التوزيع -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="bi bi-calculator"></i>
                ملخص التوزيع
            </h3>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="summary-card">
                        <h3 id="totalPaymentAmount">0.00</h3>
                        <p>إجمالي الدفعة</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="summary-card">
                        <h3 id="totalAllocated">0.00</h3>
                        <p>إجمالي المخصص</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="summary-card">
                        <h3 id="remainingAmount">0.00</h3>
                        <p>المبلغ المتبقي</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الحفظ -->
        <div class="text-center">
            <button type="submit" class="btn btn-submit me-3">
                <i class="bi bi-check-circle"></i>
                حفظ الدفعة
            </button>
            <a href="{% url 'purchases:supplier_detail' supplier.pk %}" class="btn btn-cancel">
                <i class="bi bi-x-circle"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function updateAllocations() {
        const totalAmount = parseFloat(document.getElementById('totalAmount').value) || 0;
        document.getElementById('totalPaymentAmount').textContent = totalAmount.toFixed(2);
        calculateTotals();
    }

    function validateAllocation(input) {
        const value = parseFloat(input.value) || 0;
        const max = parseFloat(input.dataset.max) || 0;
        
        if (value > max) {
            input.value = max.toFixed(2);
            alert(`المبلغ المخصص لا يمكن أن يتجاوز المبلغ المتبقي (${max.toFixed(2)} ج.م)`);
        }
        
        calculateTotals();
    }

    function calculateTotals() {
        const totalPayment = parseFloat(document.getElementById('totalAmount').value) || 0;
        let totalAllocated = 0;
        
        document.querySelectorAll('.allocation-input-field').forEach(input => {
            totalAllocated += parseFloat(input.value) || 0;
        });
        
        const remaining = totalPayment - totalAllocated;
        
        document.getElementById('totalAllocated').textContent = totalAllocated.toFixed(2);
        document.getElementById('remainingAmount').textContent = remaining.toFixed(2);
        
        // تغيير لون المبلغ المتبقي
        const remainingElement = document.getElementById('remainingAmount');
        if (remaining < 0) {
            remainingElement.style.color = '#dc3545';
        } else if (remaining > 0) {
            remainingElement.style.color = '#ffc107';
        } else {
            remainingElement.style.color = '#28a745';
        }
    }

    // التحقق من صحة النموذج قبل الإرسال
    document.getElementById('paymentForm').addEventListener('submit', function(e) {
        const totalPayment = parseFloat(document.getElementById('totalAmount').value) || 0;
        let totalAllocated = 0;
        
        document.querySelectorAll('.allocation-input-field').forEach(input => {
            totalAllocated += parseFloat(input.value) || 0;
        });
        
        if (totalAllocated > totalPayment) {
            e.preventDefault();
            alert('إجمالي المبالغ المخصصة يتجاوز إجمالي الدفعة!');
            return false;
        }
        
        if (totalPayment <= 0) {
            e.preventDefault();
            alert('يجب إدخال مبلغ الدفعة!');
            return false;
        }
    });
</script>
{% endblock %}
