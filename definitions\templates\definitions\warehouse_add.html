{% extends 'base.html' %}

{% block title %}إضافة مخزن جديد{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">إضافة مخزن جديد</h5>
                </div>
                
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" id="warehouseForm">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label class="form-label">كود المخزن <span class="text-danger">*</span></label>
                            <input type="text" name="code" class="form-control" required 
                                   placeholder="مثال: WH001" maxlength="20">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">اسم المخزن <span class="text-danger">*</span></label>
                            <input type="text" name="name" class="form-control" required 
                                   placeholder="مثال: المخزن الرئيسي" maxlength="100">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">نوع المخزن <span class="text-danger">*</span></label>
                            <select name="warehouse_type" class="form-select" required>
                                <option value="">-- اختر نوع المخزن --</option>
                                <option value="main">مخزن رئيسي</option>
                                <option value="branch">مخزن فرعي</option>
                                <option value="raw_materials">مخزن مواد خام</option>
                                <option value="finished_goods">مخزن منتجات تامة</option>
                                <option value="damaged">مخزن تالف</option>
                                <option value="quarantine">مخزن حجر صحي</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea name="address" class="form-control" rows="2" 
                                      placeholder="عنوان المخزن (اختياري)"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="text" name="phone" class="form-control" 
                                   placeholder="رقم هاتف المخزن (اختياري)" maxlength="20">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">مدير المخزن</label>
                            <input type="text" name="manager_name" class="form-control" 
                                   placeholder="اسم مدير المخزن (اختياري)" maxlength="100">
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="is_active" class="form-check-input" 
                                       id="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    المخزن نشط
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" name="allow_negative_stock" class="form-check-input" 
                                       id="allow_negative_stock">
                                <label class="form-check-label" for="allow_negative_stock">
                                    السماح بالمخزون السالب
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <input type="submit" value="حفظ المخزن" class="btn btn-success btn-lg">
                            <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-outline-secondary">
                                العودة للقائمة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
