from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from .models import UserProfile
# تم نقل Permission, Role, UserRole إلى تطبيق permissions الجديد


class UserCreateForm(UserCreationForm):
    """نموذج إنشاء مستخدم جديد مع الصورة الشخصية"""

    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'الاسم الأول'
        }),
        label="الاسم الأول"
    )

    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'الاسم الأخير'
        }),
        label="الاسم الأخير"
    )

    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'البريد الإلكتروني'
        }),
        label="البريد الإلكتروني"
    )

    # حقول UserProfile
    phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الهاتف'
        }),
        label="رقم الهاتف"
    )

    mobile = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الجوال'
        }),
        label="رقم الجوال"
    )

    department = forms.ChoiceField(
        choices=[('', 'اختر القسم')] + UserProfile.DEPARTMENTS,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label="القسم"
    )

    position = forms.ChoiceField(
        choices=[('', 'اختر المنصب')] + UserProfile.POSITIONS,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label="المنصب"
    )

    employee_id = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الموظف'
        }),
        label="رقم الموظف"
    )

    avatar = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        }),
        label="الصورة الشخصية"
    )

    is_staff = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label="مدير"
    )

    is_active = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label="نشط"
    )
    

    


    class Meta:
        model = User
        fields = ('username', 'first_name', 'last_name', 'email', 'password1', 'password2')
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المستخدم',
                'data-placeholder-en': 'Username'
            }),
        }
        labels = {
            'username': 'اسم المستخدم',
            'password1': 'كلمة المرور',
            'password2': 'تأكيد كلمة المرور',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تخصيص widgets لكلمات المرور
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'كلمة المرور',
            'data-placeholder-en': 'Password'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'تأكيد كلمة المرور',
            'data-placeholder-en': 'Confirm Password'
        })

        # تعيين القيم الافتراضية
        self.fields['is_active'].initial = True

    def clean_employee_id(self):
        """التحقق من عدم تكرار رقم الموظف"""
        employee_id = self.cleaned_data.get('employee_id', '').strip()

        if employee_id:  # فقط إذا كان هناك قيمة
            # التحقق من عدم وجود رقم موظف مماثل
            existing_profile = UserProfile.objects.filter(employee_id=employee_id).first()
            if existing_profile:
                raise forms.ValidationError('رقم الموظف هذا مستخدم بالفعل')

        return employee_id if employee_id else None

    def clean_email(self):
        """التحقق من عدم تكرار البريد الإلكتروني"""
        email = self.cleaned_data.get('email')
        if email and User.objects.filter(email=email).exists():
            raise forms.ValidationError('هذا البريد الإلكتروني مستخدم بالفعل')
        return email

    def clean_username(self):
        """التحقق من صحة اسم المستخدم"""
        username = self.cleaned_data.get('username')
        if username:
            # التحقق من عدم وجود مسافات
            if ' ' in username:
                raise forms.ValidationError('اسم المستخدم لا يجب أن يحتوي على مسافات')
            # التحقق من الطول
            if len(username) < 3:
                raise forms.ValidationError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل')
        return username



    def save(self, commit=True):
        # حفظ المستخدم الأساسي أولاً
        user = super().save(commit=False)

        # تعيين البيانات الأساسية
        user.email = self.cleaned_data.get('email', '')
        user.is_staff = self.cleaned_data.get('is_staff', False)
        user.is_active = self.cleaned_data.get('is_active', True)

        if commit:
            user.save()

            # إنشاء UserProfile بشكل منفصل
            self._create_user_profile(user)

        return user

    def _create_user_profile(self, user):
        """إنشاء ملف المستخدم الشخصي"""
        try:
            profile, created = UserProfile.objects.get_or_create(user=user)

            # تحديث البيانات فقط إذا تم توفيرها
            phone = self.cleaned_data.get('phone', '').strip()
            if phone:
                profile.phone = phone

            mobile = self.cleaned_data.get('mobile', '').strip()
            if mobile:
                profile.mobile = mobile

            department = self.cleaned_data.get('department', '').strip()
            if department:
                profile.department = department

            position = self.cleaned_data.get('position', '').strip()
            if position:
                profile.position = position

            employee_id = self.cleaned_data.get('employee_id', '').strip()
            if employee_id:
                profile.employee_id = employee_id

            avatar = self.cleaned_data.get('avatar')
            if avatar:
                profile.avatar = avatar

            profile.save()

        except Exception as e:
            # تسجيل الخطأ لكن عدم إيقاف العملية
            print(f"Warning: Could not create/update profile for {user.username}: {e}")
            pass


class UserEditForm(forms.ModelForm):
    """نموذج تعديل المستخدم"""
    
    # حقول UserProfile
    phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الهاتف'
        }),
        label="رقم الهاتف"
    )
    
    mobile = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الجوال'
        }),
        label="رقم الجوال"
    )
    
    department = forms.ChoiceField(
        choices=[('', 'اختر القسم')] + UserProfile.DEPARTMENTS,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label="القسم"
    )
    
    position = forms.ChoiceField(
        choices=[('', 'اختر المنصب')] + UserProfile.POSITIONS,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label="المنصب"
    )
    
    employee_id = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'رقم الموظف'
        }),
        label="رقم الموظف"
    )
    
    avatar = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        }),
        label="الصورة الشخصية"
    )

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'is_staff', 'is_active']
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأخير'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني'
            }),
            'is_staff': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'الاسم الأخير',
            'email': 'البريد الإلكتروني',
            'is_staff': 'مدير',
            'is_active': 'نشط',
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if self.user and hasattr(self.user, 'profile'):
            profile = self.user.profile
            self.fields['phone'].initial = profile.phone
            self.fields['mobile'].initial = profile.mobile
            self.fields['department'].initial = profile.department
            self.fields['position'].initial = profile.position
            self.fields['employee_id'].initial = profile.employee_id

    def save(self, commit=True):
        user = super().save(commit=commit)
        
        if commit:
            # تحديث UserProfile
            profile, created = UserProfile.objects.get_or_create(user=user)
            profile.phone = self.cleaned_data['phone']
            profile.mobile = self.cleaned_data['mobile']
            profile.department = self.cleaned_data['department']
            profile.position = self.cleaned_data['position']
            profile.employee_id = self.cleaned_data['employee_id']
            
            if self.cleaned_data['avatar']:
                profile.avatar = self.cleaned_data['avatar']
            
            profile.save()
        
        return user


# تم نقل نماذج الأدوار والصلاحيات إلى تطبيق permissions الجديد


