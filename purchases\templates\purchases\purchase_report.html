{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير المشتريات - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-left: 5px solid #007bff;
    }

    .stat-card h3 {
        margin: 0 0 10px 0;
        font-size: 2.5rem;
        font-weight: bold;
        color: #007bff;
    }

    .stat-card p {
        margin: 0;
        font-size: 1.1rem;
        color: #6c757d;
    }

    .filters-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .invoices-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table {
        margin: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table tbody td {
        padding: 15px;
        vertical-align: middle;
        border-bottom: 1px solid #f0f0f0;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .status-paid {
        background: #d4edda;
        color: #155724;
    }

    .status-received {
        background: #d1ecf1;
        color: #0c5460;
    }

    .status-draft {
        background: #f8d7da;
        color: #721c24;
    }

    .status-overdue {
        background: #fff3cd;
        color: #856404;
    }

    .amount-highlight {
        font-size: 1.1rem;
        font-weight: bold;
        color: #007bff;
    }

    .btn-export {
        background: linear-gradient(45deg, #6f42c1, #5a32a3);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-receipt"></i>
                    تقرير المشتريات
                </h1>
                <p class="mb-0">تقرير شامل عن فواتير المشتريات والمبالغ</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-export me-2" onclick="window.print()">
                    <i class="bi bi-printer"></i>
                    طباعة التقرير
                </button>
                <a href="{% url 'purchases:reports' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right"></i>
                    العودة
                </a>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="stats-cards">
        <div class="stat-card">
            <h3>{{ total_invoices }}</h3>
            <p>إجمالي الفواتير</p>
        </div>
        <div class="stat-card">
            <h3>{{ total_amount|floatformat:0 }}</h3>
            <p>إجمالي المبلغ (ج.م)</p>
        </div>
        <div class="stat-card">
            <h3>{{ paid_invoices }}</h3>
            <p>الفواتير المدفوعة</p>
        </div>
        <div class="stat-card">
            <h3>{{ pending_invoices }}</h3>
            <p>الفواتير المعلقة</p>
        </div>
    </div>

    <!-- الفلاتر -->
    <div class="filters-section">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            
            <div class="col-md-4">
                <label class="form-label">المورد</label>
                <select name="supplier" class="form-select">
                    <option value="">جميع الموردين</option>
                    {% for supplier in suppliers %}
                        <option value="{{ supplier.id }}" {% if supplier.id|stringformat:"s" == selected_supplier %}selected{% endif %}>
                            {{ supplier.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-funnel"></i>
                    تطبيق
                </button>
            </div>
        </form>
    </div>

    <!-- جدول الفواتير -->
    <div class="invoices-table">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>رقم الفاتورة</th>
                    <th>المورد</th>
                    <th>تاريخ الفاتورة</th>
                    <th>تاريخ الاستحقاق</th>
                    <th>المبلغ الإجمالي</th>
                    <th>الحالة</th>
                    <th>الملاحظات</th>
                </tr>
            </thead>
            <tbody>
                {% for invoice in invoices %}
                    <tr>
                        <td>
                            <strong>{{ invoice.invoice_number }}</strong>
                        </td>
                        <td>
                            <div>
                                <strong>{{ invoice.supplier.name }}</strong>
                                <br>
                                <small class="text-muted">{{ invoice.supplier.code }}</small>
                            </div>
                        </td>
                        <td>
                            <strong>{{ invoice.invoice_date|date:"d/m/Y" }}</strong>
                        </td>
                        <td>
                            {% if invoice.due_date %}
                                <strong>{{ invoice.due_date|date:"d/m/Y" }}</strong>
                                {% if invoice.is_overdue %}
                                    <br>
                                    <small class="text-danger">متأخرة</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="amount-highlight">{{ invoice.total_amount|floatformat:2 }} ج.م</span>
                        </td>
                        <td>
                            <span class="status-badge status-{{ invoice.status }}">
                                {{ invoice.get_status_display }}
                            </span>
                        </td>
                        <td>
                            {% if invoice.notes %}
                                {{ invoice.notes|truncatechars:50 }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد فواتير مشتريات</p>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- ملخص التقرير -->
    {% if invoices %}
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">ملخص التقرير</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>إجمالي الفواتير:</strong>
                            </div>
                            <div class="col-6">
                                {{ total_invoices }} فاتورة
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>إجمالي المبلغ:</strong>
                            </div>
                            <div class="col-6">
                                <span class="amount-highlight">{{ total_amount|floatformat:2 }} ج.م</span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>متوسط الفاتورة:</strong>
                            </div>
                            <div class="col-6">
                                {{ average_invoice|floatformat:2 }} ج.م
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين الطباعة
    window.addEventListener('beforeprint', function() {
        document.body.classList.add('printing');
    });
    
    window.addEventListener('afterprint', function() {
        document.body.classList.remove('printing');
    });
</script>

<style>
    @media print {
        .btn, .filters-section {
            display: none !important;
        }
        
        .page-header {
            background: #007bff !important;
            -webkit-print-color-adjust: exact;
        }
        
        .table thead th {
            background: #007bff !important;
            -webkit-print-color-adjust: exact;
        }
    }
</style>
{% endblock %}
