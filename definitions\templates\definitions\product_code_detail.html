{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل كود الصنف - {{ product_code.code }}{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Product Code Detail Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Hero Section */
    .detail-hero {
        background: var(--primary-gradient);
        padding: 4rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
        border-radius: 0 0 2rem 2rem;
    }

    .detail-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .hero-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 2rem;
    }

    /* Detail Cards */
    .detail-card {
        background: white;
        border-radius: 1.5rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .detail-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .card-header {
        background: var(--info-gradient);
        color: white;
        padding: 1.5rem;
        border: none;
    }

    .card-header h5 {
        margin: 0;
        font-weight: 700;
        font-size: 1.3rem;
    }

    .card-body {
        padding: 2rem;
    }

    /* Info Grid */
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .info-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.75rem;
        border-left: 4px solid var(--primary-color, #667eea);
    }

    .info-icon {
        width: 40px;
        height: 40px;
        background: var(--primary-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: white;
        font-size: 1.2rem;
    }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-weight: 700;
        color: #2c3e50;
        font-size: 1.1rem;
    }

    /* Status Badge */
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-weight: 600;
        font-size: 0.9rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .status-active {
        background: var(--success-gradient);
        color: white;
    }

    .status-inactive {
        background: var(--danger-gradient);
        color: white;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-top: 2rem;
    }

    .btn-action {
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .btn-primary-action {
        background: var(--primary-gradient);
        color: white;
    }

    .btn-secondary-action {
        background: #6c757d;
        color: white;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }
        
        .info-grid {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-action {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="detail-hero">
    <div class="container">
        <div class="hero-content text-center">
            <h1 class="hero-title">
                <i class="bi bi-upc-scan me-3"></i>
                {{ product_code.code }}
            </h1>
            <p class="hero-subtitle">تفاصيل كود الصنف</p>
        </div>
    </div>
</div>

<div class="container">
    <!-- Basic Information Card -->
    <div class="detail-card">
        <div class="card-header">
            <h5><i class="bi bi-info-circle me-2"></i>المعلومات الأساسية</h5>
        </div>
        <div class="card-body">
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-content">
                        <div class="info-label">كود الصنف</div>
                        <div class="info-value">{{ product_code.code }}</div>
                    </div>
                    <div class="info-icon">
                        <i class="bi bi-upc-scan"></i>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-content">
                        <div class="info-label">الصنف المرتبط</div>
                        <div class="info-value">
                            {% if product_code.product %}
                                <a href="{% url 'definitions:product_detail' product_code.product.pk %}" class="text-decoration-none">
                                    {{ product_code.product.name }}
                                </a>
                            {% else %}
                                غير محدد
                            {% endif %}
                        </div>
                    </div>
                    <div class="info-icon">
                        <i class="bi bi-box"></i>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-content">
                        <div class="info-label">الحالة</div>
                        <div class="info-value">
                            {% if product_code.is_active %}
                                <span class="status-badge status-active">
                                    <i class="bi bi-check-circle"></i>
                                    نشط
                                </span>
                            {% else %}
                                <span class="status-badge status-inactive">
                                    <i class="bi bi-x-circle"></i>
                                    غير نشط
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="info-icon">
                        <i class="bi bi-toggle-on"></i>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-content">
                        <div class="info-label">تاريخ الإنشاء</div>
                        <div class="info-value">{{ product_code.created_at|date:"d/m/Y H:i" }}</div>
                    </div>
                    <div class="info-icon">
                        <i class="bi bi-calendar-plus"></i>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-content">
                        <div class="info-label">آخر تحديث</div>
                        <div class="info-value">{{ product_code.updated_at|date:"d/m/Y H:i" }}</div>
                    </div>
                    <div class="info-icon">
                        <i class="bi bi-calendar-check"></i>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-content">
                        <div class="info-label">أنشئ بواسطة</div>
                        <div class="info-value">{{ product_code.created_by.get_full_name|default:product_code.created_by.username }}</div>
                    </div>
                    <div class="info-icon">
                        <i class="bi bi-person"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{% url 'definitions:product_code_edit' product_code.pk %}" class="btn-action btn-primary-action">
            <i class="bi bi-pencil"></i>
            تعديل
        </a>
        <a href="{% url 'definitions:product_code_list' %}" class="btn-action btn-secondary-action">
            <i class="bi bi-arrow-right"></i>
            العودة للقائمة
        </a>
    </div>
</div>
{% endblock %}
