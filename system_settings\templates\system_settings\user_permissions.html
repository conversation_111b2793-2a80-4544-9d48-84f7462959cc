{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .permissions-container {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin: 2rem 0;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .user-info {
        background: #f8fafc;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 2px solid #e5e7eb;
    }
    
    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #667eea;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        font-weight: 600;
        margin-left: 1rem;
    }
    
    .user-details h3 {
        margin: 0;
        color: #374151;
        font-size: 1.25rem;
    }
    
    .user-details p {
        margin: 0.25rem 0 0 0;
        color: #6b7280;
    }
    
    .roles-section {
        margin-top: 2rem;
    }
    
    .section-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .roles-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .role-card {
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 10px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .role-card:hover {
        border-color: #667eea;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    }
    
    .role-card.selected {
        border-color: #667eea;
        background: #f0f4ff;
    }
    
    .role-checkbox {
        margin-left: 1rem;
        transform: scale(1.2);
    }
    
    .role-name {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }
    
    .role-description {
        color: #6b7280;
        font-size: 0.875rem;
        line-height: 1.4;
    }
    
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid #e5e7eb;
    }
    
    .btn {
        padding: 0.75rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }
    
    .btn-primary {
        background: #667eea;
        color: white;
    }
    
    .btn-primary:hover {
        background: #5a67d8;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }
    
    .btn-secondary {
        background: #6b7280;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #4b5563;
        transform: translateY(-2px);
    }
    
    .current-roles {
        background: #f0f9ff;
        border: 2px solid #0ea5e9;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 2rem;
    }
    
    .current-roles h4 {
        color: #0c4a6e;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }
    
    .role-badge {
        display: inline-block;
        background: #0ea5e9;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        margin: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1><i class="bi bi-person-gear"></i> {{ title }}</h1>
        <p>تعيين الأدوار والصلاحيات للمستخدم</p>
    </div>
    
    <!-- محتوى الصلاحيات -->
    <div class="permissions-container">
        <!-- معلومات المستخدم -->
        <div class="user-info">
            <div style="display: flex; align-items: center;">
                <div class="user-avatar">
                    {{ user.first_name.0|default:user.username.0|upper }}
                </div>
                <div class="user-details">
                    <h3>{{ user.get_full_name|default:user.username }}</h3>
                    <p><i class="bi bi-envelope"></i> {{ user.email }}</p>
                    <p><i class="bi bi-person-badge"></i> {{ user.username }}</p>
                </div>
            </div>
        </div>
        
        <!-- الأدوار الحالية -->
        {% if user_role_ids %}
        <div class="current-roles">
            <h4><i class="bi bi-check-circle"></i> الأدوار الحالية:</h4>
            {% for role in all_roles %}
                {% if role.id in user_role_ids %}
                    <span class="role-badge">{{ role.name }}</span>
                {% endif %}
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- نموذج تعيين الأدوار -->
        <form method="post">
            {% csrf_token %}
            
            <div class="roles-section">
                <div class="section-title">
                    <i class="bi bi-people"></i>
                    اختر الأدوار للمستخدم
                </div>
                
                <div class="roles-grid">
                    {% for role in all_roles %}
                    <div class="role-card {% if role.id in user_role_ids %}selected{% endif %}" 
                         onclick="toggleRole({{ role.id }})">
                        <div style="display: flex; align-items: flex-start;">
                            <input type="checkbox" 
                                   name="roles" 
                                   value="{{ role.id }}" 
                                   id="role_{{ role.id }}"
                                   class="role-checkbox"
                                   {% if role.id in user_role_ids %}checked{% endif %}>
                            <div>
                                <div class="role-name">{{ role.name }}</div>
                                <div class="role-description">{{ role.description }}</div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <p style="color: #6b7280;">لا توجد أدوار متاحة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- أزرار الإجراءات -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check"></i> حفظ الصلاحيات
                </button>
                <a href="{% url 'system_settings:users_management' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> إلغاء
                </a>
            </div>
        </form>
    </div>
</div>

<script>
function toggleRole(roleId) {
    const checkbox = document.getElementById('role_' + roleId);
    const card = checkbox.closest('.role-card');
    
    checkbox.checked = !checkbox.checked;
    
    if (checkbox.checked) {
        card.classList.add('selected');
    } else {
        card.classList.remove('selected');
    }
}

// منع النقر على checkbox من تشغيل toggleRole مرتين
document.querySelectorAll('.role-checkbox').forEach(function(checkbox) {
    checkbox.addEventListener('click', function(e) {
        e.stopPropagation();
        
        const card = this.closest('.role-card');
        if (this.checked) {
            card.classList.add('selected');
        } else {
            card.classList.remove('selected');
        }
    });
});
</script>
{% endblock %}
