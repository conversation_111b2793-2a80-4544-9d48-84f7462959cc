{% extends 'base.html' %}

{% block title %}تقارير المشتريات - نظام أوساريك{% endblock %}

{% block extra_css %}
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .report-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 5px solid;
            position: relative;
            overflow: hidden;
        }
        
        .report-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            border-radius: 50%;
            transform: translate(30px, -30px);
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .report-card.suppliers { border-left-color: #28a745; }
        .report-card.purchases { border-left-color: #ffc107; }
        .report-card.inventory { border-left-color: #17a2b8; }
        .report-card.financial { border-left-color: #6f42c1; }
        
        .report-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: white;
        }
        
        .icon-suppliers { background: linear-gradient(45deg, #28a745, #20c997); }
        .icon-purchases { background: linear-gradient(45deg, #ffc107, #e0a800); }
        .icon-inventory { background: linear-gradient(45deg, #17a2b8, #138496); }
        .icon-financial { background: linear-gradient(45deg, #6f42c1, #5a32a3); }
        
        .report-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: #333;
        }
        
        .report-description {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .report-features {
            list-style: none;
            padding: 0;
            margin-bottom: 25px;
        }
        
        .report-features li {
            padding: 5px 0;
            color: #495057;
            display: flex;
            align-items: center;
        }
        
        .report-features li i {
            color: #28a745;
            margin-left: 10px;
            font-size: 0.9rem;
        }
        
        .btn-report {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            width: 100%;
            justify-content: center;
        }
        
        .btn-report:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(23, 162, 184, 0.3);
            color: white;
        }
        
        .btn-print {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
            color: white;
        }
        
        .quick-stats {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            text-align: center;
            border-bottom: 3px solid #17a2b8;
            padding-bottom: 15px;
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .stat-item:hover {
            border-color: #17a2b8;
            transform: translateY(-3px);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #17a2b8;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .recent-activity {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .activity-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 15px;
            font-size: 1.2rem;
        }
        
        .activity-content h6 {
            margin: 0;
            font-weight: 600;
            color: #333;
        }
        
        .activity-content small {
            color: #6c757d;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-graph-up"></i>
                        تقارير المشتريات
                    </h1>
                    <p class="mb-0">تقارير شاملة ومفصلة عن جميع عمليات الشراء والموردين</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 justify-content-end">
                        <button class="btn btn-print" onclick="window.print()">
                            <i class="bi bi-printer"></i>
                            طباعة
                        </button>
                        <a href="{% url 'purchases:dashboard' %}" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left"></i>
                            العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="quick-stats">
            <h3 class="section-title">إحصائيات سريعة</h3>
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-number">{{ total_suppliers|default:0 }}</div>
                    <div class="stat-label">إجمالي الموردين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ total_orders|default:0 }}</div>
                    <div class="stat-label">أوامر الشراء</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ total_invoices|default:0 }}</div>
                    <div class="stat-label">فواتير الشراء</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ total_amount|floatformat:0|default:0 }}</div>
                    <div class="stat-label">إجمالي المشتريات (ج.م)</div>
                </div>
            </div>
        </div>

        <!-- تقارير المشتريات -->
        <div class="reports-grid">
            <!-- تقرير الموردين -->
            <div class="report-card suppliers">
                <div class="report-icon icon-suppliers">
                    <i class="bi bi-people"></i>
                </div>
                <h4 class="report-title">تقرير الموردين</h4>
                <p class="report-description">
                    تقرير شامل عن جميع الموردين وبياناتهم وأنشطتهم التجارية
                </p>
                <ul class="report-features">
                    <li><i class="bi bi-check-circle"></i> قائمة الموردين النشطين وغير النشطين</li>
                    <li><i class="bi bi-check-circle"></i> بيانات الاتصال والعناوين</li>
                    <li><i class="bi bi-check-circle"></i> شروط الدفع وحدود الائتمان</li>
                    <li><i class="bi bi-check-circle"></i> إحصائيات المشتريات لكل مورد</li>
                </ul>
                <a href="{% url 'purchases:supplier_report' %}" class="btn-report">
                    <i class="bi bi-eye"></i>
                    عرض التقرير
                </a>
            </div>

            <!-- تقرير المشتريات -->
            <div class="report-card purchases">
                <div class="report-icon icon-purchases">
                    <i class="bi bi-cart-plus"></i>
                </div>
                <h4 class="report-title">تقرير المشتريات</h4>
                <p class="report-description">
                    تقرير مفصل عن جميع عمليات الشراء والفواتير والمدفوعات
                </p>
                <ul class="report-features">
                    <li><i class="bi bi-check-circle"></i> أوامر الشراء وحالاتها</li>
                    <li><i class="bi bi-check-circle"></i> فواتير الشراء والمدفوعات</li>
                    <li><i class="bi bi-check-circle"></i> تحليل المشتريات حسب الفترة</li>
                    <li><i class="bi bi-check-circle"></i> مقارنة الأسعار والموردين</li>
                </ul>
                <a href="{% url 'purchases:purchase_report' %}" class="btn-report">
                    <i class="bi bi-eye"></i>
                    عرض التقرير
                </a>
            </div>

            <!-- تقرير المخزون -->
            <div class="report-card inventory">
                <div class="report-icon icon-inventory">
                    <i class="bi bi-boxes"></i>
                </div>
                <h4 class="report-title">تقرير مخزون المشتريات</h4>
                <p class="report-description">
                    تقرير عن حالة المخزون والمنتجات التي تحتاج إعادة طلب
                </p>
                <ul class="report-features">
                    <li><i class="bi bi-check-circle"></i> كميات المخزون الحالية</li>
                    <li><i class="bi bi-check-circle"></i> المنتجات تحت الحد الأدنى</li>
                    <li><i class="bi bi-check-circle"></i> قيمة المخزون الإجمالية</li>
                    <li><i class="bi bi-check-circle"></i> تحليل حركة المخزون</li>
                </ul>
                <a href="{% url 'purchases:purchase_inventory_report' %}" class="btn-report">
                    <i class="bi bi-eye"></i>
                    عرض التقرير
                </a>
            </div>

            <!-- التقرير المالي -->
            <div class="report-card financial">
                <div class="report-icon icon-financial">
                    <i class="bi bi-calculator"></i>
                </div>
                <h4 class="report-title">التقرير المالي للمشتريات</h4>
                <p class="report-description">
                    تحليل مالي شامل لتكاليف المشتريات والمدفوعات
                </p>
                <ul class="report-features">
                    <li><i class="bi bi-check-circle"></i> إجمالي تكاليف المشتريات</li>
                    <li><i class="bi bi-check-circle"></i> المدفوعات والمستحقات</li>
                    <li><i class="bi bi-check-circle"></i> تحليل التدفق النقدي</li>
                    <li><i class="bi bi-check-circle"></i> مقارنة الميزانيات</li>
                </ul>
                <a href="{% url 'purchases:financial_report' %}" class="btn-report">
                    <i class="bi bi-eye"></i>
                    عرض التقرير
                </a>
            </div>
            <!-- تقرير الملخص المالي للموردين -->
            <div class="report-card">
                <div class="report-icon">
                    <i class="bi bi-people-fill"></i>
                </div>
                <h4 class="report-title">الملخص المالي للموردين</h4>
                <p class="report-description">
                    تقرير شامل عن الديون والمدفوعات لجميع الموردين مع نسب السداد
                </p>
                <ul class="report-features">
                    <li><i class="bi bi-check-circle"></i> إجمالي الديون لكل مورد</li>
                    <li><i class="bi bi-check-circle"></i> المبالغ المدفوعة والمتبقية</li>
                    <li><i class="bi bi-check-circle"></i> نسب السداد والتأخير</li>
                    <li><i class="bi bi-check-circle"></i> تحليل الأولويات المالية</li>
                </ul>
                <a href="{% url 'purchases:suppliers_financial_summary' %}" class="btn-report">
                    <i class="bi bi-eye"></i>
                    عرض التقرير
                </a>
            </div>

            <!-- لوحة التنبيهات -->
            <div class="report-card">
                <div class="report-icon">
                    <i class="bi bi-bell-fill"></i>
                </div>
                <h4 class="report-title">التنبيهات والإشعارات</h4>
                <p class="report-description">
                    مراقبة المستحقات والمتأخرات والتنبيهات المالية
                </p>
                <ul class="report-features">
                    <li><i class="bi bi-check-circle"></i> تنبيهات الفواتير المتأخرة</li>
                    <li><i class="bi bi-check-circle"></i> المستحقات القريبة</li>
                    <li><i class="bi bi-check-circle"></i> تجاوز حدود الائتمان</li>
                    <li><i class="bi bi-check-circle"></i> إدارة الأولويات</li>
                </ul>
                <a href="{% url 'purchases:alerts_dashboard' %}" class="btn-report">
                    <i class="bi bi-eye"></i>
                    عرض التنبيهات
                </a>
            </div>

            <!-- توقعات التدفق النقدي -->
            <div class="report-card">
                <div class="report-icon">
                    <i class="bi bi-graph-up-arrow"></i>
                </div>
                <h4 class="report-title">توقعات التدفق النقدي</h4>
                <p class="report-description">
                    تحليل وتوقع التدفقات النقدية المستقبلية
                </p>
                <ul class="report-features">
                    <li><i class="bi bi-check-circle"></i> توقعات المشتريات</li>
                    <li><i class="bi bi-check-circle"></i> توقعات المدفوعات</li>
                    <li><i class="bi bi-check-circle"></i> تحليل الاتجاهات</li>
                    <li><i class="bi bi-check-circle"></i> رسوم بيانية تفاعلية</li>
                </ul>
                <a href="{% url 'purchases:cash_flow_forecast' %}" class="btn-report">
                    <i class="bi bi-eye"></i>
                    عرض التوقعات
                </a>
            </div>

            <!-- الموافقات المعلقة -->
            <div class="report-card">
                <div class="report-icon">
                    <i class="bi bi-clipboard-check"></i>
                </div>
                <h4 class="report-title">الموافقات المعلقة</h4>
                <p class="report-description">
                    إدارة موافقات المدفوعات الكبيرة والطلبات المعلقة
                </p>
                <ul class="report-features">
                    <li><i class="bi bi-check-circle"></i> موافقات المدفوعات</li>
                    <li><i class="bi bi-check-circle"></i> تتبع الطلبات</li>
                    <li><i class="bi bi-check-circle"></i> سجل الموافقات</li>
                    <li><i class="bi bi-check-circle"></i> إدارة الصلاحيات</li>
                </ul>
                <a href="{% url 'purchases:payment_approvals' %}" class="btn-report">
                    <i class="bi bi-eye"></i>
                    عرض الموافقات
                </a>
            </div>
        </div>

        <!-- النشاط الأخير -->
        <div class="recent-activity">
            <h3 class="section-title">النشاط الأخير</h3>
            
            <div class="activity-item">
                <div class="activity-icon" style="background: #28a745;">
                    <i class="bi bi-plus-circle"></i>
                </div>
                <div class="activity-content">
                    <h6>تم إنشاء أمر شراء جديد</h6>
                    <small>أمر شراء رقم PO-2024-001 من مورد ABC للتجارة</small>
                </div>
            </div>
            
            <div class="activity-item">
                <div class="activity-icon" style="background: #ffc107;">
                    <i class="bi bi-receipt"></i>
                </div>
                <div class="activity-content">
                    <h6>تم استلام فاتورة شراء</h6>
                    <small>فاتورة رقم INV-2024-001 بقيمة 15,000 ج.م</small>
                </div>
            </div>
            
            <div class="activity-item">
                <div class="activity-icon" style="background: #17a2b8;">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="activity-content">
                    <h6>تم تأكيد أمر شراء</h6>
                    <small>أمر شراء رقم PO-2024-002 تم تأكيده من المورد</small>
                </div>
            </div>
            
            <div class="activity-item">
                <div class="activity-icon" style="background: #dc3545;">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <div class="activity-content">
                    <h6>تنبيه مخزون منخفض</h6>
                    <small>5 منتجات تحتاج إعادة طلب من الموردين</small>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.report-card, .quick-stats, .recent-activity');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
{% endblock %}
