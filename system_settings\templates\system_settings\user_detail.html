{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل المستخدم - {{ user.username }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'system_settings:dashboard' %}">الإعدادات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'system_settings:users_management' %}">إدارة المستخدمين</a></li>
        <li class="breadcrumb-item active">{{ user.username }}</li>
    </ol>
</nav>
{% endblock %}

{% block extra_css %}
<style>
    .user-detail-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .user-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .user-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .user-avatar-large {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        margin: 0 auto 1rem;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        font-weight: 700;
        border: 4px solid rgba(255, 255, 255, 0.3);
        overflow: hidden;
        position: relative;
        z-index: 2;
    }

    .user-avatar-large img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .online-indicator {
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        border: 3px solid white;
    }

    .online {
        background: #22c55e;
        animation: pulse 2s infinite;
    }

    .offline {
        background: #6b7280;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(34, 197, 94, 0); }
        100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
    }

    .user-info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .info-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: 1px solid #e5e7eb;
    }

    .card-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #374151;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e5e7eb;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f3f4f6;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #6b7280;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-value {
        color: #374151;
        font-weight: 500;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-active {
        background: #dcfce7;
        color: #166534;
    }

    .status-inactive {
        background: #fef2f2;
        color: #dc2626;
    }

    .status-staff {
        background: #dbeafe;
        color: #1e40af;
    }

    .status-superuser {
        background: #fef3c7;
        color: #92400e;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn {
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
    }

    .btn-danger {
        background: #dc2626;
        color: white;
    }

    .btn-danger:hover {
        background: #b91c1c;
        color: white;
        text-decoration: none;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border: 1px solid #e5e7eb;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin: 0 auto 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
    }

    @media (max-width: 768px) {
        .user-info-grid {
            grid-template-columns: 1fr;
        }
        
        .user-detail-container {
            padding: 1rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="user-detail-container">
    <!-- رأس المستخدم -->
    <div class="user-header">
        <div class="user-avatar-large">
            {% if user.profile and user.profile.avatar %}
                <img src="{{ user.profile.avatar.url }}" alt="{{ user.get_full_name|default:user.username }}">
            {% else %}
                {% if user.first_name %}{{ user.first_name.0|upper }}{% elif user.username %}{{ user.username.0|upper }}{% else %}U{% endif %}
            {% endif %}
            
            <!-- مؤشر الاتصال -->
            <div class="online-indicator {% if user.profile and user.profile.is_user_online %}online{% else %}offline{% endif %}"></div>
        </div>
        
        <h2>{{ user.get_full_name|default:user.username }}</h2>
        <p style="opacity: 0.9; margin: 0;">@{{ user.username }}</p>
        
        {% if user.profile and user.profile.is_user_online %}
            <p style="margin-top: 0.5rem; opacity: 0.8;">🟢 متصل الآن</p>
        {% elif user.profile and user.profile.last_activity %}
            <p style="margin-top: 0.5rem; opacity: 0.8;">⚫ {{ user.profile.get_online_status_display }}</p>
        {% else %}
            <p style="margin-top: 0.5rem; opacity: 0.8;">⚫ لم يسجل دخول</p>
        {% endif %}
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                <i class="bi bi-calendar-plus"></i>
            </div>
            <div class="stat-value">{{ user.date_joined|timesince }}</div>
            <div class="stat-label">منذ الانضمام</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                <i class="bi bi-clock"></i>
            </div>
            <div class="stat-value">
                {% if user.last_login %}
                    {{ user.last_login|timesince }}
                {% else %}
                    لم يسجل دخول
                {% endif %}
            </div>
            <div class="stat-label">آخر دخول</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                <i class="bi bi-shield-check"></i>
            </div>
            <div class="stat-value">
                {% if user.is_superuser %}
                    مدير عام
                {% elif user.is_staff %}
                    مدير
                {% else %}
                    مستخدم
                {% endif %}
            </div>
            <div class="stat-label">نوع الحساب</div>
        </div>
    </div>

    <!-- معلومات المستخدم -->
    <div class="user-info-grid">
        <!-- المعلومات الشخصية -->
        <div class="info-card">
            <div class="card-title">
                <i class="bi bi-person"></i>
                المعلومات الشخصية
            </div>
            
            <div class="info-item">
                <span class="info-label">
                    <i class="bi bi-person"></i>
                    الاسم الكامل
                </span>
                <span class="info-value">{{ user.get_full_name|default:"غير محدد" }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">
                    <i class="bi bi-at"></i>
                    اسم المستخدم
                </span>
                <span class="info-value">{{ user.username }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">
                    <i class="bi bi-envelope"></i>
                    البريد الإلكتروني
                </span>
                <span class="info-value">{{ user.email|default:"غير محدد" }}</span>
            </div>
            
            {% if user.profile %}
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-telephone"></i>
                        الهاتف
                    </span>
                    <span class="info-value">{{ user.profile.phone|default:"غير محدد" }}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-phone"></i>
                        الجوال
                    </span>
                    <span class="info-value">{{ user.profile.mobile|default:"غير محدد" }}</span>
                </div>
            {% endif %}
        </div>

        <!-- المعلومات الوظيفية -->
        <div class="info-card">
            <div class="card-title">
                <i class="bi bi-briefcase"></i>
                المعلومات الوظيفية
            </div>
            
            {% if user.profile %}
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-building"></i>
                        القسم
                    </span>
                    <span class="info-value">
                        {% if user.profile.department %}
                            {% for dept_code, dept_name in departments %}
                                {% if dept_code == user.profile.department %}{{ dept_name }}{% endif %}
                            {% endfor %}
                        {% else %}
                            غير محدد
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-person-badge"></i>
                        المنصب
                    </span>
                    <span class="info-value">
                        {% if user.profile.position %}
                            {% for pos_code, pos_name in positions %}
                                {% if pos_code == user.profile.position %}{{ pos_name }}{% endif %}
                            {% endfor %}
                        {% else %}
                            غير محدد
                        {% endif %}
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-hash"></i>
                        رقم الموظف
                    </span>
                    <span class="info-value">{{ user.profile.employee_id|default:"غير محدد" }}</span>
                </div>
            {% endif %}
            
            <div class="info-item">
                <span class="info-label">
                    <i class="bi bi-shield"></i>
                    الصلاحيات
                </span>
                <span class="info-value">
                    {% if user.is_superuser %}
                        <span class="status-badge status-superuser">🔥 مدير عام</span>
                    {% elif user.is_staff %}
                        <span class="status-badge status-staff">👑 مدير</span>
                    {% else %}
                        <span class="status-badge">👤 مستخدم عادي</span>
                    {% endif %}
                </span>
            </div>
            
            <div class="info-item">
                <span class="info-label">
                    <i class="bi bi-toggle-on"></i>
                    حالة الحساب
                </span>
                <span class="info-value">
                    {% if user.is_active %}
                        <span class="status-badge status-active">✅ نشط</span>
                    {% else %}
                        <span class="status-badge status-inactive">❌ معطل</span>
                    {% endif %}
                </span>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="action-buttons">
        <a href="{% url 'system_settings:user_edit' user.id %}" class="btn btn-primary">
            <i class="bi bi-pencil"></i>
            تعديل المستخدم
        </a>
        
        <a href="{% url 'system_settings:users_management' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i>
            العودة للقائمة
        </a>
        
        {% if not user.is_superuser and user != request.user %}
            <a href="{% url 'system_settings:user_delete' user.id %}" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                <i class="bi bi-trash"></i>
                حذف المستخدم
            </a>
        {% endif %}
    </div>
</div>
{% endblock %}
