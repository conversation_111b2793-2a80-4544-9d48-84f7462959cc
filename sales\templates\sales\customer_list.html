{% extends 'base.html' %}
{% load static %}

{% block title %}{% if user_language == 'en' %}Customer List{% else %}قائمة العملاء{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{% if user_language == 'en' %}Customer List{% else %}قائمة العملاء{% endif %}</h1>
            <p class="text-muted">{% if user_language == 'en' %}Manage all customers and their information{% else %}إدارة جميع العملاء والمعلومات الخاصة بهم{% endif %}</p>
        </div>
        <a href="{% url 'sales:customer_create' %}" class="btn btn-primary">
            <i class="bi bi-person-plus me-2"></i>
            {% if user_language == 'en' %}Add New Customer{% else %}إضافة عميل جديد{% endif %}
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" class="form-control" name="search" value="{{ search }}" 
                               placeholder="البحث بالاسم أو البريد الإلكتروني أو رقم الهاتف...">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search me-1"></i>
                            بحث
                        </button>
                        <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">العملاء ({{ customers.paginator.count }} عميل)</h5>
        </div>
        <div class="card-body p-0">
            {% if customers %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>اسم العميل</th>
                            <th>البريد الإلكتروني</th>
                            <th>رقم الهاتف</th>
                            <th>حد الائتمان</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in customers %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                        {{ customer.name.0|upper }}
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ customer.name }}</h6>
                                        {% if customer.tax_number %}
                                        <small class="text-muted">ض.ر: {{ customer.tax_number }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if customer.email %}
                                    <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                        {{ customer.email }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.phone %}
                                    <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                        {{ customer.phone }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="fw-bold">{{ customer.credit_limit|floatformat:2 }} ريال</span>
                            </td>
                            <td>
                                {% if customer.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">{{ customer.created_at|date:"Y/m/d" }}</small>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                        الإجراءات
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{% url 'sales:customer_edit' customer.pk %}">
                                                <i class="bi bi-pencil me-2"></i>تعديل
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="{% url 'sales:customer_delete' customer.pk %}">
                                                <i class="bi bi-trash me-2"></i>حذف
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if customers.has_other_pages %}
            <div class="card-footer">
                <nav aria-label="صفحات العملاء">
                    <ul class="pagination justify-content-center mb-0">
                        {% if customers.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ customers.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}

                        {% for num in customers.paginator.page_range %}
                            {% if customers.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > customers.number|add:'-3' and num < customers.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}">
                                        {{ num }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if customers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ customers.next_page_number }}{% if search %}&search={{ search }}{% endif %}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}

            {% else %}
            <!-- Empty State -->
            <div class="text-center py-5">
                <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">لا توجد عملاء</h4>
                {% if search %}
                    <p class="text-muted">لم يتم العثور على عملاء تطابق البحث "{{ search }}"</p>
                    <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-primary">
                        عرض جميع العملاء
                    </a>
                {% else %}
                    <p class="text-muted">ابدأ بإضافة عميل جديد لإدارة قاعدة عملائك</p>
                    <a href="{% url 'sales:customer_create' %}" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>
                        إضافة عميل جديد
                    </a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 0.875rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.dropdown-toggle::after {
    margin-right: 0.5rem;
}
</style>
{% endblock %}
