<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبات - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .search-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .order-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #4facfe;
        }
        
        .order-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .order-number {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
        }
        
        .order-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-draft { background: #e2e3e5; color: #495057; }
        .status-confirmed { background: #d1ecf1; color: #0c5460; }
        .status-processing { background: #fff3cd; color: #856404; }
        .status-shipped { background: #cce5ff; color: #004085; }
        .status-delivered { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .order-details {
            margin-bottom: 15px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .detail-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .detail-value {
            font-weight: 600;
            color: #333;
        }
        
        .total-amount {
            font-size: 1.3rem;
            font-weight: 700;
            color: #4facfe;
            text-align: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        .btn-create {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(79, 172, 254, 0.3);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .stats-row {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .priority-badge {
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-right: 5px;
        }
        
        .priority-high { background: #f8d7da; color: #721c24; }
        .priority-medium { background: #fff3cd; color: #856404; }
        .priority-low { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-cart"></i>
                        إدارة الطلبات
                    </h1>
                    <p class="mb-0">إدارة طلبات البيع ومتابعة التنفيذ</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'sales:order_create' %}" class="btn btn-create">
                        <i class="bi bi-plus-circle"></i>
                        إنشاء طلب جديد
                    </a>
                    <a href="{% url 'sales:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-row">
            <div class="row">
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-primary">{{ total_orders }}</div>
                        <div class="stat-label">إجمالي الطلبات</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-warning">{{ draft_orders }}</div>
                        <div class="stat-label">مسودات</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-info">{{ confirmed_orders }}</div>
                        <div class="stat-label">مؤكدة</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-primary">{{ processing_orders }}</div>
                        <div class="stat-label">قيد التنفيذ</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-success">{{ delivered_orders }}</div>
                        <div class="stat-label">مسلمة</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-item">
                        <div class="stat-number text-success">{{ total_value|floatformat:0 }}</div>
                        <div class="stat-label">إجمالي القيمة (ج.م)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم البحث -->
        <div class="search-section">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="رقم الطلب أو اسم العميل" value="{{ search }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="draft" {% if status == 'draft' %}selected{% endif %}>مسودة</option>
                        <option value="confirmed" {% if status == 'confirmed' %}selected{% endif %}>مؤكدة</option>
                        <option value="processing" {% if status == 'processing' %}selected{% endif %}>قيد التنفيذ</option>
                        <option value="shipped" {% if status == 'shipped' %}selected{% endif %}>تم الشحن</option>
                        <option value="delivered" {% if status == 'delivered' %}selected{% endif %}>مسلمة</option>
                        <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغية</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الأولوية</label>
                    <select name="priority" class="form-select">
                        <option value="">جميع الأولويات</option>
                        <option value="high" {% if priority == 'high' %}selected{% endif %}>عالية</option>
                        <option value="medium" {% if priority == 'medium' %}selected{% endif %}>متوسطة</option>
                        <option value="low" {% if priority == 'low' %}selected{% endif %}>منخفضة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </form>
        </div>

        <!-- قائمة الطلبات -->
        {% if orders %}
            <div class="row">
                {% for order in orders %}
                    <div class="col-lg-4 col-md-6">
                        <div class="order-card">
                            <div class="order-header">
                                <div class="order-number">
                                    طلب #{{ order.order_number }}
                                    {% if order.priority %}
                                        <span class="priority-badge priority-{{ order.priority }}">
                                            {% if order.priority == 'high' %}
                                                عالية
                                            {% elif order.priority == 'medium' %}
                                                متوسطة
                                            {% elif order.priority == 'low' %}
                                                منخفضة
                                            {% endif %}
                                        </span>
                                    {% endif %}
                                </div>
                                <div class="order-status status-{{ order.status }}">
                                    {% if order.status == 'draft' %}
                                        مسودة
                                    {% elif order.status == 'confirmed' %}
                                        مؤكدة
                                    {% elif order.status == 'processing' %}
                                        قيد التنفيذ
                                    {% elif order.status == 'shipped' %}
                                        تم الشحن
                                    {% elif order.status == 'delivered' %}
                                        مسلمة
                                    {% elif order.status == 'cancelled' %}
                                        ملغية
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="order-details">
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-person text-primary"></i>
                                        العميل:
                                    </span>
                                    <span class="detail-value">{{ order.customer.name }}</span>
                                </div>
                                
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-calendar text-info"></i>
                                        تاريخ الطلب:
                                    </span>
                                    <span class="detail-value">{{ order.order_date|date:"Y/m/d" }}</span>
                                </div>
                                
                                {% if order.delivery_date %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-truck text-warning"></i>
                                            تاريخ التسليم:
                                        </span>
                                        <span class="detail-value">{{ order.delivery_date|date:"Y/m/d" }}</span>
                                    </div>
                                {% endif %}
                                
                                {% if order.representative %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-person-badge text-success"></i>
                                            المندوب:
                                        </span>
                                        <span class="detail-value">{{ order.representative.name }}</span>
                                    </div>
                                {% endif %}
                                
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-box text-secondary"></i>
                                        عدد الأصناف:
                                    </span>
                                    <span class="detail-value">{{ order.items.count }}</span>
                                </div>
                            </div>
                            
                            <div class="total-amount">
                                الإجمالي: {{ order.total_amount|floatformat:2 }} ج.م
                            </div>
                            
                            <div class="text-center">
                                <a href="{% url 'sales:order_detail' order.pk %}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    عرض
                                </a>
                                {% if order.status == 'draft' %}
                                    <a href="{% url 'sales:order_edit' order.pk %}" class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-pencil"></i>
                                        تعديل
                                    </a>
                                {% endif %}
                                {% if order.status == 'confirmed' %}
                                    <a href="{% url 'sales:order_convert_to_invoice' order.pk %}" class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-receipt"></i>
                                        تحويل لفاتورة
                                    </a>
                                {% endif %}
                                <a href="{% url 'sales:order_print' order.pk %}" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-printer"></i>
                                    طباعة
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- الترقيم -->
            {% if orders.has_other_pages %}
                <nav aria-label="ترقيم الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if orders.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ orders.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if priority %}&priority={{ priority }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}

                        {% for num in orders.paginator.page_range %}
                            {% if orders.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > orders.number|add:'-3' and num < orders.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if priority %}&priority={{ priority }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                        {{ num }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if orders.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ orders.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if priority %}&priority={{ priority }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <i class="bi bi-cart"></i>
                <h3>لا توجد طلبات</h3>
                <p>لم يتم العثور على طلبات تطابق معايير البحث</p>
                <a href="{% url 'sales:order_create' %}" class="btn btn-create">
                    <i class="bi bi-plus-circle"></i>
                    إنشاء أول طلب
                </a>
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.order-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 50);
            });
        });
    </script>
</body>
</html>
