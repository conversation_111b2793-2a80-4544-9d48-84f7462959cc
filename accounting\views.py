from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.http import JsonResponse
from django.core.paginator import Paginator
from datetime import datetime, timedelta
from .models import AccountType, Account, JournalEntry, JournalEntryLine
from .forms import (AccountTypeForm, AccountForm, JournalEntryForm, JournalEntryLineFormSet,
                   AccountSearchForm, JournalEntrySearchForm)

@login_required
def accounting_dashboard(request):
    """لوحة تحكم الحسابات العامة"""
    # إحصائيات عامة
    total_account_types = AccountType.objects.filter(is_active=True).count()
    total_accounts = Account.objects.filter(is_active=True).count()
    main_accounts = Account.objects.filter(is_active=True, is_main_account=True).count()
    sub_accounts = Account.objects.filter(is_active=True, is_main_account=False).count()

    # إحصائيات القيود
    total_entries = JournalEntry.objects.count()
    posted_entries = JournalEntry.objects.filter(status='posted').count()
    draft_entries = JournalEntry.objects.filter(status='draft').count()

    # القيود هذا الشهر
    current_month = datetime.now().month
    current_year = datetime.now().year
    monthly_entries = JournalEntry.objects.filter(
        entry_date__month=current_month,
        entry_date__year=current_year
    ).count()

    # إجماليات مالية
    total_debits = JournalEntry.objects.filter(status='posted').aggregate(
        total=Sum('total_debit'))['total'] or 0
    total_credits = JournalEntry.objects.filter(status='posted').aggregate(
        total=Sum('total_credit'))['total'] or 0

    # الحسابات حسب النوع
    accounts_by_type = Account.objects.values('account_type__name').annotate(count=Count('id'))

    # أحدث القيود
    recent_entries = JournalEntry.objects.select_related('created_by').order_by('-created_at')[:10]

    context = {
        'total_account_types': total_account_types,
        'total_accounts': total_accounts,
        'main_accounts': main_accounts,
        'sub_accounts': sub_accounts,
        'total_entries': total_entries,
        'posted_entries': posted_entries,
        'draft_entries': draft_entries,
        'monthly_entries': monthly_entries,
        'total_debits': total_debits,
        'total_credits': total_credits,
        'accounts_by_type': accounts_by_type,
        'recent_entries': recent_entries,
    }
    return render(request, 'accounting/dashboard.html', context)

# ========== إدارة أنواع الحسابات ==========
@login_required
def account_type_list(request):
    """قائمة أنواع الحسابات"""
    search = request.GET.get('search', '')
    account_types = AccountType.objects.all()

    if search:
        account_types = account_types.filter(Q(name__icontains=search))

    paginator = Paginator(account_types, 20)
    page_number = request.GET.get('page')
    account_types = paginator.get_page(page_number)

    context = {
        'account_types': account_types,
        'search': search,
    }
    return render(request, 'accounting/account_type_list.html', context)

@login_required
def account_type_create(request):
    """إضافة نوع حساب جديد"""
    if request.method == 'POST':
        form = AccountTypeForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة نوع الحساب بنجاح')
            return redirect('accounting:account_type_list')
    else:
        form = AccountTypeForm()

    context = {'form': form, 'title': 'إضافة نوع حساب جديد'}
    return render(request, 'accounting/account_type_form.html', context)

@login_required
def account_type_edit(request, pk):
    """تعديل نوع حساب"""
    account_type = get_object_or_404(AccountType, pk=pk)
    if request.method == 'POST':
        form = AccountTypeForm(request.POST, instance=account_type)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث نوع الحساب بنجاح')
            return redirect('accounting:account_type_list')
    else:
        form = AccountTypeForm(instance=account_type)

    context = {'form': form, 'title': 'تعديل نوع الحساب', 'account_type': account_type}
    return render(request, 'accounting/account_type_form.html', context)

@login_required
def account_type_delete(request, pk):
    """حذف نوع حساب"""
    account_type = get_object_or_404(AccountType, pk=pk)
    if request.method == 'POST':
        account_type.delete()
        messages.success(request, 'تم حذف نوع الحساب بنجاح')
        return redirect('accounting:account_type_list')

    context = {'account_type': account_type}
    return render(request, 'accounting/account_type_confirm_delete.html', context)
