from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from django.db import transaction
from decimal import Decimal
from datetime import date, timedelta

from sales.models import (
    Customer, Product, SalesRepresentative, SalesInvoice, SalesInvoiceItem,
    ProductMovement, Payment, SalesReturn, SalesReturnItem, VehicleLoading,
    VehicleLoadingItem, DailyMovement, Inventory, InventoryItem
)


class SalesWorkflowIntegrationTest(TestCase):
    """اختبارات تكامل سير عمل المبيعات"""
    
    def setUp(self):
        """إعداد البيانات الأساسية للاختبارات"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.customer = Customer.objects.create(
            name='عميل تجريبي',
            customer_type='retail',
            credit_limit=Decimal('10000.00')
        )
        
        self.product = Product.objects.create(
            name='منتج تجريبي',
            code='PROD001',
            unit_price_retail=Decimal('100.00'),
            cost_price=Decimal('60.00'),
            stock_quantity=Decimal('50.00')
        )
        
        self.rep_user = User.objects.create_user(
            username='rep1',
            first_name='أحمد',
            last_name='محمد'
        )
        
        self.representative = SalesRepresentative.objects.create(
            user=self.rep_user,
            employee_id='EMP001',
            commission_rate=Decimal('2.5')
        )
    
    def test_complete_sales_process(self):
        """اختبار عملية البيع الكاملة"""
        # 1. إنشاء فاتورة
        invoice = SalesInvoice.objects.create(
            customer=self.customer,
            representative=self.representative,
            invoice_date=date.today(),
            invoice_type='retail',
            payment_method='cash',
            status='draft',
            created_by=self.user
        )
        
        # 2. إضافة عناصر للفاتورة
        item = SalesInvoiceItem.objects.create(
            invoice=invoice,
            product=self.product,
            quantity=Decimal('5.00'),
            unit_price=Decimal('100.00'),
            discount_percentage=Decimal('10.00')
        )
        
        # 3. حساب الإجماليات
        expected_item_total = Decimal('5.00') * Decimal('100.00') * (1 - Decimal('10.00') / 100)
        self.assertEqual(item.total_price, expected_item_total)
        
        # 4. تحديث إجمالي الفاتورة
        invoice.calculate_totals()
        self.assertEqual(invoice.total_amount, expected_item_total)
        
        # 5. اعتماد الفاتورة
        original_stock = self.product.stock_quantity
        invoice.status = 'paid'
        invoice.save()
        
        # 6. التحقق من تحديث المخزون (يجب أن يتم في signal أو method)
        # self.product.refresh_from_db()
        # expected_stock = original_stock - Decimal('5.00')
        # self.assertEqual(self.product.stock_quantity, expected_stock)
        
        # 7. التحقق من إنشاء حركة المنتج
        movement = ProductMovement.objects.filter(
            product=self.product,
            reference_number=invoice.invoice_number
        ).first()
        
        if movement:
            self.assertEqual(movement.movement_type, 'sale')
            self.assertEqual(movement.quantity, Decimal('5.00'))
    
    def test_sales_return_process(self):
        """اختبار عملية المرتجع"""
        # 1. إنشاء فاتورة أصلية
        invoice = SalesInvoice.objects.create(
            customer=self.customer,
            representative=self.representative,
            invoice_date=date.today(),
            invoice_type='retail',
            payment_method='cash',
            status='paid',
            created_by=self.user
        )
        
        SalesInvoiceItem.objects.create(
            invoice=invoice,
            product=self.product,
            quantity=Decimal('10.00'),
            unit_price=Decimal('100.00')
        )
        
        # 2. إنشاء مرتجع
        return_obj = SalesReturn.objects.create(
            original_invoice=invoice,
            customer=self.customer,
            representative=self.representative,
            return_date=date.today(),
            reason='defective',
            created_by=self.user
        )
        
        # 3. إضافة عناصر المرتجع
        return_item = SalesReturnItem.objects.create(
            sales_return=return_obj,
            product=self.product,
            quantity=Decimal('2.00'),
            unit_price=Decimal('100.00'),
            condition='damaged'
        )
        
        # 4. اعتماد المرتجع
        original_stock = self.product.stock_quantity
        return_obj.is_approved = True
        return_obj.approved_by = self.user
        return_obj.save()
        
        # 5. التحقق من تحديث المخزون (للمنتجات الصالحة)
        # self.product.refresh_from_db()
        # if return_item.condition == 'good':
        #     expected_stock = original_stock + Decimal('2.00')
        #     self.assertEqual(self.product.stock_quantity, expected_stock)
    
    def test_payment_collection_process(self):
        """اختبار عملية التحصيل"""
        # 1. إنشاء فاتورة آجلة
        invoice = SalesInvoice.objects.create(
            customer=self.customer,
            representative=self.representative,
            invoice_date=date.today(),
            invoice_type='credit',
            payment_method='credit',
            status='sent',
            total_amount=Decimal('1000.00'),
            created_by=self.user
        )
        
        # 2. تحديث رصيد العميل
        original_balance = self.customer.current_balance
        self.customer.current_balance += invoice.total_amount
        self.customer.save()
        
        # 3. إنشاء دفعة تحصيل
        payment = Payment.objects.create(
            customer=self.customer,
            representative=self.representative,
            payment_date=date.today(),
            amount=Decimal('500.00'),
            payment_type='collection',
            payment_method='cash',
            created_by=self.user
        )
        
        # 4. تحديث رصيد العميل
        self.customer.current_balance -= payment.amount
        self.customer.save()
        
        # 5. التحقق من الرصيد الجديد
        expected_balance = original_balance + invoice.total_amount - payment.amount
        self.assertEqual(self.customer.current_balance, expected_balance)
    
    def test_inventory_adjustment_process(self):
        """اختبار عملية تسوية المخزون"""
        # 1. إنشاء جرد
        inventory = Inventory.objects.create(
            inventory_type='daily',
            inventory_date=date.today(),
            warehouse='المخزن الرئيسي',
            representative=self.representative,
            created_by=self.user
        )
        
        # 2. إضافة منتج للجرد
        original_stock = self.product.stock_quantity
        actual_quantity = original_stock - Decimal('5.00')  # نقص في المخزون
        
        inventory_item = InventoryItem.objects.create(
            inventory=inventory,
            product=self.product,
            system_quantity=original_stock,
            actual_quantity=actual_quantity,
            unit_cost=self.product.cost_price
        )
        
        # 3. التحقق من حساب الفرق
        expected_variance = actual_quantity - original_stock
        self.assertEqual(inventory_item.variance_quantity, expected_variance)
        
        # 4. اعتماد الجرد
        inventory.is_approved = True
        inventory.approved_by = self.user
        inventory.save()
        
        # 5. التحقق من تحديث المخزون
        # self.product.refresh_from_db()
        # self.assertEqual(self.product.stock_quantity, actual_quantity)


class ConcurrencyTest(TransactionTestCase):
    """اختبارات التزامن والمعاملات"""
    
    def setUp(self):
        self.user = User.objects.create_user(username='testuser')
        self.product = Product.objects.create(
            name='منتج تجريبي',
            code='PROD001',
            stock_quantity=Decimal('100.00')
        )
    
    def test_concurrent_stock_updates(self):
        """اختبار تحديث المخزون المتزامن"""
        original_stock = self.product.stock_quantity
        
        # محاكاة تحديثين متزامنين للمخزون
        with transaction.atomic():
            product1 = Product.objects.select_for_update().get(pk=self.product.pk)
            product1.stock_quantity -= Decimal('10.00')
            product1.save()
        
        with transaction.atomic():
            product2 = Product.objects.select_for_update().get(pk=self.product.pk)
            product2.stock_quantity -= Decimal('5.00')
            product2.save()
        
        # التحقق من النتيجة النهائية
        self.product.refresh_from_db()
        expected_stock = original_stock - Decimal('15.00')
        self.assertEqual(self.product.stock_quantity, expected_stock)


class PerformanceTest(TestCase):
    """اختبارات الأداء"""
    
    def setUp(self):
        self.user = User.objects.create_user(username='testuser')
        
        # إنشاء بيانات كبيرة للاختبار
        self.customers = []
        for i in range(100):
            customer = Customer.objects.create(
                name=f'عميل {i}',
                customer_type='retail'
            )
            self.customers.append(customer)
        
        self.products = []
        for i in range(50):
            product = Product.objects.create(
                name=f'منتج {i}',
                code=f'PROD{i:03d}',
                unit_price_retail=Decimal('100.00'),
                stock_quantity=Decimal('1000.00')
            )
            self.products.append(product)
    
    def test_bulk_invoice_creation(self):
        """اختبار إنشاء فواتير بالجملة"""
        invoices = []
        
        for i in range(10):
            invoice = SalesInvoice(
                customer=self.customers[i],
                invoice_date=date.today(),
                invoice_type='retail',
                payment_method='cash',
                status='draft',
                created_by=self.user
            )
            invoices.append(invoice)
        
        # إنشاء الفواتير بالجملة
        created_invoices = SalesInvoice.objects.bulk_create(invoices)
        self.assertEqual(len(created_invoices), 10)
    
    def test_large_query_performance(self):
        """اختبار أداء الاستعلامات الكبيرة"""
        # إنشاء فواتير متعددة
        invoices = []
        for i in range(20):
            invoice = SalesInvoice.objects.create(
                customer=self.customers[i],
                invoice_date=date.today(),
                invoice_type='retail',
                payment_method='cash',
                status='paid',
                created_by=self.user
            )
            invoices.append(invoice)
        
        # اختبار استعلام مع select_related
        invoices_with_customer = SalesInvoice.objects.select_related('customer').all()
        
        # التحقق من أن الاستعلام يعمل بكفاءة
        self.assertEqual(len(invoices_with_customer), 20)
        
        # اختبار عدد الاستعلامات
        with self.assertNumQueries(1):
            list(SalesInvoice.objects.select_related('customer').all())


class DataIntegrityTest(TestCase):
    """اختبارات سلامة البيانات"""
    
    def setUp(self):
        self.user = User.objects.create_user(username='testuser')
        self.customer = Customer.objects.create(
            name='عميل تجريبي',
            customer_type='retail'
        )
        self.product = Product.objects.create(
            name='منتج تجريبي',
            code='PROD001',
            unit_price_retail=Decimal('100.00'),
            stock_quantity=Decimal('50.00')
        )
    
    def test_cascade_deletion(self):
        """اختبار الحذف المتسلسل"""
        # إنشاء فاتورة مع عناصر
        invoice = SalesInvoice.objects.create(
            customer=self.customer,
            invoice_date=date.today(),
            invoice_type='retail',
            payment_method='cash',
            status='draft',
            created_by=self.user
        )
        
        item = SalesInvoiceItem.objects.create(
            invoice=invoice,
            product=self.product,
            quantity=Decimal('5.00'),
            unit_price=Decimal('100.00')
        )
        
        # حذف الفاتورة
        invoice_id = invoice.id
        item_id = item.id
        invoice.delete()
        
        # التحقق من حذف العناصر المرتبطة
        self.assertFalse(SalesInvoice.objects.filter(id=invoice_id).exists())
        self.assertFalse(SalesInvoiceItem.objects.filter(id=item_id).exists())
    
    def test_foreign_key_constraints(self):
        """اختبار قيود المفاتيح الخارجية"""
        invoice = SalesInvoice.objects.create(
            customer=self.customer,
            invoice_date=date.today(),
            invoice_type='retail',
            payment_method='cash',
            status='draft',
            created_by=self.user
        )
        
        # محاولة حذف عميل له فواتير
        with self.assertRaises(Exception):
            self.customer.delete()
    
    def test_unique_constraints(self):
        """اختبار قيود التفرد"""
        # محاولة إنشاء منتج بنفس الكود
        with self.assertRaises(Exception):
            Product.objects.create(
                name='منتج آخر',
                code='PROD001',  # نفس الكود
                unit_price_retail=Decimal('150.00')
            )
