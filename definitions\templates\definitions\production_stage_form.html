{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
    }
    .form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border-radius: 25px;
        padding: 3rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(30px);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    .section-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin: 2rem 0 1rem 0;
        border-left: 4px solid #007bff;
    }
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
    }
    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    .btn {
        border-radius: 10px;
        padding: 0.75rem 2rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-list-ol me-3"></i>
                    {{ page_title }}
                </h1>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:production_stage_list' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <div class="form-card">
        <form method="post" novalidate>
            {% csrf_token %}
            
            <!-- المعلومات الأساسية -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-info-circle me-2"></i>المعلومات الأساسية</h4>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.code.id_for_label }}" class="form-label">{{ form.code.label }} <span class="text-danger">*</span></label>
                        {{ form.code }}
                        {% if form.code.errors %}
                            <div class="text-danger small">{{ form.code.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }} <span class="text-danger">*</span></label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger small">{{ form.name.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.name_en.id_for_label }}" class="form-label">{{ form.name_en.label }}</label>
                        {{ form.name_en }}
                        {% if form.name_en.errors %}
                            <div class="text-danger small">{{ form.name_en.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.stage_type.id_for_label }}" class="form-label">{{ form.stage_type.label }} <span class="text-danger">*</span></label>
                        {{ form.stage_type }}
                        {% if form.stage_type.errors %}
                            <div class="text-danger small">{{ form.stage_type.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.sequence_number.id_for_label }}" class="form-label">{{ form.sequence_number.label }} <span class="text-danger">*</span></label>
                        {{ form.sequence_number }}
                        {% if form.sequence_number.errors %}
                            <div class="text-danger small">{{ form.sequence_number.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                        {{ form.status }}
                        {% if form.status.errors %}
                            <div class="text-danger small">{{ form.status.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                {{ form.description }}
                {% if form.description.errors %}
                    <div class="text-danger small">{{ form.description.errors.0 }}</div>
                {% endif %}
            </div>

            <!-- الوقت والتكلفة -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-clock me-2"></i>الوقت والتكلفة</h4>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="{{ form.standard_duration_hours.id_for_label }}" class="form-label">{{ form.standard_duration_hours.label }} <span class="text-danger">*</span></label>
                        {{ form.standard_duration_hours }}
                        {% if form.standard_duration_hours.errors %}
                            <div class="text-danger small">{{ form.standard_duration_hours.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="{{ form.setup_time_hours.id_for_label }}" class="form-label">{{ form.setup_time_hours.label }}</label>
                        {{ form.setup_time_hours }}
                        {% if form.setup_time_hours.errors %}
                            <div class="text-danger small">{{ form.setup_time_hours.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="{{ form.labor_cost_per_hour.id_for_label }}" class="form-label">{{ form.labor_cost_per_hour.label }}</label>
                        {{ form.labor_cost_per_hour }}
                        {% if form.labor_cost_per_hour.errors %}
                            <div class="text-danger small">{{ form.labor_cost_per_hour.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="{{ form.overhead_cost_per_hour.id_for_label }}" class="form-label">{{ form.overhead_cost_per_hour.label }}</label>
                        {{ form.overhead_cost_per_hour }}
                        {% if form.overhead_cost_per_hour.errors %}
                            <div class="text-danger small">{{ form.overhead_cost_per_hour.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- المتطلبات -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-tools me-2"></i>المتطلبات</h4>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.required_skills.id_for_label }}" class="form-label">{{ form.required_skills.label }}</label>
                        {{ form.required_skills }}
                        {% if form.required_skills.errors %}
                            <div class="text-danger small">{{ form.required_skills.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.required_equipment.id_for_label }}" class="form-label">{{ form.required_equipment.label }}</label>
                        {{ form.required_equipment }}
                        {% if form.required_equipment.errors %}
                            <div class="text-danger small">{{ form.required_equipment.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.safety_requirements.id_for_label }}" class="form-label">{{ form.safety_requirements.label }}</label>
                        {{ form.safety_requirements }}
                        {% if form.safety_requirements.errors %}
                            <div class="text-danger small">{{ form.safety_requirements.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- معايير الجودة -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-award me-2"></i>معايير الجودة</h4>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.quality_checkpoints.id_for_label }}" class="form-label">{{ form.quality_checkpoints.label }}</label>
                        {{ form.quality_checkpoints }}
                        {% if form.quality_checkpoints.errors %}
                            <div class="text-danger small">{{ form.quality_checkpoints.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.acceptance_criteria.id_for_label }}" class="form-label">{{ form.acceptance_criteria.label }}</label>
                        {{ form.acceptance_criteria }}
                        {% if form.acceptance_criteria.errors %}
                            <div class="text-danger small">{{ form.acceptance_criteria.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="{{ form.rejection_criteria.id_for_label }}" class="form-label">{{ form.rejection_criteria.label }}</label>
                        {{ form.rejection_criteria }}
                        {% if form.rejection_criteria.errors %}
                            <div class="text-danger small">{{ form.rejection_criteria.errors.0 }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- التسلسل والعلاقات -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-diagram-3 me-2"></i>التسلسل والعلاقات</h4>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="{{ form.previous_stage.id_for_label }}" class="form-label">{{ form.previous_stage.label }}</label>
                        {{ form.previous_stage }}
                        {% if form.previous_stage.errors %}
                            <div class="text-danger small">{{ form.previous_stage.errors.0 }}</div>
                        {% endif %}
                        <small class="form-text text-muted">اختر المرحلة التي تسبق هذه المرحلة</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check mt-4">
                            {{ form.can_run_parallel }}
                            <label class="form-check-label" for="{{ form.can_run_parallel.id_for_label }}">
                                {{ form.can_run_parallel.label }}
                            </label>
                        </div>
                        <small class="form-text text-muted">يمكن تنفيذ هذه المرحلة بالتوازي مع مراحل أخرى</small>
                    </div>
                </div>
            </div>

            <!-- الخصائص -->
            <div class="section-header">
                <h4 class="mb-0"><i class="bi bi-toggles me-2"></i>الخصائص</h4>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_critical }}
                            <label class="form-check-label" for="{{ form.is_critical.id_for_label }}">
                                {{ form.is_critical.label }}
                            </label>
                        </div>
                        <small class="form-text text-muted">مرحلة مهمة جداً ولا يمكن تجاوزها</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_optional }}
                            <label class="form-check-label" for="{{ form.is_optional.id_for_label }}">
                                {{ form.is_optional.label }}
                            </label>
                        </div>
                        <small class="form-text text-muted">يمكن تخطي هذه المرحلة حسب الحاجة</small>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="row mt-4">
                <div class="col-12 text-center">
                    <button type="submit" class="btn btn-primary btn-lg me-3">
                        <i class="bi bi-check-circle me-2"></i>
                        {% if action == 'create' %}إنشاء المرحلة{% else %}حفظ التغييرات{% endif %}
                    </button>
                    <a href="{% url 'definitions:production_stage_list' %}" class="btn btn-secondary btn-lg">
                        <i class="bi bi-x-circle me-2"></i>إلغاء
                    </a>
                </div>
            </div>

            {% if form.non_field_errors %}
                <div class="alert alert-danger mt-3">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}
        </form>
    </div>
</div>
{% endblock %}
