{% extends 'base.html' %}

{% block title %}{{ product.name }} - تفاصيل المنتج - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{% url 'products:product_list' %}">المنتجات</a></li>
                <li class="breadcrumb-item active">{{ product.name }}</li>
            </ol>
        </nav>
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="page-title">{{ product.name }}</h1>
                <p class="page-subtitle">تفاصيل المنتج ومعلومات المخزون</p>
            </div>
            <div class="btn-group">
                <a href="{% url 'products:product_edit' product.pk %}" class="btn btn-primary">
                    <i class="bi bi-pencil me-2"></i>تعديل المنتج
                </a>
                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                    <i class="bi bi-trash me-2"></i>حذف المنتج
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Product Information -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="product-icon-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                        <i class="bi bi-box"></i>
                    </div>
                    <h4 class="mb-1">{{ product.name }}</h4>
                    <p class="text-muted mb-3">SKU: {{ product.sku }}</p>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="p-2">
                                <h5 class="mb-1 {% if product.quantity <= product.min_quantity %}text-danger{% endif %}">
                                    {{ product.quantity }}
                                </h5>
                                <p class="text-muted small mb-0">الكمية</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-2">
                                <h5 class="mb-1 text-success">{{ product.price }} ر.س</h5>
                                <p class="text-muted small mb-0">السعر</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-2">
                                <h5 class="mb-1 text-info">{{ product.quantity|mul:product.price|floatformat:2 }} ر.س</h5>
                                <p class="text-muted small mb-0">القيمة</p>
                            </div>
                        </div>
                    </div>
                    
                    {% if product.quantity <= product.min_quantity %}
                        <div class="alert alert-warning mt-3">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>تنبيه مخزون!</strong><br>
                            الكمية الحالية ({{ product.quantity }}) أقل من أو تساوي الحد الأدنى ({{ product.min_quantity }})
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Product Details -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>تفاصيل المنتج
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted small">اسم المنتج</label>
                        <div class="fw-bold">{{ product.name }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted small">رمز المنتج (SKU)</label>
                        <div class="fw-bold">{{ product.sku }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted small">الكمية المتوفرة</label>
                        <div class="fw-bold {% if product.quantity <= product.min_quantity %}text-danger{% endif %}">
                            {{ product.quantity }} وحدة
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted small">الحد الأدنى للكمية</label>
                        <div class="fw-bold">{{ product.min_quantity }} وحدة</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted small">سعر الوحدة</label>
                        <div class="fw-bold text-success">{{ product.price }} ر.س</div>
                    </div>
                    
                    <div>
                        <label class="form-label text-muted small">تاريخ الإضافة</label>
                        <div class="fw-bold">{{ product.created_at|date:"Y/m/d H:i" }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Activity -->
        <div class="col-lg-8">
            <!-- Inventory Status -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-graph-up me-2"></i>حالة المخزون
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="border rounded p-3 text-center">
                                <i class="bi bi-boxes text-primary" style="font-size: 2rem;"></i>
                                <h4 class="mt-2 mb-1">{{ product.quantity }}</h4>
                                <p class="text-muted mb-0">الكمية الحالية</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="border rounded p-3 text-center">
                                <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                                <h4 class="mt-2 mb-1">{{ product.min_quantity }}</h4>
                                <p class="text-muted mb-0">الحد الأدنى</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>مستوى المخزون</span>
                            <span class="badge {% if product.quantity <= product.min_quantity %}bg-danger{% elif product.quantity <= product.min_quantity|mul:2 %}bg-warning{% else %}bg-success{% endif %}">
                                {% if product.quantity <= product.min_quantity %}
                                    مخزون قليل
                                {% elif product.quantity <= product.min_quantity|mul:2 %}
                                    مخزون متوسط
                                {% else %}
                                    مخزون جيد
                                {% endif %}
                            </span>
                        </div>
                        <div class="progress">
                            {% widthratio product.quantity product.min_quantity|mul:3 100 as progress_width %}
                            <div class="progress-bar {% if product.quantity <= product.min_quantity %}bg-danger{% elif product.quantity <= product.min_quantity|mul:2 %}bg-warning{% else %}bg-success{% endif %}" 
                                 style="width: {% if progress_width > 100 %}100{% else %}{{ progress_width }}{% endif %}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales History -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-receipt me-2"></i>سجل المبيعات
                    </h5>
                    <a href="#" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-plus me-1"></i>إضافة مبيعة
                    </a>
                </div>
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="bi bi-receipt text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">لا توجد مبيعات</h5>
                        <p class="text-muted">لم يتم تسجيل أي مبيعات لهذا المنتج بعد.</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning me-2"></i>إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <button class="btn btn-outline-success w-100" data-bs-toggle="modal" data-bs-target="#addStockModal">
                                <i class="bi bi-plus-circle me-2"></i>إضافة للمخزون
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button class="btn btn-outline-warning w-100" data-bs-toggle="modal" data-bs-target="#adjustStockModal">
                                <i class="bi bi-pencil-square me-2"></i>تعديل المخزون
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">هل أنت متأكد من حذف المنتج؟</h5>
                        <p class="text-muted">
                            سيتم حذف المنتج "{{ product.name }}" نهائياً من النظام.<br>
                            هذا الإجراء لا يمكن التراجع عنه.
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <a href="{% url 'products:product_delete' product.pk %}" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>حذف المنتج
                    </a>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .product-icon-lg {
        width: 80px;
        height: 80px;
        font-size: 2rem;
        font-weight: 700;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .progress {
        height: 8px;
    }
</style>
{% endblock %}
