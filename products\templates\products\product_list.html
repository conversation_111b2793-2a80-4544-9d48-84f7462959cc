{% extends 'base.html' %}

{% block title %}إدارة المنتجات - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">إدارة المنتجات</h1>
            <p class="page-subtitle">عرض وإدارة جميع المنتجات والمخزون</p>
        </div>
        <a href="{% url 'products:product_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>إضافة منتج جديد
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}" 
                               placeholder="البحث في أسماء المنتجات أو رموز SKU...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="low_stock" class="form-select">
                        <option value="">جميع المنتجات</option>
                        <option value="1" {% if filter_low_stock %}selected{% endif %}>منتجات قليلة المخزون</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search me-1"></i>بحث
                        </button>
                        <a href="{% url 'products:product_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-box-seam text-primary" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-0">{{ total_products }}</h4>
                    <p class="text-muted mb-0">إجمالي المنتجات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-0 text-danger">{{ low_stock_count }}</h4>
                    <p class="text-muted mb-0">منتجات قليلة المخزون</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-currency-dollar text-success" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-0">{{ total_value|floatformat:2 }} ر.س</h4>
                    <p class="text-muted mb-0">قيمة المخزون</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-list-check text-info" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-0">{{ page_obj.paginator.count }}</h4>
                    <p class="text-muted mb-0">نتائج البحث</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">قائمة المنتجات</h5>
            <span class="badge bg-primary">{{ page_obj.paginator.count }} منتج</span>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="row g-4">
                    {% for product in page_obj %}
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 product-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="product-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center">
                                            <i class="bi bi-box"></i>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{% url 'products:product_detail' product.pk %}">
                                                    <i class="bi bi-eye me-2"></i>عرض التفاصيل
                                                </a></li>
                                                <li><a class="dropdown-item" href="{% url 'products:product_edit' product.pk %}">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="{% url 'products:product_delete' product.pk %}">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <h5 class="card-title">{{ product.name }}</h5>
                                    <p class="text-muted small mb-2">SKU: {{ product.sku }}</p>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-6">
                                            <div class="border-end">
                                                <h6 class="mb-0 {% if product.quantity <= product.min_quantity %}text-danger{% endif %}">
                                                    {{ product.quantity }}
                                                </h6>
                                                <small class="text-muted">الكمية</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="mb-0 text-success">{{ product.price }} ر.س</h6>
                                            <small class="text-muted">السعر</small>
                                        </div>
                                    </div>
                                    
                                    {% if product.quantity <= product.min_quantity %}
                                        <div class="alert alert-warning py-2 mb-3">
                                            <i class="bi bi-exclamation-triangle me-1"></i>
                                            <small>مخزون قليل! الحد الأدنى: {{ product.min_quantity }}</small>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="d-flex gap-2">
                                        <a href="{% url 'products:product_detail' product.pk %}" class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="bi bi-eye me-1"></i>عرض
                                        </a>
                                        <a href="{% url 'products:product_edit' product.pk %}" class="btn btn-outline-secondary btn-sm flex-fill">
                                            <i class="bi bi-pencil me-1"></i>تعديل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="صفحات المنتجات" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if filter_low_stock %}&low_stock={{ filter_low_stock }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if filter_low_stock %}&low_stock={{ filter_low_stock }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if filter_low_stock %}&low_stock={{ filter_low_stock }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if filter_low_stock %}&low_stock={{ filter_low_stock }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-box-seam text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد منتجات</h4>
                    <p class="text-muted">لم يتم العثور على أي منتجات مطابقة لمعايير البحث.</p>
                    <a href="{% url 'products:product_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة أول منتج
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .product-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .product-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .border-end {
        border-right: 1px solid #dee2e6 !important;
    }
</style>
{% endblock %}
