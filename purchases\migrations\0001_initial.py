# Generated by Django 5.2.4 on 2025-07-12 22:44

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود المنتج')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الشراء')),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر البيع')),
                ('stock_quantity', models.IntegerField(default=0, verbose_name='الكمية المتاحة')),
                ('min_stock_level', models.IntegerField(default=0, verbose_name='الحد الأدنى للمخزون')),
                ('unit', models.CharField(default='قطعة', max_length=20, verbose_name='الوحدة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المورد')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('payment_terms', models.CharField(blank=True, max_length=100, null=True, verbose_name='شروط الدفع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الفاتورة')),
                ('invoice_date', models.DateField(verbose_name='تاريخ الفاتورة')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('received', 'مستلمة'), ('paid', 'مدفوعة'), ('overdue', 'متأخرة'), ('cancelled', 'ملغية')], default='draft', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=15, max_digits=5, verbose_name='نسبة الضريبة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'فاتورة شراء',
                'verbose_name_plural': 'فواتير الشراء',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseInvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchaseinvoice', verbose_name='فاتورة الشراء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='purchases.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر فاتورة شراء',
                'verbose_name_plural': 'عناصر فواتير الشراء',
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الطلب')),
                ('order_date', models.DateField(verbose_name='تاريخ الطلب')),
                ('expected_delivery_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسل'), ('confirmed', 'مؤكد'), ('received', 'تم الاستلام'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=15, max_digits=5, verbose_name='نسبة الضريبة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='purchases.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'أمر شراء',
                'verbose_name_plural': 'أوامر الشراء',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='purchaseinvoice',
            name='order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='purchases.purchaseorder', verbose_name='أمر الشراء'),
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='نسبة الخصم')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchases.purchaseorder', verbose_name='أمر الشراء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='purchases.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر أمر شراء',
                'verbose_name_plural': 'عناصر أوامر الشراء',
            },
        ),
        migrations.AddField(
            model_name='purchaseinvoice',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='purchases.supplier', verbose_name='المورد'),
        ),
    ]
