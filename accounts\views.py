from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from .models import Account, AccountType

@login_required
def accounts_dashboard(request):
    """لوحة تحكم الحسابات"""
    context = {
        'total_accounts': Account.objects.filter(is_active=True).count(),
        'account_types_count': AccountType.objects.filter(is_active=True).count(),
        'debit_accounts': Account.objects.filter(nature='debit', is_active=True).count(),
        'credit_accounts': Account.objects.filter(nature='credit', is_active=True).count(),
    }
    return render(request, 'accounts/dashboard.html', context)
