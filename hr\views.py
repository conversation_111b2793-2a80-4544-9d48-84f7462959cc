from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q, Avg
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import datetime, timedelta, date
from .models import Employee, Department, Position, Attendance, Leave, Payroll
from .forms import (DepartmentForm, PositionForm, EmployeeForm, AttendanceForm,
                   LeaveForm, PayrollForm, EmployeeSearchForm, AttendanceSearchForm)

@login_required
def hr_dashboard(request):
    """لوحة تحكم شؤون العاملين"""
    # إحصائيات عامة
    total_employees = Employee.objects.filter(status='active').count()
    departments_count = Department.objects.filter(is_active=True).count()
    positions_count = Position.objects.filter(is_active=True).count()

    # إحصائيات الحضور اليوم
    today = date.today()
    attendance_today = Attendance.objects.filter(date=today).count()
    present_today = Attendance.objects.filter(date=today, is_absent=False).count()
    absent_today = Attendance.objects.filter(date=today, is_absent=True).count()

    # إحصائيات الإجازات
    pending_leaves = Leave.objects.filter(status='pending').count()
    approved_leaves_this_month = Leave.objects.filter(
        status='approved',
        start_date__month=today.month,
        start_date__year=today.year
    ).count()

    # إحصائيات الرواتب
    current_month_payrolls = Payroll.objects.filter(
        month=today.month,
        year=today.year
    ).count()
    paid_payrolls = Payroll.objects.filter(
        month=today.month,
        year=today.year,
        is_paid=True
    ).count()

    # إحصائيات الموظفين حسب القسم
    employees_by_department = Employee.objects.filter(status='active').values(
        'department__name').annotate(count=Count('id'))

    # إحصائيات الموظفين حسب الحالة
    employees_by_status = Employee.objects.values('status').annotate(count=Count('id'))

    # أحدث الموظفين
    recent_employees = Employee.objects.order_by('-created_at')[:5]

    # الإجازات المعلقة
    recent_leaves = Leave.objects.filter(status='pending').order_by('-created_at')[:5]

    context = {
        'total_employees': total_employees,
        'departments_count': departments_count,
        'positions_count': positions_count,
        'attendance_today': attendance_today,
        'present_today': present_today,
        'absent_today': absent_today,
        'pending_leaves': pending_leaves,
        'approved_leaves_this_month': approved_leaves_this_month,
        'current_month_payrolls': current_month_payrolls,
        'paid_payrolls': paid_payrolls,
        'employees_by_department': employees_by_department,
        'employees_by_status': employees_by_status,
        'recent_employees': recent_employees,
        'recent_leaves': recent_leaves,
    }
    return render(request, 'hr/dashboard.html', context)

# ========== إدارة الأقسام ==========
@login_required
def department_list(request):
    """قائمة الأقسام"""
    search = request.GET.get('search', '')
    departments = Department.objects.select_related('manager')

    if search:
        departments = departments.filter(Q(name__icontains=search) | Q(code__icontains=search))

    paginator = Paginator(departments, 20)
    page_number = request.GET.get('page')
    departments = paginator.get_page(page_number)

    context = {
        'departments': departments,
        'search': search,
    }
    return render(request, 'hr/department_list.html', context)

@login_required
def department_create(request):
    """إضافة قسم جديد"""
    if request.method == 'POST':
        form = DepartmentForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة القسم بنجاح')
            return redirect('hr:department_list')
    else:
        form = DepartmentForm()

    context = {'form': form, 'title': 'إضافة قسم جديد'}
    return render(request, 'hr/department_form.html', context)

@login_required
def department_edit(request, pk):
    """تعديل قسم"""
    department = get_object_or_404(Department, pk=pk)
    if request.method == 'POST':
        form = DepartmentForm(request.POST, instance=department)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات القسم بنجاح')
            return redirect('hr:department_list')
    else:
        form = DepartmentForm(instance=department)

    context = {'form': form, 'title': 'تعديل القسم', 'department': department}
    return render(request, 'hr/department_form.html', context)

@login_required
def department_delete(request, pk):
    """حذف قسم"""
    department = get_object_or_404(Department, pk=pk)
    if request.method == 'POST':
        department.delete()
        messages.success(request, 'تم حذف القسم بنجاح')
        return redirect('hr:department_list')

    context = {'department': department}
    return render(request, 'hr/department_confirm_delete.html', context)
