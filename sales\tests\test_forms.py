from django.test import TestCase
from django.contrib.auth.models import User
from decimal import Decimal
from datetime import date, timedelta

from sales.forms import (
    CustomerForm, ProductForm, SalesRepresentativeForm, VehicleForm,
    SalesOrderForm, SalesInvoiceForm, VehicleLoadingForm, DailyMovementForm,
    SalesReturnForm, DispensePermissionForm, InventoryForm, PaymentForm
)
from sales.models import Customer, Product, SalesRepresentative, Vehicle


class CustomerFormTest(TestCase):
    """اختبارات نموذج العميل"""
    
    def test_valid_customer_form(self):
        """اختبار نموذج عميل صحيح"""
        form_data = {
            'name': 'عميل تجريبي',
            'customer_type': 'retail',
            'phone': '0*********0',
            'email': '<EMAIL>',
            'address': 'عنوان تجريبي',
            'credit_limit': '10000.00',
            'credit_days': '30',
            'tax_number': '*********',
            'is_active': True
        }
        
        form = CustomerForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_invalid_customer_form_missing_name(self):
        """اختبار نموذج عميل بدون اسم"""
        form_data = {
            'customer_type': 'retail',
            'phone': '0*********0',
            'email': '<EMAIL>'
        }
        
        form = CustomerForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
    
    def test_invalid_customer_form_invalid_email(self):
        """اختبار نموذج عميل ببريد إلكتروني غير صحيح"""
        form_data = {
            'name': 'عميل تجريبي',
            'customer_type': 'retail',
            'phone': '0*********0',
            'email': 'invalid-email',
            'address': 'عنوان تجريبي'
        }
        
        form = CustomerForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
    
    def test_customer_form_save(self):
        """اختبار حفظ نموذج العميل"""
        form_data = {
            'name': 'عميل تجريبي',
            'customer_type': 'retail',
            'phone': '0*********0',
            'email': '<EMAIL>',
            'address': 'عنوان تجريبي',
            'credit_limit': '10000.00',
            'credit_days': '30'
        }
        
        form = CustomerForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        customer = form.save()
        self.assertEqual(customer.name, 'عميل تجريبي')
        self.assertEqual(customer.customer_type, 'retail')
        self.assertEqual(customer.credit_limit, Decimal('10000.00'))


class ProductFormTest(TestCase):
    """اختبارات نموذج المنتج"""
    
    def test_valid_product_form(self):
        """اختبار نموذج منتج صحيح"""
        form_data = {
            'name': 'منتج تجريبي',
            'code': 'PROD001',
            'category': 'فئة تجريبية',
            'unit': 'قطعة',
            'cost_price': '50.00',
            'unit_price_retail': '75.00',
            'unit_price_wholesale': '65.00',
            'stock_quantity': '100.00',
            'min_stock_level': '10.00',
            'max_stock_level': '500.00',
            'description': 'وصف المنتج',
            'is_active': True
        }
        
        form = ProductForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_invalid_product_form_missing_required_fields(self):
        """اختبار نموذج منتج بدون حقول مطلوبة"""
        form_data = {
            'category': 'فئة تجريبية',
            'unit': 'قطعة'
        }
        
        form = ProductForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
        self.assertIn('code', form.errors)
    
    def test_product_form_price_validation(self):
        """اختبار التحقق من الأسعار"""
        form_data = {
            'name': 'منتج تجريبي',
            'code': 'PROD001',
            'unit': 'قطعة',
            'cost_price': '100.00',  # سعر التكلفة أعلى من سعر البيع
            'unit_price_retail': '75.00',
            'unit_price_wholesale': '65.00',
            'stock_quantity': '100.00'
        }
        
        form = ProductForm(data=form_data)
        # يجب أن يكون النموذج غير صحيح إذا كان سعر التكلفة أعلى من سعر البيع
        # (هذا يعتمد على منطق التحقق المخصص في النموذج)
        if hasattr(form, 'clean'):
            form.is_valid()  # تشغيل التحقق
    
    def test_product_form_save(self):
        """اختبار حفظ نموذج المنتج"""
        form_data = {
            'name': 'منتج تجريبي',
            'code': 'PROD001',
            'category': 'فئة تجريبية',
            'unit': 'قطعة',
            'cost_price': '50.00',
            'unit_price_retail': '75.00',
            'unit_price_wholesale': '65.00',
            'stock_quantity': '100.00',
            'min_stock_level': '10.00'
        }
        
        form = ProductForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        product = form.save()
        self.assertEqual(product.name, 'منتج تجريبي')
        self.assertEqual(product.code, 'PROD001')
        self.assertEqual(product.cost_price, Decimal('50.00'))


class SalesRepresentativeFormTest(TestCase):
    """اختبارات نموذج المندوب"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='rep1',
            first_name='أحمد',
            last_name='محمد',
            email='<EMAIL>'
        )
    
    def test_valid_representative_form(self):
        """اختبار نموذج مندوب صحيح"""
        form_data = {
            'user': self.user.pk,
            'employee_id': 'EMP001',
            'phone': '0*********0',
            'address': 'عنوان المندوب',
            'hire_date': date.today().strftime('%Y-%m-%d'),
            'target_monthly': '50000.00',
            'commission_rate': '2.5',
            'is_active': True
        }
        
        form = SalesRepresentativeForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_invalid_representative_form_missing_user(self):
        """اختبار نموذج مندوب بدون مستخدم"""
        form_data = {
            'employee_id': 'EMP001',
            'phone': '0*********0',
            'hire_date': date.today().strftime('%Y-%m-%d')
        }
        
        form = SalesRepresentativeForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('user', form.errors)
    
    def test_representative_form_commission_validation(self):
        """اختبار التحقق من نسبة العمولة"""
        form_data = {
            'user': self.user.pk,
            'employee_id': 'EMP001',
            'phone': '0*********0',
            'hire_date': date.today().strftime('%Y-%m-%d'),
            'commission_rate': '150.00'  # نسبة عمولة غير منطقية
        }
        
        form = SalesRepresentativeForm(data=form_data)
        # يجب أن يكون النموذج غير صحيح إذا كانت نسبة العمولة أكبر من 100%
        if hasattr(form, 'clean_commission_rate'):
            form.is_valid()


class SalesInvoiceFormTest(TestCase):
    """اختبارات نموذج فاتورة البيع"""
    
    def setUp(self):
        self.customer = Customer.objects.create(
            name='عميل تجريبي',
            customer_type='retail'
        )
        
        self.rep_user = User.objects.create_user(
            username='rep1',
            first_name='أحمد',
            last_name='محمد'
        )
        
        self.representative = SalesRepresentative.objects.create(
            user=self.rep_user,
            employee_id='EMP001'
        )
    
    def test_valid_invoice_form(self):
        """اختبار نموذج فاتورة صحيح"""
        form_data = {
            'customer': self.customer.pk,
            'representative': self.representative.pk,
            'invoice_date': date.today().strftime('%Y-%m-%d'),
            'due_date': (date.today() + timedelta(days=30)).strftime('%Y-%m-%d'),
            'invoice_type': 'retail',
            'payment_method': 'cash',
            'status': 'draft',
            'discount_percentage': '0.00',
            'tax_percentage': '14.00',
            'notes': 'ملاحظات الفاتورة'
        }
        
        form = SalesInvoiceForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_invalid_invoice_form_missing_customer(self):
        """اختبار نموذج فاتورة بدون عميل"""
        form_data = {
            'invoice_date': date.today().strftime('%Y-%m-%d'),
            'due_date': (date.today() + timedelta(days=30)).strftime('%Y-%m-%d'),
            'invoice_type': 'retail',
            'payment_method': 'cash'
        }
        
        form = SalesInvoiceForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('customer', form.errors)
    
    def test_invoice_form_date_validation(self):
        """اختبار التحقق من التواريخ"""
        form_data = {
            'customer': self.customer.pk,
            'invoice_date': date.today().strftime('%Y-%m-%d'),
            'due_date': (date.today() - timedelta(days=1)).strftime('%Y-%m-%d'),  # تاريخ استحقاق في الماضي
            'invoice_type': 'retail',
            'payment_method': 'cash'
        }
        
        form = SalesInvoiceForm(data=form_data)
        # يجب أن يكون النموذج غير صحيح إذا كان تاريخ الاستحقاق قبل تاريخ الفاتورة
        if hasattr(form, 'clean'):
            form.is_valid()


class PaymentFormTest(TestCase):
    """اختبارات نموذج الدفع"""
    
    def setUp(self):
        self.customer = Customer.objects.create(
            name='عميل تجريبي',
            customer_type='credit'
        )
        
        self.rep_user = User.objects.create_user(username='rep1')
        self.representative = SalesRepresentative.objects.create(
            user=self.rep_user,
            employee_id='EMP001'
        )
    
    def test_valid_payment_form(self):
        """اختبار نموذج دفع صحيح"""
        form_data = {
            'customer': self.customer.pk,
            'representative': self.representative.pk,
            'payment_date': date.today().strftime('%Y-%m-%d'),
            'amount': '1000.00',
            'payment_type': 'collection',
            'payment_method': 'cash',
            'reference_number': 'REF001',
            'notes': 'ملاحظات الدفع'
        }
        
        form = PaymentForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_invalid_payment_form_negative_amount(self):
        """اختبار نموذج دفع بمبلغ سالب"""
        form_data = {
            'customer': self.customer.pk,
            'representative': self.representative.pk,
            'payment_date': date.today().strftime('%Y-%m-%d'),
            'amount': '-500.00',  # مبلغ سالب
            'payment_type': 'collection',
            'payment_method': 'cash'
        }
        
        form = PaymentForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('amount', form.errors)
    
    def test_payment_form_save(self):
        """اختبار حفظ نموذج الدفع"""
        form_data = {
            'customer': self.customer.pk,
            'representative': self.representative.pk,
            'payment_date': date.today().strftime('%Y-%m-%d'),
            'amount': '1000.00',
            'payment_type': 'collection',
            'payment_method': 'cash',
            'notes': 'ملاحظات الدفع'
        }
        
        form = PaymentForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        # لحفظ النموذج، نحتاج إلى تمرير المستخدم الحالي
        # payment = form.save(commit=False)
        # payment.created_by = user
        # payment.save()


class FormValidationTest(TestCase):
    """اختبارات التحقق المخصص للنماذج"""
    
    def test_phone_number_validation(self):
        """اختبار التحقق من رقم الهاتف"""
        # اختبار رقم هاتف صحيح
        form_data = {
            'name': 'عميل تجريبي',
            'customer_type': 'retail',
            'phone': '0*********0'
        }
        form = CustomerForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        # اختبار رقم هاتف قصير
        form_data['phone'] = '123'
        form = CustomerForm(data=form_data)
        self.assertFalse(form.is_valid())
    
    def test_decimal_field_validation(self):
        """اختبار التحقق من الحقول العشرية"""
        form_data = {
            'name': 'منتج تجريبي',
            'code': 'PROD001',
            'unit': 'قطعة',
            'cost_price': 'invalid_decimal',  # قيمة غير صحيحة
            'unit_price_retail': '75.00',
            'stock_quantity': '100.00'
        }
        
        form = ProductForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('cost_price', form.errors)
    
    def test_date_field_validation(self):
        """اختبار التحقق من حقول التاريخ"""
        customer = Customer.objects.create(name='عميل', customer_type='retail')
        
        form_data = {
            'customer': customer.pk,
            'invoice_date': 'invalid_date',  # تاريخ غير صحيح
            'invoice_type': 'retail',
            'payment_method': 'cash'
        }
        
        form = SalesInvoiceForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('invoice_date', form.errors)
