{% extends 'base.html' %}
{% load static %}

{% block title %}فئات الأصناف - إدارة احترافية{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Category Management Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        --warning-gradient: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
        --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --glass-bg: rgba(255, 255, 255, 0.15);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --shadow-heavy: 0 15px 35px rgba(31, 38, 135, 0.2);
    }

    .content-area {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #56ab2f 50%, #a8e6cf 75%, #4facfe 100%);
        background-attachment: fixed;
        background-size: 400% 400%;
        animation: gradientShift 20s ease infinite;
        min-height: 100vh;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Hero Section */
    .categories-hero {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 25px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 800;
        color: white;
        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        margin-bottom: 0.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        color: rgba(255,255,255,0.9);
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    /* Enhanced Buttons */
    .btn-primary {
        background: var(--primary-gradient);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 15px;
        font-weight: 600;
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
    }

    .btn-outline-light {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 15px;
        font-weight: 600;
        backdrop-filter: blur(8px);
        transition: all 0.3s ease;
    }

    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
        color: white;
    }

    /* Filters */
    .filters-section {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        padding: 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-light);
        margin-bottom: 1.5rem;
    }

    .filters-row {
        display: grid;
        grid-template-columns: 2fr 1fr auto;
        gap: 1rem;
        align-items: end;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-label {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .form-control {
        padding: 0.75rem 1rem;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--radius);
        font-size: 0.875rem;
        color: white;
        backdrop-filter: blur(4px);
        transition: all 0.3s ease;
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        color: white;
        outline: none;
    }

    .form-control option {
        background: #333;
        color: white;
    }



    /* Enhanced Table Container */
    .table-container {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: var(--shadow-light);
        margin-bottom: 2rem;
    }

    .table-header {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
        padding: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .table-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        margin: 0;
    }

    .table {
        margin: 0;
        color: white;
    }

    .table thead th {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
        border: none;
        padding: 1rem 0.75rem;
        font-weight: 700;
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        text-align: center;
        border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    }

    .table tbody td {
        border: none;
        padding: 1rem 0.75rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(255, 255, 255, 0.05);
        text-align: center;
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    }

    .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: scale(1.005);
        transition: all 0.3s ease;
    }

    /* Enhanced Category Elements */
    .category-icon {
        width: 32px;
        height: 32px;
        background: var(--info-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
    }

    .category-code {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 8px;
        font-weight: 600;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .category-name strong {
        font-size: 1rem;
        color: white;
    }

    .parent-link {
        color: #4facfe;
        text-decoration: none;
        font-weight: 600;
    }

    .parent-link:hover {
        color: #00f2fe;
        text-decoration: underline;
    }

    /* Enhanced Badges */
    .badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .badge-primary {
        background: var(--primary-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .badge-info {
        background: var(--info-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
    }

    .badge-success {
        background: var(--success-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(86, 171, 47, 0.3);
    }

    .badge-warning {
        background: var(--warning-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(247, 151, 30, 0.3);
    }

    .badge-danger {
        background: var(--danger-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(255, 65, 108, 0.3);
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .btn-action {
        padding: 0.4rem 0.6rem;
        border-radius: 10px;
        border: none;
        font-size: 0.8rem;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(8px);
    }

    .btn-view {
        background: var(--info-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
    }

    .btn-edit {
        background: var(--warning-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(247, 151, 30, 0.3);
    }

    .btn-delete {
        background: var(--danger-gradient);
        color: white;
        box-shadow: 0 2px 8px rgba(255, 65, 108, 0.3);
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        color: white;
    }

    @media (max-width: 768px) {
        .filters-row {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid" style="margin-top: 2rem;">
    <!-- Hero Section -->
    <div class="categories-hero">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="hero-title">
                    <i class="bi bi-tags me-3"></i>
                    فئات الأصناف
                </h1>
                <p class="hero-subtitle">إدارة احترافية لفئات وتصنيفات الأصناف مع تحكم كامل</p>
            </div>
            <div class="d-flex gap-3">
                <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left me-2"></i>عودة للتعريفات
                </a>
                <a href="{% url 'definitions:category_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة فئة جديدة
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <form method="get" class="filters-form">
            <div class="filters-row">
                <div class="form-group">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control"
                           placeholder="البحث بالاسم أو الكود أو الوصف..."
                           value="{{ search_query }}">
                </div>
                <div class="form-group">
                    <label class="form-label">الفئة الأب</label>
                    <select name="parent" class="form-control">
                        <option value="">جميع الفئات</option>
                        <option value="main" {% if request.GET.parent == 'main' %}selected{% endif %}>الفئات الرئيسية فقط</option>
                        {% for parent_cat in parent_categories %}
                        <option value="{{ parent_cat.id }}" {% if request.GET.parent == parent_cat.id|stringformat:"s" %}selected{% endif %}>
                            {{ parent_cat.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Enhanced Categories Table -->
    <div class="table-container">
        <div class="table-header">
            <h3 class="table-title">
                <i class="bi bi-table me-2"></i>
                قائمة فئات الأصناف
                <span class="badge bg-light text-dark ms-2">{{ page_obj.paginator.count }} فئة</span>
            </h3>
        </div>

        {% if page_obj %}
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th><i class="bi bi-hash me-1"></i>الكود</th>
                        <th><i class="bi bi-tag me-1"></i>اسم الفئة</th>
                        <th><i class="bi bi-diagram-3 me-1"></i>الفئة الأب</th>
                        <th><i class="bi bi-file-text me-1"></i>الوصف</th>
                        <th><i class="bi bi-collection me-1"></i>الفئات الفرعية</th>
                        <th><i class="bi bi-box-seam me-1"></i>الأصناف</th>
                        <th><i class="bi bi-toggle-on me-1"></i>الحالة</th>
                        <th><i class="bi bi-calendar me-1"></i>تاريخ الإنشاء</th>
                        <th><i class="bi bi-gear me-1"></i>الإجراءات</th>
                    </tr>
                </thead>
                    <tbody>
                        {% for category in page_obj %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="category-icon me-2">
                                        <i class="bi bi-tag-fill"></i>
                                    </div>
                                    <code class="category-code">{{ category.code }}</code>
                                </div>
                            </td>
                            <td>
                                <div class="category-name">
                                    <strong>{{ category.name }}</strong>
                                    {% if category.name_en %}
                                    <br><small class="text-muted">{{ category.name_en }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                {% if category.parent %}
                                <div class="parent-category">
                                    <i class="bi bi-arrow-up-circle me-1"></i>
                                    <a href="{% url 'definitions:category_detail' category.parent.id %}" class="parent-link">
                                        {{ category.parent.name }}
                                    </a>
                                </div>
                                {% else %}
                                <span class="badge badge-primary">فئة رئيسية</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="category-description">
                                    {% if category.description %}
                                    {{ category.description|truncatechars:40 }}
                                    {% else %}
                                    <span class="text-muted">لا يوجد وصف</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="badge badge-info">
                                        <i class="bi bi-collection me-1"></i>
                                        {{ category.children.count }}
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="badge badge-success">
                                        <i class="bi bi-box-seam me-1"></i>
                                        {{ category.products.count }}
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    {% if category.is_active %}
                                    <span class="badge badge-success">
                                        <i class="bi bi-check-circle me-1"></i>
                                        نشط
                                    </span>
                                    {% else %}
                                    <span class="badge badge-danger">
                                        <i class="bi bi-x-circle me-1"></i>
                                        غير نشط
                                    </span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="date-display">
                                        <i class="bi bi-calendar3 me-1"></i>
                                        {{ category.created_at|date:"d/m/Y" }}
                                    </div>
                                    <small class="text-muted">{{ category.created_at|date:"H:i" }}</small>
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{% url 'definitions:category_detail' category.id %}"
                                       class="btn-action btn-view"
                                       title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'definitions:category_edit' category.id %}"
                                       class="btn-action btn-edit"
                                       title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <form method="post" action="{% url 'definitions:category_quick_delete' category.id %}"
                                          style="display: inline;" class="delete-form">
                                        {% csrf_token %}
                                        <button type="button"
                                                class="btn-action btn-delete delete-category-btn"
                                                data-category-name="{{ category.name }}"
                                                data-category-id="{{ category.id }}"
                                                title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-tags" style="font-size: 4rem; color: #ddd;"></i>
                <h4 class="mt-3 text-muted">لا توجد فئات أصناف</h4>
                <p class="text-muted">لم يتم العثور على أي فئات تطابق معايير البحث</p>
                <a href="{% url 'definitions:category_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة فئة جديدة
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Summary -->
    {% if page_obj %}
    <div class="mt-3">
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            إجمالي فئات الأصناف: <strong>{{ total_categories }}</strong>
            {% if search_query %}
            | نتائج البحث عن: <strong>"{{ search_query }}"</strong>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة أزرار الحذف
    const deleteButtons = document.querySelectorAll('.delete-category-btn');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const categoryName = this.getAttribute('data-category-name');
            const categoryId = this.getAttribute('data-category-id');
            const form = this.closest('form');

            console.log('Delete button clicked for category:', categoryName);

            if (confirm(`هل أنت متأكد من حذف فئة الأصناف: ${categoryName}؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
                console.log('User confirmed deletion');

                // تغيير نص الزر
                this.innerHTML = '<span class="spinner-border spinner-border-sm"></span>';
                this.disabled = true;

                console.log('Submitting form...');

                // إرسال النموذج
                form.submit();
            } else {
                console.log('User cancelled deletion');
            }
        });
    });

    // وظيفة التصدير
    window.exportResults = function() {
        const searchParams = new URLSearchParams(window.location.search);
        searchParams.set('export', 'excel');

        // إنشاء رابط تحميل
        const downloadUrl = window.location.pathname + '?' + searchParams.toString();

        // إنشاء عنصر رابط مؤقت للتحميل
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = 'categories_export.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('Export initiated:', downloadUrl);
    };

    // تحديث النتائج تلقائياً عند تغيير الفلاتر
    const filterSelects = document.querySelectorAll('.search-select');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            // تأخير قصير لتحسين الأداء
            setTimeout(() => {
                document.getElementById('searchForm').submit();
            }, 300);
        });
    });
});
</script>
{% endblock %}


