from django.core.management.base import BaseCommand
from warehouses.models import InventoryItem
from manufacturing.models import ManufacturingInventoryTransaction, ManufacturingOrder
from decimal import Decimal

class Command(BaseCommand):
    help = 'فحص تكاليف منتج معين'

    def add_arguments(self, parser):
        parser.add_argument('--product', type=str, help='اسم المنتج للبحث عنه')

    def handle(self, *args, **options):
        product_name = options.get('product', 'جركن صابون أوساريك')
        
        self.stdout.write(f'البحث عن منتج: {product_name}')
        
        # البحث عن عناصر المخزون
        items = InventoryItem.objects.filter(product__name__icontains=product_name)
        self.stdout.write(f'تم العثور على {items.count()} عنصر مخزون')
        
        for item in items:
            self.stdout.write(f'المنتج: {item.product.name}')
            self.stdout.write(f'المخزن: {item.warehouse.name}')
            self.stdout.write(f'الكمية: {item.quantity_on_hand}')
            self.stdout.write(f'متوسط التكلفة: {item.average_cost}')
            self.stdout.write(f'آخر تكلفة: {item.last_cost}')
            self.stdout.write(f'إجمالي القيمة: {item.total_value}')
            self.stdout.write(f'سعر التكلفة من تعريف المنتج: {item.product.cost_price}')
            self.stdout.write('-' * 50)
            
            # البحث عن حركات التصنيع
            transactions = ManufacturingInventoryTransaction.objects.filter(
                product=item.product,
                warehouse=item.warehouse,
                transaction_type='finished_goods_production'
            ).order_by('-created_at')
            
            self.stdout.write(f'حركات التصنيع: {transactions.count()}')
            
            for trans in transactions:
                self.stdout.write(f'  أمر التصنيع: {trans.manufacturing_order.order_number}')
                self.stdout.write(f'  الكمية: {trans.quantity}')
                self.stdout.write(f'  تكلفة الوحدة: {trans.unit_cost}')
                self.stdout.write(f'  إجمالي التكلفة: {trans.total_cost}')
                self.stdout.write(f'  التكلفة الفعلية للأمر: {trans.manufacturing_order.total_actual_cost}')
                self.stdout.write(f'  تاريخ الإنشاء: {trans.created_at}')
                self.stdout.write('-' * 30)
            
            # إصلاح التكلفة إذا كانت صفر
            if item.average_cost == 0 and transactions.exists():
                self.stdout.write('محاولة إصلاح التكلفة...')
                
                total_quantity = Decimal('0')
                total_cost = Decimal('0')
                
                for trans in transactions:
                    total_quantity += trans.quantity
                    total_cost += trans.total_cost or Decimal('0')
                
                if total_quantity > 0:
                    average_cost = total_cost / total_quantity
                    item.average_cost = average_cost
                    item.last_cost = average_cost
                    item.total_value = item.quantity_on_hand * item.average_cost
                    item.save()
                    
                    self.stdout.write(self.style.SUCCESS(f'تم إصلاح التكلفة: {average_cost}'))
                    self.stdout.write(self.style.SUCCESS(f'إجمالي القيمة الجديدة: {item.total_value}'))
            
            self.stdout.write('=' * 70)
