{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if action == 'edit' %}
تعديل نوع الإيراد - {{ revenue_type.name }}
{% else %}
إضافة نوع إيراد جديد
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        min-height: 100vh;
    }

    .form-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
        color: #333;
    }

    .form-title {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 3rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .form-section {
        background: rgba(39, 174, 96, 0.1);
        backdrop-filter: blur(5px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(39, 174, 96, 0.2);
    }

    .section-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #27ae60;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-label {
        color: #333;
        font-weight: 600;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.95rem;
    }

    .required::after {
        content: " *";
        color: #dc3545;
        font-weight: bold;
    }

    .form-control, .form-select {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(39, 174, 96, 0.3);
        border-radius: 15px;
        padding: 1rem 1.25rem;
        color: #333;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-control:focus, .form-select:focus {
        background: rgba(255, 255, 255, 1);
        border-color: #27ae60;
        box-shadow: 0 0 0 4px rgba(39, 174, 96, 0.2);
        color: #333;
        outline: none;
        transform: translateY(-2px);
    }

    .btn {
        padding: 1rem 2.5rem;
        border-radius: 25px;
        font-weight: 700;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 1rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(39, 174, 96, 0.4);
        color: white;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid rgba(39, 174, 96, 0.2);
    }

    .form-check {
        background: rgba(255, 255, 255, 0.7);
        border: 2px solid rgba(39, 174, 96, 0.3);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .form-check:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-2px);
    }

    .form-check-input {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid #27ae60;
        margin-right: 0.75rem;
        width: 1.5rem;
        height: 1.5rem;
    }

    .form-check-input:checked {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        border-color: #27ae60;
    }

    .form-check-label {
        color: #333;
        font-weight: 600;
        font-size: 1rem;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.85rem;
        margin-top: 0.5rem;
        font-style: italic;
    }

    .alert {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.18);
        margin-bottom: 2rem;
    }

    .radio-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .radio-option {
        background: rgba(255, 255, 255, 0.7);
        border: 2px solid rgba(39, 174, 96, 0.3);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .radio-option:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-2px);
    }

    .radio-option.selected {
        background: rgba(39, 174, 96, 0.2);
        border-color: #27ae60;
    }

    .radio-option input[type="radio"] {
        display: none;
    }

    .radio-option-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #27ae60;
    }

    .radio-option-title {
        font-weight: 700;
        color: #333;
        margin-bottom: 0.25rem;
    }

    .radio-option-desc {
        font-size: 0.85rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-container">
                <h1 class="form-title">
                    <i class="bi bi-arrow-up-circle"></i>
                    {% if action == 'edit' %}
                    تعديل نوع الإيراد
                    {% else %}
                    إضافة نوع إيراد جديد
                    {% endif %}
                </h1>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" id="revenueTypeForm">
                    {% csrf_token %}
                    
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            المعلومات الأساسية
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="code" class="form-label required">
                                    <i class="bi bi-hash"></i>كود نوع الإيراد
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="code" 
                                       name="code" 
                                       value="{% if action == 'edit' %}{{ revenue_type.code }}{% else %}{{ form_data.code|default:'' }}{% endif %}"
                                       placeholder="مثال: REV001، SALES، SERV"
                                       maxlength="20"
                                       style="text-transform: uppercase;"
                                       required>
                                <div class="form-text">كود مختصر لنوع الإيراد</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="name" class="form-label required">
                                    <i class="bi bi-arrow-up-circle"></i>اسم نوع الإيراد
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       value="{% if action == 'edit' %}{{ revenue_type.name }}{% else %}{{ form_data.name|default:'' }}{% endif %}"
                                       placeholder="الاسم بالعربية"
                                       required>
                                <div class="form-text">اسم نوع الإيراد بالعربية</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="name_en" class="form-label">
                                    <i class="bi bi-globe"></i>الاسم بالإنجليزية
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name_en" 
                                       name="name_en" 
                                       value="{% if action == 'edit' %}{{ revenue_type.name_en }}{% else %}{{ form_data.name_en|default:'' }}{% endif %}"
                                       placeholder="Name in English">
                                <div class="form-text">اسم نوع الإيراد بالإنجليزية (اختياري)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Classification Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-tags"></i>
                            التصنيف
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label required">
                                    <i class="bi bi-arrow-repeat"></i>نوع الإيراد
                                </label>
                                <div class="radio-group">
                                    <label class="radio-option {% if action == 'edit' and revenue_type.revenue_type == 'recurring' or action != 'edit' and form_data.revenue_type == 'recurring' or action != 'edit' and not form_data.revenue_type %}selected{% endif %}" onclick="selectRadio(this, 'revenue_type', 'recurring')">
                                        <input type="radio" name="revenue_type" value="recurring" {% if action == 'edit' and revenue_type.revenue_type == 'recurring' or action != 'edit' and form_data.revenue_type == 'recurring' or action != 'edit' and not form_data.revenue_type %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-arrow-repeat"></i>
                                        </div>
                                        <div class="radio-option-title">متكرر</div>
                                        <div class="radio-option-desc">إيرادات متكررة دورياً</div>
                                    </label>
                                    
                                    <label class="radio-option {% if action == 'edit' and revenue_type.revenue_type == 'one-time' or action != 'edit' and form_data.revenue_type == 'one-time' %}selected{% endif %}" onclick="selectRadio(this, 'revenue_type', 'one-time')">
                                        <input type="radio" name="revenue_type" value="one-time" {% if action == 'edit' and revenue_type.revenue_type == 'one-time' or action != 'edit' and form_data.revenue_type == 'one-time' %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-1-circle"></i>
                                        </div>
                                        <div class="radio-option-title">لمرة واحدة</div>
                                        <div class="radio-option-desc">إيرادات غير متكررة</div>
                                    </label>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label required">
                                    <i class="bi bi-building"></i>تصنيف الإيراد
                                </label>
                                <div class="radio-group">
                                    <label class="radio-option {% if action == 'edit' and revenue_type.category == 'operational' or action != 'edit' and form_data.category == 'operational' or action != 'edit' and not form_data.category %}selected{% endif %}" onclick="selectRadio(this, 'category', 'operational')">
                                        <input type="radio" name="category" value="operational" {% if action == 'edit' and revenue_type.category == 'operational' or action != 'edit' and form_data.category == 'operational' or action != 'edit' and not form_data.category %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-gear"></i>
                                        </div>
                                        <div class="radio-option-title">تشغيلي</div>
                                        <div class="radio-option-desc">إيرادات العمليات التشغيلية</div>
                                    </label>
                                    
                                    <label class="radio-option {% if action == 'edit' and revenue_type.category == 'investment' or action != 'edit' and form_data.category == 'investment' %}selected{% endif %}" onclick="selectRadio(this, 'category', 'investment')">
                                        <input type="radio" name="category" value="investment" {% if action == 'edit' and revenue_type.category == 'investment' or action != 'edit' and form_data.category == 'investment' %}checked{% endif %}>
                                        <div class="radio-option-icon">
                                            <i class="bi bi-graph-up"></i>
                                        </div>
                                        <div class="radio-option-title">استثماري</div>
                                        <div class="radio-option-desc">إيرادات استثمارية</div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-gear"></i>
                            معلومات إضافية
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="description" class="form-label">
                                    <i class="bi bi-file-text"></i>وصف نوع الإيراد
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="4"
                                          placeholder="وصف تفصيلي لنوع الإيراد...">{% if action == 'edit' %}{{ revenue_type.description }}{% else %}{{ form_data.description|default:'' }}{% endif %}</textarea>
                                <div class="form-text">وصف تفصيلي لنوع الإيراد (اختياري)</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active"
                                           {% if action == 'edit' and revenue_type.is_active %}checked
                                           {% elif action != 'edit' %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        <i class="bi bi-toggle-on me-1"></i>نوع إيراد نشط
                                    </label>
                                    <div class="form-text">تفعيل أو إلغاء تفعيل نوع الإيراد</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="notes" class="form-label">
                                    <i class="bi bi-journal-text"></i>ملاحظات
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3"
                                          placeholder="ملاحظات إضافية...">{% if action == 'edit' %}{{ revenue_type.notes }}{% else %}{{ form_data.notes|default:'' }}{% endif %}</textarea>
                                <div class="form-text">ملاحظات إضافية (اختياري)</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <div>
                            <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary me-2">
                                <i class="bi bi-house me-2"></i>التعريفات
                            </a>
                            <a href="{% url 'definitions:revenue_type_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-2"></i>قائمة أنواع الإيرادات
                            </a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" onclick="this.disabled=true; this.innerHTML='<span class=\'spinner-border spinner-border-sm me-2\'></span>جاري الحفظ...'; this.form.submit();">
                            <i class="bi bi-check-circle me-2"></i>
                            {% if action == 'edit' %}
                            حفظ التعديلات
                            {% else %}
                            إضافة نوع الإيراد
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل صفحة نوع الإيراد');
    
    // تحويل الكود إلى أحرف كبيرة تلقائياً
    const codeInput = document.getElementById('code');
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    }
});

function selectRadio(element, name, value) {
    // إزالة التحديد من جميع الخيارات في نفس المجموعة
    const group = element.closest('.radio-group');
    group.querySelectorAll('.radio-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // إضافة التحديد للخيار المختار
    element.classList.add('selected');
    
    // تحديد الراديو بوتن
    element.querySelector('input[type="radio"]').checked = true;
}
</script>
{% endblock %}
