# Generated by Django 5.2.4 on 2025-07-25 22:39

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchases', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, verbose_name='اسم المخزن')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المخزن')),
                ('location', models.CharField(max_length=200, verbose_name='الموقع')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('manager', models.CharField(blank=True, max_length=100, null=True, verbose_name='مدير المخزن')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='الهاتف')),
                ('capacity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='السعة (متر مكعب)')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مخزن',
                'verbose_name_plural': 'المخازن',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='purchaseinvoiceitem',
            name='applied_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التطبيق'),
        ),
        migrations.AddField(
            model_name='purchaseinvoiceitem',
            name='applied_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='طبق بواسطة'),
        ),
        migrations.AddField(
            model_name='purchaseinvoiceitem',
            name='applied_to_stock',
            field=models.BooleanField(default=False, verbose_name='تم التطبيق على المخزون'),
        ),
        migrations.AddField(
            model_name='purchaseorderitem',
            name='is_received',
            field=models.BooleanField(default=False, verbose_name='تم الاستلام'),
        ),
        migrations.AddField(
            model_name='purchaseorderitem',
            name='received_quantity',
            field=models.DecimalField(decimal_places=3, default=0, max_digits=10, verbose_name='الكمية المستلمة'),
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('in', 'وارد'), ('out', 'صادر'), ('transfer', 'نقل'), ('adjustment', 'تسوية')], max_length=20, verbose_name='نوع الحركة')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='الكمية')),
                ('unit_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر الوحدة')),
                ('reference_type', models.CharField(choices=[('purchase_invoice', 'فاتورة شراء'), ('sales_invoice', 'فاتورة بيع'), ('transfer', 'نقل بين مخازن'), ('adjustment', 'تسوية مخزون'), ('manual', 'يدوي')], max_length=30, verbose_name='نوع المرجع')),
                ('reference_id', models.PositiveIntegerField(verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='purchases.product', verbose_name='المنتج')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='purchases.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='purchaseinvoiceitem',
            name='warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='purchases.warehouse', verbose_name='المخزن المستهدف'),
        ),
        migrations.AddField(
            model_name='purchaseorderitem',
            name='warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='purchases.warehouse', verbose_name='المخزن المستهدف'),
        ),
        migrations.CreateModel(
            name='Stock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=3, default=0, max_digits=10, verbose_name='الكمية')),
                ('min_stock_level', models.DecimalField(decimal_places=3, default=0, max_digits=10, verbose_name='الحد الأدنى للمخزون')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='purchases.product', verbose_name='المنتج')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_items', to='purchases.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'مخزون منتج',
                'verbose_name_plural': 'مخزون المنتجات',
                'ordering': ['warehouse', 'product'],
                'unique_together': {('product', 'warehouse')},
            },
        ),
    ]
