from django import forms
from django.contrib.auth.models import User
from .models import Branch, BranchEmployee, BranchAsset

class BranchForm(forms.ModelForm):
    """نموذج إضافة/تعديل الفروع"""
    class Meta:
        model = Branch
        fields = [
            'name', 'code', 'branch_type', 'parent_branch', 'address', 'city', 'region', 
            'postal_code', 'country', 'latitude', 'longitude', 'phone', 'mobile', 'fax', 
            'email', 'website', 'manager', 'manager_name', 'manager_phone', 'opening_date', 
            'working_hours', 'employee_count', 'area_size', 'status', 'is_active', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الفرع'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رمز الفرع'}),
            'branch_type': forms.Select(attrs={'class': 'form-select'}),
            'parent_branch': forms.Select(attrs={'class': 'form-select'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'العنوان'}),
            'city': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'المدينة'}),
            'region': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'المنطقة'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الرمز البريدي'}),
            'country': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الدولة'}),
            'latitude': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.00000001'}),
            'longitude': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.00000001'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'mobile': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الجوال'}),
            'fax': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الفاكس'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'website': forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'الموقع الإلكتروني'}),
            'manager': forms.Select(attrs={'class': 'form-select'}),
            'manager_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المدير'}),
            'manager_phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'هاتف المدير'}),
            'opening_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'working_hours': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'ساعات العمل'}),
            'employee_count': forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
            'area_size': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['manager'].queryset = User.objects.filter(is_active=True)
        self.fields['manager'].empty_label = "اختر المدير"
        self.fields['parent_branch'].queryset = Branch.objects.filter(is_active=True)
        self.fields['parent_branch'].empty_label = "لا يوجد فرع أب"

class BranchEmployeeForm(forms.ModelForm):
    """نموذج إضافة/تعديل موظفي الفروع"""
    class Meta:
        model = BranchEmployee
        fields = ['branch', 'user', 'position', 'hire_date', 'salary', 'is_active', 'notes']
        widgets = {
            'branch': forms.Select(attrs={'class': 'form-select'}),
            'user': forms.Select(attrs={'class': 'form-select'}),
            'position': forms.Select(attrs={'class': 'form-select'}),
            'hire_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'salary': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['branch'].queryset = Branch.objects.filter(is_active=True)
        self.fields['user'].queryset = User.objects.filter(is_active=True)

class BranchAssetForm(forms.ModelForm):
    """نموذج إضافة/تعديل أصول الفروع"""
    class Meta:
        model = BranchAsset
        fields = ['branch', 'name', 'asset_type', 'description', 'serial_number', 
                 'purchase_date', 'purchase_price', 'current_value', 'is_active']
        widgets = {
            'branch': forms.Select(attrs={'class': 'form-select'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الأصل'}),
            'asset_type': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف الأصل'}),
            'serial_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الرقم التسلسلي'}),
            'purchase_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'purchase_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'current_value': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['branch'].queryset = Branch.objects.filter(is_active=True)

class BranchSearchForm(forms.Form):
    """نموذج البحث في الفروع"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم أو الرمز أو المدينة...'
        })
    )
    branch_type = forms.ChoiceField(
        choices=[('', 'جميع الأنواع')] + Branch.BRANCH_TYPES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + Branch.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    city = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'المدينة'
        })
    )
    manager = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        required=False,
        empty_label="جميع المديرين",
        widget=forms.Select(attrs={'class': 'form-select'})
    )

class EmployeeSearchForm(forms.Form):
    """نموذج البحث في موظفي الفروع"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم...'
        })
    )
    branch = forms.ModelChoiceField(
        queryset=Branch.objects.filter(is_active=True),
        required=False,
        empty_label="جميع الفروع",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    position = forms.ChoiceField(
        choices=[('', 'جميع المناصب')] + BranchEmployee.POSITION_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    is_active = forms.ChoiceField(
        choices=[('', 'جميع الحالات'), ('True', 'نشط'), ('False', 'غير نشط')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
