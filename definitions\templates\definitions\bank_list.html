{% extends 'base.html' %}

{% block title %}{% if user_language == 'en' %}Define Banks{% else %}تعريف البنوك{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1>{% if user_language == 'en' %}Define Banks{% else %}تعريف البنوك{% endif %}</h1>
            <p class="text-muted">{% if user_language == 'en' %}Manage banks and bank accounts{% else %}إدارة البنوك والحسابات المصرفية{% endif %}</p>
        </div>
        <div>
            <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left me-2"></i>عودة للتعريفات
            </a>
            <a href="{% url 'definitions:bank_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>إضافة بنك جديد
            </a>
        </div>
    </div>

    <!-- Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <input type="text" 
                           name="search" 
                           class="form-control" 
                           placeholder="البحث في اسم البنك، الكود، أو SWIFT Code..."
                           value="{{ search_query }}">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-search me-1"></i>بحث
                    </button>
                    <a href="{% url 'definitions:bank_list' %}" class="btn btn-outline-secondary">
                        إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Banks Table -->
    <div class="card">
        <div class="card-body">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الكود</th>
                            <th>اسم البنك</th>
                            <th>SWIFT Code</th>
                            <th>الهاتف</th>
                            <th>العنوان</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for bank in page_obj %}
                        <tr>
                            <td>
                                <code>{{ bank.code }}</code>
                            </td>
                            <td>
                                <strong>{{ bank.name }}</strong>
                                {% if bank.name_en %}
                                <br><small class="text-muted">{{ bank.name_en }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if bank.swift_code %}
                                    <code class="text-info">{{ bank.swift_code }}</code>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if bank.phone %}
                                    <a href="tel:{{ bank.phone }}" class="text-decoration-none">
                                        <i class="bi bi-telephone me-1"></i>{{ bank.phone }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if bank.address %}
                                    {{ bank.address|truncatechars:30 }}
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if bank.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ bank.created_at|date:"d/m/Y" }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'definitions:bank_detail' bank.id %}" 
                                       class="btn btn-outline-info" 
                                       title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'definitions:bank_edit' bank.id %}" 
                                       class="btn btn-outline-warning" 
                                       title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <form method="post" action="{% url 'definitions:bank_quick_delete' bank.id %}" 
                                          style="display: inline;" 
                                          onsubmit="return confirm('هل أنت متأكد من حذف البنك {{ bank.name }}؟')">
                                        {% csrf_token %}
                                        <button type="submit" 
                                                class="btn btn-outline-danger" 
                                                title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-bank" style="font-size: 4rem; color: #ddd;"></i>
                <h4 class="mt-3 text-muted">لا توجد بنوك</h4>
                <p class="text-muted">لم يتم العثور على أي بنوك تطابق معايير البحث</p>
                <a href="{% url 'definitions:bank_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة بنك جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Summary -->
    {% if page_obj %}
    <div class="mt-3">
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            إجمالي البنوك: <strong>{{ total_banks }}</strong>
            {% if search_query %}
            | نتائج البحث عن: <strong>"{{ search_query }}"</strong>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
