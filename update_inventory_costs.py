#!/usr/bin/env python
"""
سكريبت لتحديث تكاليف المخزون
"""

import os
import sys
import django

# إعداد Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from warehouses.models import InventoryItem
from decimal import Decimal

def update_inventory_costs():
    """تحديث تكاليف المخزون"""
    print("بدء تحديث تكاليف المخزون...")
    
    updated_count = 0
    
    for item in InventoryItem.objects.all():
        # تحديث التكاليف من تعريف المنتج
        if item.product.cost_price:
            item.average_cost = item.product.cost_price
            item.last_cost = item.product.cost_price
            
            # حساب إجمالي القيمة
            item.total_value = item.quantity_on_hand * item.average_cost
            
            item.save()
            updated_count += 1
            
            print(f"تم تحديث: {item.warehouse.name} - {item.product.name}")
            print(f"  الكمية: {item.quantity_on_hand}")
            print(f"  متوسط التكلفة: {item.average_cost}")
            print(f"  إجمالي القيمة: {item.total_value}")
            print("-" * 50)
    
    print(f"تم تحديث {updated_count} عنصر مخزون بنجاح!")

if __name__ == '__main__':
    update_inventory_costs()
