{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الشخص - {{ person.name }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: #ffffff;
        min-height: 100vh;
    }

    /* Detail Container */
    .detail-container {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        color: #333;
    }

    .detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #e0e0e0;
    }

    .detail-title {
        font-size: 2rem;
        font-weight: 800;
        color: #333;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .person-type-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-left: 1rem;
    }

    .person-type-badge.customer { 
        background: #e3f2fd; 
        color: #1976d2; 
        border: 1px solid #bbdefb;
    }
    .person-type-badge.supplier { 
        background: #e8f5e8; 
        color: #388e3c; 
        border: 1px solid #c8e6c9;
    }
    .person-type-badge.employee { 
        background: #fff3e0; 
        color: #f57c00; 
        border: 1px solid #ffcc02;
    }
    .person-type-badge.both { 
        background: #f3e5f5; 
        color: #7b1fa2; 
        border: 1px solid #e1bee7;
    }
    .person-type-badge.other { 
        background: #f5f5f5; 
        color: #616161; 
        border: 1px solid #e0e0e0;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .btn {
        padding: 0.6rem 1.5rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .btn-primary {
        background: #007bff;
        color: white;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .btn-primary:hover {
        background: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-danger {
        background: #dc3545;
        color: white;
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }

    .btn-danger:hover {
        background: #c82333;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Detail Sections */
    .detail-section {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
    }

    .detail-label {
        font-weight: 600;
        color: #666;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-value {
        color: #333;
        font-size: 1rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e0e0e0;
    }

    .detail-value.empty {
        color: #999;
        font-style: italic;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-badge.active {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .detail-container {
            padding: 1.5rem;
        }
        
        .detail-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .detail-title {
            font-size: 1.5rem;
        }
        
        .action-buttons {
            width: 100%;
            justify-content: stretch;
        }
        
        .btn {
            flex: 1;
            justify-content: center;
        }
        
        .detail-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="detail-container">
                <div class="detail-header">
                    <div>
                        <h1 class="detail-title">
                            <i class="bi bi-person-circle"></i>
                            {{ person.name }}
                            <span class="person-type-badge {{ person.person_type }}">
                                {% if person.person_type == 'customer' %}
                                    <i class="bi bi-person-check me-1"></i>عميل
                                {% elif person.person_type == 'supplier' %}
                                    <i class="bi bi-building me-1"></i>مورد
                                {% elif person.person_type == 'employee' %}
                                    <i class="bi bi-person-badge me-1"></i>موظف
                                {% elif person.person_type == 'both' %}
                                    <i class="bi bi-people me-1"></i>عميل ومورد
                                {% else %}
                                    <i class="bi bi-person me-1"></i>أخرى
                                {% endif %}
                            </span>
                        </h1>
                        <p class="text-muted mb-0">
                            <i class="bi bi-hash me-1"></i>
                            كود: {{ person.code }}
                        </p>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="{% url 'definitions:person_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>عودة للقائمة
                        </a>
                        <a href="{% url 'definitions:person_edit' person.id %}" class="btn btn-primary">
                            <i class="bi bi-pencil me-2"></i>تعديل
                        </a>
                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                            <i class="bi bi-trash me-2"></i>حذف
                        </button>
                    </div>
                </div>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Basic Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        المعلومات الأساسية
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-hash"></i>كود الشخص
                            </div>
                            <div class="detail-value">{{ person.code }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-person"></i>الاسم
                            </div>
                            <div class="detail-value">{{ person.name }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-translate"></i>الاسم بالإنجليزية
                            </div>
                            <div class="detail-value {% if not person.name_en %}empty{% endif %}">
                                {{ person.name_en|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-tags"></i>نوع الشخص
                            </div>
                            <div class="detail-value">
                                <span class="person-type-badge {{ person.person_type }}">
                                    {% if person.person_type == 'customer' %}
                                        <i class="bi bi-person-check me-1"></i>عميل
                                    {% elif person.person_type == 'supplier' %}
                                        <i class="bi bi-building me-1"></i>مورد
                                    {% elif person.person_type == 'employee' %}
                                        <i class="bi bi-person-badge me-1"></i>موظف
                                    {% elif person.person_type == 'both' %}
                                        <i class="bi bi-people me-1"></i>عميل ومورد
                                    {% else %}
                                        <i class="bi bi-person me-1"></i>أخرى
                                    {% endif %}
                                </span>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-gender-ambiguous"></i>الجنس
                            </div>
                            <div class="detail-value {% if not person.gender %}empty{% endif %}">
                                {% if person.gender == 'male' %}ذكر
                                {% elif person.gender == 'female' %}أنثى
                                {% else %}غير محدد{% endif %}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar"></i>تاريخ الميلاد
                            </div>
                            <div class="detail-value {% if not person.birth_date %}empty{% endif %}">
                                {{ person.birth_date|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-toggle-on"></i>الحالة
                            </div>
                            <div class="detail-value">
                                <span class="status-badge {% if person.is_active %}active{% else %}inactive{% endif %}">
                                    {% if person.is_active %}
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    {% else %}
                                        <i class="bi bi-x-circle me-1"></i>غير نشط
                                    {% endif %}
                                </span>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-plus"></i>تاريخ الإنشاء
                            </div>
                            <div class="detail-value">{{ person.created_at|date:"d/m/Y H:i" }}</div>
                        </div>
                    </div>
                </div>

                <!-- Identity Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-card-text"></i>
                        معلومات الهوية
                    </h3>

                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-credit-card"></i>الرقم القومي/الهوية
                            </div>
                            <div class="detail-value {% if not person.national_id %}empty{% endif %}">
                                {{ person.national_id|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-passport"></i>رقم الجواز
                            </div>
                            <div class="detail-value {% if not person.passport_number %}empty{% endif %}">
                                {{ person.passport_number|default:"غير محدد" }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-telephone"></i>
                        معلومات الاتصال
                    </h3>

                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-telephone"></i>الهاتف الثابت
                            </div>
                            <div class="detail-value {% if not person.phone %}empty{% endif %}">
                                {% if person.phone %}
                                    <a href="tel:{{ person.phone }}" class="text-decoration-none">{{ person.phone }}</a>
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-phone"></i>الهاتف المحمول
                            </div>
                            <div class="detail-value {% if not person.mobile %}empty{% endif %}">
                                {% if person.mobile %}
                                    <a href="tel:{{ person.mobile }}" class="text-decoration-none">{{ person.mobile }}</a>
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-envelope"></i>البريد الإلكتروني
                            </div>
                            <div class="detail-value {% if not person.email %}empty{% endif %}">
                                {% if person.email %}
                                    <a href="mailto:{{ person.email }}" class="text-decoration-none">{{ person.email }}</a>
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-geo-alt"></i>
                        معلومات العنوان
                    </h3>

                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-house"></i>العنوان
                            </div>
                            <div class="detail-value {% if not person.address %}empty{% endif %}">
                                {{ person.address|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-building"></i>المدينة
                            </div>
                            <div class="detail-value {% if not person.city %}empty{% endif %}">
                                {{ person.city|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-map"></i>المحافظة/الولاية
                            </div>
                            <div class="detail-value {% if not person.state %}empty{% endif %}">
                                {{ person.state|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-globe"></i>البلد
                            </div>
                            <div class="detail-value {% if not person.country %}empty{% endif %}">
                                {{ person.country|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-mailbox"></i>الرمز البريدي
                            </div>
                            <div class="detail-value {% if not person.postal_code %}empty{% endif %}">
                                {{ person.postal_code|default:"غير محدد" }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-cash-coin"></i>
                        المعلومات المالية
                    </h3>

                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-credit-card-2-front"></i>حد الائتمان
                            </div>
                            <div class="detail-value">
                                {{ person.credit_limit|floatformat:2 }}
                                {% if person.currency %}{{ person.currency.symbol }}{% endif %}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-check"></i>شروط الدفع
                            </div>
                            <div class="detail-value">
                                {{ person.payment_terms }} يوم
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-currency-exchange"></i>العملة
                            </div>
                            <div class="detail-value {% if not person.currency %}empty{% endif %}">
                                {% if person.currency %}
                                    {{ person.currency.name }} ({{ person.currency.code }})
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tax Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-receipt"></i>
                        المعلومات الضريبية
                    </h3>

                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-file-earmark-text"></i>الرقم الضريبي
                            </div>
                            <div class="detail-value {% if not person.tax_number %}empty{% endif %}">
                                {{ person.tax_number|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-percent"></i>معدل الضريبة
                            </div>
                            <div class="detail-value">
                                {{ person.tax_rate|floatformat:2 }}%
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes Section -->
                {% if person.notes %}
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-journal-text"></i>
                        ملاحظات
                    </h3>

                    <div class="detail-value">
                        {{ person.notes|linebreaks }}
                    </div>
                </div>
                {% endif %}

                <!-- System Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-gear"></i>
                        معلومات النظام
                    </h3>

                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-person-plus"></i>أنشأ بواسطة
                            </div>
                            <div class="detail-value">
                                {{ person.created_by.get_full_name|default:person.created_by.username }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-plus"></i>تاريخ الإنشاء
                            </div>
                            <div class="detail-value">
                                {{ person.created_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>

                        {% if person.updated_at %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-check"></i>آخر تحديث
                            </div>
                            <div class="detail-value">
                                {{ person.updated_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="bi bi-exclamation-triangle text-danger me-2"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الشخص <strong>"{{ person.name }}"</strong>؟</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x me-2"></i>إلغاء
                </button>
                <form method="post" action="{% url 'definitions:person_delete' person.id %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>حذف نهائي
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}
