from django.core.management.base import BaseCommand
from django.utils import timezone
from purchases.models import AlertManager, SupplierRiskAssessment, Supplier


class Command(BaseCommand):
    help = 'تحديث التنبيهات وتقييمات المخاطر تلقائياً'

    def add_arguments(self, parser):
        parser.add_argument(
            '--update-risk',
            action='store_true',
            help='تحديث تقييمات المخاطر أيضاً',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('بدء تحديث التنبيهات...')
        )

        # تحديث التنبيهات
        try:
            AlertManager.create_due_soon_alerts()
            self.stdout.write(
                self.style.SUCCESS('✓ تم إنشاء تنبيهات المستحقات القريبة')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في تنبيهات المستحقات: {str(e)}')
            )

        try:
            AlertManager.create_overdue_alerts()
            self.stdout.write(
                self.style.SUCCESS('✓ تم إنشاء تنبيهات المتأخرات')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في تنبيهات المتأخرات: {str(e)}')
            )

        try:
            AlertManager.check_credit_limits()
            self.stdout.write(
                self.style.SUCCESS('✓ تم فحص حدود الائتمان')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'خطأ في فحص الائتمان: {str(e)}')
            )

        # تحديث تقييمات المخاطر إذا طُلب ذلك
        if options['update_risk']:
            self.stdout.write(
                self.style.SUCCESS('بدء تحديث تقييمات المخاطر...')
            )
            
            suppliers = Supplier.objects.filter(is_active=True)
            updated_count = 0
            
            for supplier in suppliers:
                try:
                    # الحصول على أول مستخدم متاح
                    from django.contrib.auth.models import User
                    default_user = User.objects.first()
                    if not default_user:
                        continue

                    risk_assessment, created = SupplierRiskAssessment.objects.get_or_create(
                        supplier=supplier,
                        defaults={'assessed_by': default_user}
                    )
                    
                    # تحديث تاريخ الدفع من البيانات الفعلية
                    risk_assessment.update_payment_history()
                    
                    # حساب درجة المخاطر
                    risk_assessment.calculate_risk_score()
                    
                    updated_count += 1
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'تحذير: خطأ في تحديث تقييم {supplier.name}: {str(e)}')
                    )
            
            self.stdout.write(
                self.style.SUCCESS(f'✓ تم تحديث {updated_count} تقييم مخاطر')
            )

        self.stdout.write(
            self.style.SUCCESS(
                f'تم الانتهاء من التحديث في {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}'
            )
        )
