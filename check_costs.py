#!/usr/bin/env python
import os
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from manufacturing.models import ManufacturingOrder
from django.db.models import Sum

def check_manufacturing_costs():
    """فحص تكاليف أوامر التصنيع"""
    
    # التحقق من جميع الأوامر المكتملة
    completed_orders = ManufacturingOrder.objects.filter(status='completed')
    print(f'عدد الأوامر المكتملة: {completed_orders.count()}')
    
    for order in completed_orders:
        print(f'\nأمر: {order.order_number}')
        print(f'التكاليف المقدرة: {order.total_estimated_cost}')
        print(f'التكاليف الفعلية: {order.total_actual_cost}')
        print(f'  - مواد خام مقدرة: {order.estimated_raw_material_cost}')
        print(f'  - مواد خام فعلية: {order.actual_raw_material_cost}')
        print(f'  - عمالة مقدرة: {order.estimated_labor_cost}')
        print(f'  - عمالة فعلية: {order.actual_labor_cost}')
        print(f'  - إضافية مقدرة: {order.estimated_overhead_cost}')
        print(f'  - إضافية فعلية: {order.actual_overhead_cost}')

    # حساب الإجماليات كما في لوحة التحكم
    cost_stats = completed_orders.aggregate(
        estimated_raw_material=Sum('estimated_raw_material_cost'),
        estimated_labor=Sum('estimated_labor_cost'),
        estimated_overhead=Sum('estimated_overhead_cost'),
        actual_raw_material=Sum('actual_raw_material_cost'),
        actual_labor=Sum('actual_labor_cost'),
        actual_overhead=Sum('actual_overhead_cost')
    )

    total_estimated = (
        (cost_stats['estimated_raw_material'] or 0) +
        (cost_stats['estimated_labor'] or 0) +
        (cost_stats['estimated_overhead'] or 0)
    )
    total_actual = (
        (cost_stats['actual_raw_material'] or 0) +
        (cost_stats['actual_labor'] or 0) +
        (cost_stats['actual_overhead'] or 0)
    )

    print(f'\n=== إجماليات لوحة التحكم ===')
    print(f'إجمالي التكاليف المقدرة: {total_estimated}')
    print(f'إجمالي التكاليف الفعلية: {total_actual}')
    
    print(f'\n=== تفاصيل الحسابات ===')
    print(f'مواد خام مقدرة: {cost_stats["estimated_raw_material"]}')
    print(f'مواد خام فعلية: {cost_stats["actual_raw_material"]}')
    print(f'عمالة مقدرة: {cost_stats["estimated_labor"]}')
    print(f'عمالة فعلية: {cost_stats["actual_labor"]}')
    print(f'إضافية مقدرة: {cost_stats["estimated_overhead"]}')
    print(f'إضافية فعلية: {cost_stats["actual_overhead"]}')

if __name__ == '__main__':
    check_manufacturing_costs()
