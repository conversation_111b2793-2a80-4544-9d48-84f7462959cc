{% extends 'base.html' %}
{% load static %}

{% block title %}كشف حساب العميل - {{ customer.name }}{% endblock %}

{% block extra_css %}
<style>
    .statement-header {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);
    }

    .statement-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-left: 5px solid #27ae60;
    }

    .balance-summary {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        text-align: center;
    }

    .balance-amount {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .transaction-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .transaction-item:hover {
        background: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .transaction-date {
        font-weight: 600;
        color: #2c3e50;
    }

    .transaction-amount {
        font-weight: 700;
        font-size: 1.1rem;
    }

    .amount-debit { color: #e74c3c; }
    .amount-credit { color: #27ae60; }

    .table-statement {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .table-statement thead th {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table-statement tbody td {
        padding: 12px 15px;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;
    }

    .btn-print {
        background: linear-gradient(135deg, #3498db, #2980b9);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-print:hover {
        background: linear-gradient(135deg, #2980b9, #1f4e79);
        transform: translateY(-2px);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- رأس كشف الحساب -->
    <div class="statement-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="bi bi-person-circle"></i>
                    كشف حساب العميل
                </h1>
                <h3>{{ customer.name }}</h3>
                <p class="mb-0">
                    <i class="bi bi-telephone"></i> {{ customer.phone|default:"غير محدد" }} |
                    <i class="bi bi-envelope"></i> {{ customer.email|default:"غير محدد" }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-print" onclick="window.print()">
                    <i class="bi bi-printer"></i>
                    طباعة كشف الحساب
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- ملخص الرصيد -->
        <div class="col-md-4">
            <div class="balance-summary">
                <h4>الرصيد الحالي</h4>
                <div class="balance-amount">{{ balance|floatformat:2 }} ج.م</div>
                <p class="mb-0">
                    {% if balance > 0 %}
                        مدين لنا
                    {% elif balance < 0 %}
                        دائن لنا
                    {% else %}
                        متوازن
                    {% endif %}
                </p>
            </div>

            <div class="statement-card">
                <h6>معلومات العميل</h6>
                <div class="mb-2">
                    <strong>النوع:</strong> {{ customer.get_customer_type_display }}
                </div>
                <div class="mb-2">
                    <strong>حد الائتمان:</strong> {{ customer.credit_limit|floatformat:2 }} ج.م
                </div>
                <div class="mb-2">
                    <strong>أيام الائتمان:</strong> {{ customer.credit_days }} يوم
                </div>
                <div class="mb-2">
                    <strong>المندوب:</strong> {{ customer.assigned_representative.name|default:"غير محدد" }}
                </div>
            </div>

            <div class="statement-card">
                <h6>إحصائيات سريعة</h6>
                <div class="mb-2">
                    <strong>إجمالي الفواتير:</strong> {{ total_invoices|floatformat:2 }} ج.م
                </div>
                <div class="mb-2">
                    <strong>إجمالي المدفوعات:</strong> {{ total_payments|floatformat:2 }} ج.م
                </div>
                <div class="mb-2">
                    <strong>عدد الفواتير:</strong> {{ invoices.count }}
                </div>
                <div class="mb-2">
                    <strong>عدد المدفوعات:</strong> {{ payments.count }}
                </div>
            </div>
        </div>

        <!-- تفاصيل الحساب -->
        <div class="col-md-8">
            <!-- الفواتير -->
            <div class="statement-card">
                <h5 class="mb-4">
                    <i class="bi bi-receipt"></i>
                    الفواتير
                </h5>
                
                <div class="table-responsive">
                    <table class="table table-statement">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>المدفوع</th>
                                <th>المتبقي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td>
                                    <a href="{% url 'sales:sales_invoice_detail' invoice.pk %}" class="text-decoration-none">
                                        {{ invoice.invoice_number }}
                                    </a>
                                </td>
                                <td>{{ invoice.invoice_date|date:"d/m/Y" }}</td>
                                <td class="amount-debit">{{ invoice.total_amount|floatformat:2 }} ج.م</td>
                                <td class="amount-credit">{{ invoice.paid_amount|default:0|floatformat:2 }} ج.م</td>
                                <td>
                                    {% with remaining=invoice.total_amount|add:invoice.paid_amount|default:0 %}
                                        {{ remaining|floatformat:2 }} ج.م
                                    {% endwith %}
                                </td>
                                <td>
                                    {% if invoice.status == 'paid' %}
                                        <span class="badge bg-success">مدفوعة</span>
                                    {% elif invoice.status == 'sent' %}
                                        <span class="badge bg-warning">مرسلة</span>
                                    {% elif invoice.status == 'overdue' %}
                                        <span class="badge bg-danger">متأخرة</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ invoice.get_status_display }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted">لا توجد فواتير</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- المدفوعات -->
            <div class="statement-card">
                <h5 class="mb-4">
                    <i class="bi bi-cash-stack"></i>
                    المدفوعات والتحصيلات
                </h5>
                
                <div class="table-responsive">
                    <table class="table table-statement">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>النوع</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>{{ payment.payment_date|date:"d/m/Y" }}</td>
                                <td class="amount-credit">{{ payment.amount|floatformat:2 }} ج.م</td>
                                <td>{{ payment.get_payment_method_display }}</td>
                                <td>
                                    {% if payment.payment_type == 'collection' %}
                                        <span class="badge bg-success">تحصيل</span>
                                    {% else %}
                                        <span class="badge bg-info">{{ payment.get_payment_type_display }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ payment.notes|default:"-" }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted">لا توجد مدفوعات</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="text-center mt-4 mb-4">
        <a href="{% url 'sales:customer_detail' customer.pk %}" class="btn btn-primary btn-lg me-3">
            <i class="bi bi-person"></i>
            تفاصيل العميل
        </a>
        <a href="{% url 'accounts_receivable_payable' %}" class="btn btn-outline-secondary btn-lg">
            <i class="bi bi-arrow-left"></i>
            العودة للمديونيات
        </a>
    </div>
</div>
{% endblock %}
