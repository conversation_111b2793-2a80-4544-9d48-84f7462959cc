#!/usr/bin/env python
"""
سكريبت إعداد نظام الصلاحيات الجديد
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from django.contrib.auth.models import User
from permissions.models import Department, JobPosition, Permission, UserProfile, PermissionTemplate
from permissions.decorators import create_default_permissions


def create_departments():
    """إنشاء الأقسام الافتراضية"""
    departments_data = [
        ('الإدارة العامة', 'الإدارة العليا والتخطيط الاستراتيجي'),
        ('المبيعات', 'قسم المبيعات وخدمة العملاء'),
        ('المشتريات', 'قسم المشتريات والتوريد'),
        ('المخازن', 'إدارة المخازن والمواد'),
        ('الإنتاج', 'قسم الإنتاج والتصنيع'),
        ('المحاسبة', 'قسم المحاسبة والشؤون المالية'),
        ('الموارد البشرية', 'قسم شؤون الموظفين والموارد البشرية'),
        ('تقنية المعلومات', 'قسم تقنية المعلومات والدعم الفني'),
    ]
    
    created_departments = []
    for name, description in departments_data:
        department, created = Department.objects.get_or_create(
            name=name,
            defaults={'description': description, 'is_active': True}
        )
        if created:
            created_departments.append(department)
            print(f"✅ تم إنشاء القسم: {name}")
        else:
            print(f"ℹ️  القسم موجود مسبقاً: {name}")
    
    return created_departments


def create_job_positions():
    """إنشاء المناصب الوظيفية الافتراضية"""
    positions_data = [
        # الإدارة العامة
        ('الإدارة العامة', [
            ('مدير عام', 'المدير العام للشركة'),
            ('نائب المدير العام', 'نائب المدير العام'),
            ('مساعد إداري', 'مساعد إداري'),
        ]),
        # المبيعات
        ('المبيعات', [
            ('مدير المبيعات', 'مدير قسم المبيعات'),
            ('مندوب مبيعات', 'مندوب مبيعات'),
            ('موظف خدمة عملاء', 'موظف خدمة العملاء'),
        ]),
        # المشتريات
        ('المشتريات', [
            ('مدير المشتريات', 'مدير قسم المشتريات'),
            ('موظف مشتريات', 'موظف مشتريات'),
        ]),
        # المخازن
        ('المخازن', [
            ('مدير المخازن', 'مدير المخازن'),
            ('أمين مخزن', 'أمين مخزن'),
            ('موظف مخازن', 'موظف مخازن'),
        ]),
        # الإنتاج
        ('الإنتاج', [
            ('مدير الإنتاج', 'مدير قسم الإنتاج'),
            ('مشرف إنتاج', 'مشرف خط الإنتاج'),
            ('عامل إنتاج', 'عامل في خط الإنتاج'),
        ]),
        # المحاسبة
        ('المحاسبة', [
            ('مدير مالي', 'المدير المالي'),
            ('محاسب عام', 'محاسب عام'),
            ('محاسب تكاليف', 'محاسب تكاليف'),
            ('أمين صندوق', 'أمين الصندوق'),
        ]),
        # الموارد البشرية
        ('الموارد البشرية', [
            ('مدير الموارد البشرية', 'مدير قسم الموارد البشرية'),
            ('موظف شؤون موظفين', 'موظف شؤون الموظفين'),
        ]),
        # تقنية المعلومات
        ('تقنية المعلومات', [
            ('مدير تقنية المعلومات', 'مدير قسم تقنية المعلومات'),
            ('مطور نظم', 'مطور أنظمة'),
            ('فني دعم', 'فني دعم فني'),
        ]),
    ]
    
    created_positions = []
    for dept_name, positions in positions_data:
        try:
            department = Department.objects.get(name=dept_name)
            for pos_name, pos_desc in positions:
                position, created = JobPosition.objects.get_or_create(
                    name=pos_name,
                    department=department,
                    defaults={'description': pos_desc, 'is_active': True}
                )
                if created:
                    created_positions.append(position)
                    print(f"✅ تم إنشاء المنصب: {pos_name} في {dept_name}")
                else:
                    print(f"ℹ️  المنصب موجود مسبقاً: {pos_name}")
        except Department.DoesNotExist:
            print(f"❌ القسم غير موجود: {dept_name}")
    
    return created_positions


def create_permission_templates():
    """إنشاء قوالب الصلاحيات الافتراضية"""
    templates_data = [
        {
            'name': 'مدير عام',
            'job_position': 'مدير عام',
            'permissions': 'all',  # جميع الصلاحيات
            'description': 'صلاحيات المدير العام - وصول كامل لجميع الوحدات'
        },
        {
            'name': 'مدير مبيعات',
            'job_position': 'مدير المبيعات',
            'permissions': ['sales', 'customers', 'reports'],
            'description': 'صلاحيات مدير المبيعات'
        },
        {
            'name': 'مدير مشتريات',
            'job_position': 'مدير المشتريات',
            'permissions': ['purchases', 'suppliers', 'reports'],
            'description': 'صلاحيات مدير المشتريات'
        },
        {
            'name': 'محاسب',
            'job_position': 'محاسب عام',
            'permissions': ['accounting', 'banks', 'treasuries', 'reports'],
            'description': 'صلاحيات المحاسب العام'
        },
    ]
    
    created_templates = []
    for template_data in templates_data:
        try:
            job_position = JobPosition.objects.get(name=template_data['job_position'])
            template, created = PermissionTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    'job_position': job_position,
                    'description': template_data['description'],
                    'is_active': True
                }
            )
            
            if created:
                # إضافة الصلاحيات للقالب
                if template_data['permissions'] == 'all':
                    # جميع الصلاحيات
                    template.permissions.set(Permission.objects.filter(is_active=True))
                else:
                    # صلاحيات محددة
                    permissions = Permission.objects.filter(
                        module__in=template_data['permissions'],
                        is_active=True
                    )
                    template.permissions.set(permissions)
                
                created_templates.append(template)
                print(f"✅ تم إنشاء قالب الصلاحيات: {template_data['name']}")
            else:
                print(f"ℹ️  قالب الصلاحيات موجود مسبقاً: {template_data['name']}")
                
        except JobPosition.DoesNotExist:
            print(f"❌ المنصب غير موجود: {template_data['job_position']}")
    
    return created_templates


def create_user_profiles():
    """إنشاء ملفات المستخدمين"""
    created_profiles = []
    for user in User.objects.all():
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={'is_active': True}
        )
        if created:
            created_profiles.append(profile)
            print(f"✅ تم إنشاء ملف المستخدم: {user.username}")
        else:
            print(f"ℹ️  ملف المستخدم موجود مسبقاً: {user.username}")
    
    return created_profiles


def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إعداد نظام الصلاحيات الجديد...")
    print("=" * 50)
    
    # 1. إنشاء الصلاحيات الافتراضية
    print("\n📋 إنشاء الصلاحيات الافتراضية...")
    permissions = create_default_permissions()
    print(f"✅ تم إنشاء {len(permissions)} صلاحية")
    
    # 2. إنشاء الأقسام
    print("\n🏢 إنشاء الأقسام...")
    departments = create_departments()
    
    # 3. إنشاء المناصب الوظيفية
    print("\n👔 إنشاء المناصب الوظيفية...")
    positions = create_job_positions()
    
    # 4. إنشاء ملفات المستخدمين
    print("\n👥 إنشاء ملفات المستخدمين...")
    profiles = create_user_profiles()
    
    # 5. إنشاء قوالب الصلاحيات
    print("\n📝 إنشاء قوالب الصلاحيات...")
    templates = create_permission_templates()
    
    print("\n" + "=" * 50)
    print("✅ تم إعداد نظام الصلاحيات الجديد بنجاح!")
    print(f"📊 الإحصائيات:")
    print(f"   - الأقسام: {Department.objects.count()}")
    print(f"   - المناصب: {JobPosition.objects.count()}")
    print(f"   - الصلاحيات: {Permission.objects.count()}")
    print(f"   - ملفات المستخدمين: {UserProfile.objects.count()}")
    print(f"   - قوالب الصلاحيات: {PermissionTemplate.objects.count()}")
    print("\n🎉 يمكنك الآن استخدام النظام الجديد!")


if __name__ == '__main__':
    main()
