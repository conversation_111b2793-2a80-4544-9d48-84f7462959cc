from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from notifications.views import create_notification


class Command(BaseCommand):
    help = 'إنشاء إشعارات تجريبية'

    def handle(self, *args, **options):
        # الحصول على جميع المستخدمين
        users = User.objects.all()
        
        if not users.exists():
            self.stdout.write(
                self.style.ERROR('لا يوجد مستخدمين في النظام')
            )
            return
        
        # إشعارات تجريبية
        sample_notifications = [
            {
                'title': 'مرحباً بك في النظام',
                'message': 'تم تسجيل دخولك بنجاح إلى نظام أوساريك',
                'type': 'success',
                'priority': 'normal'
            },
            {
                'title': 'تنبيه مخزون',
                'message': 'منتج "لابتوب ديل" تحت الحد الأدنى (5 قطع متبقية)',
                'type': 'warning',
                'priority': 'high'
            },
            {
                'title': 'طلب جديد',
                'message': 'تم استلام طلب شراء جديد رقم #1234 بقيمة 15,000 ريال',
                'type': 'order',
                'priority': 'normal'
            },
            {
                'title': 'دفعة جديدة',
                'message': 'تم استلام دفعة من العميل أحمد محمد بقيمة 8,500 ريال',
                'type': 'payment',
                'priority': 'normal'
            },
            {
                'title': 'تحديث النظام',
                'message': 'تم تحديث النظام إلى الإصدار 2.1.0 بنجاح',
                'type': 'system',
                'priority': 'low'
            },
            {
                'title': 'رسالة جديدة',
                'message': 'لديك رسالة جديدة من سارة أحمد',
                'type': 'message',
                'priority': 'normal'
            },
            {
                'title': 'تنبيه أمني',
                'message': 'محاولة دخول من عنوان IP غير معروف',
                'type': 'error',
                'priority': 'urgent'
            },
            {
                'title': 'مستخدم جديد',
                'message': 'تم تسجيل مستخدم جديد: محمد علي',
                'type': 'user',
                'priority': 'low'
            },
            {
                'title': 'نسخة احتياطية',
                'message': 'تم إنشاء النسخة الاحتياطية اليومية بنجاح',
                'type': 'system',
                'priority': 'low'
            },
            {
                'title': 'انتهاء صلاحية',
                'message': 'منتج "دواء الصداع" سينتهي خلال 30 يوم',
                'type': 'warning',
                'priority': 'high'
            }
        ]
        
        created_count = 0
        
        for user in users:
            for notification_data in sample_notifications:
                notification = create_notification(
                    recipient=user,
                    title=notification_data['title'],
                    message=notification_data['message'],
                    notification_type=notification_data['type'],
                    priority=notification_data['priority']
                )
                
                if notification:
                    created_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'تم إنشاء {created_count} إشعار بنجاح'
            )
        )
