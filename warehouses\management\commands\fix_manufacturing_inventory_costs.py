from django.core.management.base import BaseCommand
from warehouses.models import InventoryItem
from manufacturing.models import ManufacturingInventoryTransaction, ManufacturingOrder
from decimal import Decimal

class Command(BaseCommand):
    help = 'إصلاح تكاليف المخزون للمنتجات المصنعة'

    def handle(self, *args, **options):
        self.stdout.write('بدء إصلاح تكاليف المخزون للمنتجات المصنعة...')
        
        updated_count = 0
        
        # البحث عن حركات إنتاج المنتجات التامة
        production_transactions = ManufacturingInventoryTransaction.objects.filter(
            transaction_type='finished_goods_production',
            is_processed=True
        ).select_related('manufacturing_order', 'product', 'warehouse')
        
        self.stdout.write(f'تم العثور على {production_transactions.count()} حركة إنتاج')
        
        # تجميع البيانات حسب المنتج والمخزن
        inventory_updates = {}
        
        for transaction in production_transactions:
            key = (transaction.warehouse.id, transaction.product.id)
            
            if key not in inventory_updates:
                inventory_updates[key] = {
                    'warehouse': transaction.warehouse,
                    'product': transaction.product,
                    'total_quantity': Decimal('0'),
                    'total_cost': Decimal('0'),
                    'transactions': []
                }
            
            inventory_updates[key]['total_quantity'] += transaction.quantity
            inventory_updates[key]['total_cost'] += transaction.total_cost or Decimal('0')
            inventory_updates[key]['transactions'].append(transaction)
        
        # تحديث عناصر المخزون
        for key, data in inventory_updates.items():
            try:
                inventory_item = InventoryItem.objects.get(
                    warehouse=data['warehouse'],
                    product=data['product']
                )
                
                # حساب متوسط التكلفة
                if data['total_quantity'] > 0:
                    average_cost = data['total_cost'] / data['total_quantity']
                    inventory_item.average_cost = average_cost
                    inventory_item.last_cost = average_cost
                    
                    # حساب إجمالي القيمة
                    inventory_item.total_value = inventory_item.quantity_on_hand * inventory_item.average_cost
                    
                    inventory_item.save()
                    updated_count += 1
                    
                    self.stdout.write(f"تم تحديث: {inventory_item.warehouse.name} - {inventory_item.product.name}")
                    self.stdout.write(f"  الكمية: {inventory_item.quantity_on_hand}")
                    self.stdout.write(f"  متوسط التكلفة: {inventory_item.average_cost}")
                    self.stdout.write(f"  إجمالي القيمة: {inventory_item.total_value}")
                    self.stdout.write(f"  عدد حركات الإنتاج: {len(data['transactions'])}")
                    self.stdout.write("-" * 50)
                
            except InventoryItem.DoesNotExist:
                self.stdout.write(f"تحذير: لم يتم العثور على عنصر مخزون لـ {data['product'].name} في {data['warehouse'].name}")
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إصلاح تكاليف {updated_count} عنصر مخزون بنجاح!')
        )
