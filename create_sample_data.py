#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from definitions.models import ProductDefinition, UnitDefinition
from django.contrib.auth.models import User

def create_sample_data():
    # إنشاء مستخدم إذا لم يكن موجود
    user, created = User.objects.get_or_create(
        username='admin', 
        defaults={
            'email': '<EMAIL>', 
            'is_staff': True, 
            'is_superuser': True
        }
    )
    if created:
        user.set_password('admin123')
        user.save()
        print('تم إنشاء المستخدم admin')

    # إنشاء وحدات قياس
    kg_unit, created = UnitDefinition.objects.get_or_create(
        code='KG', 
        defaults={
            'name': 'كيلوجرام', 
            'unit_type': 'weight', 
            'is_active': True, 
            'created_by': user
        }
    )
    if created:
        print('تم إنشاء وحدة الكيلوجرام')

    pc_unit, created = UnitDefinition.objects.get_or_create(
        code='PC', 
        defaults={
            'name': 'قطعة', 
            'unit_type': 'quantity', 
            'is_active': True, 
            'created_by': user
        }
    )
    if created:
        print('تم إنشاء وحدة القطعة')

    liter_unit, created = UnitDefinition.objects.get_or_create(
        code='L', 
        defaults={
            'name': 'لتر', 
            'unit_type': 'volume', 
            'is_active': True, 
            'created_by': user
        }
    )
    if created:
        print('تم إنشاء وحدة اللتر')

    # إنشاء منتجات تجريبية
    products_data = [
        {'code': 'RAW001', 'name': 'دقيق أبيض', 'cost_price': 15.50, 'unit': kg_unit},
        {'code': 'RAW002', 'name': 'سكر أبيض', 'cost_price': 12.75, 'unit': kg_unit},
        {'code': 'RAW003', 'name': 'زيت نباتي', 'cost_price': 25.00, 'unit': liter_unit},
        {'code': 'RAW004', 'name': 'بيض', 'cost_price': 2.50, 'unit': pc_unit},
        {'code': 'RAW005', 'name': 'ملح', 'cost_price': 5.00, 'unit': kg_unit},
        {'code': 'RAW006', 'name': 'خميرة', 'cost_price': 8.00, 'unit': kg_unit},
    ]

    for product_data in products_data:
        product, created = ProductDefinition.objects.get_or_create(
            code=product_data['code'],
            defaults={
                'name': product_data['name'],
                'product_type': 'raw_material',
                'cost_price': product_data['cost_price'],
                'selling_price': product_data['cost_price'] * 1.2,
                'main_unit': product_data['unit'],
                'is_active': True,
                'track_inventory': True,
                'created_by': user
            }
        )
        if created:
            print(f'تم إنشاء المنتج: {product.name} - {product.cost_price} ج.م')
        else:
            print(f'المنتج موجود: {product.name} - {product.cost_price} ج.م')

    print('\nالمنتجات المتاحة:')
    for product in ProductDefinition.objects.filter(product_type='raw_material', is_active=True):
        print(f'ID: {product.id} | {product.name} | {product.cost_price} ج.م | {product.main_unit.name}')

if __name__ == '__main__':
    create_sample_data()
