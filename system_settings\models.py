from django.db import models
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from django.core.exceptions import ValidationError
import json


class SystemSettings(models.Model):
    """إعدادات النظام الشاملة"""
    
    # معلومات الشركة
    company_name = models.CharField(
        max_length=200,
        default='شركة أوساريك',
        verbose_name="اسم الشركة"
    )
    
    company_logo = models.ImageField(
        upload_to='company/',
        null=True,
        blank=True,
        verbose_name="شعار الشركة"
    )
    
    company_address = models.TextField(
        blank=True,
        verbose_name="عنوان الشركة"
    )
    
    company_phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="هاتف الشركة"
    )
    
    company_email = models.EmailField(
        blank=True,
        verbose_name="بريد الشركة"
    )
    
    company_website = models.URLField(
        blank=True,
        verbose_name="موقع الشركة"
    )
    
    company_tax_number = models.CharField(
        max_length=50,
        blank=True,
        verbose_name="الرقم الضريبي"
    )
    
    # إعدادات النظام العامة
    system_language = models.CharField(
        max_length=10,
        choices=[
            ('ar', 'العربية'),
            ('en', 'English'),
        ],
        default='ar',
        verbose_name="لغة النظام"
    )

    system_timezone = models.CharField(
        max_length=50,
        default='Africa/Cairo',
        verbose_name="المنطقة الزمنية"
    )

    country = models.CharField(
        max_length=50,
        default='Egypt',
        verbose_name="الدولة"
    )

    currency_code = models.CharField(
        max_length=3,
        default='EGP',
        verbose_name="رمز العملة"
    )

    currency_symbol = models.CharField(
        max_length=10,
        default='ج.م',
        verbose_name="رمز العملة"
    )

    date_format = models.CharField(
        max_length=20,
        choices=[
            ('d/m/Y', 'DD/MM/YYYY'),
            ('m/d/Y', 'MM/DD/YYYY'),
            ('Y-m-d', 'YYYY-MM-DD'),
            ('d-m-Y', 'DD-MM-YYYY'),
        ],
        default='d/m/Y',
        verbose_name="تنسيق التاريخ"
    )

    time_format = models.CharField(
        max_length=10,
        choices=[
            ('24', '24 ساعة'),
            ('12', '12 ساعة'),
        ],
        default='24',
        verbose_name="تنسيق الوقت"
    )

    decimal_places = models.IntegerField(
        default=2,
        validators=[MinValueValidator(0), MaxValueValidator(6)],
        verbose_name="عدد الخانات العشرية"
    )

    thousand_separator = models.CharField(
        max_length=5,
        default=',',
        verbose_name="فاصل الآلاف"
    )

    decimal_separator = models.CharField(
        max_length=5,
        default='.',
        verbose_name="فاصل العشرية"
    )
    
    # إعدادات الأمان
    session_timeout = models.IntegerField(
        default=30,
        validators=[MinValueValidator(5), MaxValueValidator(480)],
        verbose_name="انتهاء الجلسة (دقيقة)"
    )

    password_min_length = models.IntegerField(
        default=8,
        validators=[MinValueValidator(6), MaxValueValidator(20)],
        verbose_name="الحد الأدنى لطول كلمة المرور"
    )

    require_strong_password = models.BooleanField(
        default=True,
        verbose_name="كلمة مرور قوية مطلوبة"
    )

    max_login_attempts = models.IntegerField(
        default=5,
        validators=[MinValueValidator(3), MaxValueValidator(10)],
        verbose_name="عدد محاولات تسجيل الدخول"
    )

    lockout_duration = models.IntegerField(
        default=15,
        validators=[MinValueValidator(5), MaxValueValidator(120)],
        verbose_name="مدة الحظر (دقيقة)"
    )

    require_2fa = models.BooleanField(
        default=False,
        verbose_name="المصادقة الثنائية مطلوبة"
    )

    password_expiry_days = models.IntegerField(
        default=90,
        validators=[MinValueValidator(30), MaxValueValidator(365)],
        verbose_name="انتهاء كلمة المرور (يوم)"
    )

    auto_logout_inactive = models.BooleanField(
        default=True,
        verbose_name="تسجيل خروج تلقائي عند عدم النشاط"
    )
    
    # إعدادات البريد الإلكتروني
    email_host = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="خادم البريد"
    )

    email_port = models.IntegerField(
        default=587,
        verbose_name="منفذ البريد"
    )

    email_use_tls = models.BooleanField(
        default=True,
        verbose_name="استخدام TLS"
    )

    email_use_ssl = models.BooleanField(
        default=False,
        verbose_name="استخدام SSL"
    )

    email_host_user = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="اسم مستخدم البريد"
    )

    email_host_password = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="كلمة مرور البريد"
    )

    email_from_name = models.CharField(
        max_length=100,
        default='نظام أوساريك',
        verbose_name="اسم المرسل"
    )

    email_signature = models.TextField(
        blank=True,
        verbose_name="توقيع البريد الإلكتروني"
    )
    
    # إعدادات النسخ الاحتياطي
    backup_enabled = models.BooleanField(
        default=True,
        verbose_name="تفعيل النسخ الاحتياطي"
    )

    backup_frequency = models.CharField(
        max_length=20,
        choices=[
            ('hourly', 'كل ساعة'),
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري'),
        ],
        default='daily',
        verbose_name="تكرار النسخ الاحتياطي"
    )

    backup_retention_days = models.IntegerField(
        default=30,
        validators=[MinValueValidator(7), MaxValueValidator(365)],
        verbose_name="مدة الاحتفاظ بالنسخ (يوم)"
    )

    backup_location = models.CharField(
        max_length=200,
        default='local',
        choices=[
            ('local', 'محلي'),
            ('cloud', 'سحابي'),
            ('ftp', 'FTP'),
        ],
        verbose_name="موقع النسخ الاحتياطي"
    )

    backup_compress = models.BooleanField(
        default=True,
        verbose_name="ضغط النسخ الاحتياطي"
    )

    backup_encrypt = models.BooleanField(
        default=False,
        verbose_name="تشفير النسخ الاحتياطي"
    )
    
    # إعدادات الواجهة
    theme_color = models.CharField(
        max_length=7,
        default='#667eea',
        verbose_name="لون الواجهة الأساسي"
    )

    secondary_color = models.CharField(
        max_length=7,
        default='#764ba2',
        verbose_name="اللون الثانوي"
    )

    theme_mode = models.CharField(
        max_length=10,
        choices=[
            ('light', 'فاتح'),
            ('dark', 'داكن'),
            ('auto', 'تلقائي'),
        ],
        default='light',
        verbose_name="وضع الواجهة"
    )

    sidebar_collapsed = models.BooleanField(
        default=False,
        verbose_name="الشريط الجانبي مطوي افتراضياً"
    )

    items_per_page = models.IntegerField(
        default=20,
        validators=[MinValueValidator(10), MaxValueValidator(100)],
        verbose_name="عدد العناصر في الصفحة"
    )

    show_breadcrumbs = models.BooleanField(
        default=True,
        verbose_name="إظهار مسار التنقل"
    )

    enable_animations = models.BooleanField(
        default=True,
        verbose_name="تفعيل الرسوم المتحركة"
    )

    compact_mode = models.BooleanField(
        default=False,
        verbose_name="الوضع المضغوط"
    )

    custom_css = models.TextField(
        blank=True,
        verbose_name="CSS مخصص"
    )

    custom_js = models.TextField(
        blank=True,
        verbose_name="JavaScript مخصص"
    )
    
    # إعدادات الإشعارات
    notifications_enabled = models.BooleanField(
        default=True,
        verbose_name="تفعيل الإشعارات"
    )

    email_notifications = models.BooleanField(
        default=True,
        verbose_name="إشعارات البريد الإلكتروني"
    )

    sms_notifications = models.BooleanField(
        default=False,
        verbose_name="إشعارات الرسائل النصية"
    )

    push_notifications = models.BooleanField(
        default=True,
        verbose_name="الإشعارات الفورية"
    )

    notification_sound = models.BooleanField(
        default=True,
        verbose_name="صوت الإشعارات"
    )

    notification_frequency = models.CharField(
        max_length=20,
        choices=[
            ('instant', 'فوري'),
            ('hourly', 'كل ساعة'),
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
        ],
        default='instant',
        verbose_name="تكرار الإشعارات"
    )

    # إعدادات التقارير
    default_report_format = models.CharField(
        max_length=10,
        choices=[
            ('pdf', 'PDF'),
            ('excel', 'Excel'),
            ('csv', 'CSV'),
            ('html', 'HTML'),
        ],
        default='pdf',
        verbose_name="تنسيق التقرير الافتراضي"
    )

    auto_generate_reports = models.BooleanField(
        default=False,
        verbose_name="إنشاء التقارير تلقائياً"
    )

    report_retention_days = models.IntegerField(
        default=90,
        validators=[MinValueValidator(30), MaxValueValidator(365)],
        verbose_name="مدة الاحتفاظ بالتقارير (يوم)"
    )

    # إعدادات الأداء
    enable_caching = models.BooleanField(
        default=True,
        verbose_name="تفعيل التخزين المؤقت"
    )

    cache_timeout = models.IntegerField(
        default=300,
        validators=[MinValueValidator(60), MaxValueValidator(3600)],
        verbose_name="مهلة التخزين المؤقت (ثانية)"
    )

    enable_compression = models.BooleanField(
        default=True,
        verbose_name="تفعيل الضغط"
    )

    max_file_upload_size = models.IntegerField(
        default=10,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        verbose_name="الحد الأقصى لحجم الملف (MB)"
    )
    
    # تواريخ النظام
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="محدث بواسطة"
    )

    class Meta:
        verbose_name = "إعدادات النظام"
        verbose_name_plural = "إعدادات النظام"

    def __str__(self):
        return f"إعدادات النظام - {self.company_name}"

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات النظام"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings


class UserProfile(models.Model):
    """ملف المستخدم الشخصي المتقدم"""
    
    DEPARTMENTS = [
        ('admin', 'الإدارة'),
        ('sales', 'المبيعات'),
        ('purchases', 'المشتريات'),
        ('warehouse', 'المخازن'),
        ('accounting', 'المحاسبة'),
        ('hr', 'الموارد البشرية'),
        ('it', 'تقنية المعلومات'),
        ('customer_service', 'خدمة العملاء'),
    ]
    
    POSITIONS = [
        ('ceo', 'المدير التنفيذي'),
        ('manager', 'مدير'),
        ('supervisor', 'مشرف'),
        ('senior_employee', 'موظف أول'),
        ('employee', 'موظف'),
        ('intern', 'متدرب'),
    ]
    
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        verbose_name="المستخدم"
    )
    
    # معلومات شخصية
    phone = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="رقم الهاتف"
    )
    
    mobile = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="رقم الجوال"
    )
    
    address = models.TextField(
        blank=True,
        verbose_name="العنوان"
    )
    
    birth_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ الميلاد"
    )
    
    national_id = models.CharField(
        max_length=20,
        blank=True,
        verbose_name="رقم الهوية"
    )
    
    # معلومات وظيفية
    employee_id = models.CharField(
        max_length=20,
        unique=True,
        null=True,
        blank=True,
        verbose_name="رقم الموظف"
    )
    
    department = models.CharField(
        max_length=20,
        choices=DEPARTMENTS,
        blank=True,
        verbose_name="القسم"
    )
    
    position = models.CharField(
        max_length=20,
        choices=POSITIONS,
        blank=True,
        verbose_name="المنصب"
    )
    
    hire_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ التوظيف"
    )
    
    manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='subordinates',
        verbose_name="المدير المباشر"
    )
    
    # إعدادات النظام
    avatar = models.ImageField(
        upload_to='avatars/',
        null=True,
        blank=True,
        verbose_name="الصورة الشخصية"
    )
    
    theme_preference = models.CharField(
        max_length=20,
        choices=[
            ('light', 'فاتح'),
            ('dark', 'داكن'),
            ('auto', 'تلقائي'),
        ],
        default='light',
        verbose_name="تفضيل الواجهة"
    )
    
    # حالة الحساب
    is_active = models.BooleanField(
        default=True,
        verbose_name="نشط"
    )
    
    last_login_ip = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="آخر IP تسجيل دخول"
    )
    
    failed_login_attempts = models.IntegerField(
        default=0,
        verbose_name="محاولات تسجيل الدخول الفاشلة"
    )

    # حالة الاتصال
    is_online = models.BooleanField(
        default=False,
        verbose_name="متصل الآن"
    )

    last_activity = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="آخر نشاط"
    )
    
    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "ملف المستخدم"
        verbose_name_plural = "ملفات المستخدمين"

    def save(self, *args, **kwargs):
        # تحويل القيم الفارغة إلى None لتجنب مشاكل UNIQUE constraint
        if self.employee_id == '':
            self.employee_id = None
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username}"

    def has_permission(self, permission_code):
        """فحص ما إذا كان المستخدم لديه صلاحية معينة"""
        # المدير العام لديه جميع الصلاحيات
        if self.user.is_superuser:
            return True

        # فحص الصلاحيات من خلال الأدوار
        user_roles = UserRole.objects.filter(
            user=self.user,
            is_active=True,
            role__is_active=True
        )

        for user_role in user_roles:
            if user_role.role.permissions.filter(
                code=permission_code,
                is_active=True
            ).exists():
                return True

        return False

    def has_category_permission(self, category, action):
        """فحص صلاحية فئة معينة وإجراء معين"""
        permission_code = f"{category}_{action}"
        return self.has_permission(permission_code)

    def get_user_permissions(self):
        """الحصول على جميع صلاحيات المستخدم"""
        if self.user.is_superuser:
            return Permission.objects.filter(is_active=True)

        user_roles = UserRole.objects.filter(
            user=self.user,
            is_active=True,
            role__is_active=True
        )

        permissions = Permission.objects.none()
        for user_role in user_roles:
            permissions = permissions | user_role.role.permissions.filter(is_active=True)

        return permissions.distinct()

    def get_user_roles(self):
        """الحصول على أدوار المستخدم النشطة"""
        return UserRole.objects.filter(
            user=self.user,
            is_active=True,
            role__is_active=True
        )

    def is_user_online(self):
        """تحديد ما إذا كان المستخدم متصل الآن"""
        if not self.last_activity:
            return False

        from django.utils import timezone
        from datetime import timedelta

        # اعتبار المستخدم متصل إذا كان نشطاً خلال آخر 5 دقائق
        time_threshold = timezone.now() - timedelta(minutes=5)
        return self.last_activity > time_threshold and self.is_online

    def get_online_status_display(self):
        """الحصول على نص حالة الاتصال"""
        if self.is_user_online():
            return "متصل الآن"
        elif self.last_activity:
            from django.utils import timezone
            from datetime import timedelta

            time_diff = timezone.now() - self.last_activity

            if time_diff < timedelta(minutes=1):
                return "منذ لحظات"
            elif time_diff < timedelta(hours=1):
                minutes = int(time_diff.total_seconds() / 60)
                return f"منذ {minutes} دقيقة"
            elif time_diff < timedelta(days=1):
                hours = int(time_diff.total_seconds() / 3600)
                return f"منذ {hours} ساعة"
            else:
                days = time_diff.days
                return f"منذ {days} يوم"
        else:
            return "لم يسجل دخول"


# تم نقل نماذج الصلاحيات إلى تطبيق permissions الجديد

# # تم نقل نظام الصلاحيات إلى تطبيق permissions الجديد
# النماذج القديمة محذوفة


class Notification(models.Model):
    """نموذج الإشعارات"""

    NOTIFICATION_TYPES = [
        ('info', 'معلومات'),
        ('success', 'نجاح'),
        ('warning', 'تحذير'),
        ('error', 'خطأ'),
        ('welcome', 'ترحيب'),
        ('system', 'نظام'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='system_notifications',
        verbose_name="المستخدم"
    )

    title = models.CharField(
        max_length=200,
        verbose_name="عنوان الإشعار"
    )

    message = models.TextField(
        verbose_name="رسالة الإشعار"
    )

    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPES,
        default='info',
        verbose_name="نوع الإشعار"
    )

    is_read = models.BooleanField(
        default=False,
        verbose_name="تم القراءة"
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="تاريخ الإنشاء"
    )

    read_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="تاريخ القراءة"
    )

    action_url = models.URLField(
        blank=True,
        verbose_name="رابط الإجراء"
    )

    action_text = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="نص الإجراء"
    )

    class Meta:
        verbose_name = "إشعار"
        verbose_name_plural = "الإشعارات"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.title}"

    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()

    @classmethod
    def create_notification(cls, user, title, message, notification_type='info', action_url='', action_text=''):
        """إنشاء إشعار جديد"""
        return cls.objects.create(
            user=user,
            title=title,
            message=message,
            notification_type=notification_type,
            action_url=action_url,
            action_text=action_text
        )

    @classmethod
    def create_welcome_notification(cls, user):
        """إنشاء إشعار ترحيب للمستخدم الجديد"""
        return cls.create_notification(
            user=user,
            title=f"مرحباً {user.first_name or user.username}!",
            message=f"نرحب بك في نظام إدارة أوساريك. نتمنى لك تجربة ممتعة ومفيدة.",
            notification_type='welcome',
            action_url='/settings/',
            action_text='استكشاف النظام'
        )

    @classmethod
    def create_system_notification(cls, user, title, message, action_url='', action_text=''):
        """إنشاء إشعار نظام"""
        return cls.create_notification(
            user=user,
            title=title,
            message=message,
            notification_type='system',
            action_url=action_url,
            action_text=action_text
        )
