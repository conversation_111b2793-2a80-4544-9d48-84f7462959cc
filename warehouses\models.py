from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
from datetime import datetime, date
from definitions.models import WarehouseDefinition, ProductDefinition, WarehouseLocation


# ===== إدارة المخزون =====
class InventoryItem(models.Model):
    """عنصر المخزون - يربط المنتج بالمخزن"""
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, verbose_name="المخزن")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, verbose_name="المنتج")
    location = models.ForeignKey(WarehouseLocation, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="الموقع")

    # الكميات
    quantity_on_hand = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="الكمية المتاحة")
    quantity_reserved = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="الكمية المحجوزة")
    quantity_ordered = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="الكمية المطلوبة")

    # حدود المخزون
    minimum_stock = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="الحد الأدنى للمخزون")
    maximum_stock = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="الحد الأقصى للمخزون")
    reorder_point = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="نقطة إعادة الطلب")
    reorder_quantity = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="كمية إعادة الطلب")

    # التكاليف
    average_cost = models.DecimalField(max_digits=15, decimal_places=4, default=0, verbose_name="متوسط التكلفة")
    last_cost = models.DecimalField(max_digits=15, decimal_places=4, default=0, verbose_name="آخر تكلفة")
    total_value = models.DecimalField(max_digits=20, decimal_places=4, default=0, verbose_name="إجمالي القيمة")

    # تواريخ
    last_received_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ آخر استلام")
    last_issued_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ آخر صرف")
    last_counted_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ آخر جرد")

    # معلومات إضافية
    abc_classification = models.CharField(max_length=1, choices=[
        ('A', 'فئة A - عالية القيمة'),
        ('B', 'فئة B - متوسطة القيمة'),
        ('C', 'فئة C - منخفضة القيمة')
    ], default='C', verbose_name="تصنيف ABC")

    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "عنصر مخزون"
        verbose_name_plural = "عناصر المخزون"
        unique_together = ['warehouse', 'product']
        ordering = ['warehouse', 'product']

    def __str__(self):
        return f"{self.warehouse.name} - {self.product.name}"

    @property
    def available_quantity(self):
        """الكمية المتاحة للصرف"""
        return self.quantity_on_hand - self.quantity_reserved

    @property
    def is_low_stock(self):
        """هل المخزون منخفض؟"""
        return self.quantity_on_hand <= self.minimum_stock

    @property
    def is_out_of_stock(self):
        """هل المخزون نافد؟"""
        return self.quantity_on_hand <= 0

    @property
    def stock_status(self):
        """حالة المخزون"""
        if self.is_out_of_stock:
            return "نافد"
        elif self.is_low_stock:
            return "منخفض"
        elif self.quantity_on_hand >= self.maximum_stock:
            return "مرتفع"
        else:
            return "طبيعي"

    def save(self, *args, **kwargs):
        """حفظ عنصر المخزون مع حساب إجمالي القيمة"""
        # حساب إجمالي القيمة بناءً على متوسط التكلفة
        if self.average_cost and self.quantity_on_hand:
            self.total_value = self.quantity_on_hand * self.average_cost
        else:
            self.total_value = 0

        super().save(*args, **kwargs)


# ===== حركات المخزون =====
class InventoryTransaction(models.Model):
    """حركات المخزون"""
    TRANSACTION_TYPES = [
        ('receipt', 'استلام'),
        ('issue', 'صرف'),
        ('transfer_in', 'تحويل وارد'),
        ('transfer_out', 'تحويل صادر'),
        ('adjustment_increase', 'تسوية زيادة'),
        ('adjustment_decrease', 'تسوية نقص'),
        ('adjustment_set', 'تحديد كمية'),
        ('adjustment_revalue', 'إعادة تقييم'),
        ('count_adjustment', 'تسوية جرد'),
        ('return_in', 'مرتجع وارد'),
        ('return_out', 'مرتجع صادر'),
        ('opening_balance', 'رصيد افتتاحي'),
        ('physical_count', 'جرد فعلي'),
    ]

    # معلومات أساسية
    transaction_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الحركة")
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES, verbose_name="نوع الحركة")
    transaction_date = models.DateTimeField(default=datetime.now, verbose_name="تاريخ الحركة")

    # المخزون المتأثر
    inventory_item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, verbose_name="عنصر المخزون")

    # الكميات والتكاليف
    quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=4, default=0, verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=20, decimal_places=4, default=0, verbose_name="إجمالي التكلفة")

    # الأرصدة قبل وبعد الحركة
    balance_before = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الرصيد قبل الحركة")
    balance_after = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الرصيد بعد الحركة")

    # معلومات إضافية
    reference_number = models.CharField(max_length=100, blank=True, verbose_name="رقم المرجع")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    # معلومات التحويل (إذا كانت حركة تحويل)
    transfer_to_warehouse = models.ForeignKey(
        WarehouseDefinition,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='transfer_in_transactions',
        verbose_name="المخزن المحول إليه"
    )

    # معلومات النظام
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    is_approved = models.BooleanField(default=False, verbose_name="معتمد")
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_inventory_transactions',
        verbose_name="اعتمد بواسطة"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")

    class Meta:
        verbose_name = "حركة مخزون"
        verbose_name_plural = "حركات المخزون"
        ordering = ['-transaction_date', '-created_at']

    def __str__(self):
        return f"{self.transaction_number} - {self.get_transaction_type_display()}"

    def save(self, *args, **kwargs):
        # حساب إجمالي التكلفة
        self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)


# ===== تحويلات المخزون =====
class StockTransfer(models.Model):
    """تحويلات المخزون بين المخازن"""
    TRANSFER_STATUS = [
        ('draft', 'مسودة'),
        ('pending', 'في الانتظار'),
        ('in_transit', 'في الطريق'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
    ]

    # معلومات أساسية
    transfer_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التحويل")
    transfer_date = models.DateTimeField(default=datetime.now, verbose_name="تاريخ التحويل")

    # المخازن
    from_warehouse = models.ForeignKey(
        WarehouseDefinition,
        on_delete=models.CASCADE,
        related_name='transfers_out',
        verbose_name="من المخزن"
    )
    to_warehouse = models.ForeignKey(
        WarehouseDefinition,
        on_delete=models.CASCADE,
        related_name='transfers_in',
        verbose_name="إلى المخزن"
    )

    # الحالة والتواريخ
    status = models.CharField(max_length=20, choices=TRANSFER_STATUS, default='draft', verbose_name="الحالة")
    shipped_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الشحن")
    received_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاستلام")
    expected_date = models.DateTimeField(null=True, blank=True, verbose_name="التاريخ المتوقع")

    # معلومات إضافية
    reason = models.TextField(blank=True, verbose_name="سبب التحويل")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    # معلومات النظام
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    # الاعتماد
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_stock_transfers',
        verbose_name="اعتمد بواسطة"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")

    class Meta:
        verbose_name = "تحويل مخزون"
        verbose_name_plural = "تحويلات المخزون"
        ordering = ['-transfer_date', '-created_at']

    def __str__(self):
        return f"{self.transfer_number} - {self.from_warehouse.name} → {self.to_warehouse.name}"

    @property
    def total_items(self):
        """إجمالي عدد الأصناف"""
        return self.items.count()

    @property
    def total_quantity(self):
        """إجمالي الكمية"""
        return sum(item.quantity for item in self.items.all())


class StockTransferItem(models.Model):
    """أصناف تحويل المخزون"""
    transfer = models.ForeignKey(StockTransfer, on_delete=models.CASCADE, related_name='items', verbose_name="التحويل")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, verbose_name="المنتج")

    # الكميات
    quantity_requested = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية المطلوبة")
    quantity_shipped = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="الكمية المشحونة")
    quantity_received = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="الكمية المستلمة")

    # التكاليف
    unit_cost = models.DecimalField(max_digits=15, decimal_places=4, default=0, verbose_name="تكلفة الوحدة")
    total_cost = models.DecimalField(max_digits=20, decimal_places=4, default=0, verbose_name="إجمالي التكلفة")

    # معلومات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "صنف تحويل"
        verbose_name_plural = "أصناف التحويل"
        unique_together = ['transfer', 'product']

    def __str__(self):
        return f"{self.transfer.transfer_number} - {self.product.name}"

    @property
    def quantity(self):
        """الكمية (للتوافق مع النماذج الأخرى)"""
        return self.quantity_requested


# ===== جرد المخزون =====
class StockCount(models.Model):
    """جرد المخزون"""
    COUNT_STATUS = [
        ('draft', 'مسودة'),
        ('in_progress', 'قيد التنفيذ'),
        ('completed', 'مكتمل'),
        ('approved', 'معتمد'),
        ('cancelled', 'ملغي'),
    ]

    COUNT_TYPES = [
        ('full', 'جرد شامل'),
        ('partial', 'جرد جزئي'),
        ('cycle', 'جرد دوري'),
        ('spot', 'جرد عشوائي'),
    ]

    # معلومات أساسية
    count_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الجرد")
    count_date = models.DateTimeField(default=datetime.now, verbose_name="تاريخ الجرد")
    count_type = models.CharField(max_length=20, choices=COUNT_TYPES, verbose_name="نوع الجرد")

    # النطاق
    warehouse = models.ForeignKey(WarehouseDefinition, on_delete=models.CASCADE, verbose_name="المخزن")
    location = models.ForeignKey(WarehouseLocation, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="الموقع")

    # الحالة والتواريخ
    status = models.CharField(max_length=20, choices=COUNT_STATUS, default='draft', verbose_name="الحالة")
    start_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ البداية")
    end_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الانتهاء")

    # معلومات إضافية
    description = models.TextField(blank=True, verbose_name="الوصف")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    # معلومات النظام
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    # الاعتماد
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_stock_counts',
        verbose_name="اعتمد بواسطة"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")

    class Meta:
        verbose_name = "جرد مخزون"
        verbose_name_plural = "جرد المخزون"
        ordering = ['-count_date', '-created_at']

    def __str__(self):
        return f"{self.count_number} - {self.warehouse.name}"

    @property
    def total_items_counted(self):
        """إجمالي الأصناف المجردة"""
        return self.items.count()

    @property
    def total_variances(self):
        """إجمالي الفروقات"""
        return self.items.filter(variance_quantity__ne=0).count()


class StockCountItem(models.Model):
    """أصناف جرد المخزون"""
    count = models.ForeignKey(StockCount, on_delete=models.CASCADE, related_name='items', verbose_name="الجرد")
    product = models.ForeignKey(ProductDefinition, on_delete=models.CASCADE, verbose_name="المنتج")

    # الكميات
    system_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية النظامية")
    physical_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية الفعلية")
    variance_quantity = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name="فرق الكمية")

    # التكاليف
    unit_cost = models.DecimalField(max_digits=15, decimal_places=4, default=0, verbose_name="تكلفة الوحدة")
    variance_value = models.DecimalField(max_digits=20, decimal_places=4, default=0, verbose_name="قيمة الفرق")

    # معلومات إضافية
    counted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="جرد بواسطة")
    counted_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الجرد")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "صنف جرد"
        verbose_name_plural = "أصناف الجرد"
        unique_together = ['count', 'product']

    def __str__(self):
        return f"{self.count.count_number} - {self.product.name}"

    def save(self, *args, **kwargs):
        # حساب فرق الكمية وقيمة الفرق
        self.variance_quantity = self.physical_quantity - self.system_quantity
        self.variance_value = self.variance_quantity * self.unit_cost
        super().save(*args, **kwargs)


# ===== تسويات المخزون =====
class StockAdjustment(models.Model):
    """تسويات المخزون"""
    ADJUSTMENT_TYPES = [
        ('increase', 'زيادة'),
        ('decrease', 'نقص'),
        ('correction', 'تصحيح'),
        ('damage', 'تالف'),
        ('expired', 'منتهي الصلاحية'),
        ('lost', 'مفقود'),
        ('found', 'موجود'),
    ]

    # معلومات أساسية
    adjustment_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التسوية")
    adjustment_date = models.DateTimeField(default=datetime.now, verbose_name="تاريخ التسوية")
    adjustment_type = models.CharField(max_length=20, choices=ADJUSTMENT_TYPES, verbose_name="نوع التسوية")

    # المخزون المتأثر
    inventory_item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, verbose_name="عنصر المخزون")

    # الكميات والتكاليف
    quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name="الكمية")
    unit_cost = models.DecimalField(max_digits=15, decimal_places=4, default=0, verbose_name="تكلفة الوحدة")
    total_value = models.DecimalField(max_digits=20, decimal_places=4, default=0, verbose_name="إجمالي القيمة")

    # معلومات إضافية
    reason = models.TextField(verbose_name="السبب")
    reference_number = models.CharField(max_length=100, blank=True, verbose_name="رقم المرجع")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    # معلومات النظام
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    # الاعتماد
    is_approved = models.BooleanField(default=False, verbose_name="معتمد")
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_stock_adjustments',
        verbose_name="اعتمد بواسطة"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاعتماد")

    class Meta:
        verbose_name = "تسوية مخزون"
        verbose_name_plural = "تسويات المخزون"
        ordering = ['-adjustment_date', '-created_at']

    def __str__(self):
        return f"{self.adjustment_number} - {self.get_adjustment_type_display()}"

    def save(self, *args, **kwargs):
        # حساب إجمالي القيمة
        self.total_value = self.quantity * self.unit_cost
        super().save(*args, **kwargs)


# ===== تنبيهات المخزون =====
class StockAlert(models.Model):
    """تنبيهات المخزون"""
    ALERT_TYPES = [
        ('low_stock', 'مخزون منخفض'),
        ('out_of_stock', 'مخزون نافد'),
        ('overstock', 'مخزون زائد'),
        ('expiry_warning', 'تحذير انتهاء صلاحية'),
        ('reorder_point', 'نقطة إعادة الطلب'),
    ]

    ALERT_PRIORITY = [
        ('low', 'منخفضة'),
        ('medium', 'متوسطة'),
        ('high', 'عالية'),
        ('critical', 'حرجة'),
    ]

    # معلومات أساسية
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES, verbose_name="نوع التنبيه")
    priority = models.CharField(max_length=10, choices=ALERT_PRIORITY, default='medium', verbose_name="الأولوية")

    # المخزون المتأثر
    inventory_item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, verbose_name="عنصر المخزون")

    # معلومات التنبيه
    message = models.TextField(verbose_name="رسالة التنبيه")
    threshold_value = models.DecimalField(max_digits=15, decimal_places=3, null=True, blank=True, verbose_name="قيمة العتبة")
    current_value = models.DecimalField(max_digits=15, decimal_places=3, null=True, blank=True, verbose_name="القيمة الحالية")

    # الحالة
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_acknowledged = models.BooleanField(default=False, verbose_name="تم الاطلاع")
    acknowledged_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='acknowledged_stock_alerts',
        verbose_name="اطلع عليه"
    )
    acknowledged_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الاطلاع")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الحل")

    class Meta:
        verbose_name = "تنبيه مخزون"
        verbose_name_plural = "تنبيهات المخزون"
        ordering = ['-created_at', 'priority']

    def __str__(self):
        return f"{self.get_alert_type_display()} - {self.inventory_item.product.name}"


