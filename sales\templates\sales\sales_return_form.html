{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .return-form {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin: 20px 0;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding: 25px;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        background: #f8f9fa;
    }
    
    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #dc3545;
    }
    
    .items-table {
        margin-top: 20px;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .btn-add-item {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        padding: 12px 25px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-add-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .btn-load-invoice {
        background: linear-gradient(45deg, #17a2b8, #138496);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .btn-load-invoice:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(23, 162, 184, 0.3);
        color: white;
    }
    
    .summary-section {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 25px;
        border-radius: 10px;
        margin-top: 20px;
    }
    
    .invoice-info {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin-top: 10px;
        border-left: 4px solid #2196f3;
    }
    
    .item-row {
        transition: all 0.3s;
    }
    
    .item-row:hover {
        background: #f8f9fa;
    }
    
    .delete-btn {
        background: #dc3545;
        border: none;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 0.8rem;
    }
    
    .total-display {
        font-size: 1.1rem;
        font-weight: 600;
        color: #dc3545;
    }
    
    .condition-select {
        font-size: 0.9rem;
    }
    
    .reason-details {
        background: #fff3cd;
        padding: 15px;
        border-radius: 8px;
        margin-top: 10px;
        border-left: 4px solid #ffc107;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="return-form">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="text-danger">
                        <i class="bi bi-arrow-return-left"></i>
                        {{ title }}
                    </h2>
                    <a href="{% url 'sales:sales_return_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>

                <form method="post" id="return-form">
                    {% csrf_token %}
                    
                    <!-- معلومات المرتجع الأساسية -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-info-circle"></i>
                            معلومات المرتجع
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الفاتورة الأصلية</label>
                                    <div class="input-group">
                                        {{ form.original_invoice }}
                                        <button type="button" class="btn btn-load-invoice" id="load-invoice">
                                            <i class="bi bi-download"></i>
                                            تحميل العناصر
                                        </button>
                                    </div>
                                    <div id="invoice-info" class="invoice-info" style="display: none;">
                                        <small class="text-muted">معلومات الفاتورة ستظهر هنا</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">العميل</label>
                                    {{ form.customer }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">المندوب</label>
                                    {{ form.representative }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ المرتجع</label>
                                    {{ form.return_date }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">سبب المرتجع</label>
                                    {{ form.reason }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">تفاصيل السبب</label>
                                    {{ form.reason_details }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- عناصر المرتجع -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-list-ul"></i>
                            عناصر المرتجع
                        </h4>
                        
                        {{ formset.management_form }}
                        
                        <div class="table-responsive items-table">
                            <table class="table table-bordered mb-0" id="items-table">
                                <thead class="table-danger">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية المرتجعة</th>
                                        <th>سعر الوحدة</th>
                                        <th>حالة المنتج</th>
                                        <th>الإجمالي</th>
                                        <th>حذف</th>
                                    </tr>
                                </thead>
                                <tbody id="items-tbody">
                                    {% for form in formset %}
                                        <tr class="item-row">
                                            <td>
                                                {{ form.product }}
                                                {{ form.id }}
                                            </td>
                                            <td>{{ form.quantity }}</td>
                                            <td>{{ form.unit_price }}</td>
                                            <td>{{ form.condition }}</td>
                                            <td class="item-total total-display">0.00</td>
                                            <td>
                                                {% if not forloop.first %}
                                                    {{ form.DELETE }}
                                                    <label for="{{ form.DELETE.id_for_label }}" class="delete-btn">
                                                        <i class="bi bi-trash"></i>
                                                    </label>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-add-item" id="add-item">
                                <i class="bi bi-plus-circle"></i>
                                إضافة عنصر جديد
                            </button>
                        </div>
                    </div>

                    <!-- ملخص المرتجع -->
                    <div class="summary-section">
                        <div class="row">
                            <div class="col-md-8">
                                <h5>
                                    <i class="bi bi-clipboard-data"></i>
                                    ملخص المرتجع
                                </h5>
                                <p class="mb-0">تأكد من صحة جميع البيانات قبل الحفظ</p>
                                <div class="reason-details mt-3" id="reason-display" style="display: none;">
                                    <strong>سبب المرتجع:</strong> <span id="reason-text"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>عدد العناصر:</span>
                                    <span id="total-items">0</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>إجمالي الكمية:</span>
                                    <span id="total-quantity">0</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>القيمة الإجمالية:</strong>
                                    <strong id="total-value">0.00 ج.م</strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-danger btn-lg">
                            <i class="bi bi-save"></i>
                            حفظ المرتجع
                        </button>
                        <a href="{% url 'sales:sales_return_list' %}" class="btn btn-secondary btn-lg ms-2">
                            <i class="bi bi-x-circle"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // تحميل عناصر الفاتورة
    $('#load-invoice').click(function() {
        var invoiceId = $('#id_original_invoice').val();
        if (!invoiceId) {
            alert('يرجى اختيار فاتورة أولاً');
            return;
        }
        
        var btn = $(this);
        var originalText = btn.html();
        btn.html('<i class="bi bi-hourglass-split"></i> جاري التحميل...').prop('disabled', true);
        
        $.get('/sales/api/invoice-items/' + invoiceId + '/', function(data) {
            if (data.success) {
                // تحديث العميل والمندوب
                $('#id_customer').val(data.customer_id);
                if (data.representative_id) {
                    $('#id_representative').val(data.representative_id);
                }
                
                // مسح العناصر الحالية
                $('#items-tbody tr').not(':first').remove();
                
                // إضافة عناصر الفاتورة
                data.items.forEach(function(item, index) {
                    if (index === 0) {
                        // تحديث الصف الأول
                        var firstRow = $('#items-tbody tr:first');
                        firstRow.find('[id*="product"]').val(item.product_id);
                        firstRow.find('[id*="quantity"]').val(item.quantity);
                        firstRow.find('[id*="unit_price"]').val(item.unit_price);
                    } else {
                        // إضافة صفوف جديدة
                        addNewRow(item);
                    }
                });
                
                calculateSummary();
                
                var info = `
                    <strong>تم تحميل ${data.items.length} عنصر من الفاتورة</strong><br>
                    <small>يمكنك الآن تعديل الكميات المرتجعة حسب الحاجة</small>
                `;
                $('#invoice-info').html(info).show();
            }
        }).fail(function() {
            alert('حدث خطأ في تحميل عناصر الفاتورة');
        }).always(function() {
            btn.html(originalText).prop('disabled', false);
        });
    });

    // حساب إجمالي الصف
    function calculateRowTotal(row) {
        var quantity = parseFloat(row.find('[id*="quantity"]').val()) || 0;
        var unitPrice = parseFloat(row.find('[id*="unit_price"]').val()) || 0;
        
        var total = quantity * unitPrice;
        row.find('.item-total').text(total.toFixed(2));
        calculateSummary();
    }

    // حساب ملخص المرتجع
    function calculateSummary() {
        var totalItems = 0;
        var totalQuantity = 0;
        var totalValue = 0;
        
        $('.item-row').each(function() {
            var quantity = parseFloat($(this).find('[id*="quantity"]').val()) || 0;
            var value = parseFloat($(this).find('.item-total').text()) || 0;
            
            if (quantity > 0) {
                totalItems++;
                totalQuantity += quantity;
                totalValue += value;
            }
        });
        
        $('#total-items').text(totalItems);
        $('#total-quantity').text(totalQuantity.toFixed(2));
        $('#total-value').text(totalValue.toFixed(2) + ' ج.م');
    }

    // إضافة صف جديد
    function addNewRow(itemData = null) {
        var totalForms = $('#id_form-TOTAL_FORMS');
        var formNum = parseInt(totalForms.val());
        
        var newRow = $('.item-row:first').clone();
        newRow.find('input, select').each(function() {
            var name = $(this).attr('name');
            if (name) {
                name = name.replace('-0-', '-' + formNum + '-');
                $(this).attr('name', name);
                $(this).attr('id', 'id_' + name);
                if (!itemData) {
                    $(this).val('');
                }
            }
        });
        
        if (itemData) {
            newRow.find('[id*="product"]').val(itemData.product_id);
            newRow.find('[id*="quantity"]').val(itemData.quantity);
            newRow.find('[id*="unit_price"]').val(itemData.unit_price);
        }
        
        newRow.find('.item-total').text('0.00');
        $('#items-tbody').append(newRow);
        
        totalForms.val(formNum + 1);
    }

    // تحديث الحسابات عند تغيير القيم
    $(document).on('input', '[id*="quantity"], [id*="unit_price"]', function() {
        calculateRowTotal($(this).closest('tr'));
    });

    // إضافة صف جديد
    $('#add-item').click(function() {
        addNewRow();
    });

    // حذف صف
    $(document).on('change', '[id*="DELETE"]', function() {
        if ($(this).is(':checked')) {
            $(this).closest('tr').hide();
            calculateSummary();
        }
    });

    // تحديث عرض السبب
    $('#id_reason').change(function() {
        var reasonText = $(this).find('option:selected').text();
        if ($(this).val()) {
            $('#reason-text').text(reasonText);
            $('#reason-display').show();
        } else {
            $('#reason-display').hide();
        }
    });

    // حساب أولي
    calculateSummary();
    
    // تحديد تاريخ اليوم كافتراضي
    if (!$('#id_return_date').val()) {
        var today = new Date().toISOString().split('T')[0];
        $('#id_return_date').val(today);
    }
});
</script>
{% endblock %}
