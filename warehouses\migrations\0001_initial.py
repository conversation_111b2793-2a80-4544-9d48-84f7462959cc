# Generated by Django 5.2.4 on 2025-07-11 14:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('definitions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='InventoryTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الحركة')),
                ('transaction_type', models.CharField(choices=[('in', 'إدخال'), ('out', 'إخراج'), ('transfer', 'نقل'), ('adjustment', 'تسوية')], max_length=20, verbose_name='نوع الحركة')),
                ('transaction_reason', models.CharField(choices=[('purchase', 'شراء'), ('sale', 'بيع'), ('return_in', 'مرتجع وارد'), ('return_out', 'مرتجع صادر'), ('damage', 'تالف'), ('expired', 'منتهي الصلاحية'), ('transfer', 'نقل بين مخازن'), ('adjustment', 'تسوية جرد'), ('production', 'إنتاج'), ('initial', 'رصيد افتتاحي')], max_length=20, verbose_name='سبب الحركة')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('transaction_date', models.DateTimeField(verbose_name='تاريخ الحركة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='الصنف')),
                ('to_warehouse', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='transfers_in', to='definitions.warehousedefinition', verbose_name='المخزن المستقبل')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjustment_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التسوية')),
                ('adjustment_type', models.CharField(choices=[('increase', 'زيادة'), ('decrease', 'نقص'), ('correction', 'تصحيح')], max_length=20, verbose_name='نوع التسوية')),
                ('adjustment_date', models.DateTimeField(verbose_name='تاريخ التسوية')),
                ('reason', models.TextField(verbose_name='سبب التسوية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_adjustments', to=settings.AUTH_USER_MODEL, verbose_name='معتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'تسوية مخزون',
                'verbose_name_plural': 'تسويات المخزون',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockAdjustmentItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية الحالية')),
                ('actual_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية الفعلية')),
                ('difference', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الفرق')),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='تكلفة الوحدة')),
                ('total_cost_difference', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='فرق التكلفة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('adjustment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='warehouses.stockadjustment', verbose_name='التسوية')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='الصنف')),
            ],
            options={
                'verbose_name': 'عنصر تسوية',
                'verbose_name_plural': 'عناصر التسوية',
            },
        ),
        migrations.CreateModel(
            name='InventoryBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي التكلفة')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='الصنف')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.warehousedefinition', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'رصيد مخزون',
                'verbose_name_plural': 'أرصدة المخزون',
                'unique_together': {('warehouse', 'product')},
            },
        ),
    ]
