<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نموذج بسيط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 50px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-label {
            color: #333;
            font-weight: 600;
        }
        .form-control {
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn-primary {
            background: #007bff;
            border: none;
            padding: 10px 30px;
        }
        .alert {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="text-center mb-4">اختبار نموذج إضافة شخص مبسط</h2>
        
        <div class="alert alert-info">
            <strong>تعليمات:</strong>
            <ol>
                <li>تأكد من تسجيل الدخول أولاً: <a href="http://127.0.0.1:8000/accounts/login/" target="_blank">تسجيل الدخول</a></li>
                <li>املأ الحقول المطلوبة أدناه</li>
                <li>اضغط زر "إضافة الشخص"</li>
                <li>راقب console المتصفح للرسائل</li>
            </ol>
        </div>
        
        <form id="testForm" action="http://127.0.0.1:8000/definitions/persons/create/" method="post">
            <!-- سيتم إضافة CSRF token بواسطة JavaScript -->
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="code" class="form-label">كود الشخص *</label>
                    <input type="text" class="form-control" id="code" name="code" value="TEST001" required>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">الاسم *</label>
                    <input type="text" class="form-control" id="name" name="name" value="شخص تجريبي" required>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="person_type" class="form-label">نوع الشخص *</label>
                    <select class="form-control" id="person_type" name="person_type" required>
                        <option value="">-- اختر نوع الشخص --</option>
                        <option value="customer" selected>عميل</option>
                        <option value="supplier">مورد</option>
                        <option value="employee">موظف</option>
                        <option value="both">عميل ومورد</option>
                        <option value="other">أخرى</option>
                    </select>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">الهاتف</label>
                    <input type="text" class="form-control" id="phone" name="phone" value="01234567890">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="email" class="form-label">البريد الإلكتروني</label>
                <input type="email" class="form-control" id="email" name="email" value="<EMAIL>">
            </div>
            
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                    <label class="form-check-label" for="is_active">
                        شخص نشط
                    </label>
                </div>
            </div>
            
            <div class="text-center">
                <button type="submit" class="btn btn-primary" id="submitBtn">إضافة الشخص</button>
            </div>
        </form>
        
        <div id="messages" class="mt-3"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Simple test form loaded');
            
            const form = document.getElementById('testForm');
            const submitBtn = document.getElementById('submitBtn');
            const messagesDiv = document.getElementById('messages');
            
            // محاولة الحصول على CSRF token
            fetch('http://127.0.0.1:8000/definitions/persons/create/')
                .then(response => response.text())
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const csrfToken = doc.querySelector('[name=csrfmiddlewaretoken]');
                    
                    if (csrfToken) {
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = 'csrfmiddlewaretoken';
                        hiddenInput.value = csrfToken.value;
                        form.insertBefore(hiddenInput, form.firstChild);
                        console.log('CSRF token added:', csrfToken.value);
                        
                        messagesDiv.innerHTML = '<div class="alert alert-success">تم الحصول على CSRF token بنجاح</div>';
                    } else {
                        console.error('CSRF token not found');
                        messagesDiv.innerHTML = '<div class="alert alert-danger">لم يتم العثور على CSRF token</div>';
                    }
                })
                .catch(error => {
                    console.error('Error fetching CSRF token:', error);
                    messagesDiv.innerHTML = '<div class="alert alert-danger">خطأ في الحصول على CSRF token: ' + error.message + '</div>';
                });
            
            form.addEventListener('submit', function(e) {
                console.log('Form submission started');
                
                // التحقق من الحقول المطلوبة
                const code = document.getElementById('code').value.trim();
                const name = document.getElementById('name').value.trim();
                const personType = document.getElementById('person_type').value;
                
                console.log('Form data:', {code, name, personType});
                
                if (!code || !name || !personType) {
                    e.preventDefault();
                    alert('يرجى ملء جميع الحقول المطلوبة');
                    return false;
                }
                
                console.log('Form validation passed, submitting...');
                
                // تعطيل الزر
                submitBtn.disabled = true;
                submitBtn.textContent = 'جاري الإرسال...';
                
                messagesDiv.innerHTML = '<div class="alert alert-info">جاري إرسال النموذج...</div>';
            });
        });
    </script>
</body>
</html>
