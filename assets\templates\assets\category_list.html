{% extends 'base.html' %}
{% load static %}

{% block title %}فئات الأصول الثابتة{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
    }
    
    .page-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }
    
    .page-subtitle {
        opacity: 0.9;
        margin-top: 0.5rem;
    }
    
    .controls-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .search-container {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .search-input {
        flex: 1;
        min-width: 300px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.95rem;
    }
    
    .search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }
    
    .btn-add {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        color: white;
        text-decoration: none;
    }
    
    .categories-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .categories-header {
        background: #f8f9fa;
        padding: 1.5rem;
        border-bottom: 2px solid #e5e7eb;
    }
    
    .categories-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #374151;
        margin: 0;
    }
    
    .category-card {
        border-bottom: 1px solid #f3f4f6;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }
    
    .category-card:hover {
        background: #f9fafb;
    }
    
    .category-card:last-child {
        border-bottom: none;
    }
    
    .category-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }
    
    .category-name {
        font-size: 1.2rem;
        font-weight: 600;
        color: #374151;
        margin: 0;
    }
    
    .category-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }
    
    .status-active {
        background: #d1fae5;
        color: #065f46;
    }
    
    .status-inactive {
        background: #fee2e2;
        color: #991b1b;
    }
    
    .category-description {
        color: #6b7280;
        margin-bottom: 1rem;
        line-height: 1.5;
    }
    
    .category-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .depreciation-rate {
        background: #f0f9ff;
        color: #0369a1;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .category-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-action {
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.85rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        transition: all 0.3s ease;
    }
    
    .btn-edit {
        background: #667eea;
        color: white;
    }
    
    .btn-edit:hover {
        background: #5a67d8;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }
    
    .btn-delete {
        background: #ef4444;
        color: white;
    }
    
    .btn-delete:hover {
        background: #dc2626;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6b7280;
    }
    
    .empty-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="bi bi-folder me-3"></i>
            فئات الأصول الثابتة
        </h1>
        <p class="page-subtitle">إدارة وتنظيم فئات الأصول الثابتة ومعدلات الاستهلاك</p>
    </div>

    <!-- أدوات التحكم -->
    <div class="controls-section">
        <div class="search-container">
            <form method="get" class="d-flex flex-grow-1 gap-2">
                <input type="text" 
                       name="search" 
                       value="{{ search }}" 
                       placeholder="البحث في فئات الأصول..." 
                       class="search-input">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="bi bi-search"></i>
                </button>
            </form>
            
            <a href="{% url 'assets:category_create' %}" class="btn-add">
                <i class="bi bi-plus-circle"></i>
                إضافة فئة جديدة
            </a>
        </div>
    </div>

    <!-- قائمة الفئات -->
    <div class="categories-container">
        <div class="categories-header">
            <h3 class="categories-title">
                <i class="bi bi-list me-2"></i>
                قائمة الفئات
                {% if categories %}
                    <span class="badge bg-primary ms-2">{{ categories|length }}</span>
                {% endif %}
            </h3>
        </div>

        {% if categories %}
            {% for category in categories %}
            <div class="category-card">
                <div class="category-header">
                    <h4 class="category-name">{{ category.name }}</h4>
                    <span class="category-status {% if category.is_active %}status-active{% else %}status-inactive{% endif %}">
                        {% if category.is_active %}نشط{% else %}غير نشط{% endif %}
                    </span>
                </div>
                
                {% if category.description %}
                <div class="category-description">{{ category.description }}</div>
                {% endif %}
                
                <div class="category-meta">
                    <div class="depreciation-rate">
                        <i class="bi bi-graph-down me-1"></i>
                        معدل الاستهلاك: {{ category.depreciation_rate }}% سنوياً
                    </div>
                    
                    <div class="category-actions">
                        <a href="{% url 'assets:category_edit' category.pk %}" class="btn-action btn-edit">
                            <i class="bi bi-pencil"></i>
                            تعديل
                        </a>
                        <a href="{% url 'assets:category_delete' category.pk %}" 
                           class="btn-action btn-delete"
                           onclick="return confirm('هل أنت متأكد من حذف هذه الفئة؟')">
                            <i class="bi bi-trash"></i>
                            حذف
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="bi bi-folder-x"></i>
                </div>
                <h5>لا توجد فئات أصول</h5>
                <p>{% if search %}لم يتم العثور على فئات تطابق البحث "{{ search }}"{% else %}لم يتم إنشاء أي فئات أصول بعد{% endif %}</p>
                {% if not search %}
                <a href="{% url 'assets:category_create' %}" class="btn-add mt-3">
                    <i class="bi bi-plus-circle"></i>
                    إضافة أول فئة
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>

    <!-- الترقيم -->
    {% if categories.has_other_pages %}
    <div class="pagination-container">
        <nav aria-label="ترقيم الصفحات">
            <ul class="pagination">
                {% if categories.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ categories.previous_page_number }}{% if search %}&search={{ search }}{% endif %}">السابق</a>
                    </li>
                {% endif %}
                
                {% for num in categories.paginator.page_range %}
                    {% if categories.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if categories.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ categories.next_page_number }}{% if search %}&search={{ search }}{% endif %}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة البحث
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.focus();
    }
    
    console.log('✅ تم تحميل صفحة فئات الأصول');
});
</script>
{% endblock %}
