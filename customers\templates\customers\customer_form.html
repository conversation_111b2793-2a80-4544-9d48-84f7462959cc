{% extends 'base.html' %}

{% block title %}{{ title }} - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{% url 'customers:customer_list' %}">العملاء</a></li>
                <li class="breadcrumb-item active">{{ title }}</li>
            </ol>
        </nav>
        <h1 class="page-title">{{ title }}</h1>
        <p class="page-subtitle">
            {% if customer %}
                تعديل بيانات العميل في النظام
            {% else %}
                إضافة عميل جديد إلى النظام
            {% endif %}
        </p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person me-2"></i>بيانات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="bi bi-person me-1"></i>{{ form.name.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    <i class="bi bi-telephone me-1"></i>{{ form.phone.label }}
                                </label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">مثال: 0501234567</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    <i class="bi bi-envelope me-1"></i>{{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.email.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">مثال: <EMAIL></div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">
                                    <i class="bi bi-geo-alt me-1"></i>{{ form.address.label }}
                                </label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.address.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">العنوان الكامل للعميل</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'customers:customer_list' %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-right me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-2"></i>{{ button_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {% if customer %}
                <!-- Customer Statistics -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up me-2"></i>إحصائيات العميل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <div class="border rounded p-3">
                                    <i class="bi bi-receipt text-primary" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">0</h4>
                                    <p class="text-muted mb-0">إجمالي الفواتير</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="border rounded p-3">
                                    <i class="bi bi-currency-dollar text-success" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">0 ر.س</h4>
                                    <p class="text-muted mb-0">إجمالي المبيعات</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="border rounded p-3">
                                    <i class="bi bi-calendar-event text-info" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ customer.created_at|date:"Y/m/d" }}</h4>
                                    <p class="text-muted mb-0">تاريخ التسجيل</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .form-label {
        font-weight: 600;
        color: #495057;
    }
    
    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const nameInput = document.getElementById('{{ form.name.id_for_label }}');
        
        // Add real-time validation
        nameInput.addEventListener('input', function() {
            if (this.value.trim().length < 2) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
        
        // Phone number formatting
        const phoneInput = document.getElementById('{{ form.phone.id_for_label }}');
        if (phoneInput) {
            phoneInput.addEventListener('input', function() {
                // Remove non-digits
                let value = this.value.replace(/\D/g, '');
                
                // Format Saudi phone numbers
                if (value.startsWith('966')) {
                    value = '+' + value;
                } else if (value.startsWith('05')) {
                    // Keep as is for local format
                }
                
                this.value = value;
            });
        }
        
        // Form submission
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
