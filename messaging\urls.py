from django.urls import path
from . import views

app_name = 'messaging'

urlpatterns = [
    # الصفحات الرئيسية
    path('', views.inbox, name='inbox'),
    path('sent/', views.sent_messages, name='sent'),
    path('compose/', views.compose_message, name='compose'),
    
    # عرض وإدارة الرسائل
    path('message/<int:message_id>/', views.view_message, name='view_message'),
    path('message/<int:message_id>/reply/', views.reply_message, name='reply_message'),
    path('message/<int:message_id>/delete/', views.delete_message, name='delete_message'),
    path('message/<int:message_id>/star/', views.star_message, name='star_message'),
    
    # API endpoints
    path('api/unread-count/', views.get_unread_count, name='unread_count'),
    path('api/recent-messages/', views.get_recent_messages, name='recent_messages'),
    path('api/search-users/', views.search_users, name='search_users'),
]
