from django import forms
from django.forms import inlineformset_factory
from .models import Supplier, Product, PurchaseOrder, PurchaseOrderItem, PurchaseInvoice, PurchaseInvoiceItem, SupplierPayment, PaymentAllocation

class SupplierForm(forms.ModelForm):
    """نموذج إضافة/تعديل الموردين"""
    class Meta:
        model = Supplier
        fields = ['name', 'email', 'phone', 'address', 'tax_number', 'payment_terms', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المورد'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'العنوان'}),
            'tax_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الرقم الضريبي'}),
            'payment_terms': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'شروط الدفع'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class ProductForm(forms.ModelForm):
    """نموذج إضافة/تعديل المنتجات"""
    class Meta:
        model = Product
        fields = ['name', 'code', 'description', 'purchase_price', 'selling_price', 'stock_quantity', 'min_stock_level', 'unit', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المنتج'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'كود المنتج'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف المنتج'}),
            'purchase_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'selling_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'stock_quantity': forms.NumberInput(attrs={'class': 'form-control'}),
            'min_stock_level': forms.NumberInput(attrs={'class': 'form-control'}),
            'unit': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الوحدة'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class PurchaseOrderForm(forms.ModelForm):
    """نموذج إضافة/تعديل أوامر الشراء"""
    class Meta:
        model = PurchaseOrder
        fields = ['supplier', 'order_date', 'expected_delivery_date', 'status', 'notes', 'discount_percentage', 'tax_percentage']
        widgets = {
            'supplier': forms.Select(attrs={'class': 'form-select'}),
            'order_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'expected_delivery_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'tax_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
        }

class PurchaseOrderItemForm(forms.ModelForm):
    """نموذج عناصر أمر الشراء"""
    class Meta:
        model = PurchaseOrderItem
        fields = ['product', 'quantity', 'unit_price', 'discount_percentage']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
        }

class PurchaseInvoiceForm(forms.ModelForm):
    """نموذج إضافة/تعديل فواتير الشراء"""
    class Meta:
        model = PurchaseInvoice
        fields = ['supplier', 'order', 'invoice_date', 'due_date', 'status', 'notes', 'discount_percentage', 'tax_percentage', 'total_amount', 'paid_amount_manual']
        widgets = {
            'supplier': forms.Select(attrs={'class': 'form-select'}),
            'order': forms.Select(attrs={'class': 'form-select'}),
            'invoice_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'due_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'tax_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
            'total_amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'paid_amount_manual': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
        }

class PurchaseInvoiceItemForm(forms.ModelForm):
    """نموذج عناصر فاتورة الشراء"""
    class Meta:
        model = PurchaseInvoiceItem
        fields = ['product', 'quantity', 'unit_price', 'discount_percentage']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0.01'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'discount_percentage': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0', 'max': '100'}),
        }

# إنشاء Formsets للعناصر
PurchaseOrderItemFormSet = inlineformset_factory(
    PurchaseOrder, 
    PurchaseOrderItem, 
    form=PurchaseOrderItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)

PurchaseInvoiceItemFormSet = inlineformset_factory(
    PurchaseInvoice,
    PurchaseInvoiceItem,
    form=PurchaseInvoiceItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)


class SupplierPaymentForm(forms.ModelForm):
    """نموذج إضافة دفعة للمورد"""
    class Meta:
        model = SupplierPayment
        fields = ['payment_date', 'total_amount', 'payment_method', 'reference_number', 'notes']
        widgets = {
            'payment_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'total_amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'المبلغ المدفوع',
                'step': '0.01',
                'min': '0'
            }),
            'payment_method': forms.Select(attrs={'class': 'form-select'}),
            'reference_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم المرجع (اختياري)'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'ملاحظات (اختياري)'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.supplier = kwargs.pop('supplier', None)
        super().__init__(*args, **kwargs)

        if self.supplier:
            # إضافة معلومات المورد
            self.fields['total_amount'].help_text = f'إجمالي المديونية: {self.supplier.total_debt if hasattr(self.supplier, "total_debt") else "غير محدد"} ج.م'

    def clean_total_amount(self):
        amount = self.cleaned_data.get('total_amount')
        if amount and amount <= 0:
            raise forms.ValidationError('المبلغ المدفوع يجب أن يكون أكبر من صفر')
        return amount


class PaymentAllocationForm(forms.ModelForm):
    """نموذج توزيع الدفعة على الفواتير"""
    class Meta:
        model = PaymentAllocation
        fields = ['invoice', 'allocated_amount']
        widgets = {
            'invoice': forms.Select(attrs={'class': 'form-select'}),
            'allocated_amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'المبلغ المخصص',
                'step': '0.01',
                'min': '0'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.supplier = kwargs.pop('supplier', None)
        self.payment = kwargs.pop('payment', None)
        super().__init__(*args, **kwargs)

        if self.supplier:
            # عرض الفواتير غير المدفوعة بالكامل فقط
            unpaid_invoices = PurchaseInvoice.objects.filter(
                supplier=self.supplier,
                status__in=['received', 'partially_paid', 'overdue']
            ).exclude(status='paid')

            self.fields['invoice'].queryset = unpaid_invoices
            self.fields['invoice'].empty_label = "اختر فاتورة"

    def clean_allocated_amount(self):
        amount = self.cleaned_data.get('allocated_amount')
        invoice = self.cleaned_data.get('invoice')

        if amount and amount <= 0:
            raise forms.ValidationError('المبلغ المخصص يجب أن يكون أكبر من صفر')

        if invoice and amount:
            if amount > invoice.remaining_amount:
                raise forms.ValidationError(
                    f'المبلغ المخصص لا يمكن أن يتجاوز المبلغ المتبقي للفاتورة ({invoice.remaining_amount} ج.م)'
                )

        return amount


class PurchaseInvoiceStatusForm(forms.ModelForm):
    """نموذج تحديث حالة فاتورة الشراء"""
    class Meta:
        model = PurchaseInvoice
        fields = ['status']
        widgets = {
            'status': forms.Select(attrs={'class': 'form-select'}),
        }
