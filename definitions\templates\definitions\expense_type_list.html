{% extends 'base.html' %}
{% load static %}

{% block title %}أنواع المصروفات - أوساريك{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        min-height: 100vh;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        text-align: center;
    }

    .page-subtitle {
        color: #6c757d;
        margin: 0.5rem 0 0 0;
        text-align: center;
        font-size: 1.1rem;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(31, 38, 135, 0.5);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 800;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6c757d;
        font-size: 1rem;
        font-weight: 600;
    }

    .table-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
    }

    .table {
        margin: 0;
        font-size: 0.9rem;
    }

    .table th {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        border: none;
        font-weight: 700;
        padding: 1.5rem 1rem;
        text-align: center;
    }

    .table td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid rgba(0,0,0,0.1);
        text-align: center;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 36, 0.1) 100%);
        transform: scale(1.02);
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.9rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .btn-primary {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .btn-outline-info {
        background: transparent;
        border: 2px solid #17a2b8;
        color: #17a2b8;
    }

    .btn-outline-info:hover {
        background: #17a2b8;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .btn-outline-success {
        background: transparent;
        border: 2px solid #28a745;
        color: #28a745;
    }

    .btn-outline-success:hover {
        background: #28a745;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .btn-outline-danger {
        background: transparent;
        border: 2px solid #dc3545;
        color: #dc3545;
    }

    .btn-outline-danger:hover {
        background: #dc3545;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .badge.active {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .badge.inactive {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
    }

    .badge.fixed {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        color: white;
    }

    .badge.variable {
        background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
        color: white;
    }

    .badge.operational {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        color: white;
    }

    .badge.administrative {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 4rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .expense-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin: 0 auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- CSRF Token for JavaScript -->
    {% csrf_token %}
    
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="bi bi-receipt me-3"></i>أنواع المصروفات
        </h1>
        <p class="page-subtitle">إدارة أنواع وتصنيفات المصروفات في النظام</p>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>عودة للتعريفات
        </a>
        <a href="{% url 'definitions:expense_type_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>إضافة نوع مصروف جديد
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-number">{{ total_types }}</div>
            <div class="stat-label">إجمالي الأنواع</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ active_types }}</div>
            <div class="stat-label">الأنواع النشطة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ fixed_types }}</div>
            <div class="stat-label">المصروفات الثابتة</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ variable_types }}</div>
            <div class="stat-label">المصروفات المتغيرة</div>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.18);">
                <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Expense Types Table -->
    <div class="table-container">
        {% if expense_types %}
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الأيقونة</th>
                        <th>الكود</th>
                        <th>الاسم</th>
                        <th>النوع</th>
                        <th>التصنيف</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for expense_type in expense_types %}
                        <tr>
                            <td>
                                <div class="expense-icon">
                                    <i class="bi bi-receipt"></i>
                                </div>
                            </td>
                            <td>
                                <strong style="color: #ff6b6b;">{{ expense_type.code }}</strong>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ expense_type.name }}</strong>
                                    {% if expense_type.name_en %}
                                        <br><small class="text-muted">{{ expense_type.name_en }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge {{ expense_type.expense_type }}">
                                    {% if expense_type.expense_type == 'fixed' %}
                                        <i class="bi bi-lock me-1"></i>ثابت
                                    {% else %}
                                        <i class="bi bi-graph-up me-1"></i>متغير
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <span class="badge {{ expense_type.category }}">
                                    {% if expense_type.category == 'operational' %}
                                        <i class="bi bi-gear me-1"></i>تشغيلي
                                    {% else %}
                                        <i class="bi bi-building me-1"></i>إداري
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <span class="badge {% if expense_type.is_active %}active{% else %}inactive{% endif %}">
                                    {% if expense_type.is_active %}
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    {% else %}
                                        <i class="bi bi-x-circle me-1"></i>غير نشط
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex gap-1 justify-content-center">
                                    <a href="{% url 'definitions:expense_type_detail' expense_type.id %}" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="عرض التفاصيل">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{% url 'definitions:expense_type_edit' expense_type.id %}" 
                                       class="btn btn-sm btn-outline-success" 
                                       title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger" 
                                            title="حذف"
                                            onclick="deleteExpenseType({{ expense_type.id }}, '{{ expense_type.name|escapejs }}')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <div class="empty-state">
                <i class="bi bi-receipt"></i>
                <h3>لا توجد أنواع مصروفات</h3>
                <p>لم يتم العثور على أنواع مصروفات مطابقة للبحث.</p>
                <a href="{% url 'definitions:expense_type_create' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>إضافة أول نوع مصروف
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
function deleteExpenseType(typeId, typeName) {
    if (confirm('هل أنت متأكد من حذف نوع المصروف "' + typeName + '"؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        // إنشاء نموذج مخفي وإرساله
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/definitions/expense-types/' + typeId + '/quick-delete/';
        
        // إضافة CSRF token
        var csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        var csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        // إضافة النموذج للصفحة وإرساله
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
