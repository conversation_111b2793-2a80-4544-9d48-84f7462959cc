{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل المخزن - {{ warehouse.name }}{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-bottom: 3px solid #007bff;
    }

    .page-title {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        color: #6c757d;
        margin-bottom: 0;
    }

    .detail-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .detail-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #eee;
    }

    .detail-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #007bff;
        display: inline-block;
    }

    .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 3px solid #007bff;
    }

    .detail-label {
        font-weight: 600;
        color: #495057;
        min-width: 150px;
        margin-right: 1rem;
    }

    .detail-value {
        color: #212529;
        flex: 1;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-weight: 500;
        font-size: 0.875rem;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .warehouse-type-badge {
        background: #e3f2fd;
        color: #1565c0;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-weight: 500;
        font-size: 0.875rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn-action {
        padding: 0.75rem 2rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.2s ease;
        border: none;
    }

    .btn-edit {
        background: #28a745;
        color: white;
    }

    .btn-edit:hover {
        background: #218838;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .btn-delete {
        background: #dc3545;
        color: white;
    }

    .btn-delete:hover {
        background: #c82333;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
        color: white;
    }

    .empty-value {
        color: #6c757d;
        font-style: italic;
    }

    .alert-info {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        color: #1565c0;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-building me-2"></i>
                    {{ warehouse.name }}
                </h1>
                <p class="page-subtitle">تفاصيل المخزن - {{ warehouse.code }}</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-right me-1"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Detail Content -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="detail-card">
                <!-- Basic Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-info-circle me-2"></i>المعلومات الأساسية
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">كود المخزن:</div>
                        <div class="detail-value">
                            <strong>{{ warehouse.code }}</strong>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">اسم المخزن:</div>
                        <div class="detail-value">{{ warehouse.name }}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">نوع المخزن:</div>
                        <div class="detail-value">
                            <span class="warehouse-type-badge">
                                {{ warehouse.get_warehouse_type_display }}
                            </span>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">مدير المخزن:</div>
                        <div class="detail-value">
                            {% if warehouse.manager_name %}
                                {{ warehouse.manager_name }}
                            {% else %}
                                <span class="empty-value">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">الحالة:</div>
                        <div class="detail-value">
                            <span class="status-badge {% if warehouse.is_active %}status-active{% else %}status-inactive{% endif %}">
                                <i class="bi bi-{% if warehouse.is_active %}check-circle{% else %}x-circle{% endif %} me-1"></i>
                                {{ warehouse.is_active|yesno:"نشط,غير نشط" }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-telephone me-2"></i>معلومات الاتصال والموقع
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">رقم الهاتف:</div>
                        <div class="detail-value">
                            {% if warehouse.phone %}
                                <a href="tel:{{ warehouse.phone }}" class="text-decoration-none">
                                    <i class="bi bi-telephone me-1"></i>{{ warehouse.phone }}
                                </a>
                            {% else %}
                                <span class="empty-value">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">العنوان:</div>
                        <div class="detail-value">
                            {% if warehouse.address %}
                                <i class="bi bi-geo-alt me-1"></i>{{ warehouse.address }}
                            {% else %}
                                <span class="empty-value">غير محدد</span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- حقل الوصف غير متوفر في النموذج الحالي -->
                </div>

                <!-- Settings -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-gear me-2"></i>إعدادات المخزن
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="detail-item">
                                <div class="detail-label">المخزون السالب:</div>
                                <div class="detail-value">
                                    <i class="bi bi-{% if warehouse.allow_negative_stock %}check-circle text-success{% else %}x-circle text-danger{% endif %} me-1"></i>
                                    {{ warehouse.allow_negative_stock|yesno:"مسموح,غير مسموح" }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="detail-item">
                                <div class="detail-label">إعادة الطلب التلقائي:</div>
                                <div class="detail-value">
                                    <i class="bi bi-{% if warehouse.auto_reorder %}check-circle text-success{% else %}x-circle text-danger{% endif %} me-1"></i>
                                    {{ warehouse.auto_reorder|yesno:"مفعل,غير مفعل" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-clock-history me-2"></i>معلومات النظام
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">تاريخ الإنشاء:</div>
                        <div class="detail-value">
                            <i class="bi bi-calendar me-1"></i>
                            {{ warehouse.created_at|date:"d/m/Y H:i" }}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">آخر تحديث:</div>
                        <div class="detail-value">
                            <i class="bi bi-clock me-1"></i>
                            {{ warehouse.updated_at|date:"d/m/Y H:i" }}
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">أنشئ بواسطة:</div>
                        <div class="detail-value">
                            <i class="bi bi-person me-1"></i>
                            {{ warehouse.created_by.get_full_name|default:warehouse.created_by.username }}
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="{% url 'definitions:warehouse_edit' warehouse.id %}" class="btn-action btn-edit">
                        <i class="bi bi-pencil me-2"></i>تعديل المخزن
                    </a>
                    <a href="{% url 'definitions:warehouse_delete' warehouse.id %}" class="btn-action btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا المخزن؟\n\nتحذير: سيتم حذف جميع البيانات المرتبطة بهذا المخزن!')">
                        <i class="bi bi-trash me-2"></i>حذف المخزن
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle me-2"></i>معلومات إضافية:</h6>
                <ul class="mb-0">
                    <li>يمكنك تعديل جميع بيانات المخزن عدا الكود</li>
                    <li>إلغاء تفعيل المخزن يمنع استخدامه في العمليات الجديدة</li>
                    <li>حذف المخزن سيؤثر على جميع البيانات المرتبطة به</li>
                    <li>يمكنك عرض تقارير المخزون من قسم إدارة المخازن</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
