# Generated by Django 5.2.4 on 2025-07-26 00:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchases', '0004_alter_purchaseinvoice_status_supplierpayment'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='supplierpayment',
            name='amount',
        ),
        migrations.RemoveField(
            model_name='supplierpayment',
            name='invoice',
        ),
        migrations.AddField(
            model_name='supplierpayment',
            name='supplier',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='all_payments', to='purchases.supplier', verbose_name='المورد'),
        ),
        migrations.AddField(
            model_name='supplierpayment',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي المبلغ المدفوع'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='supplierpayment',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.CreateModel(
            name='SupplierAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الحالي')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='حد الائتمان')),
                ('last_transaction_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر معاملة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('supplier', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='account', to='purchases.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'حساب مورد',
                'verbose_name_plural': 'حسابات الموردين',
            },
        ),
        migrations.CreateModel(
            name='SupplierTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('invoice', 'فاتورة شراء'), ('payment', 'دفعة'), ('credit_note', 'إشعار دائن'), ('debit_note', 'إشعار مدين'), ('adjustment', 'تسوية'), ('opening_balance', 'رصيد افتتاحي')], max_length=20, verbose_name='نوع المعاملة')),
                ('transaction_date', models.DateTimeField(verbose_name='تاريخ المعاملة')),
                ('reference_number', models.CharField(max_length=100, verbose_name='رقم المرجع')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('debit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='مدين')),
                ('credit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='دائن')),
                ('balance_after', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='الرصيد بعد المعاملة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='purchases.purchaseinvoice', verbose_name='فاتورة الشراء')),
                ('payment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='purchases.supplierpayment', verbose_name='الدفعة')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='purchases.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'معاملة مورد',
                'verbose_name_plural': 'معاملات الموردين',
                'ordering': ['-transaction_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PaymentAllocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('allocated_amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='المبلغ المخصص')),
                ('allocation_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التخصيص')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_allocations', to='purchases.purchaseinvoice', verbose_name='الفاتورة')),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allocations', to='purchases.supplierpayment', verbose_name='الدفعة')),
            ],
            options={
                'verbose_name': 'تخصيص دفعة',
                'verbose_name_plural': 'تخصيصات الدفعات',
                'unique_together': {('payment', 'invoice')},
            },
        ),
    ]
