from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import datetime, timedelta, date
from .models import ReportCategory, Report, ReportExecution, SavedReport, ReportSchedule
from .forms import (ReportCategoryForm, ReportForm, SavedReportForm, ReportScheduleForm,
                   ReportParametersForm, ReportSearchForm, ReportExecutionSearchForm)

@login_required
def reports_dashboard(request):
    """لوحة تحكم التقارير"""
    # إحصائيات عامة
    total_categories = ReportCategory.objects.filter(is_active=True).count()
    total_reports = Report.objects.filter(status='active').count()
    total_executions = ReportExecution.objects.count()
    total_saved_reports = SavedReport.objects.filter(user=request.user).count()

    # إحصائيات التنفيذ
    today = date.today()
    executions_today = ReportExecution.objects.filter(start_time__date=today).count()
    successful_executions = ReportExecution.objects.filter(status='completed').count()
    failed_executions = ReportExecution.objects.filter(status='failed').count()

    # التقارير حسب الفئة
    reports_by_category = ReportCategory.objects.filter(is_active=True).annotate(
        reports_count=Count('reports', filter=Q(reports__status='active'))
    )

    # أحدث التنفيذات
    recent_executions = ReportExecution.objects.select_related('report', 'executed_by').order_by('-start_time')[:10]

    # التقارير المفضلة
    favorite_reports = SavedReport.objects.filter(user=request.user, is_favorite=True).select_related('report')[:5]

    # الجدولة النشطة
    active_schedules = ReportSchedule.objects.filter(status='active').count()

    # أكثر التقارير استخداماً
    popular_reports = Report.objects.filter(status='active').annotate(
        execution_count=Count('executions')
    ).order_by('-execution_count')[:5]

    context = {
        'total_categories': total_categories,
        'total_reports': total_reports,
        'total_executions': total_executions,
        'total_saved_reports': total_saved_reports,
        'executions_today': executions_today,
        'successful_executions': successful_executions,
        'failed_executions': failed_executions,
        'active_schedules': active_schedules,
        'reports_by_category': reports_by_category,
        'recent_executions': recent_executions,
        'favorite_reports': favorite_reports,
        'popular_reports': popular_reports,
    }
    return render(request, 'reports/dashboard.html', context)

# ========== إدارة فئات التقارير ==========
@login_required
def category_list(request):
    """قائمة فئات التقارير"""
    search = request.GET.get('search', '')
    categories = ReportCategory.objects.all()

    if search:
        categories = categories.filter(Q(name__icontains=search))

    paginator = Paginator(categories, 20)
    page_number = request.GET.get('page')
    categories = paginator.get_page(page_number)

    context = {
        'categories': categories,
        'search': search,
    }
    return render(request, 'reports/category_list.html', context)

@login_required
def category_create(request):
    """إضافة فئة تقرير جديدة"""
    if request.method == 'POST':
        form = ReportCategoryForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم إضافة فئة التقرير بنجاح')
            return redirect('reports:category_list')
    else:
        form = ReportCategoryForm()

    context = {'form': form, 'title': 'إضافة فئة تقرير جديدة'}
    return render(request, 'reports/category_form.html', context)

@login_required
def category_edit(request, pk):
    """تعديل فئة تقرير"""
    category = get_object_or_404(ReportCategory, pk=pk)
    if request.method == 'POST':
        form = ReportCategoryForm(request.POST, instance=category)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث فئة التقرير بنجاح')
            return redirect('reports:category_list')
    else:
        form = ReportCategoryForm(instance=category)

    context = {'form': form, 'title': 'تعديل فئة التقرير', 'category': category}
    return render(request, 'reports/category_form.html', context)

@login_required
def category_delete(request, pk):
    """حذف فئة تقرير"""
    category = get_object_or_404(ReportCategory, pk=pk)
    if request.method == 'POST':
        category.delete()
        messages.success(request, 'تم حذف فئة التقرير بنجاح')
        return redirect('reports:category_list')

    context = {'category': category}
    return render(request, 'reports/category_confirm_delete.html', context)

# ========== إدارة التقارير ==========
@login_required
def report_list(request):
    """قائمة التقارير"""
    form = ReportSearchForm(request.GET)
    reports = Report.objects.select_related('category', 'created_by')

    if form.is_valid():
        search = form.cleaned_data.get('search')
        category = form.cleaned_data.get('category')
        report_type = form.cleaned_data.get('report_type')
        status = form.cleaned_data.get('status')
        is_public = form.cleaned_data.get('is_public')

        if search:
            reports = reports.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        if category:
            reports = reports.filter(category=category)

        if report_type:
            reports = reports.filter(report_type=report_type)

        if status:
            reports = reports.filter(status=status)

        if is_public:
            reports = reports.filter(is_public=is_public == 'True')

    paginator = Paginator(reports, 20)
    page_number = request.GET.get('page')
    reports = paginator.get_page(page_number)

    context = {
        'reports': reports,
        'form': form,
    }
    return render(request, 'reports/report_list.html', context)
