{% extends 'base.html' %}
{% load static %}

{% block title %}تعديل المستخدم - {{ user.username }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'system_settings:dashboard' %}">الإعدادات</a></li>
        <li class="breadcrumb-item"><a href="{% url 'system_settings:users_management' %}">إدارة المستخدمين</a></li>
        <li class="breadcrumb-item"><a href="{% url 'system_settings:user_detail' user.id %}">{{ user.username }}</a></li>
        <li class="breadcrumb-item active">تعديل</li>
    </ol>
</nav>
{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }

    .form-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .form-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .form-body {
        padding: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        background: #f8fafc;
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #374151;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .form-check-input {
        width: 18px;
        height: 18px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-secondary {
        background: #6b7280;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
    }

    .btn-danger {
        background: #dc2626;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-danger:hover {
        background: #b91c1c;
        color: white;
        text-decoration: none;
    }

    .avatar-preview {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        border: 3px solid #e2e8f0;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        background: #f3f4f6;
        color: #6b7280;
        font-size: 2rem;
        overflow: hidden;
    }

    .avatar-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .error-list {
        color: #dc2626;
        font-size: 0.85rem;
        margin-top: 0.25rem;
    }

    .error-list ul {
        margin: 0;
        padding-left: 1rem;
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }

        .form-container {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <div class="form-card">
        <div class="form-header">
            <h2><i class="bi bi-person-gear"></i> تعديل المستخدم</h2>
            <p>تعديل بيانات المستخدم: {{ user.get_full_name|default:user.username }}</p>
        </div>

        <div class="form-body">
            <!-- عرض الأخطاء العامة -->
            {% if form.non_field_errors %}
                <div style="background: #fef2f2; border: 2px solid #dc2626; border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
                    <h5 style="color: #dc2626; margin-bottom: 0.5rem;">أخطاء في النموذج:</h5>
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <!-- عرض جميع أخطاء النموذج للتشخيص -->
            {% if form.errors %}
                <div style="background: #fef2f2; border: 2px solid #dc2626; border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
                    <h5 style="color: #dc2626; margin-bottom: 0.5rem;">أخطاء تفصيلية:</h5>
                    {% for field, errors in form.errors.items %}
                        <p style="color: #dc2626;"><strong>{{ field }}:</strong> {{ errors|join:", " }}</p>
                    {% endfor %}
                </div>
            {% endif %}

            <form method="post" enctype="multipart/form-data" id="userEditForm">
                {% csrf_token %}

                <!-- معلومات أساسية -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="bi bi-person"></i>
                        المعلومات الأساسية
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" value="{{ user.username }}" readonly style="background-color: #f8f9fa;">
                            <small style="color: #6b7280;">لا يمكن تغيير اسم المستخدم</small>
                        </div>
                        <div class="form-group">
                            <label class="form-label">{{ form.email.label }}</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="error-list">{{ form.email.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">{{ form.first_name.label }}</label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <div class="error-list">{{ form.first_name.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="form-group">
                            <label class="form-label">{{ form.last_name.label }}</label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <div class="error-list">{{ form.last_name.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- الصورة الشخصية -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="bi bi-image"></i>
                        الصورة الشخصية
                    </div>

                    <div class="avatar-preview" id="avatarPreview">
                        {% if user.profile and user.profile.avatar %}
                            <img src="{{ user.profile.avatar.url }}" alt="الصورة الحالية">
                        {% else %}
                            <i class="bi bi-person"></i>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label class="form-label">{{ form.avatar.label }}</label>
                        {{ form.avatar }}
                        {% if form.avatar.errors %}
                            <div class="error-list">{{ form.avatar.errors }}</div>
                        {% endif %}
                        <small style="color: #6b7280;">اتركه فارغاً للاحتفاظ بالصورة الحالية</small>
                    </div>
                </div>

                <!-- معلومات الاتصال -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="bi bi-telephone"></i>
                        معلومات الاتصال
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">{{ form.phone.label }}</label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="error-list">{{ form.phone.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="form-group">
                            <label class="form-label">{{ form.mobile.label }}</label>
                            {{ form.mobile }}
                            {% if form.mobile.errors %}
                                <div class="error-list">{{ form.mobile.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- معلومات وظيفية -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="bi bi-briefcase"></i>
                        المعلومات الوظيفية
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">{{ form.department.label }}</label>
                            {{ form.department }}
                            {% if form.department.errors %}
                                <div class="error-list">{{ form.department.errors }}</div>
                            {% endif %}
                        </div>
                        <div class="form-group">
                            <label class="form-label">{{ form.position.label }}</label>
                            {{ form.position }}
                            {% if form.position.errors %}
                                <div class="error-list">{{ form.position.errors }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">{{ form.employee_id.label }}</label>
                        {{ form.employee_id }}
                        {% if form.employee_id.errors %}
                            <div class="error-list">{{ form.employee_id.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- صلاحيات -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="bi bi-shield-check"></i>
                        الصلاحيات والحالة
                    </div>

                    <div class="form-check">
                        {{ form.is_staff }}
                        <label class="form-label">{{ form.is_staff.label }}</label>
                    </div>

                    <div class="form-check">
                        {{ form.is_active }}
                        <label class="form-label">{{ form.is_active.label }}</label>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div style="display: flex; gap: 1rem; justify-content: center; margin-top: 2rem;">
                    <input type="submit" value="حفظ التغييرات" class="btn-primary" style="border: none; cursor: pointer;">
                    <a href="{% url 'system_settings:users_management' %}" class="btn-secondary">
                        <i class="bi bi-arrow-left"></i> العودة للقائمة
                    </a>
                    {% if not user.is_superuser and user != request.user %}
                        <a href="{% url 'system_settings:user_delete' user.id %}" class="btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                            <i class="bi bi-trash"></i> حذف المستخدم
                        </a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// معاينة الصورة فقط
document.addEventListener('DOMContentLoaded', function() {
    const avatarInput = document.getElementById('id_avatar');
    const avatarPreview = document.getElementById('avatarPreview');

    if (avatarInput && avatarPreview) {
        avatarInput.addEventListener('change', function(e) {
            const file = e.target.files[0];

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    avatarPreview.innerHTML = `<img src="${e.target.result}" alt="معاينة الصورة">`;
                };
                reader.readAsDataURL(file);
            } else {
                // إعادة الصورة الأصلية إذا لم يتم اختيار ملف
                {% if user.profile and user.profile.avatar %}
                    avatarPreview.innerHTML = '<img src="{{ user.profile.avatar.url }}" alt="الصورة الحالية">';
                {% else %}
                    avatarPreview.innerHTML = '<i class="bi bi-person"></i>';
                {% endif %}
            }
        });
    }
});
</script>
{% endblock %}
