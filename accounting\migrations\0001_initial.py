# Generated by Django 5.2.4 on 2025-07-12 23:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النوع')),
                ('type_category', models.CharField(choices=[('asset', 'أصول'), ('liability', 'خصوم'), ('equity', 'حقوق الملكية'), ('revenue', 'إيرادات'), ('expense', 'مصروفات')], max_length=20, verbose_name='فئة النوع')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'نوع حساب',
                'verbose_name_plural': 'أنواع الحسابات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_code', models.CharField(max_length=20, unique=True, verbose_name='كود الحساب')),
                ('account_name', models.CharField(max_length=200, verbose_name='اسم الحساب')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الافتتاحي')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الحالي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_main_account', models.BooleanField(default=False, verbose_name='حساب رئيسي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('parent_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sub_accounts', to='accounting.account', verbose_name='الحساب الأب')),
                ('account_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accounttype', verbose_name='نوع الحساب')),
            ],
            options={
                'verbose_name': 'حساب',
                'verbose_name_plural': 'دليل الحسابات',
                'ordering': ['account_code'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_number', models.CharField(max_length=50, unique=True, verbose_name='رقم القيد')),
                ('entry_date', models.DateField(verbose_name='تاريخ القيد')),
                ('description', models.TextField(verbose_name='وصف القيد')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم المرجع')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('posted', 'مرحل'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='الحالة')),
                ('total_debit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المدين')),
                ('total_credit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي الدائن')),
                ('posted_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الترحيل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posted_entries', to=settings.AUTH_USER_MODEL, verbose_name='رحل بواسطة')),
            ],
            options={
                'verbose_name': 'قيد محاسبي',
                'verbose_name_plural': 'القيود المحاسبية',
                'ordering': ['-entry_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntryLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('debit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ المدين')),
                ('credit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='المبلغ الدائن')),
                ('line_order', models.PositiveIntegerField(default=1, verbose_name='ترتيب السطر')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.account', verbose_name='الحساب')),
                ('journal_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='accounting.journalentry', verbose_name='القيد المحاسبي')),
            ],
            options={
                'verbose_name': 'سطر قيد محاسبي',
                'verbose_name_plural': 'أسطر القيود المحاسبية',
                'ordering': ['line_order'],
            },
        ),
    ]
