{% extends 'base.html' %}
{% load static %}

{% block title %}حذف الصنف - {{ object.name }}{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block extra_css %}
<style>
    /* Glass Delete Confirmation Styles */
    :root {
        --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

        /* Glass Effects */
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: rgba(255, 255, 255, 0.18);
        --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        --glass-backdrop: blur(8px);

        --radius: 12px;
        --radius-lg: 16px;
    }

    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        background-attachment: fixed;
        min-height: 100vh;
        font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
        color: white;
    }

    /* Hero Section */
    .delete-hero {
        background: var(--danger-gradient);
        padding: 3rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
        border-radius: 0 0 2rem 2rem;
    }

    .delete-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        text-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .hero-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    /* Confirmation Container */
    .delete-container {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
        border-radius: var(--radius-lg);
        padding: 3rem;
        box-shadow: var(--glass-shadow);
        position: relative;
        overflow: hidden;
        max-width: 800px;
        margin: 2rem auto;
    }

    .delete-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: var(--danger-gradient);
    }

    /* Warning Icon */
    .warning-icon {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: var(--warning-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        font-size: 3rem;
        color: white;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    /* Product Info */
    .product-info {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(4px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius);
        padding: 2rem;
        margin: 2rem 0;
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .product-image {
        width: 100px;
        height: 100px;
        border-radius: 12px;
        object-fit: cover;
        border: 2px solid #e5e7eb;
    }

    .product-placeholder {
        width: 100px;
        height: 100px;
        border-radius: 12px;
        background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #9ca3af;
        font-size: 2rem;
    }

    .product-details h4 {
        color: white;
        margin-bottom: 0.5rem;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .product-meta {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #e5e7eb;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-value {
        color: white;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    /* Warning Messages */
    .warning-message {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border: 2px solid #f59e0b;
        border-radius: 16px;
        padding: 2rem;
        margin: 2rem 0;
        text-align: center;
    }

    .warning-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #92400e;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .warning-text {
        color: #92400e;
        margin-bottom: 0;
        line-height: 1.6;
    }

    .critical-warning {
        background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
        border: 2px solid #ef4444;
    }

    .critical-warning .warning-title,
    .critical-warning .warning-text {
        color: #991b1b;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        margin-top: 3rem;
    }

    .btn-delete {
        background: var(--danger-gradient);
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 1rem 3rem;
        border-radius: var(--radius);
        font-weight: 700;
        font-size: 1.1rem;
        color: white;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        backdrop-filter: blur(4px);
        box-shadow: 0 8px 25px rgba(252, 70, 107, 0.4);
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .btn-delete:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(252, 70, 107, 0.6);
        color: white;
    }

    .btn-cancel {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 1rem 3rem;
        border-radius: var(--radius);
        font-weight: 700;
        font-size: 1.1rem;
        color: white;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        backdrop-filter: blur(4px);
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .btn-cancel:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.3);
        color: white;
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .delete-container {
            padding: 2rem;
            margin: 1rem;
        }
        
        .hero-title {
            font-size: 2rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-delete, .btn-cancel {
            justify-content: center;
        }
        
        .product-info {
            flex-direction: column;
            text-align: center;
        }
    }

    /* Loading Animation */
    .spinner-border {
        width: 1rem;
        height: 1rem;
        border: 0.125em solid currentColor;
        border-right-color: transparent;
        border-radius: 50%;
        animation: spinner-border 0.75s linear infinite;
    }

    @keyframes spinner-border {
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// دالة للحصول على CSRF token
function getCSRFToken() {
    const tokenFromForm = document.querySelector('[name=csrfmiddlewaretoken]');
    const tokenFromMeta = document.querySelector('meta[name="csrf-token"]');

    if (tokenFromForm) {
        return tokenFromForm.value;
    } else if (tokenFromMeta) {
        return tokenFromMeta.getAttribute('content');
    }
    return null;
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('deleteForm');
    const deleteBtn = document.getElementById('deleteBtn');
    const btnText = deleteBtn.querySelector('.btn-text');
    const btnLoading = deleteBtn.querySelector('.btn-loading');

    form.addEventListener('submit', function(e) {
        // تأكيد إضافي قبل الحذف
        if (!confirm('هل أنت متأكد من حذف هذا الصنف نهائياً؟ لا يمكن التراجع عن هذا الإجراء.')) {
            e.preventDefault();
            return false;
        }

        // إظهار رسالة التحميل
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
        deleteBtn.disabled = true;

        // السماح بإرسال النموذج بالطريقة العادية
        console.log('إرسال طلب الحذف...');
        return true;
    });
});
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="delete-hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">
                <i class="bi bi-exclamation-triangle me-3"></i>
                تأكيد حذف الصنف
            </h1>
            <p class="hero-subtitle">
                هذا الإجراء لا يمكن التراجع عنه وسيؤثر على جميع البيانات المرتبطة
            </p>
        </div>
    </div>
</div>

<div class="container">
    <div class="delete-container">
        <!-- Warning Icon -->
        <div class="warning-icon">
            <i class="bi bi-exclamation-triangle"></i>
        </div>

        <!-- Confirmation Message -->
        <div class="text-center mb-4">
            <h2 class="text-danger mb-3">هل أنت متأكد من حذف هذا الصنف؟</h2>
            <p class="text-muted">
                سيتم حذف جميع البيانات المرتبطة بهذا الصنف نهائياً ولن يمكن استردادها.
            </p>
        </div>

        <!-- Product Information -->
        <div class="product-info">
            <div class="product-image-container">
                {% if object.image %}
                <img src="{{ object.image.url }}" alt="{{ object.name }}" class="product-image">
                {% else %}
                <div class="product-placeholder">
                    <i class="bi bi-image"></i>
                </div>
                {% endif %}
            </div>
            
            <div class="product-details flex-grow-1">
                <h4>{{ object.name }}</h4>
                <div class="product-meta">كود الصنف: {{ object.code }}</div>
                {% if object.barcode %}
                <div class="product-meta">الباركود: {{ object.barcode }}</div>
                {% endif %}
                {% if object.category %}
                <div class="product-meta">الفئة: {{ object.category.name }}</div>
                {% endif %}
                <div class="product-meta">سعر البيع: {{ object.selling_price|floatformat:2 }} ريال</div>
                {% if object.track_stock %}
                <div class="product-meta">المخزون الحالي: {{ object.current_stock|floatformat:0 }} {{ object.unit.name|default:"وحدة" }}</div>
                {% endif %}
            </div>
        </div>

        <!-- Detailed Information -->
        <div class="row">
            <div class="col-md-6">
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-hash"></i>
                        كود الصنف
                    </span>
                    <span class="info-value">{{ object.code }}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-tag"></i>
                        اسم الصنف
                    </span>
                    <span class="info-value">{{ object.name }}</span>
                </div>
                
                {% if object.category %}
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-tags"></i>
                        الفئة
                    </span>
                    <span class="info-value">{{ object.category.name }}</span>
                </div>
                {% endif %}
            </div>
            
            <div class="col-md-6">
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-currency-dollar"></i>
                        سعر البيع
                    </span>
                    <span class="info-value">{{ object.selling_price|floatformat:2 }} ريال</span>
                </div>
                
                {% if object.track_stock %}
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-box"></i>
                        المخزون الحالي
                    </span>
                    <span class="info-value">{{ object.current_stock|floatformat:0 }} {{ object.unit.name|default:"وحدة" }}</span>
                </div>
                {% endif %}
                
                <div class="info-item">
                    <span class="info-label">
                        <i class="bi bi-calendar"></i>
                        تاريخ الإنشاء
                    </span>
                    <span class="info-value">{{ object.created_at|date:"Y/m/d" }}</span>
                </div>
            </div>
        </div>

        <!-- Warning Messages -->
        {% if object.current_stock > 0 and object.track_stock %}
        <div class="critical-warning">
            <div class="warning-title">
                <i class="bi bi-exclamation-triangle"></i>
                تحذير: الصنف يحتوي على مخزون
            </div>
            <p class="warning-text">
                هذا الصنف يحتوي حالياً على <strong>{{ object.current_stock|floatformat:0 }} {{ object.unit.name|default:"وحدة" }}</strong> في المخزون.
                حذف الصنف سيؤدي إلى فقدان هذه البيانات نهائياً.
                يُنصح بتصفير المخزون أولاً أو نقله إلى صنف آخر.
            </p>
        </div>
        {% endif %}

        <div class="warning-message">
            <div class="warning-title">
                <i class="bi bi-info-circle"></i>
                البيانات التي ستتأثر بالحذف
            </div>
            <p class="warning-text">
                • جميع حركات المخزون المرتبطة بهذا الصنف<br>
                • تاريخ المبيعات والمشتريات<br>
                • التقارير والإحصائيات<br>
                • أي معاملات مالية مرتبطة بالصنف
            </p>
        </div>

        <!-- Confirmation Form -->
        <form method="post" id="deleteForm">
            {% csrf_token %}
            <div class="action-buttons">
                <button type="submit" class="btn btn-delete" id="deleteBtn">
                    <span class="btn-text">
                        <i class="bi bi-trash"></i>
                        نعم، احذف الصنف نهائياً
                    </span>
                    <span class="btn-loading d-none">
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        جاري الحذف...
                    </span>
                </button>
                <a href="{% url 'definitions:product_detail' object.pk %}" class="btn btn-cancel">
                    <i class="bi bi-x-circle"></i>
                    إلغاء العملية
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
