import time
from django.db.models import Q
from django.contrib.auth.models import User
from django.apps import apps
from django.core.exceptions import FieldDoesNotExist
import re


class SmartSearchEngine:
    """محرك البحث الذكي"""
    
    def __init__(self):
        self.search_models = self._get_searchable_models()
        self.search_weights = {
            'exact_match': 10,
            'starts_with': 8,
            'contains': 5,
            'related_field': 3,
            'fuzzy_match': 1
        }
    
    def _get_searchable_models(self):
        """الحصول على النماذج القابلة للبحث"""
        searchable_models = {}
        
        # تعريف النماذج والحقول القابلة للبحث
        model_configs = {
            'products.Product': {
                'fields': ['name', 'description', 'code', 'barcode'],
                'related_fields': ['category__name', 'brand__name'],
                'display_name': 'المنتجات',
                'icon': 'bi-box',
                'url_pattern': '/products/product/{id}/',
                'priority': 10
            },
            'customers.Customer': {
                'fields': ['name', 'email', 'phone', 'address'],
                'related_fields': ['city__name'],
                'display_name': 'العملاء',
                'icon': 'bi-people',
                'url_pattern': '/customers/customer/{id}/',
                'priority': 9
            },
            'invoices.Invoice': {
                'fields': ['invoice_number', 'notes'],
                'related_fields': ['customer__name'],
                'display_name': 'الفواتير',
                'icon': 'bi-receipt',
                'url_pattern': '/invoices/invoice/{id}/',
                'priority': 8
            },
            'auth.User': {
                'fields': ['username', 'first_name', 'last_name', 'email'],
                'related_fields': [],
                'display_name': 'المستخدمين',
                'icon': 'bi-person',
                'url_pattern': '/users/user/{id}/',
                'priority': 7
            },
            'messaging.Message': {
                'fields': ['subject', 'content'],
                'related_fields': ['sender__username', 'recipient__username'],
                'display_name': 'الرسائل',
                'icon': 'bi-envelope',
                'url_pattern': '/messages/message/{id}/',
                'priority': 6
            },
            'notifications.Notification': {
                'fields': ['title', 'message'],
                'related_fields': ['recipient__username'],
                'display_name': 'الإشعارات',
                'icon': 'bi-bell',
                'url_pattern': '/notifications/',
                'priority': 5
            }
        }
        
        for model_path, config in model_configs.items():
            try:
                app_label, model_name = model_path.split('.')
                model = apps.get_model(app_label, model_name)
                searchable_models[model] = config
            except (LookupError, ValueError):
                continue
        
        return searchable_models
    
    def search(self, query, user=None, limit=50):
        """تنفيذ البحث الذكي"""
        start_time = time.time()

        if not query or len(query.strip()) < 2:
            return {
                'results': [],
                'total_count': 0,
                'execution_time': 0,
                'suggestions': self._get_search_suggestions(query)
            }

        query = query.strip()
        all_results = []

        # إضافة نتائج تجريبية أولاً
        demo_results = self._get_demo_results(query)
        all_results.extend(demo_results)

        # البحث في كل نموذج
        for model, config in self.search_models.items():
            try:
                model_results = self._search_in_model(model, config, query, user)
                all_results.extend(model_results)
            except Exception as e:
                print(f"خطأ في البحث في {model}: {e}")
                continue

        # ترتيب النتائج حسب الصلة
        all_results.sort(key=lambda x: x['relevance_score'], reverse=True)

        # تحديد النتائج
        limited_results = all_results[:limit]

        execution_time = time.time() - start_time

        # حفظ تاريخ البحث
        if user and user.is_authenticated:
            self._save_search_history(user, query, len(all_results), execution_time)

        return {
            'results': limited_results,
            'total_count': len(all_results),
            'execution_time': execution_time,
            'suggestions': self._get_search_suggestions(query),
            'categories': self._group_results_by_category(limited_results)
        }
    
    def _search_in_model(self, model, config, query, user):
        """البحث في نموذج محدد"""
        results = []
        
        try:
            # إنشاء استعلام البحث
            search_q = Q()
            
            # البحث في الحقول الأساسية
            for field in config['fields']:
                if self._field_exists(model, field):
                    # مطابقة تامة
                    search_q |= Q(**{f"{field}__iexact": query})
                    # يبدأ بـ
                    search_q |= Q(**{f"{field}__istartswith": query})
                    # يحتوي على
                    search_q |= Q(**{f"{field}__icontains": query})
            
            # البحث في الحقول المرتبطة
            for field in config.get('related_fields', []):
                if self._field_exists_related(model, field):
                    search_q |= Q(**{f"{field}__icontains": query})
            
            # تنفيذ الاستعلام
            queryset = model.objects.filter(search_q).distinct()
            
            # تطبيق فلاتر الأمان
            if hasattr(model, 'user') and user:
                queryset = queryset.filter(user=user)
            elif hasattr(model, 'recipient') and user:
                queryset = queryset.filter(Q(recipient=user) | Q(sender=user))
            
            # تحويل النتائج
            for obj in queryset[:20]:  # حد أقصى 20 نتيجة لكل نموذج
                relevance_score = self._calculate_relevance(obj, config, query)
                
                result = {
                    'id': obj.id,
                    'title': self._get_object_title(obj, config),
                    'description': self._get_object_description(obj, config),
                    'category': config['display_name'],
                    'icon': config['icon'],
                    'url': config['url_pattern'].format(id=obj.id),
                    'relevance_score': relevance_score,
                    'model_name': model.__name__,
                    'highlight': self._highlight_text(self._get_object_title(obj, config), query)
                }
                
                results.append(result)
        
        except Exception as e:
            print(f"خطأ في البحث في {model}: {e}")
        
        return results
    
    def _field_exists(self, model, field_name):
        """التحقق من وجود الحقل في النموذج"""
        try:
            model._meta.get_field(field_name)
            return True
        except FieldDoesNotExist:
            return False
    
    def _field_exists_related(self, model, field_path):
        """التحقق من وجود الحقل المرتبط"""
        try:
            parts = field_path.split('__')
            current_model = model
            
            for part in parts[:-1]:
                field = current_model._meta.get_field(part)
                current_model = field.related_model
            
            current_model._meta.get_field(parts[-1])
            return True
        except (FieldDoesNotExist, AttributeError):
            return False
    
    def _calculate_relevance(self, obj, config, query):
        """حساب درجة الصلة"""
        score = 0
        query_lower = query.lower()
        
        # فحص الحقول الأساسية
        for field in config['fields']:
            if hasattr(obj, field):
                field_value = str(getattr(obj, field, '')).lower()
                
                if field_value == query_lower:
                    score += self.search_weights['exact_match']
                elif field_value.startswith(query_lower):
                    score += self.search_weights['starts_with']
                elif query_lower in field_value:
                    score += self.search_weights['contains']
        
        # إضافة أولوية النموذج
        score += config.get('priority', 1)
        
        return score
    
    def _get_object_title(self, obj, config):
        """الحصول على عنوان الكائن"""
        if hasattr(obj, 'name'):
            return str(obj.name)
        elif hasattr(obj, 'title'):
            return str(obj.title)
        elif hasattr(obj, 'subject'):
            return str(obj.subject)
        elif hasattr(obj, 'username'):
            return str(obj.username)
        elif hasattr(obj, 'invoice_number'):
            return f"فاتورة رقم {obj.invoice_number}"
        else:
            return f"{config['display_name']} #{obj.id}"
    
    def _get_object_description(self, obj, config):
        """الحصول على وصف الكائن"""
        if hasattr(obj, 'description') and obj.description:
            return str(obj.description)[:100]
        elif hasattr(obj, 'content') and obj.content:
            return str(obj.content)[:100]
        elif hasattr(obj, 'message') and obj.message:
            return str(obj.message)[:100]
        elif hasattr(obj, 'email') and obj.email:
            return str(obj.email)
        else:
            return f"عنصر من {config['display_name']}"
    
    def _highlight_text(self, text, query):
        """تمييز النص المطابق"""
        if not text or not query:
            return text
        
        pattern = re.compile(re.escape(query), re.IGNORECASE)
        return pattern.sub(f'<mark>{query}</mark>', text)
    
    def _get_search_suggestions(self, query):
        """الحصول على اقتراحات البحث"""
        suggestions = []
        
        if len(query) >= 2:
            # اقتراحات من البحثات الشائعة
            from .models import PopularSearch
            try:
                popular = PopularSearch.objects.filter(
                    query__icontains=query
                ).order_by('-search_count')[:5]
                
                for item in popular:
                    suggestions.append({
                        'text': item.query,
                        'type': 'popular',
                        'count': item.search_count
                    })
            except:
                pass
        
        # اقتراحات ثابتة
        static_suggestions = [
            'المنتجات', 'العملاء', 'الفواتير', 'المستخدمين',
            'الرسائل', 'الإشعارات', 'التقارير', 'الإعدادات'
        ]
        
        for suggestion in static_suggestions:
            if query.lower() in suggestion.lower():
                suggestions.append({
                    'text': suggestion,
                    'type': 'category',
                    'count': 0
                })
        
        return suggestions[:8]
    
    def _group_results_by_category(self, results):
        """تجميع النتائج حسب الفئة"""
        categories = {}
        
        for result in results:
            category = result['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(result)
        
        return categories
    
    def _save_search_history(self, user, query, results_count, execution_time):
        """حفظ تاريخ البحث"""
        try:
            from .models import SearchHistory, PopularSearch
            
            # حفظ في تاريخ البحث
            SearchHistory.objects.create(
                user=user,
                query=query,
                results_count=results_count,
                execution_time=execution_time
            )
            
            # تحديث البحثات الشائعة
            popular, created = PopularSearch.objects.get_or_create(
                query=query,
                defaults={'search_count': 1}
            )
            
            if not created:
                popular.search_count += 1
                popular.save()
        
        except Exception as e:
            print(f"خطأ في حفظ تاريخ البحث: {e}")

    def _get_demo_results(self, query):
        """الحصول على نتائج تجريبية للعرض"""
        demo_data = [
            {
                'title': 'لابتوب ديل XPS 13',
                'description': 'لابتوب عالي الأداء مع معالج Intel Core i7 وذاكرة 16GB',
                'category': 'المنتجات',
                'icon': 'bi-box',
                'url': '/products/1/',
                'keywords': ['لابتوب', 'ديل', 'كمبيوتر', 'منتج']
            },
            {
                'title': 'أحمد محمد علي',
                'description': 'عميل مميز - شركة التقنية المتقدمة - الرياض',
                'category': 'العملاء',
                'icon': 'bi-people',
                'url': '/customers/1/',
                'keywords': ['أحمد', 'محمد', 'علي', 'عميل', 'شركة']
            },
            {
                'title': 'فاتورة رقم INV-2024-001',
                'description': 'فاتورة مبيعات بتاريخ 2024-01-15 بقيمة 25,000 ريال',
                'category': 'الفواتير',
                'icon': 'bi-receipt',
                'url': '/invoices/1/',
                'keywords': ['فاتورة', 'مبيعات', '2024', 'ريال']
            },
            {
                'title': 'طابعة HP LaserJet Pro',
                'description': 'طابعة ليزر احترافية عالية الجودة للمكاتب',
                'category': 'المنتجات',
                'icon': 'bi-box',
                'url': '/products/2/',
                'keywords': ['طابعة', 'ليزر', 'مكتب', 'HP']
            },
            {
                'title': 'سارة أحمد الزهراني',
                'description': 'مديرة المشتريات - مؤسسة النور التجارية',
                'category': 'العملاء',
                'icon': 'bi-people',
                'url': '/customers/2/',
                'keywords': ['سارة', 'أحمد', 'الزهراني', 'مديرة', 'مشتريات']
            },
            {
                'title': 'محمد عبدالله المدير',
                'description': 'مدير النظام - صلاحيات كاملة',
                'category': 'المستخدمين',
                'icon': 'bi-person',
                'url': '/users/1/',
                'keywords': ['محمد', 'عبدالله', 'مدير', 'نظام', 'مستخدم']
            },
            {
                'title': 'رسالة: اجتماع الفريق',
                'description': 'دعوة لحضور اجتماع فريق التطوير يوم الأحد',
                'category': 'الرسائل',
                'icon': 'bi-envelope',
                'url': '/messages/1/',
                'keywords': ['رسالة', 'اجتماع', 'فريق', 'تطوير']
            },
            {
                'title': 'تنبيه: نفاد المخزون',
                'description': 'تحذير من نفاد مخزون منتج "ماوس لاسلكي"',
                'category': 'الإشعارات',
                'icon': 'bi-bell',
                'url': '/notifications/',
                'keywords': ['تنبيه', 'مخزون', 'ماوس', 'نفاد']
            },
            {
                'title': 'كيبورد لاسلكي Logitech',
                'description': 'كيبورد لاسلكي عالي الجودة مع إضاءة خلفية',
                'category': 'المنتجات',
                'icon': 'bi-box',
                'url': '/products/3/',
                'keywords': ['كيبورد', 'لاسلكي', 'لوجيتك', 'إضاءة']
            },
            {
                'title': 'فاتورة رقم INV-2024-002',
                'description': 'فاتورة خدمات بتاريخ 2024-01-20 بقيمة 15,500 ريال',
                'category': 'الفواتير',
                'icon': 'bi-receipt',
                'url': '/invoices/2/',
                'keywords': ['فاتورة', 'خدمات', '2024', 'ريال']
            }
        ]

        results = []
        query_lower = query.lower()

        for item in demo_data:
            # حساب درجة الصلة
            relevance_score = 0

            # فحص الكلمات المفتاحية
            for keyword in item['keywords']:
                if keyword.lower() in query_lower:
                    relevance_score += 10
                elif query_lower in keyword.lower():
                    relevance_score += 5

            # فحص العنوان والوصف
            if query_lower in item['title'].lower():
                relevance_score += 15
            if query_lower in item['description'].lower():
                relevance_score += 8

            # إضافة النتيجة إذا كانت ذات صلة
            if relevance_score > 0:
                result = {
                    'id': len(results) + 1,
                    'title': item['title'],
                    'description': item['description'],
                    'category': item['category'],
                    'icon': item['icon'],
                    'url': item['url'],
                    'relevance_score': relevance_score,
                    'model_name': 'Demo',
                    'highlight': self._highlight_text(item['title'], query)
                }
                results.append(result)

        return results


# إنشاء مثيل عام من محرك البحث
search_engine = SmartSearchEngine()
