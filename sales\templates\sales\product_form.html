<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if product %}تعديل المنتج{% else %}إضافة منتج جديد{% endif %} - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .form-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #e9ecef;
            border-radius: 15px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f093fb;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #f093fb;
            box-shadow: 0 0 0 0.2rem rgba(240, 147, 251, 0.25);
        }
        
        .btn-save {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            min-width: 150px;
        }
        
        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(240, 147, 251, 0.3);
            color: white;
        }
        
        .btn-cancel {
            background: #6c757d;
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            min-width: 150px;
        }
        
        .btn-cancel:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: white;
        }
        
        .required-field {
            color: #dc3545;
        }
        
        .help-text {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 5px;
        }
        
        .price-input-group {
            position: relative;
        }
        
        .currency-label {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-weight: 600;
        }
        
        .price-input {
            padding-left: 50px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-box-seam"></i>
                        {% if product %}تعديل المنتج: {{ product.name }}{% else %}إضافة منتج جديد{% endif %}
                    </h1>
                    <p class="mb-0">{% if product %}تعديل بيانات المنتج الموجود{% else %}إضافة منتج جديد إلى الكتالوج{% endif %}</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'sales:product_list' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <!-- نموذج المنتج -->
        <div class="form-container">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <!-- المعلومات الأساسية -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        المعلومات الأساسية
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    اسم المنتج <span class="required-field">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ form.name.value|default:'' }}" required>
                                <div class="help-text">أدخل اسم المنتج كما سيظهر في الفواتير</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="code" class="form-label">
                                    كود المنتج <span class="required-field">*</span>
                                </label>
                                <input type="text" class="form-control" id="code" name="code" 
                                       value="{{ form.code.value|default:'' }}" required>
                                <div class="help-text">كود فريد للمنتج (مثل: PRD001)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label">الفئة</label>
                                <input type="text" class="form-control" id="category" name="category" 
                                       value="{{ form.category.value|default:'' }}">
                                <div class="help-text">فئة المنتج (مثل: إلكترونيات، ملابس)</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unit" class="form-label">
                                    وحدة القياس <span class="required-field">*</span>
                                </label>
                                <select class="form-select" id="unit" name="unit" required>
                                    <option value="">اختر وحدة القياس</option>
                                    <option value="قطعة" {% if form.unit.value == 'قطعة' %}selected{% endif %}>قطعة</option>
                                    <option value="كيلو" {% if form.unit.value == 'كيلو' %}selected{% endif %}>كيلو</option>
                                    <option value="متر" {% if form.unit.value == 'متر' %}selected{% endif %}>متر</option>
                                    <option value="لتر" {% if form.unit.value == 'لتر' %}selected{% endif %}>لتر</option>
                                    <option value="علبة" {% if form.unit.value == 'علبة' %}selected{% endif %}>علبة</option>
                                    <option value="كرتونة" {% if form.unit.value == 'كرتونة' %}selected{% endif %}>كرتونة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ form.description.value|default:'' }}</textarea>
                        <div class="help-text">وصف تفصيلي للمنتج</div>
                    </div>
                </div>

                <!-- الأسعار -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-currency-dollar"></i>
                        الأسعار
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="unit_price_retail" class="form-label">
                                    سعر التجزئة <span class="required-field">*</span>
                                </label>
                                <div class="price-input-group">
                                    <input type="number" class="form-control price-input" id="unit_price_retail" 
                                           name="unit_price_retail" step="0.01" min="0"
                                           value="{{ form.unit_price_retail.value|default:'' }}" required>
                                    <span class="currency-label">ج.م</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="unit_price_wholesale" class="form-label">سعر الجملة</label>
                                <div class="price-input-group">
                                    <input type="number" class="form-control price-input" id="unit_price_wholesale" 
                                           name="unit_price_wholesale" step="0.01" min="0"
                                           value="{{ form.unit_price_wholesale.value|default:'' }}">
                                    <span class="currency-label">ج.م</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="cost_price" class="form-label">سعر التكلفة</label>
                                <div class="price-input-group">
                                    <input type="number" class="form-control price-input" id="cost_price" 
                                           name="cost_price" step="0.01" min="0"
                                           value="{{ form.cost_price.value|default:'' }}">
                                    <span class="currency-label">ج.م</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المخزون -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-boxes"></i>
                        إدارة المخزون
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="stock_quantity" class="form-label">
                                    الكمية الحالية <span class="required-field">*</span>
                                </label>
                                <input type="number" class="form-control" id="stock_quantity" 
                                       name="stock_quantity" min="0"
                                       value="{{ form.stock_quantity.value|default:'0' }}" required>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="min_stock_level" class="form-label">الحد الأدنى للمخزون</label>
                                <input type="number" class="form-control" id="min_stock_level" 
                                       name="min_stock_level" min="0"
                                       value="{{ form.min_stock_level.value|default:'0' }}">
                                <div class="help-text">تنبيه عند الوصول لهذا الحد</div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max_stock_level" class="form-label">الحد الأقصى للمخزون</label>
                                <input type="number" class="form-control" id="max_stock_level" 
                                       name="max_stock_level" min="0"
                                       value="{{ form.max_stock_level.value|default:'' }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- صورة المنتج -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-image"></i>
                        صورة المنتج
                    </h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="image" class="form-label">رفع صورة</label>
                                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                <div class="help-text">اختر صورة للمنتج (JPG, PNG, GIF)</div>
                            </div>
                        </div>
                        
                        {% if product and product.image %}
                            <div class="col-md-6">
                                <label class="form-label">الصورة الحالية</label>
                                <div>
                                    <img src="{{ product.image.url }}" alt="{{ product.name }}" class="image-preview">
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="text-center">
                    <button type="submit" class="btn btn-save me-3">
                        <i class="bi bi-check-circle"></i>
                        {% if product %}حفظ التعديلات{% else %}إضافة المنتج{% endif %}
                    </button>
                    <a href="{% url 'sales:product_list' %}" class="btn btn-cancel">
                        <i class="bi bi-x-circle"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للنموذج
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.form-section');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(30px)';
                    section.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });

            // حساب هامش الربح تلقائياً
            const costPrice = document.getElementById('cost_price');
            const retailPrice = document.getElementById('unit_price_retail');
            
            function calculateMargin() {
                const cost = parseFloat(costPrice.value) || 0;
                const retail = parseFloat(retailPrice.value) || 0;
                
                if (cost > 0 && retail > 0) {
                    const margin = ((retail - cost) / cost * 100).toFixed(2);
                    console.log(`هامش الربح: ${margin}%`);
                }
            }
            
            costPrice.addEventListener('input', calculateMargin);
            retailPrice.addEventListener('input', calculateMargin);
        });
    </script>
</body>
</html>
