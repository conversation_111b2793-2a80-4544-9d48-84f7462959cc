#!/usr/bin/env python
"""
سكريبت لإنشاء مستخدم تجريبي للنظام
"""
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from django.contrib.auth.models import User

def create_test_user():
    """إنشاء مستخدم تجريبي"""
    try:
        # التحقق من وجود مستخدمين
        users_count = User.objects.count()
        print(f"عدد المستخدمين الحاليين: {users_count}")
        
        # عرض المستخدمين الموجودين
        if users_count > 0:
            print("\nالمستخدمين الموجودين:")
            for user in User.objects.all():
                print(f"- {user.username} ({'نشط' if user.is_active else 'غير نشط'}) ({'مدير' if user.is_superuser else 'مستخدم عادي'})")
        
        # إنشاء مستخدم تجريبي إذا لم يكن موجود
        username = 'admin'
        if not User.objects.filter(username=username).exists():
            user = User.objects.create_user(
                username=username,
                email='<EMAIL>',
                password='admin123',
                is_staff=True,
                is_superuser=True
            )
            print(f"\n✅ تم إنشاء المستخدم: {username}")
            print(f"كلمة السر: admin123")
        else:
            print(f"\n⚠️ المستخدم {username} موجود بالفعل")
            
        # إنشاء مستخدم عادي للاختبار
        test_username = 'test'
        if not User.objects.filter(username=test_username).exists():
            test_user = User.objects.create_user(
                username=test_username,
                email='<EMAIL>',
                password='test123',
                is_staff=False,
                is_superuser=False
            )
            print(f"✅ تم إنشاء المستخدم التجريبي: {test_username}")
            print(f"كلمة السر: test123")
        else:
            print(f"⚠️ المستخدم التجريبي {test_username} موجود بالفعل")
            
        print("\n" + "="*50)
        print("معلومات تسجيل الدخول:")
        print("="*50)
        print("المدير:")
        print("  اسم المستخدم: admin")
        print("  كلمة السر: admin123")
        print("\nالمستخدم التجريبي:")
        print("  اسم المستخدم: test")
        print("  كلمة السر: test123")
        print("="*50)
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")

if __name__ == '__main__':
    create_test_user()
