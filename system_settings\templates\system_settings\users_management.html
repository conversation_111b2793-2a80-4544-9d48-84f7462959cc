{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة المستخدمين{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'system_settings:dashboard' %}">الإعدادات</a></li>
        <li class="breadcrumb-item active">إدارة المستخدمين</li>
    </ol>
</nav>
{% endblock %}

{% block extra_css %}
<style>
    .users-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 1rem;
    }

    /* تأكد من ظهور المحتوى تحت الشريط العلوي */
    .content-area {
        margin-top: 0;
        padding-top: 1rem;
    }

    .users-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .users-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .users-header .d-flex {
        position: relative;
        z-index: 2;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        padding: 2rem 1.5rem;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        text-align: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.6s ease;
    }

    .stat-card:hover::before {
        left: 100%;
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
    }

    .users-card {
        background: white;
        border-radius: 20px;
        padding: 0;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        overflow: hidden;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .users-card .search-filters {
        margin-bottom: 0;
        border-radius: 20px 20px 0 0;
        border: none;
        box-shadow: none;
        background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
        border-bottom: 1px solid rgba(226, 232, 240, 0.5);
        padding: 2rem;
        position: relative;
        overflow: visible;
        display: block !important;
        visibility: visible !important;
    }

    .users-card .search-filters * {
        display: block !important;
        visibility: visible !important;
    }

    .users-card .search-filters .search-content {
        display: block !important;
        width: 100% !important;
    }

    .users-card .search-filters .search-row {
        display: grid !important;
        grid-template-columns: 2fr 1fr 1fr !important;
        gap: 1.5rem !important;
        width: 100% !important;
    }

    .users-card .search-filters .search-input,
    .users-card .search-filters .filter-select {
        display: block !important;
        width: 100% !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .users-card .search-filters .action-buttons-row {
        display: flex !important;
        gap: 1rem !important;
        justify-content: center !important;
        flex-wrap: wrap !important;
        width: 100% !important;
    }

    .users-card .search-filters::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.08), transparent);
        animation: shimmer 4s infinite;
    }

    .users-card .search-filters::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
        border-radius: 20px 20px 0 0;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .users-card .search-filters:hover::after {
        opacity: 0.05;
    }

    .search-filters {
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 1.5rem;
        box-shadow:
            0 15px 35px rgba(102, 126, 234, 0.12),
            0 0 0 1px rgba(255,255,255,0.8),
            inset 0 1px 0 rgba(255,255,255,0.9);
        border: 1px solid rgba(102, 126, 234, 0.08);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .search-filters::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.08), transparent);
        animation: shimmer 4s infinite;
    }

    .search-filters::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
        border-radius: 25px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .search-filters:hover::after {
        opacity: 0.1;
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .search-header {
        position: relative;
        z-index: 2;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .search-title {
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 1.3rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .search-subtitle {
        color: #6b7280;
        font-size: 0.9rem;
        margin-top: 0.25rem;
        font-weight: 500;
        opacity: 0.8;
    }

    .search-row {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr;
        gap: 1.5rem;
        margin-bottom: 1.5rem;
        align-items: end;
        position: relative;
        z-index: 2;
        width: 100%;
    }

    .action-buttons-row {
        display: flex;
        gap: 1rem;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        position: relative;
        z-index: 2;
        width: 100%;
        margin-top: 1rem;
    }

    @media (max-width: 1200px) {
        .search-row {
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .action-buttons-row {
            justify-content: center;
            margin-top: 1rem;
        }
    }

    @media (max-width: 768px) {
        .search-row {
            grid-template-columns: 1fr;
        }

        .search-filters {
            padding: 1.5rem;
        }
    }

    .search-input {
        width: 100%;
        padding: 1rem 1.25rem 1rem 3rem;
        border: 2px solid rgba(226, 232, 240, 0.8);
        border-radius: 16px;
        font-size: 1rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        box-shadow:
            inset 0 2px 4px rgba(0,0,0,0.05),
            0 4px 12px rgba(0,0,0,0.05);
        position: relative;
        font-weight: 500;
    }

    .search-input-container {
        position: relative;
        width: 100%;
    }

    .search-input-container::before {
        content: '🔍';
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.1rem;
        z-index: 2;
        opacity: 0.6;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        border-color: #667eea;
        box-shadow:
            0 0 0 4px rgba(102, 126, 234, 0.15),
            inset 0 2px 4px rgba(0,0,0,0.05),
            0 8px 25px rgba(102, 126, 234, 0.1);
        outline: none;
        transform: translateY(-2px);
        background: #ffffff;
    }

    .search-input:focus + .search-input-container::before {
        opacity: 1;
        transform: translateY(-50%) scale(1.1);
    }

    .search-input::placeholder {
        color: #9ca3af;
        font-weight: 400;
    }

    .filter-select {
        padding: 1rem 1.25rem;
        border: 2px solid rgba(226, 232, 240, 0.8);
        border-radius: 16px;
        background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        width: 100%;
        font-size: 0.95rem;
        font-weight: 500;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow:
            inset 0 2px 4px rgba(0,0,0,0.05),
            0 4px 12px rgba(0,0,0,0.05);
        cursor: pointer;
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 1rem center;
        background-repeat: no-repeat;
        background-size: 1rem;
        padding-right: 3rem;
    }

    .filter-select:focus {
        border-color: #667eea;
        box-shadow:
            0 0 0 4px rgba(102, 126, 234, 0.15),
            inset 0 2px 4px rgba(0,0,0,0.05),
            0 8px 25px rgba(102, 126, 234, 0.1);
        outline: none;
        transform: translateY(-2px);
        background-color: #ffffff;
    }

    .filter-select:hover {
        border-color: rgba(102, 126, 234, 0.5);
        transform: translateY(-1px);
    }

    .filter-select option {
        padding: 0.75rem;
        background: white;
        color: #374151;
        font-weight: 500;
    }

    .btn-search {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 16px;
        font-weight: 700;
        font-size: 0.95rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        box-shadow:
            0 8px 20px rgba(102, 126, 234, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        text-decoration: none;
        white-space: nowrap;
    }

    .btn-search::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
    }

    .btn-search:hover::before {
        left: 100%;
    }

    .btn-search:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 12px 30px rgba(102, 126, 234, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        color: white;
    }

    .btn-search:active {
        transform: translateY(-1px) scale(0.98);
    }

    .btn-reset {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 16px;
        font-weight: 700;
        font-size: 0.95rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        box-shadow:
            0 8px 20px rgba(107, 114, 128, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        overflow: hidden;
        text-decoration: none;
        white-space: nowrap;
    }

    .btn-reset::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
    }

    .btn-reset:hover::before {
        left: 100%;
    }

    .btn-reset:hover {
        background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 12px 30px rgba(107, 114, 128, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        color: white;
    }

    .btn-reset:active {
        transform: translateY(-1px) scale(0.98);
    }

    .btn-add-user {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-add-user:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
        color: white;
    }

    .users-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
    }

    .users-table th,
    .users-table td {
        padding: 1rem 0.75rem;
        text-align: center;
        border-bottom: 1px solid #e5e7eb;
        vertical-align: middle;
    }

    .users-table th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        font-weight: 600;
        color: #374151;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .users-table tr:hover {
        background: #f8fafc;
        transform: scale(1.01);
        transition: all 0.2s ease;
    }

    .users-table tbody tr {
        transition: all 0.2s ease;
    }

    .user-avatar {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        margin: 0 auto;
        font-size: 1.1rem;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        border: 3px solid white;
        overflow: hidden;
        position: relative;
    }

    .user-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
    }

    /* تأثير النبض للضوء الأخضر */
    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
        }
    }

    /* تحسين عرض حالة الاتصال */
    .online-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
        white-space: nowrap;
    }

    .status-online {
        background: #dcfce7;
        color: #166534;
    }

    .status-offline {
        background: #f3f4f6;
        color: #6b7280;
    }

    .status-never {
        background: #fef2f2;
        color: #dc2626;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .indicator-online {
        background: #22c55e;
        animation: pulse 2s infinite;
    }

    .indicator-offline {
        background: #9ca3af;
    }

    .indicator-never {
        background: #ef4444;
    }

    .user-info {
        text-align: center;
    }

    .user-name {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
    }

    .user-username {
        color: #6b7280;
        font-size: 0.8rem;
        font-style: italic;
    }

    .user-email {
        color: #4b5563;
        font-size: 0.85rem;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-active {
        background: #d1fae5;
        color: #065f46;
    }

    .status-inactive {
        background: #fee2e2;
        color: #991b1b;
    }

    .status-staff {
        background: #dbeafe;
        color: #1e40af;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-action {
        padding: 0.6rem;
        border: none;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        width: 38px;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        position: relative;
        overflow: hidden;
    }

    .btn-action::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s ease;
    }

    .btn-action:hover::before {
        left: 100%;
    }

    .btn-edit {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .btn-edit:hover {
        background: linear-gradient(135deg, #2563eb, #1e40af);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    }

    .btn-permissions {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .btn-permissions:hover {
        background: linear-gradient(135deg, #059669, #047857);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }

    .btn-delete {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }

    .btn-delete:hover {
        background: linear-gradient(135deg, #dc2626, #b91c1c);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
    }

    .btn-toggle {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    }

    .btn-toggle:hover {
        background: linear-gradient(135deg, #d97706, #b45309);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
    }

    .btn-view {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .btn-view:hover {
        background: linear-gradient(135deg, #059669, #047857);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }

    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
        gap: 0.5rem;
    }

    .page-link {
        padding: 0.5rem 1rem;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        text-decoration: none;
        color: #374151;
        transition: all 0.3s ease;
    }

    .page-link:hover,
    .page-link.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .no-users {
        text-align: center;
        padding: 4rem 2rem;
        color: #6b7280;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 15px;
        border: 2px dashed #cbd5e1;
    }

    .no-users i {
        font-size: 5rem;
        margin-bottom: 1.5rem;
        opacity: 0.4;
        color: #94a3b8;
    }

    .no-users h3 {
        color: #475569;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .no-users p {
        color: #64748b;
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    /* تحسينات إضافية */
    .table-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0;
    }

    .table-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin: 0;
    }

    .table-count {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }

    /* إصلاح مشكلة عدم ظهور العناصر - قواعد قوية */
    .users-card .search-filters .search-input-container,
    .users-card .search-filters .search-input,
    .users-card .search-filters .filter-select,
    .users-card .search-filters .search-content,
    .users-card .search-filters .search-row,
    .users-card .search-filters .action-buttons-row,
    .users-card .search-filters form,
    .users-card .search-filters form > *,
    .search-input-container,
    .search-input,
    .filter-select,
    .search-content,
    .search-row,
    .action-buttons-row {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 10 !important;
        height: auto !important;
        width: auto !important;
        max-width: 100% !important;
        overflow: visible !important;
    }

    .users-card .search-filters .search-row,
    .search-row {
        display: grid !important;
        grid-template-columns: 2fr 1fr 1fr !important;
        gap: 1.5rem !important;
    }

    .users-card .search-filters .action-buttons-row,
    .action-buttons-row {
        display: flex !important;
        flex-wrap: wrap !important;
        justify-content: center !important;
        gap: 1rem !important;
    }

    /* تأكد من ظهور الـ input والـ select */
    input.search-input,
    select.filter-select {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        width: 100% !important;
        height: auto !important;
        min-height: 50px !important;
        border: 2px solid rgba(226, 232, 240, 0.8) !important;
        background: white !important;
        color: #374151 !important;
        font-size: 1rem !important;
        padding: 1rem !important;
        border-radius: 16px !important;
    }

    /* تحسين التمرير */
    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }

    .table-responsive::-webkit-scrollbar {
        width: 8px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: #f1f5f9;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* تحسين الرسوم المتحركة */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .users-table tbody tr {
        animation: fadeInUp 0.3s ease-out;
    }

    .users-table tbody tr:nth-child(even) {
        animation-delay: 0.1s;
    }

    .users-table tbody tr:nth-child(odd) {
        animation-delay: 0.05s;
    }
</style>
{% endblock %}

{% block content %}
<div class="users-container">
    <!-- Header -->
    <div class="users-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="bi bi-people-fill me-2"></i>إدارة المستخدمين</h1>
                <p class="mb-0">إدارة حسابات المستخدمين وصلاحياتهم</p>
            </div>
            <a href="{% url 'system_settings:dashboard' %}" class="btn btn-outline-light">
                <i class="bi bi-arrow-right me-2"></i>العودة
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-icon" style="color: #10b981;">
                <i class="bi bi-people"></i>
            </div>
            <div class="stat-number" style="color: #10b981;">{{ stats.total_users }}</div>
            <div class="stat-label">إجمالي المستخدمين</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="color: #3b82f6;">
                <i class="bi bi-person-check"></i>
            </div>
            <div class="stat-number" style="color: #3b82f6;">{{ stats.active_users }}</div>
            <div class="stat-label">المستخدمين النشطين</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="color: #f59e0b;">
                <i class="bi bi-person-x"></i>
            </div>
            <div class="stat-number" style="color: #f59e0b;">{{ stats.inactive_users }}</div>
            <div class="stat-label">المستخدمين غير النشطين</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="color: #8b5cf6;">
                <i class="bi bi-shield-check"></i>
            </div>
            <div class="stat-number" style="color: #8b5cf6;">{{ stats.staff_users }}</div>
            <div class="stat-label">المديرين</div>
        </div>
    </div>

    <!-- Search Bar Section -->
    <div style="background: white; border-radius: 20px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); margin-bottom: 2rem; overflow: hidden;">
        <!-- Search Header -->
        <div style="background: linear-gradient(135deg, #667eea, #764ba2); padding: 1.5rem 2rem; color: white;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <i class="bi bi-search" style="font-size: 1.5rem;"></i>
                    <div>
                        <h3 style="margin: 0; font-size: 1.3rem; font-weight: 700;">البحث والفلترة</h3>
                        <p style="margin: 0; font-size: 0.9rem; opacity: 0.9;">ابحث وفلتر المستخدمين بسهولة</p>
                    </div>
                </div>
                <div style="display: flex; gap: 0.5rem;">
                    <button onclick="resetFilters()" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 0.5rem 1rem; border-radius: 8px; font-size: 0.85rem; cursor: pointer;">
                        <i class="bi bi-arrow-clockwise"></i> إعادة تعيين
                    </button>
                    <button onclick="exportUsers()" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 0.5rem 1rem; border-radius: 8px; font-size: 0.85rem; cursor: pointer;">
                        <i class="bi bi-download"></i> تصدير
                    </button>
                </div>
            </div>
        </div>

        <!-- Search Content -->
        <div style="padding: 2rem;">
            <!-- Search Form -->
            <form method="get" style="width: 100%;">
                <!-- Main Search Bar -->
                <div style="position: relative; margin-bottom: 1.5rem;">
                    <i class="bi bi-search" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: #6b7280; font-size: 1.1rem; z-index: 2;"></i>
                    <input type="text"
                           name="search"
                           placeholder="البحث بالاسم أو البريد الإلكتروني أو اسم المستخدم..."
                           value="{{ request.GET.search }}"
                           style="width: 100%; padding: 1rem 1rem 1rem 3rem; border: 2px solid #e2e8f0; border-radius: 16px; font-size: 1rem; background: white; box-shadow: 0 4px 12px rgba(0,0,0,0.05); transition: all 0.3s ease; outline: none;"
                           onfocus="this.style.borderColor='#667eea'; this.style.boxShadow='0 0 0 3px rgba(102, 126, 234, 0.1)'"
                           onblur="this.style.borderColor='#e2e8f0'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.05)'">
                </div>

                <!-- Filters Row -->
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                    <!-- Status Filter -->

                    <!-- Status Filter -->
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #374151; font-size: 0.9rem;">الحالة</label>
                        <select name="status" style="width: 100%; padding: 0.75rem 1rem; border: 2px solid #e2e8f0; border-radius: 12px; background: white; font-size: 0.9rem; cursor: pointer; transition: all 0.3s ease;"
                                onfocus="this.style.borderColor='#667eea'"
                                onblur="this.style.borderColor='#e2e8f0'">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>نشط</option>
                            <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>غير نشط</option>
                            <option value="staff" {% if request.GET.status == 'staff' %}selected{% endif %}>مدير</option>
                            <option value="superuser" {% if request.GET.status == 'superuser' %}selected{% endif %}>مدير عام</option>
                        </select>
                    </div>

                    <!-- Sort Filter -->
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #374151; font-size: 0.9rem;">ترتيب حسب</label>
                        <select name="sort" style="width: 100%; padding: 0.75rem 1rem; border: 2px solid #e2e8f0; border-radius: 12px; background: white; font-size: 0.9rem; cursor: pointer; transition: all 0.3s ease;"
                                onfocus="this.style.borderColor='#667eea'"
                                onblur="this.style.borderColor='#e2e8f0'">
                            <option value="">الترتيب الافتراضي</option>
                            <option value="name" {% if request.GET.sort == 'name' %}selected{% endif %}>الاسم</option>
                            <option value="email" {% if request.GET.sort == 'email' %}selected{% endif %}>البريد الإلكتروني</option>
                            <option value="date_joined" {% if request.GET.sort == 'date_joined' %}selected{% endif %}>تاريخ الانضمام</option>
                            <option value="last_login" {% if request.GET.sort == 'last_login' %}selected{% endif %}>آخر دخول</option>
                        </select>
                    </div>

                    <!-- Quick Actions -->
                    <div>
                        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600; color: #374151; font-size: 0.9rem;">إجراءات سريعة</label>
                        <div style="display: flex; gap: 0.5rem;">
                            <button type="submit" style="flex: 1; padding: 0.75rem; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 12px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.85rem;"
                                    onmouseover="this.style.transform='translateY(-2px)'"
                                    onmouseout="this.style.transform='translateY(0)'">
                                <i class="bi bi-search"></i> بحث
                            </button>
                            <a href="{% url 'system_settings:user_create' %}" style="flex: 1; padding: 0.75rem; background: linear-gradient(135deg, #10b981, #059669); color: white; border: none; border-radius: 12px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; font-size: 0.85rem; text-decoration: none; text-align: center; display: flex; align-items: center; justify-content: center;"
                               onmouseover="this.style.transform='translateY(-2px)'"
                               onmouseout="this.style.transform='translateY(0)'">
                                <i class="bi bi-person-plus"></i> إضافة
                            </a>
                        </div>
                    </div>
                </div>

            </form>
        </div>
    </div>

    <!-- Users Management -->
    <div class="users-card">

        <!-- Users Table -->
        {% if users %}
            <div style="background: white; padding: 2rem; border-radius: 0 0 20px 20px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); margin-top: 0;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 2px solid #f1f5f9;">
                    <h3 style="background: linear-gradient(135deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-size: 1.3rem; font-weight: 700; margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="bi bi-people-fill"></i>
                        قائمة المستخدمين
                    </h3>
                    <span style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 0.5rem 1rem; border-radius: 12px; font-weight: 600; font-size: 0.9rem;">{{ users|length }} مستخدم</span>
                </div>

                <div style="overflow-x: auto; border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                    <table class="users-table">
                    <thead>
                        <tr>
                            <th style="width: 60px;">👤</th>
                            <th style="width: 200px;">👨‍💼 معلومات المستخدم</th>
                            <th style="width: 180px;">📧 البريد الإلكتروني</th>
                            <th style="width: 140px;">🟢 حالة الاتصال</th>
                            <th style="width: 130px;">📅 تاريخ الانضمام</th>
                            <th style="width: 130px;">🕐 آخر دخول</th>
                            <th style="width: 150px;">⚙️ الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                            <tr data-user-id="{{ user.id }}">
                                <td>
                                    <div class="user-avatar">
                                        {% if user.profile and user.profile.avatar %}
                                            <img src="{{ user.profile.avatar.url }}" alt="{{ user.get_full_name|default:user.username }}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;">
                                        {% else %}
                                            {{ user.first_name.0|default:user.username.0|upper }}
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-name">{{ user.get_full_name|default:user.username }}</div>
                                        <div class="user-username">@{{ user.username }}</div>
                                        {% if user.is_superuser %}
                                            <small style="color: #dc2626; font-weight: 600;">🔥 مدير عام</small>
                                        {% elif user.is_staff %}
                                            <small style="color: #2563eb; font-weight: 600;">👑 مدير</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="user-email">
                                        {% if user.email %}
                                            <i class="bi bi-envelope me-1"></i>{{ user.email }}
                                        {% else %}
                                            <span style="color: #9ca3af;">غير محدد</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="online-status-cell">
                                    <div style="display: flex; flex-direction: column; align-items: center; gap: 0.25rem;">
                                        <!-- حالة الاتصال -->
                                        {% if user.profile and user.profile.is_user_online %}
                                            <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.25rem 0.75rem; background: #dcfce7; color: #166534; border-radius: 12px; font-size: 0.8rem; font-weight: 600;">
                                                <div style="width: 8px; height: 8px; background: #22c55e; border-radius: 50%; animation: pulse 2s infinite;"></div>
                                                متصل الآن
                                            </div>
                                        {% elif user.profile and user.profile.last_activity %}
                                            <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.25rem 0.75rem; background: #f3f4f6; color: #6b7280; border-radius: 12px; font-size: 0.8rem; font-weight: 600;">
                                                <div style="width: 8px; height: 8px; background: #9ca3af; border-radius: 50%;"></div>
                                                {{ user.profile.get_online_status_display }}
                                            </div>
                                        {% else %}
                                            <div style="display: flex; align-items: center; gap: 0.5rem; padding: 0.25rem 0.75rem; background: #fef2f2; color: #dc2626; border-radius: 12px; font-size: 0.8rem; font-weight: 600;">
                                                <div style="width: 8px; height: 8px; background: #ef4444; border-radius: 50%;"></div>
                                                غير متصل
                                            </div>
                                        {% endif %}

                                        <!-- نوع المستخدم -->
                                        {% if user.is_superuser %}
                                            <span style="background: #fef3c7; color: #92400e; padding: 0.125rem 0.5rem; border-radius: 8px; font-size: 0.7rem; font-weight: 600;">🔥 مدير عام</span>
                                        {% elif user.is_staff %}
                                            <span style="background: #dbeafe; color: #1e40af; padding: 0.125rem 0.5rem; border-radius: 8px; font-size: 0.7rem; font-weight: 600;">👑 مدير</span>
                                        {% elif not user.is_active %}
                                            <span style="background: #fef2f2; color: #dc2626; padding: 0.125rem 0.5rem; border-radius: 8px; font-size: 0.7rem; font-weight: 600;">❌ معطل</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div style="text-align: center;">
                                        <i class="bi bi-calendar-plus me-1" style="color: #6b7280;"></i>
                                        {{ user.date_joined|date:"d/m/Y" }}
                                        <br>
                                        <small style="color: #9ca3af;">{{ user.date_joined|date:"H:i" }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div style="text-align: center;">
                                        {% if user.last_login %}
                                            <i class="bi bi-clock me-1" style="color: #10b981;"></i>
                                            {{ user.last_login|date:"d/m/Y" }}
                                            <br>
                                            <small style="color: #9ca3af;">{{ user.last_login|date:"H:i" }}</small>
                                        {% else %}
                                            <span style="color: #ef4444;">
                                                <i class="bi bi-x-circle me-1"></i>
                                                لم يسجل دخول
                                            </span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-action btn-view" title="عرض التفاصيل" onclick="viewUser({{ user.id }})">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn-action btn-edit" title="تعديل المستخدم" onclick="editUser({{ user.id }})">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn-action btn-permissions" title="إدارة الصلاحيات" onclick="managePermissions({{ user.id }})">
                                            <i class="bi bi-shield-check"></i>
                                        </button>
                                        <button class="btn-action btn-toggle" title="تغيير الحالة" onclick="toggleUser({{ user.id }}, {{ user.is_active|yesno:'true,false' }})">
                                            <i class="bi bi-toggle-{% if user.is_active %}on{% else %}off{% endif %}"></i>
                                        </button>
                                        {% if not user.is_superuser %}
                                            <button class="btn-action btn-delete" title="حذف المستخدم" onclick="deleteUser({{ user.id }}, '{{ user.username }}')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
                <div class="pagination">
                    {% if page_obj.has_previous %}
                        <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" class="page-link">الأولى</a>
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" class="page-link">السابقة</a>
                    {% endif %}

                    <span class="page-link active">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>

                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" class="page-link">التالية</a>
                        <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" class="page-link">الأخيرة</a>
                    {% endif %}
                </div>
            {% endif %}
            </div>
        {% else %}
            <div style="text-align: center; padding: 4rem 2rem; background: white; border-radius: 20px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                <i class="bi bi-people" style="font-size: 4rem; color: #9ca3af; margin-bottom: 1rem;"></i>
                <h3 style="color: #374151; font-size: 1.5rem; margin-bottom: 0.5rem;">لا توجد مستخدمين</h3>
                <p style="color: #6b7280; font-size: 1rem;">لم يتم العثور على مستخدمين مطابقين لمعايير البحث</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
// عرض تفاصيل المستخدم
function viewUser(userId) {
    window.location.href = '{% url "system_settings:user_detail" user_id=0 %}'.replace('0', userId);
}

// تعديل المستخدم
function editUser(userId) {
    window.location.href = '{% url "system_settings:user_edit" user_id=0 %}'.replace('0', userId);
}

// إدارة صلاحيات المستخدم
function managePermissions(userId) {
    window.location.href = '{% url "permissions:user_permissions" user_id=0 %}'.replace('0', userId);
}

// تغيير حالة المستخدم
function toggleUser(userId, currentStatus) {
    const newStatus = currentStatus ? 'تعطيل' : 'تفعيل';
    const confirmMessage = `هل أنت متأكد من ${newStatus} هذا المستخدم؟`;

    if (confirm(confirmMessage)) {
        console.log('تغيير حالة المستخدم:', userId, 'إلى:', !currentStatus);
        showNotification(`تم ${newStatus} المستخدم بنجاح`, 'success');

        // إعادة تحميل الصفحة بعد ثانيتين
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }
}

// حذف المستخدم
function deleteUser(userId, username) {
    const confirmMessage = `⚠️ تحذير!\n\nهل أنت متأكد من حذف المستخدم "${username}"؟\n\nهذا الإجراء لا يمكن التراجع عنه!`;

    if (confirm(confirmMessage)) {
        console.log('حذف المستخدم:', userId);
        showNotification('تم حذف المستخدم بنجاح', 'success');

        // إعادة تحميل الصفحة بعد ثانيتين
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    }
}

// تصدير بيانات المستخدمين
function exportUsers() {
    showNotification('جاري تصدير بيانات المستخدمين...', 'info');

    // محاكاة عملية التصدير
    setTimeout(() => {
        showNotification('تم تصدير البيانات بنجاح!', 'success');
    }, 2000);
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.style.borderRadius = '10px';
    notification.style.boxShadow = '0 10px 30px rgba(0,0,0,0.2)';

    // أيقونة حسب النوع
    let icon = '';
    switch(type) {
        case 'success':
            icon = '<i class="bi bi-check-circle me-2"></i>';
            break;
        case 'info':
            icon = '<i class="bi bi-info-circle me-2"></i>';
            break;
        case 'warning':
            icon = '<i class="bi bi-exclamation-triangle me-2"></i>';
            break;
        case 'danger':
            icon = '<i class="bi bi-x-circle me-2"></i>';
            break;
    }

    notification.innerHTML = `
        ${icon}${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// تحسين تجربة البحث والفلترة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-input');
    const filterSelects = document.querySelectorAll('.filter-select');
    const searchForm = document.querySelector('.search-content form');

    // تحسين تفاعل البحث
    if (searchInput) {
        let searchTimeout;

        // البحث المباشر
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const searchContainer = this.closest('.search-input-container');

            // إضافة مؤشر التحميل
            searchContainer.style.position = 'relative';
            if (this.value.length > 2) {
                searchTimeout = setTimeout(() => {
                    console.log('البحث عن:', this.value);
                    // يمكن إضافة AJAX search هنا
                }, 500);
            }
        });

        // تأثيرات التركيز
        searchInput.addEventListener('focus', function() {
            const container = this.closest('.search-input-container');
            container.style.transform = 'scale(1.02)';
            container.style.zIndex = '10';
        });

        searchInput.addEventListener('blur', function() {
            const container = this.closest('.search-input-container');
            container.style.transform = 'scale(1)';
            container.style.zIndex = '1';
        });

        // تأثير الكتابة
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchForm.submit();
                showNotification('جاري البحث...', 'info');
            }
        });
    }

    // تحسين القوائم المنسدلة
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            this.style.transform = 'scale(1.02)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);

            // إرسال النموذج تلقائياً عند تغيير الفلتر
            if (this.value !== '') {
                showNotification('جاري تطبيق الفلتر...', 'info');
                setTimeout(() => {
                    searchForm.submit();
                }, 300);
            }
        });

        select.addEventListener('focus', function() {
            this.style.zIndex = '10';
        });

        select.addEventListener('blur', function() {
            this.style.zIndex = '1';
        });
    });

    // تأثيرات الأزرار
    const actionButtons = document.querySelectorAll('.btn-search, .btn-reset, .btn-add-user');
    actionButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });

        button.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-1px) scale(0.98)';
        });

        button.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });
    });

    // تأثير الشريط المتحرك
    const searchFilters = document.querySelector('.search-filters');
    if (searchFilters) {
        searchFilters.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 30px 60px rgba(102, 126, 234, 0.2)';
        });

        searchFilters.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 25px 50px rgba(102, 126, 234, 0.15)';
        });
    }

    // تحسين تأثيرات الجدول
    const tableRows = document.querySelectorAll('.users-table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f0f9ff';
            this.style.borderLeft = '4px solid #667eea';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.borderLeft = '';
        });
    });
});

// إعادة تعيين الفلاتر
function resetFilters() {
    window.location.href = '{% url "system_settings:users_management" %}';
}

// توجيه لصفحة إضافة مستخدم
function showAddUserModal() {
    window.location.href = '{% url "system_settings:user_create" %}';
}

// تحديث الإحصائيات تلقائياً
function updateStats() {
    // يمكن إضافة AJAX لتحديث الإحصائيات
    console.log('تحديث الإحصائيات...');
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(updateStats, 30000);

// تحديث حالة الاتصال للمستخدمين كل دقيقة
function updateOnlineStatus() {
    // يمكن إضافة AJAX لتحديث حالة الاتصال
    fetch('{% url "system_settings:users_management" %}?ajax=online_status')
        .then(response => response.json())
        .then(data => {
            // تحديث حالة المستخدمين في الجدول
            if (data.users) {
                data.users.forEach(user => {
                    const userRow = document.querySelector(`tr[data-user-id="${user.id}"]`);
                    if (userRow) {
                        const statusCell = userRow.querySelector('.online-status-cell');
                        if (statusCell) {
                            statusCell.innerHTML = user.status_html;
                        }
                    }
                });
            }
        })
        .catch(error => {
            console.log('خطأ في تحديث حالة الاتصال:', error);
        });
}

// تحديث حالة الاتصال كل دقيقة
setInterval(updateOnlineStatus, 60000);
</script>
{% endblock %}
