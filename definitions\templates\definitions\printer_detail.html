{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الطابعة - {{ printer.name }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        min-height: 100vh;
    }

    .detail-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
        color: #333;
    }

    .detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 3rem;
        padding-bottom: 2rem;
        border-bottom: 3px solid rgba(52, 73, 94, 0.3);
    }

    .detail-title {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .printer-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 2rem;
        margin-right: 1rem;
        box-shadow: 0 10px 30px rgba(52, 73, 94, 0.3);
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .btn {
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 700;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.95rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(52, 73, 94, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
    }

    .btn-danger:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(220, 53, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    .detail-section {
        background: rgba(52, 73, 94, 0.1);
        backdrop-filter: blur(5px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(52, 73, 94, 0.2);
    }

    .section-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #34495e;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
    }

    .detail-label {
        font-weight: 700;
        color: #34495e;
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-value {
        color: #333;
        font-size: 1.1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 15px;
        border: 1px solid rgba(52, 73, 94, 0.2);
        min-height: 50px;
        display: flex;
        align-items: center;
    }

    .detail-value.empty {
        color: #999;
        font-style: italic;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .status-badge.active {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .status-badge.inactive {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
    }

    .status-badge.thermal {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        color: white;
    }

    .status-badge.inkjet {
        background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
        color: white;
    }

    .status-badge.laser {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        color: white;
    }

    .status-badge.dot_matrix {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
    }

    .connection-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background: rgba(52, 73, 94, 0.2);
        border-radius: 15px;
        font-size: 0.9rem;
        color: #34495e;
        font-weight: 600;
    }

    .alert {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.18);
        margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
        .detail-container {
            padding: 2rem;
        }
        
        .detail-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1.5rem;
        }
        
        .detail-title {
            font-size: 2rem;
        }
        
        .action-buttons {
            width: 100%;
            justify-content: stretch;
        }
        
        .btn {
            flex: 1;
            justify-content: center;
        }
        
        .detail-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="detail-container">
                <div class="detail-header">
                    <div class="d-flex align-items-center">
                        <div class="printer-icon">
                            <i class="bi bi-printer"></i>
                        </div>
                        <div>
                            <h1 class="detail-title">
                                <i class="bi bi-printer"></i>
                                {{ printer.name }}
                            </h1>
                            {% if printer.model %}
                            <p class="text-muted mb-0" style="font-size: 1.1rem;">
                                <i class="bi bi-cpu me-1"></i>
                                موديل: {{ printer.model }}
                            </p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-house me-2"></i>التعريفات
                        </a>
                        <a href="{% url 'definitions:printer_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>قائمة الطابعات
                        </a>
                        <a href="{% url 'definitions:printer_edit' printer.id %}" class="btn btn-primary">
                            <i class="bi bi-pencil me-2"></i>تعديل
                        </a>
                        <button type="button" class="btn btn-danger" onclick="deletePrinter({{ printer.id }}, '{{ printer.name|escapejs }}')">
                            <i class="bi bi-trash me-2"></i>حذف
                        </button>
                    </div>
                </div>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Basic Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        المعلومات الأساسية
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-printer"></i>اسم الطابعة
                            </div>
                            <div class="detail-value">{{ printer.name }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-geo-alt"></i>الموقع
                            </div>
                            <div class="detail-value {% if not printer.location %}empty{% endif %}">
                                {{ printer.location|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-toggle-on"></i>الحالة
                            </div>
                            <div class="detail-value">
                                <span class="status-badge {% if printer.is_active %}active{% else %}inactive{% endif %}">
                                    {% if printer.is_active %}
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    {% else %}
                                        <i class="bi bi-x-circle me-1"></i>غير نشط
                                    {% endif %}
                                </span>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-star"></i>افتراضية للإيصالات
                            </div>
                            <div class="detail-value">
                                {% if printer.default_for_receipts %}
                                    <i class="bi bi-star-fill text-warning me-2"></i>نعم
                                {% else %}
                                    <i class="bi bi-star text-muted me-2"></i>لا
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technical Specifications Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-gear"></i>
                        المواصفات التقنية
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-printer"></i>نوع الطابعة
                            </div>
                            <div class="detail-value">
                                <span class="status-badge {{ printer.printer_type }}">
                                    {% if printer.printer_type == 'thermal' %}
                                        <i class="bi bi-thermometer me-1"></i>حرارية
                                    {% elif printer.printer_type == 'inkjet' %}
                                        <i class="bi bi-droplet me-1"></i>نفث حبر
                                    {% elif printer.printer_type == 'laser' %}
                                        <i class="bi bi-lightning me-1"></i>ليزر
                                    {% else %}
                                        <i class="bi bi-grid-3x3 me-1"></i>نقطية
                                    {% endif %}
                                </span>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-wifi"></i>نوع الاتصال
                            </div>
                            <div class="detail-value">
                                <span class="connection-badge">
                                    {% if printer.connection_type == 'usb' %}
                                        <i class="bi bi-usb"></i>USB
                                    {% elif printer.connection_type == 'network' %}
                                        <i class="bi bi-wifi"></i>شبكة
                                    {% elif printer.connection_type == 'bluetooth' %}
                                        <i class="bi bi-bluetooth"></i>بلوتوث
                                    {% else %}
                                        <i class="bi bi-hdd"></i>محلي
                                    {% endif %}
                                </span>
                            </div>
                        </div>

                        {% if printer.ip_address %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-router"></i>عنوان IP
                            </div>
                            <div class="detail-value">{{ printer.ip_address }}</div>
                        </div>
                        {% endif %}

                        {% if printer.port %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-outlet"></i>المنفذ
                            </div>
                            <div class="detail-value">{{ printer.port }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Additional Information Section -->
                {% if printer.description or printer.notes %}
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-file-text"></i>
                        معلومات إضافية
                    </h3>
                    
                    <div class="detail-grid">
                        {% if printer.description %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-file-text"></i>الوصف
                            </div>
                            <div class="detail-value">{{ printer.description|linebreaks }}</div>
                        </div>
                        {% endif %}

                        {% if printer.notes %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-journal-text"></i>ملاحظات
                            </div>
                            <div class="detail-value">{{ printer.notes|linebreaks }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- System Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-gear"></i>
                        معلومات النظام
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-person-plus"></i>أنشأ بواسطة
                            </div>
                            <div class="detail-value">
                                {{ printer.created_by.get_full_name|default:printer.created_by.username }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-plus"></i>تاريخ الإنشاء
                            </div>
                            <div class="detail-value">
                                {{ printer.created_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>

                        {% if printer.updated_at %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-check"></i>آخر تحديث
                            </div>
                            <div class="detail-value">
                                {{ printer.updated_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deletePrinter(printerId, printerName) {
    if (confirm('هل أنت متأكد من حذف الطابعة "' + printerName + '"؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        // إنشاء نموذج مخفي وإرساله
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/definitions/printers/' + printerId + '/quick-delete/';
        
        // إضافة CSRF token
        var csrfToken = '{{ csrf_token }}';
        var csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        // إضافة النموذج للصفحة وإرساله
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
