<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صندوق الوارد - الرسائل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            padding-top: 2rem;
        }
        
        .messaging-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .messaging-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .messaging-body {
            padding: 2rem;
        }
        
        .message-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .message-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .message-unread {
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        
        .btn-compose {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .btn-compose:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="messaging-card">
                    <div class="messaging-header">
                        <h1><i class="bi bi-envelope me-3"></i>صندوق الوارد</h1>
                        <p class="mb-0">نظام الرسائل المتكامل</p>
                    </div>
                    
                    <div class="messaging-body">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h3>الرسائل الواردة</h3>
                            <a href="/messages/compose/" class="btn-compose">
                                <i class="bi bi-plus-circle"></i>
                                رسالة جديدة
                            </a>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-primary">{{ stats.total|default:0 }}</h4>
                                        <p class="mb-0">إجمالي الرسائل</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-warning">{{ stats.unread|default:0 }}</h4>
                                        <p class="mb-0">غير مقروءة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-success">{{ stats.starred|default:0 }}</h4>
                                        <p class="mb-0">مميزة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="messages-list">
                            {% for message in messages %}
                            <div class="message-item {% if not message.is_read %}message-unread{% endif %}">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                                {% if message.sender.first_name %}{{ message.sender.first_name.0|upper }}{% elif message.sender.username %}{{ message.sender.username.0|upper }}{% else %}U{% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="mb-1">{{ message.subject }}</h6>
                                        <p class="text-muted mb-1">من: {{ message.sender.get_full_name|default:message.sender.username }}</p>
                                        <p class="mb-0">{{ message.content|truncatechars:100 }}</p>
                                    </div>
                                    <div class="col-md-2">
                                        <small class="text-muted">{{ message.time_since_sent }}</small>
                                    </div>
                                    <div class="col-md-2">
                                        <a href="/messages/message/{{ message.id }}/" class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-eye"></i> عرض
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-center py-5">
                                <i class="bi bi-inbox" style="font-size: 4rem; color: #dee2e6;"></i>
                                <h4 class="mt-3 text-muted">لا توجد رسائل</h4>
                                <p class="text-muted">صندوق الوارد فارغ</p>
                                <a href="/messages/compose/" class="btn-compose">
                                    <i class="bi bi-plus-circle"></i>
                                    إرسال أول رسالة
                                </a>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Navigation -->
                        {% if messages.has_other_pages %}
                        <nav class="mt-4">
                            <ul class="pagination justify-content-center">
                                {% if messages.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ messages.previous_page_number }}">السابق</a>
                                </li>
                                {% endif %}
                                
                                {% for num in messages.paginator.page_range %}
                                {% if num == messages.number %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                                {% endif %}
                                {% endfor %}
                                
                                {% if messages.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ messages.next_page_number }}">التالي</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                        
                        <div class="mt-4 text-center">
                            <a href="/" class="btn btn-secondary">
                                <i class="bi bi-house"></i> العودة للرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
