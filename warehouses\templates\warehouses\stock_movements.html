{% extends 'base.html' %}

{% block title %}{% if user_language == 'en' %}Stock Movements - Osaric{% else %}حركات المخزون - أوساريك{% endif %}{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">{% if user_language == 'en' %}Home{% else %}الرئيسية{% endif %}</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'warehouses:dashboard' %}">{% if user_language == 'en' %}Warehouses{% else %}المخازن{% endif %}</a></li>
                    <li class="breadcrumb-item active">{% if user_language == 'en' %}Stock Movements{% else %}حركات المخزون{% endif %}</li>
                </ol>
            </nav>
            <h1 class="page-title">{% if user_language == 'en' %}Stock Movements{% else %}حركات المخزون{% endif %}</h1>
            <p class="page-subtitle">{% if user_language == 'en' %}Track all incoming and outgoing product movements{% else %}متابعة جميع حركات الوارد والصادر للمنتجات{% endif %}</p>
        </div>
        <a href="#" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>{% if user_language == 'en' %}Add New Movement{% else %}إضافة حركة جديدة{% endif %}
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}" 
                               placeholder="البحث في المنتجات أو رقم المرجع...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="warehouse" class="form-select">
                        <option value="">جميع المخازن</option>
                        {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}" {% if warehouse_filter == warehouse.id|stringformat:"s" %}selected{% endif %}>
                                {{ warehouse.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="movement_type" class="form-select">
                        <option value="">جميع أنواع الحركات</option>
                        {% for type_code, type_name in movement_types %}
                            <option value="{{ type_code }}" {% if movement_type == type_code %}selected{% endif %}>
                                {{ type_name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search"></i>
                        </button>
                        <a href="{% url 'warehouses:stock_movements' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Movements Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">قائمة حركات المخزون</h5>
            <span class="badge bg-primary">{{ page_obj.paginator.count }} حركة</span>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>التاريخ</th>
                                <th>المنتج</th>
                                <th>المخزن</th>
                                <th>نوع الحركة</th>
                                <th>الكمية</th>
                                <th>المرجع</th>
                                <th>الرصيد بعد الحركة</th>
                                <th>المستخدم</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in page_obj %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ movement.created_at|date:"Y/m/d" }}</strong>
                                            <br>
                                            <small class="text-muted">{{ movement.created_at|date:"H:i" }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="product-icon bg-primary text-white rounded me-2 d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                <i class="bi bi-box" style="font-size: 0.8rem;"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ movement.product.name }}</h6>
                                                <small class="text-muted">{{ movement.product.code }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ movement.warehouse.name }}</span>
                                        {% if movement.location %}
                                            <br>
                                            <small class="text-muted">{{ movement.location.location_code }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if movement.movement_type == 'in' %}
                                            <span class="badge bg-success">
                                                <i class="bi bi-arrow-down me-1"></i>{{ movement.get_movement_type_display }}
                                            </span>
                                        {% elif movement.movement_type == 'out' %}
                                            <span class="badge bg-danger">
                                                <i class="bi bi-arrow-up me-1"></i>{{ movement.get_movement_type_display }}
                                            </span>
                                        {% elif movement.movement_type == 'transfer' %}
                                            <span class="badge bg-warning">
                                                <i class="bi bi-arrow-left-right me-1"></i>{{ movement.get_movement_type_display }}
                                            </span>
                                        {% elif movement.movement_type == 'adjustment' %}
                                            <span class="badge bg-info">
                                                <i class="bi bi-gear me-1"></i>{{ movement.get_movement_type_display }}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ movement.get_movement_type_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if movement.movement_type == 'in' %}
                                            <span class="text-success fw-bold">+{{ movement.quantity }}</span>
                                        {% else %}
                                            <span class="text-danger fw-bold">-{{ movement.quantity }}</span>
                                        {% endif %}
                                        <small class="text-muted d-block">{{ movement.product.unit.symbol }}</small>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="badge bg-light text-dark">{{ movement.get_reference_type_display }}</span>
                                            {% if movement.reference_number %}
                                                <br>
                                                <small class="text-muted">{{ movement.reference_number }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ movement.balance_after }}</strong>
                                        <small class="text-muted d-block">{{ movement.product.unit.symbol }}</small>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ movement.created_by }}</small>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="صفحات حركات المخزون" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}{% if movement_type %}&movement_type={{ movement_type }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}{% if movement_type %}&movement_type={{ movement_type }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}{% if movement_type %}&movement_type={{ movement_type }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}{% if movement_type %}&movement_type={{ movement_type }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-arrow-left-right text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد حركات مخزون</h4>
                    <p class="text-muted">لم يتم العثور على أي حركات مطابقة لمعايير البحث.</p>
                    <a href="#" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة حركة جديدة
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .table-responsive {
        border-radius: 0.375rem;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .product-icon {
        font-size: 0.8rem;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .badge {
        font-size: 0.75rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when filters change
        const warehouseSelect = document.querySelector('select[name="warehouse"]');
        const movementTypeSelect = document.querySelector('select[name="movement_type"]');
        
        if (warehouseSelect) {
            warehouseSelect.addEventListener('change', function() {
                this.form.submit();
            });
        }
        
        if (movementTypeSelect) {
            movementTypeSelect.addEventListener('change', function() {
                this.form.submit();
            });
        }
    });
</script>
{% endblock %}
