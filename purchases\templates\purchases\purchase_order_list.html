{% extends 'base.html' %}

{% block title %}قائمة أوامر الشراء - نظام أوساريك{% endblock %}

{% block extra_css %}
    <style>
        
        .page-header {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .search-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .orders-table {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 25px;
            color: #333;
            text-align: center;
            border-bottom: 3px solid #ffc107;
            padding-bottom: 15px;
        }
        
        .table th {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: white;
            border: none;
            padding: 15px;
            font-weight: 600;
        }
        
        .table td {
            padding: 15px;
            vertical-align: middle;
            border-color: #e9ecef;
        }
        
        .order-row:hover {
            background-color: #f8f9fa;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: 600;
            font-size: 0.8rem;
            text-align: center;
        }
        
        .status-draft { background: #e2e3e5; color: #495057; }
        .status-sent { background: #cce5ff; color: #004085; }
        .status-confirmed { background: #d1ecf1; color: #0c5460; }
        .status-received { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .priority-badge {
            padding: 4px 8px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 0.7rem;
            margin-right: 5px;
        }
        
        .priority-high { background: #f8d7da; color: #721c24; }
        .priority-medium { background: #fff3cd; color: #856404; }
        .priority-low { background: #d4edda; color: #155724; }
        
        .btn-action {
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: 600;
            font-size: 0.8rem;
            border: none;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin: 2px;
        }
        
        .btn-view {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
        }
        
        .btn-view:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
            color: white;
        }
        
        .btn-edit {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: white;
        }
        
        .btn-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
            color: white;
        }
        
        .btn-print {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            color: white;
        }
        
        .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(111, 66, 193, 0.3);
            color: white;
        }
        
        .btn-delete {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .btn-delete:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
            color: white;
        }
        
        .btn-add {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn-add:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(255, 193, 7, 0.3);
            color: white;
        }
        
        .btn-print-all {
            background: linear-gradient(45deg, #6f42c1, #5a32a3);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-print-all:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(111, 66, 193, 0.3);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .stats-summary {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stats-row {
            display: flex;
            justify-content: space-around;
            text-align: center;
        }
        
        .stat-item {
            flex: 1;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #ffc107;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .order-amount {
            font-weight: 700;
            color: #28a745;
        }
        
        .supplier-name {
            font-weight: 600;
            color: #333;
        }
        
        .order-date {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-cart-plus"></i>
                        قائمة أوامر الشراء
                    </h1>
                    <p class="mb-0">إدارة وعرض جميع أوامر الشراء في النظام</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="{% url 'purchases:dashboard' %}" class="btn btn-outline-light">
                            <i class="bi bi-house"></i>
                            لوحة التحكم
                        </a>
                        <button class="btn btn-print-all" onclick="window.print()">
                            <i class="bi bi-printer"></i>
                            طباعة
                        </button>
                        <a href="{% url 'purchases:purchase_order_create' %}" class="btn-add">
                            <i class="bi bi-plus-circle"></i>
                            أمر شراء جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-summary">
            <div class="stats-row">
                <div class="stat-item">
                    <div class="stat-number">{{ orders.count }}</div>
                    <div class="stat-label">إجمالي الأوامر</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ draft_orders_count }}</div>
                    <div class="stat-label">أوامر مسودة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ confirmed_orders_count }}</div>
                    <div class="stat-label">أوامر مؤكدة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ total_amount|floatformat:0 }}</div>
                    <div class="stat-label">إجمالي القيمة (ج.م)</div>
                </div>
            </div>
        </div>

        <!-- قسم البحث والفلترة -->
        <div class="search-section">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" name="search" class="form-control" 
                               placeholder="رقم الأمر أو المورد" value="{{ search }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="draft" {% if status == 'draft' %}selected{% endif %}>مسودة</option>
                        <option value="sent" {% if status == 'sent' %}selected{% endif %}>مرسل</option>
                        <option value="confirmed" {% if status == 'confirmed' %}selected{% endif %}>مؤكد</option>
                        <option value="received" {% if status == 'received' %}selected{% endif %}>مستلم</option>
                        <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الأولوية</label>
                    <select name="priority" class="form-select">
                        <option value="">جميع الأولويات</option>
                        <option value="high" {% if priority == 'high' %}selected{% endif %}>عالية</option>
                        <option value="medium" {% if priority == 'medium' %}selected{% endif %}>متوسطة</option>
                        <option value="low" {% if priority == 'low' %}selected{% endif %}>منخفضة</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-funnel"></i>
                        تطبيق
                    </button>
                </div>
            </form>
        </div>

        <!-- جدول أوامر الشراء -->
        {% if orders %}
            <div class="orders-table">
                <h3 class="section-title">أوامر الشراء</h3>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الأمر</th>
                                <th>المورد</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الأولوية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in orders %}
                                <tr class="order-row">
                                    <td>
                                        <strong>{{ order.order_number }}</strong>
                                        {% if order.reference_number %}
                                            <br><small class="text-muted">مرجع: {{ order.reference_number }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="supplier-name">{{ order.supplier.name }}</div>
                                        {% if order.supplier.phone %}
                                            <small class="text-muted">{{ order.supplier.phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="order-date">{{ order.order_date|date:"Y/m/d" }}</div>
                                        {% if order.expected_delivery_date %}
                                            <small class="text-muted">تسليم: {{ order.expected_delivery_date|date:"Y/m/d" }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="order-amount">{{ order.total_amount|floatformat:2 }} ج.م</div>
                                        <small class="text-muted">{{ order.items.count }} صنف</small>
                                    </td>
                                    <td>
                                        <span class="status-badge status-{{ order.status }}">
                                            {% if order.status == 'draft' %}
                                                مسودة
                                            {% elif order.status == 'sent' %}
                                                مرسل
                                            {% elif order.status == 'confirmed' %}
                                                مؤكد
                                            {% elif order.status == 'received' %}
                                                مستلم
                                            {% elif order.status == 'cancelled' %}
                                                ملغي
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        {% if order.priority %}
                                            <span class="priority-badge priority-{{ order.priority }}">
                                                {% if order.priority == 'high' %}
                                                    عالية
                                                {% elif order.priority == 'medium' %}
                                                    متوسطة
                                                {% elif order.priority == 'low' %}
                                                    منخفضة
                                                {% endif %}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="d-flex flex-wrap">
                                            <a href="{% url 'purchases:purchase_order_detail' order.pk %}" class="btn-action btn-view">
                                                <i class="bi bi-eye"></i>
                                                عرض
                                            </a>
                                            {% if order.status == 'draft' %}
                                                <a href="{% url 'purchases:purchase_order_edit' order.pk %}" class="btn-action btn-edit">
                                                    <i class="bi bi-pencil"></i>
                                                    تعديل
                                                </a>
                                            {% endif %}
                                            <a href="{% url 'purchases:purchase_order_print' order.pk %}" class="btn-action btn-print" target="_blank">
                                                <i class="bi bi-printer"></i>
                                                طباعة
                                            </a>
                                            {% if order.status == 'draft' %}
                                                <button class="btn-action btn-delete" onclick="confirmDelete('{{ order.order_number }}', '{% url 'purchases:purchase_order_delete' order.pk %}')">
                                                    <i class="bi bi-trash"></i>
                                                    حذف
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- الترقيم -->
                {% if orders.has_other_pages %}
                    <nav aria-label="ترقيم الصفحات" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if orders.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ orders.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if priority %}&priority={{ priority }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                        السابق
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in orders.paginator.page_range %}
                                {% if orders.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > orders.number|add:'-3' and num < orders.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if priority %}&priority={{ priority }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                            {{ num }}
                                        </a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if orders.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ orders.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if priority %}&priority={{ priority }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                        التالي
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            </div>
        {% else %}
            <div class="empty-state">
                <i class="bi bi-cart-plus"></i>
                <h3>لا توجد أوامر شراء</h3>
                <p>لم يتم العثور على أوامر شراء تطابق معايير البحث</p>
                <a href="{% url 'purchases:purchase_order_create' %}" class="btn-add mt-3">
                    <i class="bi bi-plus-circle"></i>
                    إنشاء أول أمر شراء
                </a>
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        // تأثيرات بصرية للجدول
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.orders-table, .search-section, .stats-summary');
            elements.forEach((element, index) => {
                setTimeout(() => {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(30px)';
                    element.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });

        function confirmDelete(orderNumber, deleteUrl) {
            if (confirm(`هل أنت متأكد من حذف أمر الشراء "${orderNumber}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                window.location.href = deleteUrl;
            }
        }
    </script>
{% endblock %}
