# Generated by Django 5.2.4 on 2025-07-14 01:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0011_alter_expensename_options_alter_revenuename_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='profitcenter',
            name='fiscal_year_end',
        ),
        migrations.RemoveField(
            model_name='profitcenter',
            name='fiscal_year_start',
        ),
        migrations.RemoveField(
            model_name='profitcenter',
            name='manager_name',
        ),
        migrations.RemoveField(
            model_name='profitcenter',
            name='target_profit_margin',
        ),
        migrations.AddField(
            model_name='profitcenter',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_profit_centers', to=settings.AUTH_USER_MODEL, verbose_name='المدير'),
        ),
        migrations.AddField(
            model_name='profitcenter',
            name='name_en',
            field=models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية'),
        ),
        migrations.AddField(
            model_name='profitcenter',
            name='notes',
            field=models.TextField(blank=True, verbose_name='ملاحظات'),
        ),
        migrations.AddField(
            model_name='profitcenter',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='definitions.profitcenter', verbose_name='المركز الأب'),
        ),
        migrations.AddField(
            model_name='profitcenter',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='profitcenter',
            name='target_profit',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الربح المستهدف'),
        ),
        migrations.AlterField(
            model_name='profitcenter',
            name='target_revenue',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الإيراد المستهدف'),
        ),
    ]
