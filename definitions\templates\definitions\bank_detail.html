{% extends 'base.html' %}

{% block title %}تفاصيل البنك - {{ bank.name }}{% endblock %}

{% block extra_css %}
<style>
    .bank-detail-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .detail-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .detail-section {
        padding: 2rem;
        border-bottom: 1px solid #eee;
    }

    .detail-section:last-child {
        border-bottom: none;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #007bff;
    }

    .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .detail-label {
        font-weight: 600;
        color: #495057;
        min-width: 150px;
        margin-bottom: 0;
    }

    .detail-value {
        color: #212529;
        flex: 1;
    }

    .detail-value strong {
        color: #007bff;
    }

    .swift-code {
        font-family: 'Courier New', monospace;
        background: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-weight: 600;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        padding: 2rem;
        background: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<!-- Detail Header -->
<div class="bank-detail-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-bank me-2"></i>
                    {{ bank.name }}
                </h1>
                <p class="mb-0 opacity-75">تفاصيل البنك - {{ bank.code }}</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:bank_list' %}" class="btn btn-light">
                    <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Detail Content -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="detail-card">
                <!-- Basic Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-info-circle me-2"></i>المعلومات الأساسية
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">كود البنك:</div>
                        <div class="detail-value">
                            <strong>{{ bank.code }}</strong>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">اسم البنك:</div>
                        <div class="detail-value">
                            <strong>{{ bank.name }}</strong>
                        </div>
                    </div>

                    {% if bank.name_en %}
                    <div class="detail-item">
                        <div class="detail-label">الاسم بالإنجليزية:</div>
                        <div class="detail-value">{{ bank.name_en }}</div>
                    </div>
                    {% endif %}

                    {% if bank.swift_code %}
                    <div class="detail-item">
                        <div class="detail-label">SWIFT Code:</div>
                        <div class="detail-value">
                            <span class="swift-code">{{ bank.swift_code }}</span>
                        </div>
                    </div>
                    {% endif %}

                    <div class="detail-item">
                        <div class="detail-label">حالة البنك:</div>
                        <div class="detail-value">
                            <span class="status-badge {% if bank.is_active %}status-active{% else %}status-inactive{% endif %}">
                                <i class="bi bi-{% if bank.is_active %}check-circle{% else %}x-circle{% endif %} me-1"></i>
                                {{ bank.is_active|yesno:"نشط,غير نشط" }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-telephone me-2"></i>معلومات الاتصال
                    </h4>
                    
                    {% if bank.phone %}
                    <div class="detail-item">
                        <div class="detail-label">رقم الهاتف:</div>
                        <div class="detail-value">
                            <a href="tel:{{ bank.phone }}" class="text-decoration-none">
                                <i class="bi bi-telephone me-1"></i>{{ bank.phone }}
                            </a>
                        </div>
                    </div>
                    {% endif %}

                    {% if bank.address %}
                    <div class="detail-item">
                        <div class="detail-label">العنوان:</div>
                        <div class="detail-value">{{ bank.address }}</div>
                    </div>
                    {% endif %}

                    {% if not bank.phone and not bank.address %}
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-info-circle me-2"></i>
                        لا توجد معلومات اتصال مسجلة لهذا البنك
                    </div>
                    {% endif %}
                </div>

                <!-- System Information -->
                <div class="detail-section">
                    <h4 class="section-title">
                        <i class="bi bi-gear me-2"></i>معلومات النظام
                    </h4>
                    
                    <div class="detail-item">
                        <div class="detail-label">تاريخ الإنشاء:</div>
                        <div class="detail-value">{{ bank.created_at|date:"d/m/Y H:i" }}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">آخر تحديث:</div>
                        <div class="detail-value">{{ bank.updated_at|date:"d/m/Y H:i" }}</div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">أنشئ بواسطة:</div>
                        <div class="detail-value">{{ bank.created_by.get_full_name|default:bank.created_by.username }}</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="{% url 'definitions:bank_edit' bank.id %}" class="btn btn-warning btn-lg">
                        <i class="bi bi-pencil me-2"></i>تعديل البنك
                    </a>
                    <a href="{% url 'definitions:bank_delete' bank.id %}" class="btn btn-danger btn-lg">
                        <i class="bi bi-trash me-2"></i>حذف البنك
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
