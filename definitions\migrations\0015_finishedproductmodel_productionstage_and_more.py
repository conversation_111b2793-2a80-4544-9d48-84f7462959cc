# Generated by Django 5.2.4 on 2025-07-19 13:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0014_auto_20250714_0411'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FinishedProductModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود النموذج')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النموذج')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('product_type', models.CharField(choices=[('simple', 'منتج بسيط'), ('complex', 'منتج مركب'), ('assembly', 'منتج تجميعي'), ('kit', 'طقم'), ('bundle', 'حزمة')], default='simple', max_length=20, verbose_name='نوع المنتج')),
                ('quality_level', models.CharField(choices=[('standard', 'جودة عادية'), ('premium', 'جودة ممتازة'), ('luxury', 'جودة فاخرة'), ('economy', 'جودة اقتصادية')], default='standard', max_length=20, verbose_name='مستوى الجودة')),
                ('production_time_hours', models.DecimalField(decimal_places=2, default=0, max_digits=8, verbose_name='وقت الإنتاج (ساعة)')),
                ('labor_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='تكلفة العمالة')),
                ('overhead_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='التكاليف الإضافية')),
                ('quantity_produced', models.DecimalField(decimal_places=3, default=1, max_digits=10, verbose_name='الكمية المنتجة')),
                ('specifications', models.TextField(blank=True, verbose_name='المواصفات الفنية')),
                ('quality_standards', models.TextField(blank=True, verbose_name='معايير الجودة')),
                ('testing_requirements', models.TextField(blank=True, verbose_name='متطلبات الاختبار')),
                ('estimated_material_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='التكلفة المقدرة للمواد')),
                ('estimated_total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي التكلفة المقدرة')),
                ('target_selling_price', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='سعر البيع المستهدف')),
                ('shelf_life_days', models.IntegerField(blank=True, default=0, verbose_name='مدة الصلاحية (يوم)')),
                ('storage_conditions', models.TextField(blank=True, verbose_name='شروط التخزين')),
                ('packaging_requirements', models.TextField(blank=True, verbose_name='متطلبات التعبئة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('approval_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_product_models', to=settings.AUTH_USER_MODEL, verbose_name='معتمد بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('final_product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productdefinition', verbose_name='المنتج النهائي')),
                ('unit_of_measure', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.unitdefinition', verbose_name='وحدة القياس')),
            ],
            options={
                'verbose_name': 'نموذج إنتاج تام',
                'verbose_name_plural': 'نماذج الإنتاج التام',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='ProductionStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود المرحلة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المرحلة')),
                ('name_en', models.CharField(blank=True, max_length=100, verbose_name='الاسم بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='وصف المرحلة')),
                ('stage_type', models.CharField(choices=[('preparation', 'تحضير'), ('processing', 'معالجة'), ('assembly', 'تجميع'), ('testing', 'اختبار'), ('finishing', 'تشطيب'), ('packaging', 'تعبئة'), ('quality_control', 'مراقبة جودة')], max_length=20, verbose_name='نوع المرحلة')),
                ('sequence_number', models.IntegerField(verbose_name='رقم التسلسل')),
                ('standard_duration_hours', models.DecimalField(decimal_places=2, default=0, max_digits=8, verbose_name='المدة المعيارية (ساعة)')),
                ('setup_time_hours', models.DecimalField(decimal_places=2, default=0, max_digits=8, verbose_name='وقت الإعداد (ساعة)')),
                ('labor_cost_per_hour', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='تكلفة العمالة/ساعة')),
                ('overhead_cost_per_hour', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='التكاليف الإضافية/ساعة')),
                ('required_skills', models.TextField(blank=True, verbose_name='المهارات المطلوبة')),
                ('required_equipment', models.TextField(blank=True, verbose_name='المعدات المطلوبة')),
                ('safety_requirements', models.TextField(blank=True, verbose_name='متطلبات السلامة')),
                ('quality_checkpoints', models.TextField(blank=True, verbose_name='نقاط فحص الجودة')),
                ('acceptance_criteria', models.TextField(blank=True, verbose_name='معايير القبول')),
                ('rejection_criteria', models.TextField(blank=True, verbose_name='معايير الرفض')),
                ('can_run_parallel', models.BooleanField(default=False, verbose_name='يمكن تشغيلها بالتوازي')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('under_review', 'قيد المراجعة'), ('deprecated', 'ملغي')], default='active', max_length=20, verbose_name='حالة المرحلة')),
                ('is_critical', models.BooleanField(default=False, verbose_name='مرحلة حرجة')),
                ('is_optional', models.BooleanField(default=False, verbose_name='مرحلة اختيارية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('previous_stage', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='next_stages', to='definitions.productionstage', verbose_name='المرحلة السابقة')),
            ],
            options={
                'verbose_name': 'مرحلة إنتاج',
                'verbose_name_plural': 'مراحل الإنتاج',
                'ordering': ['sequence_number', 'code'],
                'unique_together': {('sequence_number', 'previous_stage')},
            },
        ),
        migrations.CreateModel(
            name='ProductionModelStage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sequence_in_model', models.IntegerField(verbose_name='التسلسل في النموذج')),
                ('estimated_duration_hours', models.DecimalField(decimal_places=2, max_digits=8, verbose_name='المدة المقدرة (ساعة)')),
                ('estimated_cost', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='التكلفة المقدرة')),
                ('special_instructions', models.TextField(blank=True, verbose_name='تعليمات خاصة')),
                ('required_quantity', models.DecimalField(decimal_places=3, default=1, max_digits=10, verbose_name='الكمية المطلوبة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_mandatory', models.BooleanField(default=True, verbose_name='إجباري')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('production_model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stages', to='definitions.finishedproductmodel', verbose_name='نموذج الإنتاج')),
                ('production_stage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='definitions.productionstage', verbose_name='مرحلة الإنتاج')),
            ],
            options={
                'verbose_name': 'مرحلة نموذج الإنتاج',
                'verbose_name_plural': 'مراحل نماذج الإنتاج',
                'ordering': ['sequence_in_model'],
                'unique_together': {('production_model', 'production_stage', 'sequence_in_model')},
            },
        ),
    ]
