{% extends 'base.html' %}

{% block title %}{{ title }} - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{% url 'products:product_list' %}">المنتجات</a></li>
                <li class="breadcrumb-item active">{{ title }}</li>
            </ol>
        </nav>
        <h1 class="page-title">{{ title }}</h1>
        <p class="page-subtitle">
            {% if product %}
                تعديل بيانات المنتج في النظام
            {% else %}
                إضافة منتج جديد إلى المخزون
            {% endif %}
        </p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-box me-2"></i>بيانات المنتج
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="bi bi-tag me-1"></i>{{ form.name.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.sku.id_for_label }}" class="form-label">
                                    <i class="bi bi-upc me-1"></i>{{ form.sku.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.sku }}
                                {% if form.sku.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.sku.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">رمز فريد للمنتج</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.quantity.id_for_label }}" class="form-label">
                                    <i class="bi bi-boxes me-1"></i>{{ form.quantity.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.quantity }}
                                {% if form.quantity.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.quantity.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.min_quantity.id_for_label }}" class="form-label">
                                    <i class="bi bi-exclamation-triangle me-1"></i>{{ form.min_quantity.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.min_quantity }}
                                {% if form.min_quantity.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.min_quantity.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">تنبيه عند الوصول لهذا الحد</div>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.price.id_for_label }}" class="form-label">
                                    <i class="bi bi-currency-dollar me-1"></i>{{ form.price.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.price }}
                                {% if form.price.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.price.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'products:product_list' %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-right me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-2"></i>{{ button_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {% if product %}
                <!-- Product Statistics -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up me-2"></i>إحصائيات المنتج
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-boxes text-primary" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1 {% if product.quantity <= product.min_quantity %}text-danger{% endif %}">
                                        {{ product.quantity }}
                                    </h4>
                                    <p class="text-muted mb-0">الكمية الحالية</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-currency-dollar text-success" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ product.price }} ر.س</h4>
                                    <p class="text-muted mb-0">سعر الوحدة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-calculator text-info" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ product.quantity|mul:product.price|floatformat:2 }} ر.س</h4>
                                    <p class="text-muted mb-0">القيمة الإجمالية</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-calendar-event text-warning" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ product.created_at|date:"Y/m/d" }}</h4>
                                    <p class="text-muted mb-0">تاريخ الإضافة</p>
                                </div>
                            </div>
                        </div>
                        
                        {% if product.quantity <= product.min_quantity %}
                            <div class="alert alert-warning mt-3">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تنبيه:</strong> هذا المنتج وصل للحد الأدنى من المخزون ويحتاج إعادة تموين!
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .form-label {
        font-weight: 600;
        color: #495057;
    }
    
    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation and enhancements
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const nameInput = document.getElementById('{{ form.name.id_for_label }}');
        const skuInput = document.getElementById('{{ form.sku.id_for_label }}');
        const quantityInput = document.getElementById('{{ form.quantity.id_for_label }}');
        const minQuantityInput = document.getElementById('{{ form.min_quantity.id_for_label }}');
        const priceInput = document.getElementById('{{ form.price.id_for_label }}');
        
        // Auto-generate SKU from product name
        nameInput.addEventListener('input', function() {
            if (!skuInput.value) {
                const sku = this.value
                    .replace(/[^a-zA-Z0-9\u0600-\u06FF\s]/g, '')
                    .replace(/\s+/g, '-')
                    .toUpperCase()
                    .substring(0, 10);
                skuInput.value = sku;
            }
        });
        
        // Quantity validation
        quantityInput.addEventListener('input', function() {
            const quantity = parseInt(this.value) || 0;
            const minQuantity = parseInt(minQuantityInput.value) || 0;
            
            if (quantity <= minQuantity && quantity > 0) {
                this.classList.add('border-warning');
                showQuantityWarning();
            } else {
                this.classList.remove('border-warning');
                hideQuantityWarning();
            }
        });
        
        minQuantityInput.addEventListener('input', function() {
            quantityInput.dispatchEvent(new Event('input'));
        });
        
        function showQuantityWarning() {
            let warning = document.getElementById('quantity-warning');
            if (!warning) {
                warning = document.createElement('div');
                warning.id = 'quantity-warning';
                warning.className = 'alert alert-warning mt-2';
                warning.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>تنبيه: الكمية أقل من أو تساوي الحد الأدنى!';
                quantityInput.parentNode.appendChild(warning);
            }
        }
        
        function hideQuantityWarning() {
            const warning = document.getElementById('quantity-warning');
            if (warning) {
                warning.remove();
            }
        }
        
        // Form submission
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
