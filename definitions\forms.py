from django import forms
from django.core.exceptions import ValidationError
from .models import (
    WarehouseDefinition, FinishedProductModel, ProductionStage,
    ProductionModelStage, ProductDefinition, UnitDefinition
)

class WarehouseForm(forms.ModelForm):
    class Meta:
        model = WarehouseDefinition
        fields = ['code', 'name', 'warehouse_type', 'address', 'phone', 'manager_name', 'is_active', 'allow_negative_stock']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # جعل الحقول المطلوبة واضحة
        self.fields['code'].required = True
        self.fields['name'].required = True
        self.fields['warehouse_type'].required = True

        widgets = {
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل كود المخزن'
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل اسم المخزن'
            }),
            'warehouse_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل العنوان'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل رقم الهاتف'
            }),
            'manager_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل اسم المدير'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'allow_negative_stock': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }

        labels = {
            'code': 'كود المخزن',
            'name': 'اسم المخزن',
            'warehouse_type': 'نوع المخزن',
            'address': 'العنوان',
            'phone': 'رقم الهاتف',
            'manager_name': 'مدير المخزن',
            'is_active': 'المخزن نشط',
            'allow_negative_stock': 'السماح بالمخزون السالب',
        }
from django.core.exceptions import ValidationError
from .models import (
    WarehouseDefinition, ProductDefinition, ProductCategory,
    CurrencyDefinition, BankDefinition, CashBoxDefinition,
    PersonDefinition, WarehouseLocation, AssetGroup, AssetBrand,
    ExpenseType, ExpenseName, RevenueType, RevenueName, ProfitCenter
)
from dashboard.translations import get_translation

# ===== نماذج أماكن الأصناف في المخازن =====

class WarehouseLocationForm(forms.ModelForm):
    """نموذج إضافة وتعديل أماكن الأصناف في المخازن"""

    class Meta:
        model = WarehouseLocation
        fields = [
            'warehouse', 'code', 'name', 'description',
            'max_capacity', 'max_weight', 'max_items',
            'current_capacity', 'current_weight', 'current_items',
            'is_active'
        ]

        widgets = {
            'warehouse': forms.Select(attrs={
                'class': 'form-select',
                'required': True
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: A1, B2, C3',
                'data-placeholder-en': 'Example: A1, B2, C3',
                'required': True
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: الرف الأول، المنطقة الشمالية',
                'data-placeholder-en': 'Example: First shelf, North area',
                'required': True
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف تفصيلي للمكان وخصائصه',
                'data-placeholder-en': 'Detailed description of location and its features'
            }),
            'max_capacity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'max_weight': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'max_items': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': '0'
            }),
            'current_capacity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00',
                'value': '0.00'
            }),
            'current_weight': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00',
                'value': '0.00'
            }),
            'current_items': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'placeholder': '0',
                'value': '0'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'checked': True
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تخصيص queryset للمخازن النشطة فقط
        self.fields['warehouse'].queryset = WarehouseDefinition.objects.filter(is_active=True)

        # إضافة خيار فارغ للمخزن
        self.fields['warehouse'].empty_label = "اختر المخزن"

        # تعيين القيم الافتراضية للحقول الحالية إذا كان هذا إنشاء جديد
        if not self.instance.pk:
            self.fields['current_capacity'].initial = 0
            self.fields['current_weight'].initial = 0
            self.fields['current_items'].initial = 0
            self.fields['is_active'].initial = True

        # جعل الحقول الحالية غير مطلوبة
        self.fields['current_capacity'].required = False
        self.fields['current_weight'].required = False
        self.fields['current_items'].required = False

    def clean_code(self):
        """التحقق من صحة كود المكان"""
        code = self.cleaned_data.get('code')
        warehouse = self.cleaned_data.get('warehouse')

        if code and warehouse:
            # التحقق من عدم تكرار الكود في نفس المخزن
            existing = WarehouseLocation.objects.filter(
                warehouse=warehouse,
                code=code
            )

            # استثناء الكائن الحالي في حالة التعديل
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)

            if existing.exists():
                raise ValidationError(f'يوجد مكان آخر بنفس الكود "{code}" في هذا المخزن')

        return code

    def clean_current_capacity(self):
        """التحقق من صحة السعة الحالية"""
        current_capacity = self.cleaned_data.get('current_capacity')

        # إذا كان الحقل فارغ، استخدم 0 كقيمة افتراضية
        if current_capacity is None or current_capacity == '':
            current_capacity = 0

        max_capacity = self.cleaned_data.get('max_capacity')

        if current_capacity < 0:
            raise ValidationError('السعة الحالية لا يمكن أن تكون سالبة')

        if max_capacity and current_capacity > max_capacity:
            raise ValidationError('السعة الحالية لا يمكن أن تتجاوز السعة القصوى')

        return current_capacity

    def clean_current_weight(self):
        """التحقق من صحة الوزن الحالي"""
        current_weight = self.cleaned_data.get('current_weight')

        # إذا كان الحقل فارغ، استخدم 0 كقيمة افتراضية
        if current_weight is None or current_weight == '':
            current_weight = 0

        max_weight = self.cleaned_data.get('max_weight')

        if current_weight < 0:
            raise ValidationError('الوزن الحالي لا يمكن أن يكون سالباً')

        if max_weight and current_weight > max_weight:
            raise ValidationError('الوزن الحالي لا يمكن أن يتجاوز الوزن الأقصى')

        return current_weight

    def clean_current_items(self):
        """التحقق من صحة عدد الأصناف الحالي"""
        current_items = self.cleaned_data.get('current_items')

        # إذا كان الحقل فارغ، استخدم 0 كقيمة افتراضية
        if current_items is None or current_items == '':
            current_items = 0

        max_items = self.cleaned_data.get('max_items')

        if current_items < 0:
            raise ValidationError('عدد الأصناف الحالي لا يمكن أن يكون سالباً')

        if max_items and current_items > max_items:
            raise ValidationError('عدد الأصناف الحالي لا يمكن أن يتجاوز العدد الأقصى')

        return current_items

    def clean(self):
        """التحقق الشامل من صحة البيانات"""
        cleaned_data = super().clean()

        # تعيين القيم الافتراضية للحقول المطلوبة إذا كانت فارغة
        if 'current_capacity' not in cleaned_data or cleaned_data['current_capacity'] is None:
            cleaned_data['current_capacity'] = 0

        if 'current_weight' not in cleaned_data or cleaned_data['current_weight'] is None:
            cleaned_data['current_weight'] = 0

        if 'current_items' not in cleaned_data or cleaned_data['current_items'] is None:
            cleaned_data['current_items'] = 0

        # التحقق من الحدود القصوى (اختياري الآن)
        max_capacity = cleaned_data.get('max_capacity')
        max_weight = cleaned_data.get('max_weight')
        max_items = cleaned_data.get('max_items')

        # إزالة التحقق الإجباري - يمكن إنشاء مكان بدون حدود قصوى
        # if not any([max_capacity, max_weight, max_items]):
        #     raise ValidationError(
        #         'يجب تحديد على الأقل حد واحد من الحدود (السعة، الوزن، أو عدد الأصناف)'
        #     )

        return cleaned_data

class ProductDefinitionForm(forms.ModelForm):
    """نموذج إضافة وتعديل تعريف الأصناف"""

    def clean_barcode(self):
        """تنظيف حقل الباركود - جعله اختياري"""
        barcode = self.cleaned_data.get('barcode')

        # إذا كان الباركود فارغاً أو يحتوي على مسافات فقط
        if not barcode or not barcode.strip():
            return None  # إرجاع None بدلاً من string فارغ

        barcode = barcode.strip()

        # التحقق من عدم تكرار الباركود (فقط للباركود غير الفارغ)
        # استثناء القيم NULL و empty strings
        existing = ProductDefinition.objects.filter(
            barcode=barcode
        ).exclude(
            barcode__isnull=True
        ).exclude(
            barcode=''
        )

        if self.instance and self.instance.pk:
            existing = existing.exclude(pk=self.instance.pk)

        if existing.exists():
            raise forms.ValidationError('هذا الباركود موجود بالفعل')

        return barcode

    class Meta:
        model = ProductDefinition
        fields = [
            'code', 'barcode', 'name', 'description', 'category', 'product_type',
            'cost_price', 'selling_price', 'wholesale_price',
            'main_unit', 'minimum_stock', 'maximum_stock',
            'is_active', 'track_inventory', 'image'
        ]

        widgets = {
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'PRD001, ITM-2024-001',
                'required': True
            }),
            'barcode': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '1234567890123'
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم الصنف',
                'data-placeholder-en': 'Product name',
                'required': True
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'وصف تفصيلي للصنف ومواصفاته',
                'data-placeholder-en': 'Detailed description of product and its specifications'
            }),
            'category': forms.Select(attrs={
                'class': 'form-select'
            }),
            'product_type': forms.Select(attrs={
                'class': 'form-select'
            }, choices=[
                ('product', 'منتج'),
                ('service', 'خدمة'),
                ('raw_material', 'مادة خام'),
                ('finished_goods', 'منتج تام'),
                ('semi_finished', 'منتج نصف مصنع'),
                ('consumable', 'مستهلكات'),
                ('spare_parts', 'قطع غيار'),
                ('tools', 'أدوات'),
                ('equipment', 'معدات'),
                ('software', 'برمجيات'),
            ]),
            'cost_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'selling_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'wholesale_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'main_unit': forms.Select(attrs={
                'class': 'form-select'
            }, choices=[
                ('piece', 'قطعة'),
                ('kg', 'كيلوجرام'),
                ('gram', 'جرام'),
                ('meter', 'متر'),
                ('cm', 'سنتيمتر'),
                ('liter', 'لتر'),
                ('ml', 'مليلتر'),
                ('box', 'علبة'),
                ('carton', 'كرتونة'),
                ('pack', 'عبوة'),
                ('bottle', 'زجاجة'),
                ('bag', 'كيس'),
                ('roll', 'لفة'),
                ('sheet', 'ورقة'),
                ('set', 'طقم'),
                ('pair', 'زوج'),
                ('dozen', 'دستة'),
                ('ton', 'طن'),
                ('hour', 'ساعة'),
                ('day', 'يوم'),
                ('month', 'شهر'),
                ('year', 'سنة'),
            ]),
            'minimum_stock': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'maximum_stock': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'placeholder': '0.00'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'checked': True
            }),
            'track_inventory': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'checked': True
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تخصيص queryset للفئات النشطة فقط
        self.fields['category'].queryset = ProductCategory.objects.filter(is_active=True)
        self.fields['category'].empty_label = "اختر الفئة"
        self.fields['category'].required = False

        # تعيين القيم الافتراضية
        if not self.instance.pk:
            self.fields['minimum_stock'].initial = 0
            self.fields['maximum_stock'].initial = 100
            self.fields['is_active'].initial = True
            self.fields['track_inventory'].initial = True
            self.fields['product_type'].initial = 'product'
            self.fields['cost_price'].initial = 0
            self.fields['selling_price'].initial = 0

    def clean_code(self):
        """التحقق من صحة كود الصنف"""
        code = self.cleaned_data.get('code')

        if code:
            # التحقق من عدم تكرار الكود
            existing = ProductDefinition.objects.filter(code=code)

            # استثناء الكائن الحالي في حالة التعديل
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)

            if existing.exists():
                raise ValidationError(f'يوجد صنف آخر بنفس الكود "{code}"')

        return code

    def clean_barcode(self):
        """التحقق من صحة الباركود"""
        barcode = self.cleaned_data.get('barcode')

        # إذا كان الباركود فارغاً، إرجاع سلسلة فارغة بدلاً من None
        if not barcode:
            return ''

        # التحقق من عدم تكرار الباركود
        existing = ProductDefinition.objects.filter(barcode=barcode)

        # استثناء الكائن الحالي في حالة التعديل
        if self.instance.pk:
            existing = existing.exclude(pk=self.instance.pk)

        if existing.exists():
            raise ValidationError(f'يوجد صنف آخر بنفس الباركود "{barcode}"')

        return barcode

    def clean_selling_price(self):
        """التحقق من صحة سعر البيع"""
        selling_price = self.cleaned_data.get('selling_price')

        # السماح بسعر البيع = 0 للمواد الخام أو الخدمات المجانية
        if selling_price is not None and selling_price < 0:
            raise ValidationError('سعر البيع لا يمكن أن يكون سالباً')

        return selling_price

# تم حذف clean_current_stock لأن الحقل غير موجود

    def clean_minimum_stock(self):
        """التحقق من صحة الحد الأدنى للمخزون"""
        minimum_stock = self.cleaned_data.get('minimum_stock', 0)

        if minimum_stock < 0:
            raise ValidationError('الحد الأدنى للمخزون لا يمكن أن يكون سالباً')

        return minimum_stock

    def clean(self):
        """التحقق الشامل من صحة البيانات"""
        cleaned_data = super().clean()

        cost_price = cleaned_data.get('cost_price')
        selling_price = cleaned_data.get('selling_price')
        wholesale_price = cleaned_data.get('wholesale_price')
        minimum_stock = cleaned_data.get('minimum_stock')
        maximum_stock = cleaned_data.get('maximum_stock')

        # التحقق من أن سعر البيع أكبر من سعر التكلفة (إذا كان سعر البيع أكبر من 0)
        if cost_price and selling_price and selling_price > 0 and cost_price >= selling_price:
            raise ValidationError('سعر البيع يجب أن يكون أكبر من سعر التكلفة')

        # التحقق من أن سعر الجملة أقل من سعر البيع (إذا كان سعر البيع أكبر من 0)
        if wholesale_price and selling_price and selling_price > 0 and wholesale_price >= selling_price:
            raise ValidationError('سعر الجملة يجب أن يكون أقل من سعر البيع')

        # التحقق من أن الحد الأقصى أكبر من الحد الأدنى
        if minimum_stock and maximum_stock and minimum_stock >= maximum_stock:
            raise ValidationError('الحد الأقصى للمخزون يجب أن يكون أكبر من الحد الأدنى')

        return cleaned_data


# ===== نماذج الإنتاج التام =====
class FinishedProductModelForm(forms.ModelForm):
    class Meta:
        model = FinishedProductModel
        fields = [
            'code', 'name', 'name_en', 'description', 'product_type', 'quality_level',
            'production_time_hours', 'labor_cost', 'overhead_cost', 'final_product',
            'quantity_produced', 'unit_of_measure', 'specifications', 'quality_standards',
            'testing_requirements', 'estimated_material_cost', 'estimated_total_cost',
            'target_selling_price', 'shelf_life_days', 'storage_conditions',
            'packaging_requirements', 'is_active'
        ]

        widgets = {
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل كود النموذج'
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل اسم النموذج'
            }),
            'name_en': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter model name in English'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل وصف النموذج'
            }),
            'product_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'quality_level': forms.Select(attrs={
                'class': 'form-select'
            }),
            'production_time_hours': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'labor_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'overhead_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'final_product': forms.Select(attrs={
                'class': 'form-select'
            }),
            'quantity_produced': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001'
            }),
            'unit_of_measure': forms.Select(attrs={
                'class': 'form-select'
            }),
            'specifications': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'أدخل المواصفات الفنية'
            }),
            'quality_standards': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل معايير الجودة'
            }),
            'testing_requirements': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل متطلبات الاختبار'
            }),
            'estimated_material_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'estimated_total_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'target_selling_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'shelf_life_days': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0'
            }),
            'storage_conditions': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'أدخل شروط التخزين'
            }),
            'packaging_requirements': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'أدخل متطلبات التعبئة'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }

        labels = {
            'code': 'كود النموذج',
            'name': 'اسم النموذج',
            'name_en': 'الاسم بالإنجليزية',
            'description': 'الوصف',
            'product_type': 'نوع المنتج',
            'quality_level': 'مستوى الجودة',
            'production_time_hours': 'وقت الإنتاج (ساعة)',
            'labor_cost': 'تكلفة العمالة',
            'overhead_cost': 'التكاليف الإضافية',
            'final_product': 'المنتج النهائي',
            'quantity_produced': 'الكمية المنتجة',
            'unit_of_measure': 'وحدة القياس',
            'specifications': 'المواصفات الفنية',
            'quality_standards': 'معايير الجودة',
            'testing_requirements': 'متطلبات الاختبار',
            'estimated_material_cost': 'التكلفة المقدرة للمواد',
            'estimated_total_cost': 'إجمالي التكلفة المقدرة',
            'target_selling_price': 'سعر البيع المستهدف',
            'shelf_life_days': 'مدة الصلاحية (يوم)',
            'storage_conditions': 'شروط التخزين',
            'packaging_requirements': 'متطلبات التعبئة',
            'is_active': 'نشط',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # جعل الحقول المطلوبة واضحة
        self.fields['code'].required = True
        self.fields['name'].required = True
        self.fields['final_product'].required = True
        self.fields['quantity_produced'].required = True
        self.fields['unit_of_measure'].required = True

        # تحديد الخيارات للحقول المرتبطة
        self.fields['final_product'].queryset = ProductDefinition.objects.filter(is_active=True)
        self.fields['unit_of_measure'].queryset = UnitDefinition.objects.filter(is_active=True)

    def clean(self):
        cleaned_data = super().clean()
        labor_cost = cleaned_data.get('labor_cost', 0)
        overhead_cost = cleaned_data.get('overhead_cost', 0)
        estimated_material_cost = cleaned_data.get('estimated_material_cost', 0)
        target_selling_price = cleaned_data.get('target_selling_price', 0)
        quantity_produced = cleaned_data.get('quantity_produced', 0)

        # التحقق من أن الكمية المنتجة أكبر من صفر
        if quantity_produced and quantity_produced <= 0:
            raise ValidationError('الكمية المنتجة يجب أن تكون أكبر من صفر')

        # التحقق من أن سعر البيع المستهدف أكبر من إجمالي التكلفة
        total_cost = labor_cost + overhead_cost + estimated_material_cost
        if target_selling_price and total_cost and target_selling_price <= total_cost:
            raise ValidationError('سعر البيع المستهدف يجب أن يكون أكبر من إجمالي التكلفة')

        return cleaned_data


# ===== مراحل الإنتاج =====
class ProductionStageForm(forms.ModelForm):
    class Meta:
        model = ProductionStage
        fields = [
            'code', 'name', 'name_en', 'description', 'stage_type', 'sequence_number',
            'standard_duration_hours', 'setup_time_hours', 'labor_cost_per_hour',
            'overhead_cost_per_hour', 'required_skills', 'required_equipment',
            'safety_requirements', 'quality_checkpoints', 'acceptance_criteria',
            'rejection_criteria', 'previous_stage', 'can_run_parallel', 'status',
            'is_critical', 'is_optional'
        ]

        widgets = {
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل كود المرحلة'
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل اسم المرحلة'
            }),
            'name_en': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter stage name in English'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل وصف المرحلة'
            }),
            'stage_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'sequence_number': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'standard_duration_hours': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'setup_time_hours': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'labor_cost_per_hour': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'overhead_cost_per_hour': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'required_skills': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل المهارات المطلوبة'
            }),
            'required_equipment': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل المعدات المطلوبة'
            }),
            'safety_requirements': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل متطلبات السلامة'
            }),
            'quality_checkpoints': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل نقاط فحص الجودة'
            }),
            'acceptance_criteria': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل معايير القبول'
            }),
            'rejection_criteria': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل معايير الرفض'
            }),
            'previous_stage': forms.Select(attrs={
                'class': 'form-select'
            }),
            'can_run_parallel': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'is_critical': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_optional': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }

        labels = {
            'code': 'كود المرحلة',
            'name': 'اسم المرحلة',
            'name_en': 'الاسم بالإنجليزية',
            'description': 'وصف المرحلة',
            'stage_type': 'نوع المرحلة',
            'sequence_number': 'رقم التسلسل',
            'standard_duration_hours': 'المدة المعيارية (ساعة)',
            'setup_time_hours': 'وقت الإعداد (ساعة)',
            'labor_cost_per_hour': 'تكلفة العمالة/ساعة',
            'overhead_cost_per_hour': 'التكاليف الإضافية/ساعة',
            'required_skills': 'المهارات المطلوبة',
            'required_equipment': 'المعدات المطلوبة',
            'safety_requirements': 'متطلبات السلامة',
            'quality_checkpoints': 'نقاط فحص الجودة',
            'acceptance_criteria': 'معايير القبول',
            'rejection_criteria': 'معايير الرفض',
            'previous_stage': 'المرحلة السابقة',
            'can_run_parallel': 'يمكن تشغيلها بالتوازي',
            'status': 'حالة المرحلة',
            'is_critical': 'مرحلة حرجة',
            'is_optional': 'مرحلة اختيارية',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # جعل الحقول المطلوبة واضحة
        self.fields['code'].required = True
        self.fields['name'].required = True
        self.fields['stage_type'].required = True
        self.fields['sequence_number'].required = True

        # تحديد الخيارات للمرحلة السابقة (استبعاد المرحلة الحالية)
        if self.instance and self.instance.pk:
            self.fields['previous_stage'].queryset = ProductionStage.objects.filter(
                status='active'
            ).exclude(pk=self.instance.pk)
        else:
            self.fields['previous_stage'].queryset = ProductionStage.objects.filter(status='active')

    def clean(self):
        cleaned_data = super().clean()
        sequence_number = cleaned_data.get('sequence_number')
        previous_stage = cleaned_data.get('previous_stage')
        standard_duration_hours = cleaned_data.get('standard_duration_hours', 0)
        setup_time_hours = cleaned_data.get('setup_time_hours', 0)

        # التحقق من أن رقم التسلسل أكبر من صفر
        if sequence_number and sequence_number <= 0:
            raise ValidationError('رقم التسلسل يجب أن يكون أكبر من صفر')

        # التحقق من أن المدة المعيارية أكبر من صفر
        if standard_duration_hours <= 0:
            raise ValidationError('المدة المعيارية يجب أن تكون أكبر من صفر')

        # التحقق من عدم وجود تسلسل دائري
        if previous_stage and self.instance:
            current_stage = previous_stage
            visited = set()
            while current_stage:
                if current_stage.pk == self.instance.pk:
                    raise ValidationError('لا يمكن إنشاء تسلسل دائري في المراحل')
                if current_stage.pk in visited:
                    break
                visited.add(current_stage.pk)
                current_stage = current_stage.previous_stage

        return cleaned_data


# ===== ربط نماذج الإنتاج بمراحل الإنتاج =====
class ProductionModelStageForm(forms.ModelForm):
    class Meta:
        model = ProductionModelStage
        fields = [
            'production_model', 'production_stage', 'sequence_in_model',
            'estimated_duration_hours', 'estimated_cost', 'special_instructions',
            'required_quantity', 'is_active', 'is_mandatory'
        ]

        widgets = {
            'production_model': forms.Select(attrs={
                'class': 'form-select'
            }),
            'production_stage': forms.Select(attrs={
                'class': 'form-select'
            }),
            'sequence_in_model': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'estimated_duration_hours': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'estimated_cost': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'special_instructions': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل تعليمات خاصة'
            }),
            'required_quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0.001'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_mandatory': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }

        labels = {
            'production_model': 'نموذج الإنتاج',
            'production_stage': 'مرحلة الإنتاج',
            'sequence_in_model': 'التسلسل في النموذج',
            'estimated_duration_hours': 'المدة المقدرة (ساعة)',
            'estimated_cost': 'التكلفة المقدرة',
            'special_instructions': 'تعليمات خاصة',
            'required_quantity': 'الكمية المطلوبة',
            'is_active': 'نشط',
            'is_mandatory': 'إجباري',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # جعل الحقول المطلوبة واضحة
        self.fields['production_model'].required = True
        self.fields['production_stage'].required = True
        self.fields['sequence_in_model'].required = True
        self.fields['estimated_duration_hours'].required = True
        self.fields['estimated_cost'].required = True
        self.fields['required_quantity'].required = True

        # تحديد الخيارات للحقول المرتبطة
        self.fields['production_model'].queryset = FinishedProductModel.objects.filter(is_active=True)
        self.fields['production_stage'].queryset = ProductionStage.objects.filter(status='active')

    def clean(self):
        cleaned_data = super().clean()
        sequence_in_model = cleaned_data.get('sequence_in_model')
        estimated_duration_hours = cleaned_data.get('estimated_duration_hours', 0)
        estimated_cost = cleaned_data.get('estimated_cost', 0)
        required_quantity = cleaned_data.get('required_quantity', 0)

        # التحقق من أن التسلسل أكبر من صفر
        if sequence_in_model and sequence_in_model <= 0:
            raise ValidationError('التسلسل في النموذج يجب أن يكون أكبر من صفر')

        # التحقق من أن المدة المقدرة أكبر من صفر
        if estimated_duration_hours <= 0:
            raise ValidationError('المدة المقدرة يجب أن تكون أكبر من صفر')

        # التحقق من أن التكلفة المقدرة أكبر من أو تساوي صفر
        if estimated_cost < 0:
            raise ValidationError('التكلفة المقدرة لا يمكن أن تكون سالبة')

        # التحقق من أن الكمية المطلوبة أكبر من صفر
        if required_quantity <= 0:
            raise ValidationError('الكمية المطلوبة يجب أن تكون أكبر من صفر')

        return cleaned_data
