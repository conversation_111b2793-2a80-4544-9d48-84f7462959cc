#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
django.setup()

from warehouses.models import InventoryItem
from definitions.models import ProductDefinition, WarehouseDefinition

print("=== تحديث الحد الأقصى للسلفونيك في مخزن المواد الخام ===")

try:
    # البحث عن منتج السلفونيك في مخزن المواد الخام
    sulfonic_product = ProductDefinition.objects.get(name='سلفونيك')
    raw_materials_warehouse = WarehouseDefinition.objects.get(name='مخزن المواد الخام')
    
    inventory_item = InventoryItem.objects.get(
        product=sulfonic_product,
        warehouse=raw_materials_warehouse
    )
    
    print(f"تم العثور على المنتج: {inventory_item.product.name}")
    print(f"في المخزن: {inventory_item.warehouse.name}")
    print(f"الكمية الحالية: {inventory_item.quantity_on_hand}")
    print(f"الحد الأقصى الحالي: {inventory_item.maximum_stock}")
    
    # تحديث الحد الأقصى إلى 10000
    inventory_item.maximum_stock = 10000
    inventory_item.save()
    
    print(f"تم تحديث الحد الأقصى إلى: {inventory_item.maximum_stock}")
    print("✅ تم التحديث بنجاح!")
    
except ProductDefinition.DoesNotExist:
    print("❌ لم يتم العثور على منتج السلفونيك")
except WarehouseDefinition.DoesNotExist:
    print("❌ لم يتم العثور على مخزن المواد الخام")
except InventoryItem.DoesNotExist:
    print("❌ لم يتم العثور على عنصر المخزون")
except Exception as e:
    print(f"❌ خطأ: {e}")

print("\n=== فحص النتيجة ===")

try:
    updated_item = InventoryItem.objects.get(
        product__name='سلفونيك',
        warehouse__name='مخزن المواد الخام'
    )
    
    print(f"المنتج: {updated_item.product.name}")
    print(f"المخزن: {updated_item.warehouse.name}")
    print(f"الكمية الحالية: {updated_item.quantity_on_hand}")
    print(f"الحد الأدنى: {updated_item.minimum_stock}")
    print(f"الحد الأقصى: {updated_item.maximum_stock}")
    
    # فحص حالة التنبيه
    if updated_item.maximum_stock > 0 and updated_item.quantity_on_hand >= updated_item.maximum_stock:
        print(f"⚠️ ما زال هناك تنبيه زيادة")
    else:
        print(f"✅ لا يوجد تنبيه زيادة")
        
except Exception as e:
    print(f"❌ خطأ في الفحص: {e}")

print("\n=== انتهى التحديث ===")
