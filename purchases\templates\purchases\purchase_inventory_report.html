{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير مخزون المشتريات - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border-left: 5px solid #6f42c1;
    }

    .stat-card h3 {
        margin: 0 0 10px 0;
        font-size: 2.5rem;
        font-weight: bold;
        color: #6f42c1;
    }

    .stat-card p {
        margin: 0;
        font-size: 1.1rem;
        color: #6c757d;
    }

    .stat-card.warning {
        border-left-color: #ffc107;
    }

    .stat-card.warning h3 {
        color: #ffc107;
    }

    .stat-card.danger {
        border-left-color: #dc3545;
    }

    .stat-card.danger h3 {
        color: #dc3545;
    }

    .filters-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .products-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table {
        margin: 0;
    }

    .table thead th {
        background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table tbody td {
        padding: 15px;
        vertical-align: middle;
        border-bottom: 1px solid #f0f0f0;
    }

    .stock-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .stock-normal {
        background: #d4edda;
        color: #155724;
    }

    .stock-low {
        background: #fff3cd;
        color: #856404;
    }

    .stock-out {
        background: #f8d7da;
        color: #721c24;
    }

    .value-highlight {
        font-size: 1.1rem;
        font-weight: bold;
        color: #6f42c1;
    }

    .search-bar {
        position: relative;
        margin-bottom: 20px;
    }

    .search-bar input {
        padding-right: 45px;
    }

    .search-bar .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }

    .btn-export {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-boxes"></i>
                    تقرير مخزون المشتريات
                </h1>
                <p class="mb-0">تقرير شامل عن مخزون المنتجات وحالاتها</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-export me-2" onclick="window.print()">
                    <i class="bi bi-printer"></i>
                    طباعة التقرير
                </button>
                <a href="{% url 'purchases:reports' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right"></i>
                    العودة
                </a>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="stats-cards">
        <div class="stat-card">
            <h3>{{ total_products }}</h3>
            <p>إجمالي المنتجات</p>
        </div>
        <div class="stat-card warning">
            <h3>{{ low_stock_products }}</h3>
            <p>منتجات قليلة المخزون</p>
        </div>
        <div class="stat-card danger">
            <h3>{{ out_of_stock_products }}</h3>
            <p>منتجات نفدت من المخزون</p>
        </div>
        <div class="stat-card">
            <h3>{{ total_inventory_value|floatformat:0 }}</h3>
            <p>إجمالي قيمة المخزون (ج.م)</p>
        </div>
    </div>

    <!-- الفلاتر -->
    <div class="filters-section">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <div class="search-bar">
                    <input type="text" name="search" class="form-control" placeholder="البحث عن منتج..." value="{{ search }}">
                    <i class="bi bi-search search-icon"></i>
                </div>
            </div>
            
            <div class="col-md-4">
                <select name="category" class="form-select">
                    <option value="">جميع الفئات</option>
                    {% for category in categories %}
                        <option value="{{ category.id }}" {% if category.id|stringformat:"s" == selected_category %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-funnel"></i>
                    تطبيق
                </button>
            </div>
        </form>
    </div>

    <!-- جدول المنتجات -->
    <div class="products-table">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>اسم المنتج</th>
                    <th>الكود</th>
                    <th>الفئة</th>
                    <th>الكمية الحالية</th>
                    <th>الحد الأدنى</th>
                    <th>سعر الشراء</th>
                    <th>قيمة المخزون</th>
                    <th>حالة المخزون</th>
                </tr>
            </thead>
            <tbody>
                {% for item in products_with_inventory %}
                    {% with product=item.product %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ product.name }}</strong>
                                {% if product.description %}
                                    <br>
                                    <small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <strong>{{ product.code }}</strong>
                        </td>
                        <td>
                            {% if product.category %}
                                {{ product.category.name }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ item.total_quantity }}</strong>
                            {% if product.main_unit %}
                                {{ product.main_unit }}
                            {% endif %}
                        </td>
                        <td>
                            {% if product.min_stock_level %}
                                {{ product.min_stock_level }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.cost_price %}
                                {{ product.cost_price|floatformat:2 }} ج.م
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if item.inventory_value > 0 %}
                                <span class="value-highlight">
                                    {{ item.inventory_value|floatformat:2 }} ج.م
                                </span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if item.stock_status == 'out' %}
                                <span class="stock-badge stock-out">
                                    <i class="bi bi-x-circle"></i>
                                    نفد المخزون
                                </span>
                            {% elif item.stock_status == 'low' %}
                                <span class="stock-badge stock-low">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    مخزون قليل
                                </span>
                            {% else %}
                                <span class="stock-badge stock-normal">
                                    <i class="bi bi-check-circle"></i>
                                    مخزون طبيعي
                                </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endwith %}
                {% empty %}
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="text-muted mt-2">لا توجد منتجات</p>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- ملخص التقرير -->
    {% if products %}
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header" style="background: #6f42c1; color: white;">
                        <h5 class="mb-0">ملخص المخزون</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>إجمالي المنتجات:</strong>
                            </div>
                            <div class="col-6">
                                {{ total_products }} منتج
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>إجمالي قيمة المخزون:</strong>
                            </div>
                            <div class="col-6">
                                <span class="value-highlight">{{ total_inventory_value|floatformat:2 }} ج.م</span>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <strong>منتجات تحتاج إعادة طلب:</strong>
                            </div>
                            <div class="col-6">
                                <span class="text-warning">{{ low_stock_products }} منتج</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين الطباعة
    window.addEventListener('beforeprint', function() {
        document.body.classList.add('printing');
    });
    
    window.addEventListener('afterprint', function() {
        document.body.classList.remove('printing');
    });
</script>

<style>
    @media print {
        .btn, .filters-section {
            display: none !important;
        }
        
        .page-header {
            background: #6f42c1 !important;
            -webkit-print-color-adjust: exact;
        }
        
        .table thead th {
            background: #6f42c1 !important;
            -webkit-print-color-adjust: exact;
        }
    }
</style>
{% endblock %}
