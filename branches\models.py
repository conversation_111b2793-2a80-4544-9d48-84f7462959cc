from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator

class Branch(models.Model):
    """الفروع والمراكز"""
    BRANCH_TYPES = [
        ('headquarters', 'المركز الرئيسي'),
        ('branch', 'فرع'),
        ('warehouse', 'مستودع'),
        ('office', 'مكتب'),
        ('factory', 'مصنع'),
        ('showroom', 'معرض'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('under_construction', 'تحت الإنشاء'),
        ('closed', 'مغلق'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم الفرع")
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز الفرع")
    branch_type = models.CharField(max_length=20, choices=BRANCH_TYPES, verbose_name="نوع الفرع")
    parent_branch = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True,
                                     related_name='sub_branches', verbose_name="الفرع الأب")

    # معلومات الموقع
    address = models.TextField(verbose_name="العنوان")
    city = models.CharField(max_length=100, verbose_name="المدينة")
    region = models.CharField(max_length=100, blank=True, null=True, verbose_name="المنطقة")
    postal_code = models.CharField(max_length=20, blank=True, null=True, verbose_name="الرمز البريدي")
    country = models.CharField(max_length=100, default="السعودية", verbose_name="الدولة")
    latitude = models.DecimalField(max_digits=10, decimal_places=8, blank=True, null=True, verbose_name="خط العرض")
    longitude = models.DecimalField(max_digits=11, decimal_places=8, blank=True, null=True, verbose_name="خط الطول")

    # معلومات الاتصال
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    mobile = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الجوال")
    fax = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الفاكس")
    email = models.EmailField(blank=True, null=True, verbose_name="البريد الإلكتروني")
    website = models.URLField(blank=True, null=True, verbose_name="الموقع الإلكتروني")

    # معلومات الإدارة
    manager = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                               related_name='managed_branches', verbose_name="المدير")
    manager_name = models.CharField(max_length=100, blank=True, null=True, verbose_name="اسم المدير")
    manager_phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="هاتف المدير")

    # معلومات التشغيل
    opening_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الافتتاح")
    working_hours = models.CharField(max_length=200, blank=True, null=True, verbose_name="ساعات العمل")
    employee_count = models.PositiveIntegerField(default=0, verbose_name="عدد الموظفين")
    area_size = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="المساحة (متر مربع)")

    # الحالة والملاحظات
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "فرع"
        verbose_name_plural = "الفروع"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def full_address(self):
        """العنوان الكامل"""
        address_parts = [self.address, self.city]
        if self.region:
            address_parts.append(self.region)
        if self.postal_code:
            address_parts.append(self.postal_code)
        address_parts.append(self.country)
        return ', '.join(filter(None, address_parts))

    @property
    def is_headquarters(self):
        """هل هو المركز الرئيسي"""
        return self.branch_type == 'headquarters'

class BranchEmployee(models.Model):
    """نموذج موظفي الفروع"""
    POSITION_CHOICES = [
        ('manager', 'مدير'),
        ('assistant_manager', 'مساعد مدير'),
        ('supervisor', 'مشرف'),
        ('employee', 'موظف'),
        ('security', 'أمن'),
        ('cleaner', 'عامل نظافة'),
    ]

    branch = models.ForeignKey(Branch, related_name='employees', on_delete=models.CASCADE, verbose_name="الفرع")
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    position = models.CharField(max_length=20, choices=POSITION_CHOICES, verbose_name="المنصب")
    hire_date = models.DateField(verbose_name="تاريخ التوظيف")
    salary = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="الراتب")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "موظف فرع"
        verbose_name_plural = "موظفي الفروع"
        unique_together = ['branch', 'user']
        ordering = ['branch', 'position']

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - {self.branch.name}"

class BranchAsset(models.Model):
    """نموذج أصول الفروع"""
    ASSET_TYPE_CHOICES = [
        ('equipment', 'معدات'),
        ('furniture', 'أثاث'),
        ('vehicle', 'مركبة'),
        ('computer', 'حاسوب'),
        ('other', 'أخرى'),
    ]

    branch = models.ForeignKey(Branch, related_name='assets', on_delete=models.CASCADE, verbose_name="الفرع")
    name = models.CharField(max_length=200, verbose_name="اسم الأصل")
    asset_type = models.CharField(max_length=20, choices=ASSET_TYPE_CHOICES, verbose_name="نوع الأصل")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    serial_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="الرقم التسلسلي")
    purchase_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الشراء")
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="سعر الشراء")
    current_value = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="القيمة الحالية")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "أصل فرع"
        verbose_name_plural = "أصول الفروع"
        ordering = ['branch', 'name']

    def __str__(self):
        return f"{self.name} - {self.branch.name}"
