# Generated by Django 5.2.4 on 2025-07-20 22:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PopularSearch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.CharField(max_length=500, unique=True, verbose_name='استعلام البحث')),
                ('search_count', models.IntegerField(default=1, verbose_name='عدد مرات البحث')),
                ('last_searched', models.DateTimeField(auto_now=True, verbose_name='آخر بحث')),
            ],
            options={
                'verbose_name': 'بحث شائع',
                'verbose_name_plural': 'البحثات الشائعة',
                'ordering': ['-search_count', '-last_searched'],
            },
        ),
        migrations.CreateModel(
            name='SearchSuggestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword', models.CharField(max_length=200, unique=True, verbose_name='الكلمة المفتاحية')),
                ('suggestion', models.CharField(max_length=200, verbose_name='الاقتراح')),
                ('category', models.CharField(max_length=50, verbose_name='الفئة')),
                ('priority', models.IntegerField(default=1, verbose_name='الأولوية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'اقتراح بحث',
                'verbose_name_plural': 'اقتراحات البحث',
                'ordering': ['-priority', 'suggestion'],
            },
        ),
        migrations.CreateModel(
            name='SearchHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.CharField(max_length=500, verbose_name='استعلام البحث')),
                ('results_count', models.IntegerField(default=0, verbose_name='عدد النتائج')),
                ('search_type', models.CharField(default='global', max_length=50, verbose_name='نوع البحث')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ البحث')),
                ('execution_time', models.FloatField(blank=True, null=True, verbose_name='وقت التنفيذ (ثانية)')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='search_history', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'تاريخ البحث',
                'verbose_name_plural': 'تاريخ البحث',
                'ordering': ['-created_at'],
            },
        ),
    ]
