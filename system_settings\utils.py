from django.contrib import messages
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import connection
import json


class NotificationManager:
    """مدير الإشعارات الحقيقية"""
    
    @staticmethod
    def create_notification_table():
        """إنشاء جدول الإشعارات إذا لم يكن موجوداً"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_notifications (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        title VARCHAR(200) NOT NULL,
                        message TEXT NOT NULL,
                        notification_type VARCHAR(20) DEFAULT 'info',
                        is_read BOOLEAN DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        read_at DATETIME NULL,
                        action_url TEXT,
                        action_text VARCHAR(100),
                        FOREIGN KEY (user_id) REFERENCES auth_user (id)
                    )
                """)
                cursor.execute("""
                    CREATE INDEX IF NOT EXISTS idx_user_notifications 
                    ON system_notifications (user_id, is_read, created_at)
                """)
        except Exception as e:
            print(f"خطأ في إنشاء جدول الإشعارات: {e}")
    
    @staticmethod
    def add_notification(user, title, message, notification_type='info', action_url='', action_text=''):
        """إضافة إشعار جديد"""
        try:
            NotificationManager.create_notification_table()
            
            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO system_notifications 
                    (user_id, title, message, notification_type, action_url, action_text, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, [
                    user.id, title, message, notification_type, 
                    action_url, action_text, timezone.now()
                ])
            return True
        except Exception as e:
            print(f"خطأ في إضافة الإشعار: {e}")
            return False
    
    @staticmethod
    def get_user_notifications(user, limit=10, unread_only=False):
        """الحصول على إشعارات المستخدم"""
        try:
            NotificationManager.create_notification_table()
            
            with connection.cursor() as cursor:
                where_clause = "WHERE user_id = ?"
                params = [user.id]
                
                if unread_only:
                    where_clause += " AND is_read = 0"
                
                cursor.execute(f"""
                    SELECT id, title, message, notification_type, is_read, 
                           created_at, action_url, action_text
                    FROM system_notifications 
                    {where_clause}
                    ORDER BY created_at DESC 
                    LIMIT ?
                """, params + [limit])
                
                notifications = []
                for row in cursor.fetchall():
                    notifications.append({
                        'id': row[0],
                        'title': row[1],
                        'message': row[2],
                        'type': row[3],
                        'is_read': bool(row[4]),
                        'created_at': row[5],
                        'action_url': row[6],
                        'action_text': row[7],
                    })
                
                return notifications
        except Exception as e:
            print(f"خطأ في جلب الإشعارات: {e}")
            return []
    
    @staticmethod
    def get_unread_count(user):
        """الحصول على عدد الإشعارات غير المقروءة"""
        try:
            NotificationManager.create_notification_table()
            
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) FROM system_notifications 
                    WHERE user_id = ? AND is_read = 0
                """, [user.id])
                
                return cursor.fetchone()[0]
        except Exception as e:
            print(f"خطأ في عد الإشعارات: {e}")
            return 0
    
    @staticmethod
    def mark_as_read(notification_id, user):
        """تحديد الإشعار كمقروء"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE system_notifications 
                    SET is_read = 1, read_at = ?
                    WHERE id = ? AND user_id = ?
                """, [timezone.now(), notification_id, user.id])
                return True
        except Exception as e:
            print(f"خطأ في تحديث الإشعار: {e}")
            return False
    
    @staticmethod
    def mark_all_as_read(user):
        """تحديد جميع الإشعارات كمقروءة"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE system_notifications 
                    SET is_read = 1, read_at = ?
                    WHERE user_id = ? AND is_read = 0
                """, [timezone.now(), user.id])
                return True
        except Exception as e:
            print(f"خطأ في تحديث الإشعارات: {e}")
            return False
    
    @staticmethod
    def delete_notification(notification_id, user):
        """حذف إشعار"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    DELETE FROM system_notifications 
                    WHERE id = ? AND user_id = ?
                """, [notification_id, user.id])
                return True
        except Exception as e:
            print(f"خطأ في حذف الإشعار: {e}")
            return False
    
    @staticmethod
    def create_welcome_notification(user):
        """إنشاء إشعار ترحيب للمستخدم الجديد"""
        title = f"مرحباً {user.first_name or user.username}!"
        message = f"نرحب بك في نظام إدارة أوساريك. نتمنى لك تجربة ممتعة ومفيدة في استخدام النظام."
        
        return NotificationManager.add_notification(
            user=user,
            title=title,
            message=message,
            notification_type='welcome',
            action_url='/settings/',
            action_text='استكشاف النظام'
        )
    
    @staticmethod
    def create_system_notification(user, title, message, action_url='', action_text=''):
        """إنشاء إشعار نظام"""
        return NotificationManager.add_notification(
            user=user,
            title=title,
            message=message,
            notification_type='system',
            action_url=action_url,
            action_text=action_text
        )
    
    @staticmethod
    def create_success_notification(user, title, message, action_url='', action_text=''):
        """إنشاء إشعار نجاح"""
        return NotificationManager.add_notification(
            user=user,
            title=title,
            message=message,
            notification_type='success',
            action_url=action_url,
            action_text=action_text
        )
    
    @staticmethod
    def create_warning_notification(user, title, message, action_url='', action_text=''):
        """إنشاء إشعار تحذير"""
        return NotificationManager.add_notification(
            user=user,
            title=title,
            message=message,
            notification_type='warning',
            action_url=action_url,
            action_text=action_text
        )
    
    @staticmethod
    def create_error_notification(user, title, message, action_url='', action_text=''):
        """إنشاء إشعار خطأ"""
        return NotificationManager.add_notification(
            user=user,
            title=title,
            message=message,
            notification_type='error',
            action_url=action_url,
            action_text=action_text
        )


def create_activity_notification(user, action, details=''):
    """إنشاء إشعار نشاط"""
    activity_messages = {
        'login': 'تم تسجيل الدخول بنجاح',
        'logout': 'تم تسجيل الخروج',
        'profile_update': 'تم تحديث الملف الشخصي',
        'password_change': 'تم تغيير كلمة المرور',
        'permission_change': 'تم تحديث الصلاحيات',
        'group_join': 'تم إضافتك إلى مجموعة جديدة',
        'group_leave': 'تم إزالتك من مجموعة',
        'settings_update': 'تم تحديث إعدادات النظام',
    }
    
    title = activity_messages.get(action, 'نشاط جديد')
    message = f"{title}. {details}" if details else title
    
    return NotificationManager.create_system_notification(
        user=user,
        title=title,
        message=message
    )
