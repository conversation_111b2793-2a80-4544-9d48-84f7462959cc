# Generated by Django 5.2.4 on 2025-07-12 23:03

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Treasury',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الخزينة')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الخزينة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('location', models.CharField(blank=True, max_length=200, null=True, verbose_name='الموقع')),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الافتتاحي')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الحالي')),
                ('max_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأقصى للرصيد')),
                ('min_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الأدنى للرصيد')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('responsible_person', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='المسؤول')),
            ],
            options={
                'verbose_name': 'خزينة',
                'verbose_name_plural': 'الخزائن',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='TreasuryTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_date', models.DateField(verbose_name='تاريخ المعاملة')),
                ('transaction_type', models.CharField(choices=[('receipt', 'قبض'), ('payment', 'دفع'), ('transfer_in', 'تحويل وارد'), ('transfer_out', 'تحويل صادر'), ('adjustment', 'تسوية'), ('opening_balance', 'رصيد افتتاحي')], max_length=20, verbose_name='نوع المعاملة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='المبلغ')),
                ('description', models.TextField(verbose_name='وصف المعاملة')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم المرجع')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('balance_before', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='الرصيد قبل المعاملة')),
                ('balance_after', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='الرصيد بعد المعاملة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_treasury', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfers_out', to='treasuries.treasurytransaction', verbose_name='من الخزينة')),
                ('to_treasury', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfers_in', to='treasuries.treasurytransaction', verbose_name='إلى الخزينة')),
                ('treasury', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='treasuries.treasury', verbose_name='الخزينة')),
            ],
            options={
                'verbose_name': 'معاملة خزينة',
                'verbose_name_plural': 'معاملات الخزائن',
                'ordering': ['-transaction_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TreasuryTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='المبلغ')),
                ('transfer_date', models.DateField(verbose_name='تاريخ التحويل')),
                ('description', models.TextField(verbose_name='وصف التحويل')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم المرجع')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to=settings.AUTH_USER_MODEL, verbose_name='تمت الموافقة بواسطة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_treasury', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='outgoing_transfers', to='treasuries.treasury', verbose_name='من الخزينة')),
                ('to_treasury', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='incoming_transfers', to='treasuries.treasury', verbose_name='إلى الخزينة')),
            ],
            options={
                'verbose_name': 'تحويل بين الخزائن',
                'verbose_name_plural': 'التحويلات بين الخزائن',
                'ordering': ['-transfer_date', '-created_at'],
            },
        ),
    ]
