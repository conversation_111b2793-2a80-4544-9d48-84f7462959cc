from django.core.management.base import BaseCommand
from warehouses.models import InventoryItem
from decimal import Decimal

class Command(BaseCommand):
    help = 'تحديث تكاليف المخزون'

    def handle(self, *args, **options):
        self.stdout.write('بدء تحديث تكاليف المخزون...')
        
        updated_count = 0
        
        for item in InventoryItem.objects.all():
            # تحديث التكاليف من تعريف المنتج
            if item.product.cost_price:
                item.average_cost = item.product.cost_price
                item.last_cost = item.product.cost_price
                
                # حساب إجمالي القيمة
                item.total_value = item.quantity_on_hand * item.average_cost
                
                item.save()
                updated_count += 1
                
                self.stdout.write(f"تم تحديث: {item.warehouse.name} - {item.product.name}")
                self.stdout.write(f"  الكمية: {item.quantity_on_hand}")
                self.stdout.write(f"  متوسط التكلفة: {item.average_cost}")
                self.stdout.write(f"  إجمالي القيمة: {item.total_value}")
                self.stdout.write("-" * 50)
        
        self.stdout.write(
            self.style.SUCCESS(f'تم تحديث {updated_count} عنصر مخزون بنجاح!')
        )
