<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: white;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h2>اختبار إضافة شخص</h2>
    
    <form id="testForm">
        <div class="form-group">
            <label for="code">كود الشخص *</label>
            <input type="text" id="code" name="code" value="TEST001" required>
        </div>
        
        <div class="form-group">
            <label for="name">الاسم *</label>
            <input type="text" id="name" name="name" value="شخص تجريبي" required>
        </div>
        
        <div class="form-group">
            <label for="person_type">نوع الشخص *</label>
            <select id="person_type" name="person_type" required>
                <option value="">-- اختر نوع الشخص --</option>
                <option value="customer" selected>عميل</option>
                <option value="supplier">مورد</option>
                <option value="employee">موظف</option>
                <option value="both">عميل ومورد</option>
                <option value="other">أخرى</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="phone">الهاتف</label>
            <input type="text" id="phone" name="phone" value="01234567890">
        </div>
        
        <div class="form-group">
            <label for="email">البريد الإلكتروني</label>
            <input type="email" id="email" name="email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="address">العنوان</label>
            <textarea id="address" name="address" rows="3">عنوان تجريبي</textarea>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="is_active" name="is_active" checked>
                شخص نشط
            </label>
        </div>
        
        <button type="button" onclick="testSubmit()">اختبار الإرسال</button>
    </form>

    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ddd; display: none;"></div>

    <script>
        function testSubmit() {
            console.log('Testing form submission...');
            
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            // إضافة CSRF token (سنحتاج للحصول عليه من Django)
            // formData.append('csrfmiddlewaretoken', 'test');
            
            console.log('Form data:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }
            
            // اختبار الإرسال باستخدام fetch
            fetch('http://127.0.0.1:8000/definitions/persons/create/', {
                method: 'POST',
                body: formData,
                credentials: 'include'
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(data => {
                console.log('Response data:', data);
                document.getElementById('result').style.display = 'block';
                document.getElementById('result').innerHTML = '<h3>النتيجة:</h3><pre>' + data + '</pre>';
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('result').style.display = 'block';
                document.getElementById('result').innerHTML = '<h3>خطأ:</h3><pre>' + error + '</pre>';
            });
        }
    </script>
</body>
</html>
