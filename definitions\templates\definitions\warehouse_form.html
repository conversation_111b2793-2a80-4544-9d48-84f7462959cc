{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if action == 'create' %}{% if user_language == 'en' %}Add New Warehouse{% else %}إضافة مخزن جديد{% endif %}{% else %}{% if user_language == 'en' %}Edit Warehouse{% else %}تعديل المخزن{% endif %}{% endif %} - {% if user_language == 'en' %}Warehouse Definition{% else %}تعريف المخازن{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-bottom: 3px solid #007bff;
    }

    .page-title {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        color: #6c757d;
        margin-bottom: 0;
    }

    .form-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #eee;
    }

    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #007bff;
        display: inline-block;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .required {
        color: #dc3545;
    }

    .form-control, .form-select {
        border-radius: 6px;
        border: 1px solid #ced4da;
        padding: 0.75rem 1rem;
        transition: all 0.2s ease;
        font-size: 0.95rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-check {
        margin-bottom: 1rem;
    }

    .form-check-input:checked {
        background-color: #007bff;
        border-color: #007bff;
    }



    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .warehouse-type-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .alert-info {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        color: #1565c0;
    }

    /* Validation styles */
    .form-control.is-invalid,
    .form-select.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .form-control.is-invalid:focus,
    .form-select.is-invalid:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="bi bi-{% if action == 'create' %}plus-circle{% else %}pencil{% endif %} me-2"></i>
                    {% if action == 'create' %}{% if user_language == 'en' %}Add New Warehouse{% else %}إضافة مخزن جديد{% endif %}{% else %}{% if user_language == 'en' %}Edit Warehouse{% else %}تعديل المخزن{% endif %}{% endif %}
                </h1>
                <p class="page-subtitle">
                    {% if action == 'create' %}{% if user_language == 'en' %}Create a new warehouse definition in the system{% else %}إنشاء تعريف مخزن جديد في النظام{% endif %}{% else %}{% if user_language == 'en' %}Update existing warehouse data{% else %}تحديث بيانات المخزن الموجود{% endif %}{% endif %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-right me-1"></i>{% if user_language == 'en' %}Back to List{% else %}العودة للقائمة{% endif %}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Form Content -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card">
                <!-- عرض رسائل الخطأ -->
                {% if messages %}
                    <div class="alert-container mb-3">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <form method="post" id="warehouseForm">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-info-circle me-2"></i>{% if user_language == 'en' %}Basic Information{% else %}المعلومات الأساسية{% endif %}
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="code" class="form-label">
                                        {% if user_language == 'en' %}Warehouse Code{% else %}كود المخزن{% endif %} <span class="required">*</span>
                                    </label>
                                    <input type="text"
                                           id="code"
                                           name="code"
                                           class="form-control"
                                           value="{% if warehouse %}{{ warehouse.code }}{% elif form_data %}{{ form_data.code }}{% endif %}"
                                           required
                                           placeholder="{% if user_language == 'en' %}Example: WH001{% else %}مثال: WH001{% endif %}">
                                    <div class="help-text">{% if user_language == 'en' %}Unique warehouse code (letters and numbers only){% else %}كود فريد للمخزن (أحرف وأرقام فقط){% endif %}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">
                                        {% if user_language == 'en' %}Warehouse Name{% else %}اسم المخزن{% endif %} <span class="required">*</span>
                                    </label>
                                    <input type="text"
                                           id="name"
                                           name="name"
                                           class="form-control"
                                           value="{% if warehouse %}{{ warehouse.name }}{% elif form_data %}{{ form_data.name }}{% endif %}"
                                           required
                                           placeholder="{% if user_language == 'en' %}Example: Main Warehouse{% else %}مثال: المخزن الرئيسي{% endif %}">
                                    <div class="help-text">{% if user_language == 'en' %}Full warehouse name{% else %}الاسم الكامل للمخزن{% endif %}</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warehouse_type" class="form-label">
                                        {% if user_language == 'en' %}Warehouse Type{% else %}نوع المخزن{% endif %} <span class="required">*</span>
                                    </label>
                                    <select id="warehouse_type" name="warehouse_type" class="form-select" required>
                                        <option value="">{% if user_language == 'en' %}Select warehouse type{% else %}اختر نوع المخزن{% endif %}</option>
                                        {% for value, label in warehouse_types %}
                                            <option value="{{ value }}" 
                                                {% if warehouse and warehouse.warehouse_type == value %}selected{% endif %}
                                                {% if form_data and form_data.warehouse_type == value %}selected{% endif %}>
                                                {{ label }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                    <div class="warehouse-type-info" id="typeInfo" style="display: none;">
                                        <small class="text-muted">{% if user_language == 'en' %}Selected type information will be displayed here{% else %}سيتم عرض معلومات النوع المختار هنا{% endif %}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="manager_name" class="form-label">{% if user_language == 'en' %}Warehouse Manager Name{% else %}اسم مدير المخزن{% endif %}</label>
                                    <input type="text"
                                           id="manager_name"
                                           name="manager_name"
                                           class="form-control"
                                           value="{% if warehouse %}{{ warehouse.manager_name }}{% elif form_data %}{{ form_data.manager_name }}{% endif %}"
                                           placeholder="{% if user_language == 'en' %}Name of warehouse manager{% else %}اسم المسؤول عن المخزن{% endif %}">
                                    <div class="help-text">{% if user_language == 'en' %}Optional - Name of the person responsible for warehouse management{% else %}اختياري - اسم الشخص المسؤول عن إدارة المخزن{% endif %}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-telephone me-2"></i>{% if user_language == 'en' %}Contact & Location Information{% else %}معلومات الاتصال والموقع{% endif %}
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="form-label">{% if user_language == 'en' %}Phone Number{% else %}رقم الهاتف{% endif %}</label>
                                    <input type="tel"
                                           id="phone"
                                           name="phone"
                                           class="form-control"
                                           value="{% if warehouse %}{{ warehouse.phone }}{% elif form_data %}{{ form_data.phone }}{% endif %}"
                                           placeholder="{% if user_language == 'en' %}Example: 01234567890{% else %}مثال: 01234567890{% endif %}">
                                    <div class="help-text">{% if user_language == 'en' %}Warehouse or manager phone number{% else %}رقم هاتف المخزن أو المسؤول{% endif %}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="address" class="form-label">{% if user_language == 'en' %}Address{% else %}العنوان{% endif %}</label>
                                    <input type="text"
                                           id="address"
                                           name="address"
                                           class="form-control"
                                           value="{% if warehouse %}{{ warehouse.address }}{% elif form_data %}{{ form_data.address }}{% endif %}"
                                           placeholder="{% if user_language == 'en' %}Full warehouse address{% else %}العنوان الكامل للمخزن{% endif %}">
                                    <div class="help-text">{% if user_language == 'en' %}Geographical location of warehouse{% else %}الموقع الجغرافي للمخزن{% endif %}</div>
                                </div>
                            </div>
                        </div>

                        <!-- {% if user_language == 'en' %}Description field not available in current form{% else %}حقل الوصف غير متوفر في النموذج الحالي{% endif %} -->
                    </div>

                    <!-- Settings -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-gear me-2"></i>{% if user_language == 'en' %}Warehouse Settings{% else %}إعدادات المخزن{% endif %}
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" 
                                           id="is_active" 
                                           name="is_active" 
                                           class="form-check-input"
                                           {% if not warehouse or warehouse.is_active %}checked{% endif %}
                                           {% if form_data and form_data.is_active %}checked{% endif %}>
                                    <label for="is_active" class="form-check-label">
                                        <strong>{% if user_language == 'en' %}Active Warehouse{% else %}مخزن نشط{% endif %}</strong>
                                    </label>
                                    <div class="help-text">{% if user_language == 'en' %}Warehouse can be used in operations{% else %}يمكن استخدام المخزن في العمليات{% endif %}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" 
                                           id="allow_negative_stock" 
                                           name="allow_negative_stock" 
                                           class="form-check-input"
                                           {% if warehouse and warehouse.allow_negative_stock %}checked{% endif %}
                                           {% if form_data and form_data.allow_negative_stock %}checked{% endif %}>
                                    <label for="allow_negative_stock" class="form-check-label">
                                        <strong>{% if user_language == 'en' %}Allow Negative Stock{% else %}السماح بالمخزون السالب{% endif %}</strong>
                                    </label>
                                    <div class="help-text">{% if user_language == 'en' %}Ability to issue more than available{% else %}إمكانية صرف أكثر من المتوفر{% endif %}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" 
                                           id="auto_reorder" 
                                           name="auto_reorder" 
                                           class="form-check-input"
                                           {% if warehouse and warehouse.auto_reorder %}checked{% endif %}
                                           {% if form_data and form_data.auto_reorder %}checked{% endif %}>
                                    <label for="auto_reorder" class="form-check-label">
                                        <strong>{% if user_language == 'en' %}Auto Reorder{% else %}إعادة الطلب التلقائي{% endif %}</strong>
                                    </label>
                                    <div class="help-text">{% if user_language == 'en' %}Alerts when stock runs out{% else %}تنبيهات عند نفاد المخزون{% endif %}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="text-center mt-4">
                        <input type="submit" class="btn btn-success btn-lg me-3" style="padding: 15px 40px; font-size: 18px;" value="{% if action == 'create' %}حفظ المخزن{% else %}حفظ التغييرات{% endif %}">
                        <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-outline-secondary btn-lg" style="padding: 15px 40px; font-size: 18px;">
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle me-2"></i>{% if user_language == 'en' %}Important Information:{% else %}معلومات مهمة:{% endif %}</h6>
                <ul class="mb-0">
                    {% if user_language == 'en' %}
                        <li><strong>Warehouse Code:</strong> Must be unique and cannot be changed after creation</li>
                        <li><strong>Warehouse Type:</strong> Determines the nature of warehouse use (main, raw materials, finished goods, etc.)</li>
                        <li><strong>Negative Stock:</strong> If enabled, quantities larger than available can be issued</li>
                        <li><strong>Auto Reorder:</strong> Creates alerts when stock falls below minimum level</li>
                    {% else %}
                        <li><strong>كود المخزن:</strong> يجب أن يكون فريداً ولا يمكن تغييره بعد الإنشاء</li>
                        <li><strong>نوع المخزن:</strong> يحدد طبيعة استخدام المخزن (رئيسي، مواد خام، منتجات تامة، إلخ)</li>
                        <li><strong>المخزون السالب:</strong> إذا تم تفعيله، يمكن صرف كميات أكبر من المتوفر</li>
                        <li><strong>إعادة الطلب التلقائي:</strong> ينشئ تنبيهات عندما ينخفض المخزون عن الحد الأدنى</li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // {% if user_language == 'en' %}Warehouse types information{% else %}معلومات أنواع المخازن{% endif %}
    const warehouseTypeInfo = {
        {% if user_language == 'en' %}
            'main': 'Main Warehouse - Used to store all types of goods and products',
            'raw_materials': 'Raw Materials Warehouse - Dedicated to raw materials used in production',
            'finished_goods': 'Finished Goods Warehouse - For products ready for sale',
            'damaged': 'Damaged Warehouse - For damaged or returned goods',
            'quarantine': 'Quarantine Warehouse - For goods under inspection or review',
            'transit': 'Transit Warehouse - For goods on their way to other destinations'
        {% else %}
            'main': 'المخزن الرئيسي - يستخدم لتخزين جميع أنواع البضائع والمنتجات',
            'raw_materials': 'مخزن المواد الخام - مخصص للمواد الأولية المستخدمة في الإنتاج',
            'finished_goods': 'مخزن المنتجات التامة - للمنتجات الجاهزة للبيع',
            'damaged': 'مخزن التالف - للبضائع التالفة أو المرتجعة',
            'quarantine': 'مخزن الحجر - للبضائع تحت الفحص أو المراجعة',
            'transit': 'مخزن العبور - للبضائع في طريقها لوجهات أخرى'
        {% endif %}
    };
    
    const warehouseTypeSelect = document.getElementById('warehouse_type');
    const typeInfo = document.getElementById('typeInfo');
    
    warehouseTypeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        if (selectedType && warehouseTypeInfo[selectedType]) {
            typeInfo.innerHTML = '<small class="text-info"><i class="bi bi-info-circle me-1"></i>' + warehouseTypeInfo[selectedType] + '</small>';
            typeInfo.style.display = 'block';
        } else {
            typeInfo.style.display = 'none';
        }
    });
    
    // {% if user_language == 'en' %}Run function on page load if there is a selected type{% else %}تشغيل الدالة عند تحميل الصفحة إذا كان هناك نوع محدد{% endif %}
    if (warehouseTypeSelect.value) {
        warehouseTypeSelect.dispatchEvent(new Event('change'));
    }
    
    // {% if user_language == 'en' %}Code validation{% else %}التحقق من صحة الكود{% endif %}
    const codeInput = document.getElementById('code');
    codeInput.addEventListener('input', function() {
        // {% if user_language == 'en' %}Remove spaces and special characters{% else %}إزالة المسافات والأحرف الخاصة{% endif %}
        this.value = this.value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
    });

    // إضافة معلومات debug للنموذج
    const form = document.getElementById('warehouseForm');

    form.addEventListener('submit', function(e) {
        console.log('Form submission started');
        console.log('Form action:', form.action);
        console.log('Form method:', form.method);

        // جمع بيانات النموذج
        const formData = new FormData(form);
        console.log('Form data:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ':', value);
        }

        // التحقق من الحقول المطلوبة
        const code = document.getElementById('code').value.trim();
        const name = document.getElementById('name').value.trim();
        const warehouseType = document.getElementById('warehouse_type').value;

        console.log('Required fields check:');
        console.log('Code:', code);
        console.log('Name:', name);
        console.log('Warehouse Type:', warehouseType);

        if (!code || !name || !warehouseType) {
            console.log('Missing required fields!');
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة:\n' +
                  (!code ? '- كود المخزن\n' : '') +
                  (!name ? '- اسم المخزن\n' : '') +
                  (!warehouseType ? '- نوع المخزن\n' : ''));
            return false;
        }

        console.log('All required fields filled, submitting form...');
    });
});
</script>
{% endblock %}
