# Generated by Django 5.2.4 on 2025-07-18 13:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('warehouses', '0003_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='inventorytransaction',
            name='transaction_type',
            field=models.CharField(choices=[('receipt', 'استلام'), ('issue', 'صرف'), ('transfer_in', 'تحويل وارد'), ('transfer_out', 'تحويل صادر'), ('adjustment_increase', 'تسوية زيادة'), ('adjustment_decrease', 'تسوية نقص'), ('adjustment_set', 'تحديد كمية'), ('adjustment_revalue', 'إعادة تقييم'), ('count_adjustment', 'تسوية جرد'), ('return_in', 'مرتجع وارد'), ('return_out', 'مرتجع صادر'), ('opening_balance', 'رصيد افتتاحي'), ('physical_count', 'جرد فعلي')], max_length=20, verbose_name='نوع الحركة'),
        ),
    ]
