<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المنتجات</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid #ddd; 
            border-radius: 5px;
        }
        button { 
            padding: 10px 20px; 
            margin: 10px 5px; 
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .result { 
            background: #f8f9fa; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        input[type="number"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100px;
        }
        .log {
            background: #f1f1f1;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API للمنتجات</h1>
        
        <div class="test-section">
            <h2>اختبار جلب بيانات المنتج</h2>
            <div>
                <label>معرف المنتج:</label>
                <input type="number" id="productId" value="1" min="1">
                <button class="btn-primary" onclick="testProductAPI()">اختبار API</button>
                <button class="btn-success" onclick="testMultipleProducts()">اختبار منتجات متعددة</button>
                <button class="btn-warning" onclick="clearResults()">مسح النتائج</button>
            </div>
            <div id="result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>سجل العمليات</h2>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function showResult(content, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.innerHTML = content;
        }

        function clearResults() {
            document.getElementById('result').style.display = 'none';
            document.getElementById('log').innerHTML = '';
            log('تم مسح النتائج');
        }

        async function testProductAPI() {
            const productId = document.getElementById('productId').value;
            
            if (!productId) {
                showResult('يرجى إدخال معرف المنتج', true);
                return;
            }

            log(`بدء اختبار API للمنتج ID: ${productId}`);
            showResult('جاري التحميل...');

            try {
                const url = `http://localhost:8000/manufacturing/api/product-cost/${productId}/`;
                log(`إرسال طلب إلى: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                log(`حالة الاستجابة: ${response.status} ${response.statusText}`);
                
                const data = await response.json();
                log(`البيانات المستلمة: ${JSON.stringify(data)}`);
                
                if (response.ok && data.success) {
                    showResult(`
                        <h3>✅ نجح الطلب!</h3>
                        <table style="width: 100%; border-collapse: collapse;">
                            <tr><td style="padding: 5px; border: 1px solid #ddd;"><strong>الاسم:</strong></td><td style="padding: 5px; border: 1px solid #ddd;">${data.name}</td></tr>
                            <tr><td style="padding: 5px; border: 1px solid #ddd;"><strong>الكود:</strong></td><td style="padding: 5px; border: 1px solid #ddd;">${data.code}</td></tr>
                            <tr><td style="padding: 5px; border: 1px solid #ddd;"><strong>سعر التكلفة:</strong></td><td style="padding: 5px; border: 1px solid #ddd;">${data.cost_price} ج.م</td></tr>
                            <tr><td style="padding: 5px; border: 1px solid #ddd;"><strong>سعر البيع:</strong></td><td style="padding: 5px; border: 1px solid #ddd;">${data.selling_price} ج.م</td></tr>
                            <tr><td style="padding: 5px; border: 1px solid #ddd;"><strong>وحدة القياس:</strong></td><td style="padding: 5px; border: 1px solid #ddd;">${data.main_unit_name || 'غير محدد'}</td></tr>
                            <tr><td style="padding: 5px; border: 1px solid #ddd;"><strong>نوع المنتج:</strong></td><td style="padding: 5px; border: 1px solid #ddd;">${data.product_type}</td></tr>
                            <tr><td style="padding: 5px; border: 1px solid #ddd;"><strong>يوجد سعر:</strong></td><td style="padding: 5px; border: 1px solid #ddd;">${data.has_cost_price ? 'نعم' : 'لا'}</td></tr>
                        </table>
                    `);
                    log('✅ تم جلب البيانات بنجاح');
                } else {
                    showResult(`
                        <h3>❌ فشل الطلب</h3>
                        <p><strong>الخطأ:</strong> ${data.error || 'خطأ غير معروف'}</p>
                        <p><strong>كود الحالة:</strong> ${response.status}</p>
                    `, true);
                    log(`❌ فشل الطلب: ${data.error}`);
                }
            } catch (error) {
                showResult(`
                    <h3>❌ خطأ في الاتصال</h3>
                    <p><strong>الخطأ:</strong> ${error.message}</p>
                    <p><strong>التفاصيل:</strong> تأكد من أن الخادم يعمل على http://localhost:8000</p>
                `, true);
                log(`❌ خطأ في الاتصال: ${error.message}`);
            }
        }

        async function testMultipleProducts() {
            log('بدء اختبار منتجات متعددة...');
            const productIds = [1, 2, 3, 4, 5];
            
            for (const id of productIds) {
                document.getElementById('productId').value = id;
                await testProductAPI();
                await new Promise(resolve => setTimeout(resolve, 1000)); // انتظار ثانية بين كل طلب
            }
            log('انتهاء اختبار المنتجات المتعددة');
        }

        // تسجيل تحميل الصفحة
        window.onload = function() {
            log('تم تحميل صفحة اختبار API');
            log('جاهز للاختبار...');
        };
    </script>
</body>
</html>
