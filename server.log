D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.assetgroup' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.expensetype' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.expensename' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.revenuetype' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.revenuename' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.profitcenter' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.assetgroup' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.expensetype' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.expensename' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.revenuetype' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.revenuename' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
D:\osaric try\venv\Lib\site-packages\django\db\models\base.py:368: RuntimeWarning: Model 'definitions.profitcenter' was already registered. Reloading models is not advised as it can lead to inconsistencies, most notably with related models.
  new_class._meta.apps.register_model(new_class._meta.app_label, new_class)
Watching for file changes with StatReloader
Exception in thread django-main-thread:
Traceback (most recent call last):
  File "D:\osaric try\venv\Lib\site-packages\django\core\checks\urls.py", line 136, in check_custom_error_handlers
    handler = resolver.resolve_error_handler(status_code)
  File "D:\osaric try\venv\Lib\site-packages\django\urls\resolvers.py", line 732, in resolve_error_handler
    callback = getattr(self.urlconf_module, "handler%s" % view_type, None)
                       ^^^^^^^^^^^^^^^^^^^
  File "D:\osaric try\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "D:\osaric try\venv\Lib\site-packages\django\urls\resolvers.py", line 711, in urlconf_module
    return import_module(self.urlconf_name)
  File "C:\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\osaric try\osaric\urls.py", line 26, in <module>
    path('warehouses/', include('warehouses.urls', namespace='warehouses')),
                        ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\osaric try\venv\Lib\site-packages\django\urls\conf.py", line 39, in include
    urlconf_module = import_module(urlconf_module)
  File "C:\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\osaric try\warehouses\urls.py", line 2, in <module>
    from . import views
  File "D:\osaric try\warehouses\views.py", line 9, in <module>
    from .models import InventoryTransaction, InventoryBalance, StockAdjustment, StockAdjustmentItem
ImportError: cannot import name 'InventoryTransaction' from 'warehouses.models' (D:\osaric try\warehouses\models.py)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Python313\Lib\threading.py", line 1043, in _bootstrap_inner
    self.run()
    ~~~~~~~~^^
  File "C:\Python313\Lib\threading.py", line 994, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\osaric try\venv\Lib\site-packages\django\utils\autoreload.py", line 64, in wrapper
    fn(*args, **kwargs)
    ~~^^^^^^^^^^^^^^^^^
  File "D:\osaric try\venv\Lib\site-packages\django\core\management\commands\runserver.py", line 134, in inner_run
    self.check(**check_kwargs)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "D:\osaric try\venv\Lib\site-packages\django\core\management\base.py", line 492, in check
    all_issues = checks.run_checks(
        app_configs=app_configs,
    ...<2 lines>...
        databases=databases,
    )
  File "D:\osaric try\venv\Lib\site-packages\django\core\checks\registry.py", line 89, in run_checks
    new_errors = check(app_configs=app_configs, databases=databases)
  File "D:\osaric try\venv\Lib\site-packages\django\core\checks\urls.py", line 138, in check_custom_error_handlers
    path = getattr(resolver.urlconf_module, "handler%s" % status_code)
                   ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\osaric try\venv\Lib\site-packages\django\utils\functional.py", line 47, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "D:\osaric try\venv\Lib\site-packages\django\urls\resolvers.py", line 711, in urlconf_module
    return import_module(self.urlconf_name)
  File "C:\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\osaric try\osaric\urls.py", line 26, in <module>
    path('warehouses/', include('warehouses.urls', namespace='warehouses')),
                        ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\osaric try\venv\Lib\site-packages\django\urls\conf.py", line 39, in include
    urlconf_module = import_module(urlconf_module)
  File "C:\Python313\Lib\importlib\__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\osaric try\warehouses\urls.py", line 2, in <module>
    from . import views
  File "D:\osaric try\warehouses\views.py", line 9, in <module>
    from .models import InventoryTransaction, InventoryBalance, StockAdjustment, StockAdjustmentItem
ImportError: cannot import name 'InventoryTransaction' from 'warehouses.models' (D:\osaric try\warehouses\models.py)
