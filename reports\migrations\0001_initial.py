# Generated by Django 5.2.4 on 2025-07-12 23:22

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('category_type', models.CharField(choices=[('financial', 'تقارير مالية'), ('sales', 'تقارير المبيعات'), ('purchases', 'تقارير المشتريات'), ('inventory', 'تقارير المخزون'), ('hr', 'تقارير الموارد البشرية'), ('accounting', 'تقارير محاسبية'), ('assets', 'تقارير الأصول'), ('branches', 'تقارير الفروع'), ('custom', 'تقارير مخصصة')], max_length=20, verbose_name='نوع الفئة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('icon', models.CharField(default='bi-file-earmark-text', max_length=50, verbose_name='أيقونة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'فئة تقرير',
                'verbose_name_plural': 'فئات التقارير',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم التقرير')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('report_type', models.CharField(choices=[('table', 'جدول'), ('chart', 'رسم بياني'), ('summary', 'ملخص'), ('detailed', 'تفصيلي'), ('dashboard', 'لوحة تحكم')], max_length=20, verbose_name='نوع التقرير')),
                ('query', models.TextField(verbose_name='استعلام التقرير')),
                ('parameters', models.JSONField(blank=True, default=dict, verbose_name='معاملات التقرير')),
                ('template_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم القالب')),
                ('is_public', models.BooleanField(default=True, verbose_name='عام')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('draft', 'مسودة')], default='active', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='reports.reportcategory', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'تقرير',
                'verbose_name_plural': 'التقارير',
                'ordering': ['category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ReportExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parameters', models.JSONField(blank=True, default=dict, verbose_name='المعاملات المستخدمة')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('running', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('failed', 'فشل')], default='pending', max_length=20, verbose_name='الحالة')),
                ('start_time', models.DateTimeField(auto_now_add=True, verbose_name='وقت البداية')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='وقت الانتهاء')),
                ('execution_time', models.DurationField(blank=True, null=True, verbose_name='مدة التنفيذ')),
                ('result_count', models.PositiveIntegerField(default=0, verbose_name='عدد النتائج')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='رسالة الخطأ')),
                ('file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='مسار الملف')),
                ('executed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='نفذ بواسطة')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='executions', to='reports.report', verbose_name='التقرير')),
            ],
            options={
                'verbose_name': 'تنفيذ تقرير',
                'verbose_name_plural': 'تنفيذات التقارير',
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='ReportSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الجدولة')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي'), ('yearly', 'سنوي')], max_length=20, verbose_name='التكرار')),
                ('parameters', models.JSONField(default=dict, verbose_name='المعاملات')),
                ('recipients', models.JSONField(default=list, verbose_name='المستقبلين')),
                ('next_run', models.DateTimeField(verbose_name='التشغيل التالي')),
                ('last_run', models.DateTimeField(blank=True, null=True, verbose_name='آخر تشغيل')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('paused', 'متوقف مؤقتاً')], default='active', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='reports.report', verbose_name='التقرير')),
            ],
            options={
                'verbose_name': 'جدولة تقرير',
                'verbose_name_plural': 'جدولة التقارير',
                'ordering': ['next_run'],
            },
        ),
        migrations.CreateModel(
            name='SavedReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم التقرير المحفوظ')),
                ('parameters', models.JSONField(default=dict, verbose_name='المعاملات')),
                ('is_favorite', models.BooleanField(default=False, verbose_name='مفضل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الحفظ')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reports.report', verbose_name='التقرير')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'تقرير محفوظ',
                'verbose_name_plural': 'التقارير المحفوظة',
                'ordering': ['-created_at'],
                'unique_together': {('report', 'user', 'name')},
            },
        ),
    ]
