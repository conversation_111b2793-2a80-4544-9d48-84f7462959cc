{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة التنبيهات والإشعارات - نظام أوساريك{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .alert-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-left: 5px solid #dc3545;
        transition: all 0.3s ease;
    }

    .alert-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .alert-card.critical {
        border-left-color: #dc3545;
        background: linear-gradient(45deg, #fff5f5, #ffffff);
    }

    .alert-card.high {
        border-left-color: #fd7e14;
        background: linear-gradient(45deg, #fff8f0, #ffffff);
    }

    .alert-card.medium {
        border-left-color: #ffc107;
        background: linear-gradient(45deg, #fffbf0, #ffffff);
    }

    .alert-card.low {
        border-left-color: #28a745;
        background: linear-gradient(45deg, #f0fff4, #ffffff);
    }

    .alert-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .alert-title {
        font-weight: bold;
        color: #2c3e50;
        margin: 0;
    }

    .alert-priority {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .priority-critical {
        background: #dc3545;
        color: white;
    }

    .priority-high {
        background: #fd7e14;
        color: white;
    }

    .priority-medium {
        background: #ffc107;
        color: #212529;
    }

    .priority-low {
        background: #28a745;
        color: white;
    }

    .alert-message {
        color: #6c757d;
        margin-bottom: 15px;
        line-height: 1.6;
    }

    .alert-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
        color: #6c757d;
    }

    .alert-amount {
        font-weight: bold;
        color: #dc3545;
        font-size: 1.1rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-top: 4px solid #dc3545;
    }

    .stat-card.overdue {
        border-top-color: #dc3545;
    }

    .stat-card.due-soon {
        border-top-color: #ffc107;
    }

    .stat-card.approvals {
        border-top-color: #17a2b8;
    }

    .stat-card.total {
        border-top-color: #6f42c1;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .stat-number.overdue {
        color: #dc3545;
    }

    .stat-number.due-soon {
        color: #ffc107;
    }

    .stat-number.approvals {
        color: #17a2b8;
    }

    .stat-number.total {
        color: #6f42c1;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 500;
    }

    .section-title {
        color: #2c3e50;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }

    .btn-action {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .btn-view {
        background: #17a2b8;
        color: white;
        border: none;
    }

    .btn-view:hover {
        background: #138496;
        color: white;
        transform: translateY(-1px);
    }

    .btn-resolve {
        background: #28a745;
        color: white;
        border: none;
    }

    .btn-resolve:hover {
        background: #218838;
        color: white;
        transform: translateY(-1px);
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .approval-card {
        background: linear-gradient(45deg, #e3f2fd, #ffffff);
        border-left: 5px solid #2196f3;
    }

    .approval-amount {
        font-size: 1.3rem;
        font-weight: bold;
        color: #2196f3;
    }

    .days-pending {
        background: #fff3cd;
        color: #856404;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-bell-fill"></i>
                    لوحة التنبيهات والإشعارات
                </h1>
                <p class="mb-0">مراقبة المستحقات والمتأخرات والموافقات المطلوبة</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-outline-light me-2" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise"></i>
                    تحديث
                </button>
                <a href="{% url 'purchases:dashboard' %}" class="btn btn-outline-light">
                    <i class="bi bi-arrow-right"></i>
                    العودة للوحة الرئيسية
                </a>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="stats-grid">
        <div class="stat-card overdue">
            <div class="stat-number overdue">{{ overdue_count }}</div>
            <div class="stat-label">فواتير متأخرة</div>
        </div>
        <div class="stat-card due-soon">
            <div class="stat-number due-soon">{{ due_soon_count }}</div>
            <div class="stat-label">مستحقة قريباً</div>
        </div>
        <div class="stat-card approvals">
            <div class="stat-number approvals">{{ pending_approvals.count }}</div>
            <div class="stat-label">موافقات معلقة</div>
        </div>
        <div class="stat-card total">
            <div class="stat-number total">{{ total_alerts }}</div>
            <div class="stat-label">إجمالي التنبيهات</div>
        </div>
    </div>

    <div class="row">
        <!-- التنبيهات الحرجة -->
        <div class="col-md-6">
            <h3 class="section-title">
                <i class="bi bi-exclamation-triangle-fill text-danger"></i>
                التنبيهات الحرجة
            </h3>
            
            {% if critical_alerts %}
                {% for alert in critical_alerts %}
                    <div class="alert-card critical">
                        <div class="alert-header">
                            <h5 class="alert-title">{{ alert.title }}</h5>
                            <span class="alert-priority priority-critical">حرج</span>
                        </div>
                        <div class="alert-message">{{ alert.message }}</div>
                        <div class="alert-meta">
                            <div>
                                <i class="bi bi-calendar"></i>
                                {{ alert.created_at|date:"Y-m-d H:i" }}
                                {% if alert.supplier %}
                                    | <i class="bi bi-person"></i> {{ alert.supplier.name }}
                                {% endif %}
                            </div>
                            {% if alert.amount %}
                                <div class="alert-amount">{{ alert.amount|floatformat:2 }} ج.م</div>
                            {% endif %}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'purchases:alert_detail' alert.id %}" class="btn btn-action btn-view">
                                <i class="bi bi-eye"></i> عرض التفاصيل
                            </a>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="bi bi-check-circle text-success"></i>
                    <h4>لا توجد تنبيهات حرجة</h4>
                    <p>جميع الأمور تحت السيطرة</p>
                </div>
            {% endif %}
        </div>

        <!-- الموافقات المعلقة -->
        <div class="col-md-6">
            <h3 class="section-title">
                <i class="bi bi-clipboard-check text-info"></i>
                الموافقات المعلقة
            </h3>
            
            {% if pending_approvals %}
                {% for approval in pending_approvals %}
                    <div class="alert-card approval-card">
                        <div class="alert-header">
                            <h5 class="alert-title">موافقة دفعة للمورد {{ approval.payment.supplier.name }}</h5>
                            <span class="days-pending">{{ approval.days_pending }} يوم</span>
                        </div>
                        <div class="alert-message">{{ approval.justification }}</div>
                        <div class="alert-meta">
                            <div>
                                <i class="bi bi-person"></i> {{ approval.requested_by.get_full_name|default:approval.requested_by.username }}
                                <br>
                                <i class="bi bi-calendar"></i> {{ approval.requested_at|date:"Y-m-d H:i" }}
                            </div>
                            <div class="approval-amount">{{ approval.payment.total_amount|floatformat:2 }} ج.م</div>
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'purchases:approve_payment' approval.id %}" class="btn btn-action btn-view">
                                <i class="bi bi-check-circle"></i> مراجعة الموافقة
                            </a>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="bi bi-check-circle text-success"></i>
                    <h4>لا توجد موافقات معلقة</h4>
                    <p>جميع الطلبات تمت مراجعتها</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- جميع التنبيهات -->
    <div class="row mt-4">
        <div class="col-12">
            <h3 class="section-title">
                <i class="bi bi-list-ul"></i>
                جميع التنبيهات الحديثة
            </h3>
            
            {% if unread_alerts %}
                {% for alert in unread_alerts %}
                    <div class="alert-card {{ alert.priority }}">
                        <div class="alert-header">
                            <h5 class="alert-title">{{ alert.title }}</h5>
                            <span class="alert-priority priority-{{ alert.priority }}">{{ alert.get_priority_display }}</span>
                        </div>
                        <div class="alert-message">{{ alert.message }}</div>
                        <div class="alert-meta">
                            <div>
                                <i class="bi bi-calendar"></i> {{ alert.created_at|date:"Y-m-d H:i" }}
                                {% if alert.supplier %}
                                    | <i class="bi bi-person"></i> {{ alert.supplier.name }}
                                {% endif %}
                                {% if alert.due_date %}
                                    | <i class="bi bi-clock"></i> مستحق: {{ alert.due_date }}
                                {% endif %}
                            </div>
                            {% if alert.amount %}
                                <div class="alert-amount">{{ alert.amount|floatformat:2 }} ج.م</div>
                            {% endif %}
                        </div>
                        <div class="mt-3">
                            <a href="{% url 'purchases:alert_detail' alert.id %}" class="btn btn-action btn-view me-2">
                                <i class="bi bi-eye"></i> عرض
                            </a>
                            <form method="post" action="{% url 'purchases:alert_detail' alert.id %}" style="display: inline;">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="resolve">
                                <button type="submit" class="btn btn-action btn-resolve">
                                    <i class="bi bi-check"></i> حل
                                </button>
                            </form>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="bi bi-bell-slash"></i>
                    <h4>لا توجد تنبيهات جديدة</h4>
                    <p>جميع التنبيهات تمت مراجعتها</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث تلقائي كل 5 دقائق
    setInterval(function() {
        location.reload();
    }, 300000);
    
    // تأثيرات تفاعلية
    document.querySelectorAll('.alert-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>
{% endblock %}
