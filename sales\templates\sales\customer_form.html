{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">{{ title }}</h1>
            <p class="text-muted">
                {% if customer %}
                    تعديل بيانات العميل: {{ customer.name }}
                {% else %}
                    إضافة عميل جديد إلى قاعدة البيانات
                {% endif %}
            </p>
        </div>
        <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            العودة للقائمة
        </a>
    </div>

    <!-- Customer Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person me-2"></i>
                        بيانات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- Basic Information -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    اسم العميل <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    البريد الإلكتروني
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.email.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    رقم الهاتف
                                </label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.phone.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6">
                                <label for="{{ form.tax_number.id_for_label }}" class="form-label">
                                    الرقم الضريبي
                                </label>
                                {{ form.tax_number }}
                                {% if form.tax_number.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.tax_number.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="mb-4">
                            <label for="{{ form.address.id_for_label }}" class="form-label">
                                العنوان
                            </label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.address.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Financial Information -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label for="{{ form.credit_limit.id_for_label }}" class="form-label">
                                    حد الائتمان (ريال)
                                </label>
                                <div class="input-group">
                                    {{ form.credit_limit }}
                                    <span class="input-group-text">ريال</span>
                                </div>
                                {% if form.credit_limit.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.credit_limit.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6">
                                <label class="form-label">الحالة</label>
                                <div class="form-check form-switch">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        العميل نشط
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.is_active.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'sales:customer_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>
                                {% if customer %}تحديث البيانات{% else %}إضافة العميل{% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
}

.btn {
    font-weight: 600;
}

.invalid-feedback {
    font-size: 0.875rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on first input
    const firstInput = document.querySelector('input[type="text"]');
    if (firstInput) {
        firstInput.focus();
    }
    
    // Phone number formatting
    const phoneInput = document.querySelector('input[name="phone"]');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 0) {
                if (value.startsWith('966')) {
                    value = '+' + value;
                } else if (value.startsWith('05')) {
                    value = '+966' + value.substring(1);
                } else if (value.startsWith('5')) {
                    value = '+966' + value;
                }
            }
            e.target.value = value;
        });
    }
});
</script>
{% endblock %}
