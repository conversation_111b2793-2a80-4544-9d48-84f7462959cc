{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل مكان الصنف - {{ object.name }}{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Detail View Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Hero Section */
    .detail-hero {
        background: var(--primary-gradient);
        padding: 3rem 0;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        border-radius: 0 0 2rem 2rem;
    }

    .detail-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        text-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .hero-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 1.5rem;
    }

    .hero-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .hero-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .hero-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    /* Info Cards */
    .info-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e5e7eb;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .card-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #374151;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .card-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }

    /* Detail Items */
    .detail-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f1f5f9;
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: #6b7280;
        min-width: 150px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-value {
        color: #374151;
        font-weight: 500;
        flex: 1;
    }

    /* Status Badges */
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-flex;
        align-items: center;
        gap: 0.3rem;
    }

    .status-active {
        background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
        color: white;
    }

    .status-inactive {
        background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
        color: white;
    }

    /* Capacity Visualization */
    .capacity-visual {
        background: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
    }

    .capacity-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .capacity-title {
        font-weight: 600;
        color: #374151;
    }

    .capacity-percentage {
        font-size: 1.2rem;
        font-weight: 700;
    }

    .capacity-bar-container {
        width: 100%;
        height: 20px;
        background: #e5e7eb;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
        margin-bottom: 1rem;
    }

    .capacity-bar {
        height: 100%;
        border-radius: 10px;
        transition: width 1s ease;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .capacity-bar.low {
        background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
    }

    .capacity-bar.medium {
        background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
    }

    .capacity-bar.high {
        background: linear-gradient(90deg, #ef4444 0%, #f87171 100%);
    }

    .capacity-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        text-align: center;
    }

    .capacity-detail {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .capacity-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #374151;
    }

    .capacity-label {
        font-size: 0.8rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }

    /* Statistics Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }
        
        .info-card {
            padding: 1.5rem;
        }
        
        .detail-label {
            min-width: 120px;
            font-size: 0.9rem;
        }
        
        .hero-actions {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="detail-hero">
    <div class="container">
        <div class="hero-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">
                        <i class="bi bi-geo-alt me-3"></i>
                        {{ object.name }}
                    </h1>
                    <p class="hero-subtitle">
                        {{ object.warehouse.name }} - {{ object.code }}
                    </p>
                    <div class="hero-actions">
                        <a href="{% url 'definitions:warehouse_location_edit' object.id %}" class="hero-btn">
                            <i class="bi bi-pencil me-2"></i>تعديل
                        </a>
                        <a href="{% url 'definitions:warehouse_location_list' %}" class="hero-btn">
                            <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                        </a>
                        <a href="{% url 'definitions:warehouse_location_delete' object.id %}" class="hero-btn" 
                           onclick="return confirm('هل أنت متأكد من حذف هذا المكان؟')">
                            <i class="bi bi-trash me-2"></i>حذف
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    {% if object.is_active %}
                    <div class="status-badge status-active">
                        <i class="bi bi-check-circle"></i>
                        نشط
                    </div>
                    {% else %}
                    <div class="status-badge status-inactive">
                        <i class="bi bi-x-circle"></i>
                        غير نشط
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Overview -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon" style="background: var(--primary-gradient);">
                <i class="bi bi-box"></i>
            </div>
            <div class="stat-number text-primary">{{ object.current_capacity|floatformat:1 }}</div>
            <div class="stat-label">السعة الحالية</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background: var(--success-gradient);">
                <i class="bi bi-speedometer"></i>
            </div>
            <div class="stat-number text-success">{{ object.current_weight|floatformat:1 }}</div>
            <div class="stat-label">الوزن الحالي (كجم)</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background: var(--warning-gradient);">
                <i class="bi bi-collection"></i>
            </div>
            <div class="stat-number text-warning">{{ object.current_items }}</div>
            <div class="stat-label">عدد الأصناف</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background: var(--info-gradient);">
                <i class="bi bi-percent"></i>
            </div>
            <div class="stat-number text-info">{{ object.capacity_percentage|floatformat:1 }}%</div>
            <div class="stat-label">نسبة الاستخدام</div>
        </div>
    </div>

    <div class="row">
        <!-- Basic Information -->
        <div class="col-lg-6">
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon" style="background: var(--primary-gradient);">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    المعلومات الأساسية
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-hash"></i>
                        كود المكان
                    </div>
                    <div class="detail-value">
                        <strong class="text-primary">{{ object.code }}</strong>
                    </div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-tag"></i>
                        اسم المكان
                    </div>
                    <div class="detail-value">{{ object.name }}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-building"></i>
                        المخزن
                    </div>
                    <div class="detail-value">
                        <strong>{{ object.warehouse.name }}</strong>
                        <br><small class="text-muted">{{ object.warehouse.code }}</small>
                    </div>
                </div>

                {% if object.description %}
                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-file-text"></i>
                        الوصف
                    </div>
                    <div class="detail-value">{{ object.description }}</div>
                </div>
                {% endif %}

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-calendar"></i>
                        تاريخ الإنشاء
                    </div>
                    <div class="detail-value">{{ object.created_at|date:"Y/m/d H:i" }}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-person"></i>
                        أنشئ بواسطة
                    </div>
                    <div class="detail-value">{{ object.created_by.get_full_name|default:object.created_by.username }}</div>
                </div>
            </div>
        </div>

        <!-- Capacity Information -->
        <div class="col-lg-6">
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon" style="background: var(--warning-gradient);">
                        <i class="bi bi-speedometer2"></i>
                    </div>
                    معلومات السعة
                </div>

                {% if object.max_capacity %}
                <!-- Capacity Visualization -->
                <div class="capacity-visual">
                    <div class="capacity-header">
                        <div class="capacity-title">السعة التخزينية</div>
                        <div class="capacity-percentage text-primary">{{ object.capacity_percentage|floatformat:1 }}%</div>
                    </div>

                    <div class="capacity-bar-container">
                        {% with percentage=object.capacity_percentage %}
                        <div class="capacity-bar {% if percentage < 50 %}low{% elif percentage < 80 %}medium{% else %}high{% endif %}"
                             style="width: {{ percentage }}%">
                            {{ percentage|floatformat:1 }}%
                        </div>
                        {% endwith %}
                    </div>

                    <div class="capacity-details">
                        <div class="capacity-detail">
                            <div class="capacity-number text-success">{{ object.current_capacity|floatformat:1 }}</div>
                            <div class="capacity-label">مستخدم</div>
                        </div>
                        <div class="capacity-detail">
                            <div class="capacity-number text-info">{{ object.available_capacity|floatformat:1 }}</div>
                            <div class="capacity-label">متاح</div>
                        </div>
                        <div class="capacity-detail">
                            <div class="capacity-number text-primary">{{ object.max_capacity|floatformat:1 }}</div>
                            <div class="capacity-label">الحد الأقصى</div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-box"></i>
                        السعة القصوى
                    </div>
                    <div class="detail-value">
                        {% if object.max_capacity %}
                        {{ object.max_capacity|floatformat:1 }} وحدة
                        {% else %}
                        <span class="text-muted">غير محدد</span>
                        {% endif %}
                    </div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-speedometer"></i>
                        الوزن الأقصى
                    </div>
                    <div class="detail-value">
                        {% if object.max_weight %}
                        {{ object.max_weight|floatformat:1 }} كجم
                        {% else %}
                        <span class="text-muted">غير محدد</span>
                        {% endif %}
                    </div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-collection"></i>
                        عدد الأصناف الأقصى
                    </div>
                    <div class="detail-value">
                        {% if object.max_items %}
                        {{ object.max_items }} صنف
                        {% else %}
                        <span class="text-muted">غير محدد</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Status -->
    <div class="row">
        <div class="col-12">
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon" style="background: var(--info-gradient);">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    الحالة الحالية والاستخدام
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-box-fill"></i>
                                السعة الحالية
                            </div>
                            <div class="detail-value">
                                <strong class="text-primary">{{ object.current_capacity|floatformat:1 }}</strong> وحدة
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-speedometer2"></i>
                                الوزن الحالي
                            </div>
                            <div class="detail-value">
                                <strong class="text-success">{{ object.current_weight|floatformat:1 }}</strong> كجم
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-collection-fill"></i>
                                عدد الأصناف الحالي
                            </div>
                            <div class="detail-value">
                                <strong class="text-warning">{{ object.current_items }}</strong> صنف
                            </div>
                        </div>
                    </div>
                </div>

                {% if object.max_capacity %}
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="capacity-detail">
                            <div class="capacity-number text-info">{{ object.available_capacity|floatformat:1 }}</div>
                            <div class="capacity-label">السعة المتاحة</div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="capacity-detail">
                            <div class="capacity-number
                                {% if object.capacity_percentage < 50 %}text-success
                                {% elif object.capacity_percentage < 80 %}text-warning
                                {% else %}text-danger{% endif %}">
                                {{ object.capacity_percentage|floatformat:1 }}%
                            </div>
                            <div class="capacity-label">نسبة الاستخدام</div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="capacity-detail">
                            <div class="capacity-number
                                {% if object.capacity_percentage < 80 %}text-success
                                {% elif object.capacity_percentage < 95 %}text-warning
                                {% else %}text-danger{% endif %}">
                                {% if object.capacity_percentage < 80 %}متاح
                                {% elif object.capacity_percentage < 95 %}محدود
                                {% else %}ممتلئ{% endif %}
                            </div>
                            <div class="capacity-label">حالة المكان</div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
