{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if action == 'edit' %}
تعديل تعريف الشخص - {{ person.name }}
{% else %}
إضافة شخص جديد
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Person Form Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        --warning-gradient: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
        --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --glass-bg: rgba(255, 255, 255, 0.15);
        --glass-border: rgba(255, 255, 255, 0.2);
        --glass-backdrop: blur(15px);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --radius: 12px;
        --radius-lg: 20px;
    }

    body {
        background: #ffffff;
        min-height: 100vh;
    }

    /* Form Container */
    .form-container {
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: var(--radius-lg);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        color: #333;
    }

    .form-title {
        font-size: 2rem;
        font-weight: 800;
        color: #333;
        margin-bottom: 2rem;
        text-align: center;
    }

    /* Form Sections */
    .form-section {
        background: #f8f9fa;
        border: 1px solid #e0e0e0;
        border-radius: var(--radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* Enhanced Form Controls */
    .form-label {
        color: #333;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .form-control, .form-select {
        background: #ffffff;
        border: 1px solid #ddd;
        border-radius: var(--radius);
        padding: 0.75rem 1rem;
        color: #333;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .form-control::placeholder {
        color: #999;
    }

    .form-control:focus, .form-select:focus {
        background: #ffffff;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        color: #333;
        outline: none;
    }

    .form-select option {
        background: #ffffff;
        color: #333;
    }

    /* Enhanced Buttons */
    .btn {
        padding: 0.75rem 2rem;
        border-radius: var(--radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }

    .btn-primary {
        background: var(--primary-gradient);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
    }

    .btn-outline-light {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        backdrop-filter: blur(8px);
    }

    .btn-outline-light:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 255, 255, 0.2);
        color: white;
        text-decoration: none;
    }

    /* Form Check */
    .form-check {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius);
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .form-check-input {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin-right: 0.5rem;
    }

    .form-check-input:checked {
        background: var(--success-gradient);
        border-color: rgba(255, 255, 255, 0.5);
    }

    .form-check-label {
        color: white;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    /* Error Messages */
    .invalid-feedback {
        color: #ff6b6b;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        margin-top: 0.5rem;
    }

    .is-invalid {
        border-color: #ff6b6b !important;
        box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2) !important;
    }

    /* Help Text */
    .form-text {
        color: #666;
        font-size: 0.8rem;
        margin-top: 0.5rem;
    }

    /* Required Field Indicator */
    .required::after {
        content: " *";
        color: #ff6b6b;
        font-weight: bold;
    }

    /* Person Type Badges */
    .person-type-info {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius);
        padding: 1rem;
        margin-top: 1rem;
    }

    .person-type-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
        margin: 0.2rem;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .person-type-badge.customer { background: var(--primary-gradient); }
    .person-type-badge.supplier { background: var(--success-gradient); }
    .person-type-badge.employee { background: var(--warning-gradient); }
    .person-type-badge.both { background: var(--info-gradient); }
    .person-type-badge.other { background: var(--primary-gradient); }

    /* Responsive */
    @media (max-width: 768px) {
        .form-container {
            padding: 1.5rem;
        }
        
        .form-title {
            font-size: 1.5rem;
        }
        
        .btn {
            padding: 0.6rem 1.5rem;
            font-size: 0.8rem;
        }
        
        .form-section {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-container">
                <h1 class="form-title">
                    <i class="bi bi-person-plus me-3"></i>
                    {% if action == 'edit' %}
                    تعديل تعريف الشخص
                    {% else %}
                    إضافة شخص جديد
                    {% endif %}
                </h1>

                <form method="post" action="{% url 'definitions:person_create' %}" novalidate>
                    {% csrf_token %}
                    
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-person-circle"></i>
                            المعلومات الأساسية
                        </h3>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="code" class="form-label required">
                                    <i class="bi bi-hash"></i>كود الشخص
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="code" 
                                       name="code" 
                                       value="{% if action == 'edit' %}{{ person.code }}{% else %}{{ form_data.code|default:'' }}{% endif %}"
                                       placeholder="مثال: CUST001، SUP001"
                                       required>
                                <div class="form-text">كود فريد لتعريف الشخص</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="name" class="form-label required">
                                    <i class="bi bi-person"></i>الاسم
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       value="{% if action == 'edit' %}{{ person.name }}{% else %}{{ form_data.name|default:'' }}{% endif %}"
                                       placeholder="الاسم الكامل"
                                       required>
                                <div class="form-text">الاسم الكامل للشخص</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="name_en" class="form-label">
                                    <i class="bi bi-translate"></i>الاسم بالإنجليزية
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name_en" 
                                       name="name_en" 
                                       value="{% if action == 'edit' %}{{ person.name_en }}{% else %}{{ form_data.name_en|default:'' }}{% endif %}"
                                       placeholder="Full Name in English">
                                <div class="form-text">الاسم بالإنجليزية (اختياري)</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="person_type" class="form-label required">
                                    <i class="bi bi-tags"></i>نوع الشخص
                                </label>
                                <select class="form-select" 
                                        id="person_type" 
                                        name="person_type" 
                                        required>
                                    <option value="">-- اختر نوع الشخص --</option>
                                    {% for type_code, type_name in person_types %}
                                    <option value="{{ type_code }}" 
                                            {% if action == 'edit' and person.person_type == type_code %}selected
                                            {% elif form_data.person_type == type_code %}selected{% endif %}>
                                        {{ type_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">تصنيف الشخص (عميل، مورد، موظف، إلخ)</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="gender" class="form-label">
                                    <i class="bi bi-gender-ambiguous"></i>الجنس
                                </label>
                                <select class="form-select" 
                                        id="gender" 
                                        name="gender">
                                    <option value="">-- اختر الجنس --</option>
                                    {% for gender_code, gender_name in gender_choices %}
                                    <option value="{{ gender_code }}" 
                                            {% if action == 'edit' and person.gender == gender_code %}selected
                                            {% elif form_data.gender == gender_code %}selected{% endif %}>
                                        {{ gender_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">جنس الشخص (اختياري)</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="birth_date" class="form-label">
                                    <i class="bi bi-calendar"></i>تاريخ الميلاد
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="birth_date" 
                                       name="birth_date" 
                                       value="{% if action == 'edit' and person.birth_date %}{{ person.birth_date|date:'Y-m-d' }}{% else %}{{ form_data.birth_date|default:'' }}{% endif %}">
                                <div class="form-text">تاريخ الميلاد (اختياري)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Identity Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-card-text"></i>
                            معلومات الهوية
                        </h3>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="national_id" class="form-label">
                                    <i class="bi bi-credit-card"></i>الرقم القومي/الهوية
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="national_id"
                                       name="national_id"
                                       value="{% if action == 'edit' %}{{ person.national_id }}{% else %}{{ form_data.national_id|default:'' }}{% endif %}"
                                       placeholder="رقم الهوية الوطنية">
                                <div class="form-text">رقم الهوية الوطنية أو البطاقة الشخصية</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="passport_number" class="form-label">
                                    <i class="bi bi-passport"></i>رقم الجواز
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="passport_number"
                                       name="passport_number"
                                       value="{% if action == 'edit' %}{{ person.passport_number }}{% else %}{{ form_data.passport_number|default:'' }}{% endif %}"
                                       placeholder="رقم جواز السفر">
                                <div class="form-text">رقم جواز السفر (اختياري)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-telephone"></i>
                            معلومات الاتصال
                        </h3>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="phone" class="form-label">
                                    <i class="bi bi-telephone"></i>الهاتف الثابت
                                </label>
                                <input type="tel"
                                       class="form-control"
                                       id="phone"
                                       name="phone"
                                       value="{% if action == 'edit' %}{{ person.phone }}{% else %}{{ form_data.phone|default:'' }}{% endif %}"
                                       placeholder="رقم الهاتف الثابت">
                                <div class="form-text">رقم الهاتف الثابت</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="mobile" class="form-label">
                                    <i class="bi bi-phone"></i>الهاتف المحمول
                                </label>
                                <input type="tel"
                                       class="form-control"
                                       id="mobile"
                                       name="mobile"
                                       value="{% if action == 'edit' %}{{ person.mobile }}{% else %}{{ form_data.mobile|default:'' }}{% endif %}"
                                       placeholder="رقم الهاتف المحمول">
                                <div class="form-text">رقم الهاتف المحمول</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="email" class="form-label">
                                    <i class="bi bi-envelope"></i>البريد الإلكتروني
                                </label>
                                <input type="email"
                                       class="form-control"
                                       id="email"
                                       name="email"
                                       value="{% if action == 'edit' %}{{ person.email }}{% else %}{{ form_data.email|default:'' }}{% endif %}"
                                       placeholder="البريد الإلكتروني">
                                <div class="form-text">عنوان البريد الإلكتروني</div>
                            </div>
                        </div>
                    </div>

                    <!-- Address Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-geo-alt"></i>
                            معلومات العنوان
                        </h3>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="address" class="form-label">
                                    <i class="bi bi-house"></i>العنوان
                                </label>
                                <textarea class="form-control"
                                          id="address"
                                          name="address"
                                          rows="3"
                                          placeholder="العنوان التفصيلي...">{% if action == 'edit' %}{{ person.address }}{% else %}{{ form_data.address|default:'' }}{% endif %}</textarea>
                                <div class="form-text">العنوان التفصيلي</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="city" class="form-label">
                                    <i class="bi bi-building"></i>المدينة
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="city"
                                       name="city"
                                       value="{% if action == 'edit' %}{{ person.city }}{% else %}{{ form_data.city|default:'' }}{% endif %}"
                                       placeholder="اسم المدينة">
                                <div class="form-text">المدينة</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="state" class="form-label">
                                    <i class="bi bi-map"></i>المحافظة/الولاية
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="state"
                                       name="state"
                                       value="{% if action == 'edit' %}{{ person.state }}{% else %}{{ form_data.state|default:'' }}{% endif %}"
                                       placeholder="المحافظة أو الولاية">
                                <div class="form-text">المحافظة أو الولاية</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="country" class="form-label">
                                    <i class="bi bi-globe"></i>البلد
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="country"
                                       name="country"
                                       value="{% if action == 'edit' %}{{ person.country }}{% else %}{{ form_data.country|default:'' }}{% endif %}"
                                       placeholder="اسم البلد">
                                <div class="form-text">البلد</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="postal_code" class="form-label">
                                    <i class="bi bi-mailbox"></i>الرمز البريدي
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="postal_code"
                                       name="postal_code"
                                       value="{% if action == 'edit' %}{{ person.postal_code }}{% else %}{{ form_data.postal_code|default:'' }}{% endif %}"
                                       placeholder="الرمز البريدي">
                                <div class="form-text">الرمز البريدي</div>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-cash-coin"></i>
                            المعلومات المالية
                        </h3>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="credit_limit" class="form-label">
                                    <i class="bi bi-credit-card-2-front"></i>حد الائتمان
                                </label>
                                <input type="number"
                                       class="form-control"
                                       id="credit_limit"
                                       name="credit_limit"
                                       step="0.01"
                                       value="{% if action == 'edit' %}{{ person.credit_limit }}{% else %}{{ form_data.credit_limit|default:'0' }}{% endif %}"
                                       placeholder="0.00">
                                <div class="form-text">الحد الأقصى للائتمان</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="payment_terms" class="form-label">
                                    <i class="bi bi-calendar-check"></i>شروط الدفع (أيام)
                                </label>
                                <input type="number"
                                       class="form-control"
                                       id="payment_terms"
                                       name="payment_terms"
                                       value="{% if action == 'edit' %}{{ person.payment_terms }}{% else %}{{ form_data.payment_terms|default:'0' }}{% endif %}"
                                       placeholder="0">
                                <div class="form-text">عدد أيام السداد المسموحة</div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="currency" class="form-label">
                                    <i class="bi bi-currency-exchange"></i>العملة
                                </label>
                                <select class="form-select"
                                        id="currency"
                                        name="currency">
                                    <option value="">-- اختر العملة --</option>
                                    {% for currency in currencies %}
                                    <option value="{{ currency.id }}"
                                            {% if action == 'edit' and person.currency and person.currency.id == currency.id %}selected
                                            {% elif form_data.currency == currency.id|stringformat:"s" %}selected{% endif %}>
                                        {{ currency.code }} - {{ currency.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">العملة المفضلة للتعامل</div>
                            </div>
                        </div>
                    </div>

                    <!-- Tax Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-receipt"></i>
                            المعلومات الضريبية
                        </h3>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tax_number" class="form-label">
                                    <i class="bi bi-file-earmark-text"></i>الرقم الضريبي
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="tax_number"
                                       name="tax_number"
                                       value="{% if action == 'edit' %}{{ person.tax_number }}{% else %}{{ form_data.tax_number|default:'' }}{% endif %}"
                                       placeholder="الرقم الضريبي">
                                <div class="form-text">الرقم الضريبي المسجل</div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="tax_rate" class="form-label">
                                    <i class="bi bi-percent"></i>معدل الضريبة (%)
                                </label>
                                <input type="number"
                                       class="form-control"
                                       id="tax_rate"
                                       name="tax_rate"
                                       step="0.01"
                                       min="0"
                                       max="100"
                                       value="{% if action == 'edit' %}{{ person.tax_rate }}{% else %}{{ form_data.tax_rate|default:'0' }}{% endif %}"
                                       placeholder="0.00">
                                <div class="form-text">معدل الضريبة المطبق (%)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Notes and Status Section -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="bi bi-sticky"></i>
                            ملاحظات وحالة
                        </h3>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="notes" class="form-label">
                                    <i class="bi bi-journal-text"></i>ملاحظات
                                </label>
                                <textarea class="form-control"
                                          id="notes"
                                          name="notes"
                                          rows="4"
                                          placeholder="ملاحظات إضافية...">{% if action == 'edit' %}{{ person.notes }}{% else %}{{ form_data.notes|default:'' }}{% endif %}</textarea>
                                <div class="form-text">ملاحظات إضافية أو تعليقات</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input"
                                           type="checkbox"
                                           id="is_active"
                                           name="is_active"
                                           {% if action == 'edit' and person.is_active %}checked
                                           {% elif action != 'edit' %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        <i class="bi bi-toggle-on me-1"></i>شخص نشط
                                    </label>
                                    <div class="form-text">تفعيل أو إلغاء تفعيل الشخص</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            <a href="{% url 'definitions:dashboard' %}" class="btn btn-info me-2">
                                <i class="bi bi-house me-2"></i>العودة للتعريفات
                            </a>
                            <a href="{% url 'definitions:person_list' %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>العودة للقائمة
                            </a>
                        </div>
                        <input type="submit" class="btn btn-primary"
                               value="{% if action == 'edit' %}حفظ التعديلات{% else %}إضافة الشخص{% endif %}">
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>




{% endblock %}
