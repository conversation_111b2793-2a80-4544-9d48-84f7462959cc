<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفواتير - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .search-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .invoice-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #43e97b;
        }
        
        .invoice-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .invoice-number {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
        }
        
        .invoice-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-draft { background: #e2e3e5; color: #495057; }
        .status-confirmed { background: #d1ecf1; color: #0c5460; }
        .status-paid { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .invoice-details {
            margin-bottom: 15px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .detail-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .detail-value {
            font-weight: 600;
            color: #333;
        }
        
        .total-amount {
            font-size: 1.4rem;
            font-weight: 700;
            color: #43e97b;
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            margin: 15px 0;
        }
        
        .btn-create {
            background: linear-gradient(45deg, #43e97b, #38f9d7);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(67, 233, 123, 0.3);
            color: white;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .stats-row {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-receipt"></i>
                        إدارة الفواتير
                    </h1>
                    <p class="mb-0">إدارة فواتير البيع والمدفوعات</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="{% url 'sales:invoice_create' %}" class="btn btn-create">
                        <i class="bi bi-plus-circle"></i>
                        إنشاء فاتورة جديدة
                    </a>
                    <a href="{% url 'sales:dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-row">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number text-primary">{{ total_invoices }}</div>
                        <div class="stat-label">إجمالي الفواتير</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number text-success">{{ paid_invoices }}</div>
                        <div class="stat-label">فواتير مدفوعة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number text-warning">{{ pending_invoices }}</div>
                        <div class="stat-label">فواتير معلقة</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number text-info">{{ total_amount|floatformat:0 }}</div>
                        <div class="stat-label">إجمالي المبيعات (ج.م)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم البحث -->
        <div class="search-section">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="رقم الفاتورة أو اسم العميل" value="{{ search }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="draft" {% if status == 'draft' %}selected{% endif %}>مسودة</option>
                        <option value="confirmed" {% if status == 'confirmed' %}selected{% endif %}>مؤكدة</option>
                        <option value="paid" {% if status == 'paid' %}selected{% endif %}>مدفوعة</option>
                        <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>ملغية</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">نوع العميل</label>
                    <select name="customer_type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="retail" {% if customer_type == 'retail' %}selected{% endif %}>تجزئة</option>
                        <option value="wholesale" {% if customer_type == 'wholesale' %}selected{% endif %}>جملة</option>
                        <option value="credit" {% if customer_type == 'credit' %}selected{% endif %}>آجل</option>
                    </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </form>
        </div>

        <!-- قائمة الفواتير -->
        {% if invoices %}
            <div class="row">
                {% for invoice in invoices %}
                    <div class="col-lg-4 col-md-6">
                        <div class="invoice-card">
                            <div class="invoice-header">
                                <div class="invoice-number">فاتورة #{{ invoice.invoice_number }}</div>
                                <div class="invoice-status status-{{ invoice.status }}">
                                    {% if invoice.status == 'draft' %}
                                        مسودة
                                    {% elif invoice.status == 'confirmed' %}
                                        مؤكدة
                                    {% elif invoice.status == 'paid' %}
                                        مدفوعة
                                    {% elif invoice.status == 'cancelled' %}
                                        ملغية
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="invoice-details">
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-person text-primary"></i>
                                        العميل:
                                    </span>
                                    <span class="detail-value">{{ invoice.customer.name }}</span>
                                </div>
                                
                                <div class="detail-row">
                                    <span class="detail-label">
                                        <i class="bi bi-calendar text-info"></i>
                                        التاريخ:
                                    </span>
                                    <span class="detail-value">{{ invoice.invoice_date|date:"Y/m/d" }}</span>
                                </div>
                                
                                {% if invoice.due_date %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-clock text-warning"></i>
                                            تاريخ الاستحقاق:
                                        </span>
                                        <span class="detail-value">{{ invoice.due_date|date:"Y/m/d" }}</span>
                                    </div>
                                {% endif %}
                                
                                {% if invoice.representative %}
                                    <div class="detail-row">
                                        <span class="detail-label">
                                            <i class="bi bi-person-badge text-success"></i>
                                            المندوب:
                                        </span>
                                        <span class="detail-value">{{ invoice.representative.name }}</span>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="total-amount">
                                الإجمالي: {{ invoice.total_amount|floatformat:2 }} ج.م
                            </div>
                            
                            <div class="text-center">
                                <a href="{% url 'sales:invoice_detail' invoice.pk %}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye"></i>
                                    عرض
                                </a>
                                {% if invoice.status == 'draft' %}
                                    <a href="{% url 'sales:invoice_edit' invoice.pk %}" class="btn btn-outline-warning btn-sm">
                                        <i class="bi bi-pencil"></i>
                                        تعديل
                                    </a>
                                {% endif %}
                                <a href="{% url 'sales:invoice_print_pdf' invoice.pk %}" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-printer"></i>
                                    طباعة
                                </a>
                                {% if invoice.status == 'confirmed' and invoice.customer.customer_type == 'credit' %}
                                    <a href="{% url 'sales:payment_create' %}?invoice={{ invoice.pk }}" class="btn btn-outline-success btn-sm">
                                        <i class="bi bi-cash"></i>
                                        تحصيل
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- الترقيم -->
            {% if invoices.has_other_pages %}
                <nav aria-label="ترقيم الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if invoices.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ invoices.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if customer_type %}&customer_type={{ customer_type }}{% endif %}">
                                    السابق
                                </a>
                            </li>
                        {% endif %}

                        {% for num in invoices.paginator.page_range %}
                            {% if invoices.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > invoices.number|add:'-3' and num < invoices.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if customer_type %}&customer_type={{ customer_type }}{% endif %}">
                                        {{ num }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if invoices.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ invoices.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if status %}&status={{ status }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if customer_type %}&customer_type={{ customer_type }}{% endif %}">
                                    التالي
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <i class="bi bi-receipt"></i>
                <h3>لا توجد فواتير</h3>
                <p>لم يتم العثور على فواتير تطابق معايير البحث</p>
                <a href="{% url 'sales:invoice_create' %}" class="btn btn-create">
                    <i class="bi bi-plus-circle"></i>
                    إنشاء أول فاتورة
                </a>
            </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.invoice-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 50);
            });
        });
    </script>
</body>
</html>
