{% extends 'base.html' %}
{% load static %}

{% block title %}حذف الأصل - {{ asset.name }}{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        max-width: 600px;
        margin: 3rem auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .delete-header {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .delete-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.9;
    }
    
    .delete-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }
    
    .delete-body {
        padding: 2rem;
    }
    
    .warning-message {
        background: #fef3c7;
        border: 2px solid #f59e0b;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .warning-icon {
        font-size: 2rem;
        color: #d97706;
        margin-bottom: 1rem;
    }
    
    .warning-text {
        color: #92400e;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .warning-subtext {
        color: #a16207;
        font-size: 0.9rem;
    }
    
    .asset-info {
        background: #f9fafb;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .asset-info h5 {
        color: #374151;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #6b7280;
    }
    
    .info-value {
        color: #374151;
    }
    
    .button-group {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }
    
    .btn-delete {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    }
    
    .btn-cancel {
        background: #6b7280;
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }
    
    .consequences {
        background: #fee2e2;
        border: 2px solid #fca5a5;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .consequences h6 {
        color: #991b1b;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .consequences ul {
        color: #b91c1c;
        margin: 0;
        padding-left: 1.5rem;
    }
    
    .consequences li {
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="delete-container">
        <div class="delete-header">
            <div class="delete-icon">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <h2 class="delete-title">تأكيد حذف الأصل</h2>
        </div>
        
        <div class="delete-body">
            <div class="warning-message">
                <div class="warning-icon">
                    <i class="bi bi-shield-exclamation"></i>
                </div>
                <div class="warning-text">تحذير: هذا الإجراء لا يمكن التراجع عنه!</div>
                <div class="warning-subtext">سيتم حذف الأصل وجميع البيانات المرتبطة به نهائياً</div>
            </div>

            <div class="asset-info">
                <h5>معلومات الأصل المراد حذفه:</h5>
                <div class="info-item">
                    <span class="info-label">اسم الأصل:</span>
                    <span class="info-value">{{ asset.name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">كود الأصل:</span>
                    <span class="info-value">{{ asset.asset_code }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الفئة:</span>
                    <span class="info-value">{{ asset.category.name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">سعر الشراء:</span>
                    <span class="info-value">{{ asset.purchase_price|floatformat:2 }} ج.م</span>
                </div>
                <div class="info-item">
                    <span class="info-label">القيمة الدفترية الحالية:</span>
                    <span class="info-value">{{ asset.current_book_value|floatformat:2 }} ج.م</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">
                        {% if asset.status == 'active' %}نشط
                        {% elif asset.status == 'maintenance' %}تحت الصيانة
                        {% elif asset.status == 'disposed' %}تم التخلص منه
                        {% elif asset.status == 'sold' %}تم البيع
                        {% endif %}
                    </span>
                </div>
            </div>

            <div class="consequences">
                <h6>
                    <i class="bi bi-exclamation-circle me-2"></i>
                    عواقب الحذف:
                </h6>
                <ul>
                    <li>سيتم حذف جميع سجلات الصيانة المرتبطة بهذا الأصل</li>
                    <li>سيتم حذف جميع سجلات التحويلات المرتبطة بهذا الأصل</li>
                    <li>سيتم فقدان جميع البيانات المالية والتاريخية للأصل</li>
                    <li>لن يكون بالإمكان استرداد هذه البيانات بعد الحذف</li>
                </ul>
            </div>

            <form method="post">
                {% csrf_token %}
                <div class="button-group">
                    <button type="submit" class="btn-delete" onclick="return confirmDelete()">
                        <i class="bi bi-trash me-2"></i>
                        نعم، احذف الأصل نهائياً
                    </button>
                    <a href="{% url 'assets:asset_detail' asset.pk %}" class="btn-cancel">
                        <i class="bi bi-arrow-left me-2"></i>
                        إلغاء والعودة
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    return confirm('هل أنت متأكد تماماً من حذف هذا الأصل؟\n\nهذا الإجراء لا يمكن التراجع عنه وسيتم فقدان جميع البيانات المرتبطة بالأصل نهائياً.');
}

document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية
    const deleteBtn = document.querySelector('.btn-delete');
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحذف...';
    });
    
    console.log('✅ تم تحميل صفحة تأكيد حذف الأصل');
});
</script>
{% endblock %}
