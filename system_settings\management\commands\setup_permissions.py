from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission, User
from django.contrib.contenttypes.models import ContentType


class Command(BaseCommand):
    help = 'إعداد نظام الصلاحيات والأدوار'

    def handle(self, *args, **options):
        self.stdout.write('بدء إعداد نظام الصلاحيات...')
        
        # إنشاء المجموعات (الأدوار)
        self.create_groups()
        
        # تعيين الصلاحيات للمجموعات
        self.assign_permissions()
        
        self.stdout.write(
            self.style.SUCCESS('تم إعداد نظام الصلاحيات بنجاح!')
        )

    def create_groups(self):
        """إنشاء المجموعات الأساسية"""
        
        groups = [
            ('مدير عام', 'جميع الصلاحيات'),
            ('مدير المبيعات', 'إدارة المبيعات والعملاء'),
            ('مدير المشتريات', 'إدارة المشتريات والموردين'),
            ('أمين المخزن', 'إدارة المخازن والمنتجات'),
            ('موظف مبيعات', 'صلاحيات محدودة للمبيعات'),
            ('محاسب', 'المحاسبة والتقارير المالية'),
            ('مدير الموارد البشرية', 'إدارة شؤون العاملين'),
            ('مشاهد فقط', 'عرض البيانات فقط'),
        ]
        
        for name, description in groups:
            group, created = Group.objects.get_or_create(name=name)
            if created:
                self.stdout.write(f'تم إنشاء المجموعة: {name}')

    def assign_permissions(self):
        """تعيين الصلاحيات للمجموعات"""
        
        # الحصول على جميع الصلاحيات
        all_permissions = Permission.objects.all()
        
        # مدير عام - جميع الصلاحيات
        admin_group = Group.objects.get(name='مدير عام')
        admin_group.permissions.set(all_permissions)
        self.stdout.write('تم تعيين جميع الصلاحيات للمدير العام')
        
        # مدير المبيعات
        sales_manager = Group.objects.get(name='مدير المبيعات')
        sales_permissions = Permission.objects.filter(
            codename__in=[
                'view_user', 'add_user', 'change_user',
                'view_group', 'add_group', 'change_group',
            ]
        )
        sales_manager.permissions.set(sales_permissions)
        self.stdout.write('تم تعيين صلاحيات مدير المبيعات')
        
        # مدير المشتريات
        purchase_manager = Group.objects.get(name='مدير المشتريات')
        purchase_permissions = Permission.objects.filter(
            codename__in=[
                'view_user', 'add_user', 'change_user',
            ]
        )
        purchase_manager.permissions.set(purchase_permissions)
        self.stdout.write('تم تعيين صلاحيات مدير المشتريات')
        
        # أمين المخزن
        warehouse_keeper = Group.objects.get(name='أمين المخزن')
        warehouse_permissions = Permission.objects.filter(
            codename__in=[
                'view_user', 'change_user',
            ]
        )
        warehouse_keeper.permissions.set(warehouse_permissions)
        self.stdout.write('تم تعيين صلاحيات أمين المخزن')
        
        # موظف مبيعات
        sales_employee = Group.objects.get(name='موظف مبيعات')
        sales_emp_permissions = Permission.objects.filter(
            codename__in=[
                'view_user',
            ]
        )
        sales_employee.permissions.set(sales_emp_permissions)
        self.stdout.write('تم تعيين صلاحيات موظف المبيعات')
        
        # محاسب
        accountant = Group.objects.get(name='محاسب')
        accountant_permissions = Permission.objects.filter(
            codename__in=[
                'view_user', 'view_group',
            ]
        )
        accountant.permissions.set(accountant_permissions)
        self.stdout.write('تم تعيين صلاحيات المحاسب')
        
        # مدير الموارد البشرية
        hr_manager = Group.objects.get(name='مدير الموارد البشرية')
        hr_permissions = Permission.objects.filter(
            codename__in=[
                'view_user', 'add_user', 'change_user', 'delete_user',
                'view_group', 'add_group', 'change_group',
            ]
        )
        hr_manager.permissions.set(hr_permissions)
        self.stdout.write('تم تعيين صلاحيات مدير الموارد البشرية')
        
        # مشاهد فقط
        viewer = Group.objects.get(name='مشاهد فقط')
        viewer_permissions = Permission.objects.filter(
            codename__startswith='view_'
        )
        viewer.permissions.set(viewer_permissions)
        self.stdout.write('تم تعيين صلاحيات المشاهد فقط')
