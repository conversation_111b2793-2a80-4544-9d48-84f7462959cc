#!/usr/bin/env python
"""
سكريبت لإصلاح مشاكل Django وتشغيل الخادم
"""

import os
import sys
import subprocess
import django
from django.core.management import execute_from_command_line

def run_command(command):
    """تشغيل أمر في terminal"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        print(f"Command: {command}")
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"Output: {result.stdout}")
        if result.stderr:
            print(f"Error: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"Error running command {command}: {e}")
        return False

def main():
    """الوظيفة الرئيسية"""
    
    # تأكد من أننا في المجلد الصحيح
    if not os.path.exists('manage.py'):
        print("Error: manage.py not found. Make sure you're in the correct directory.")
        return
    
    # إعداد Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'osaric.settings')
    
    print("🔧 بدء إصلاح مشاكل Django...")
    
    # 1. تحقق من إعدادات Django
    print("\n1. التحقق من إعدادات Django...")
    if not run_command("python manage.py check --deploy"):
        print("⚠️ هناك مشاكل في الإعدادات، سنحاول المتابعة...")
    
    # 2. إنشاء migrations للتطبيقات الجديدة
    print("\n2. إنشاء migrations...")
    apps_to_migrate = [
        'notifications',
        'search', 
        'messaging',
        'settings'
    ]
    
    for app in apps_to_migrate:
        print(f"Creating migrations for {app}...")
        run_command(f"python manage.py makemigrations {app}")
    
    # 3. تطبيق migrations
    print("\n3. تطبيق migrations...")
    if not run_command("python manage.py migrate"):
        print("⚠️ فشل في تطبيق migrations، سنحاول fake-initial...")
        run_command("python manage.py migrate --fake-initial")
    
    # 4. إنشاء superuser إذا لم يكن موجوداً
    print("\n4. التحقق من وجود superuser...")
    try:
        django.setup()
        from django.contrib.auth.models import User
        if not User.objects.filter(is_superuser=True).exists():
            print("إنشاء superuser...")
            run_command('python manage.py shell -c "from django.contrib.auth.models import User; User.objects.create_superuser(\'admin\', \'<EMAIL>\', \'admin123\')"')
    except Exception as e:
        print(f"Error checking superuser: {e}")
    
    # 5. جمع الملفات الثابتة
    print("\n5. جمع الملفات الثابتة...")
    run_command("python manage.py collectstatic --noinput")
    
    # 6. تشغيل الخادم
    print("\n6. تشغيل خادم Django...")
    print("🚀 بدء تشغيل الخادم على http://127.0.0.1:8000/")
    print("اضغط Ctrl+C لإيقاف الخادم")
    
    try:
        run_command("python manage.py runserver 127.0.0.1:8000")
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف الخادم بنجاح")

if __name__ == "__main__":
    main()
