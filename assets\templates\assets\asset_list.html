{% extends 'base.html' %}

{% block title %}إدارة الأصول الثابتة - أوساريك{% endblock %}

{% block content %}
    <div class="page-header d-flex justify-content-between align-items-center">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'assets:dashboard' %}">الأصول الثابتة</a></li>
                    <li class="breadcrumb-item active">قائمة الأصول</li>
                </ol>
            </nav>
            <h1 class="page-title">إدارة الأصول الثابتة</h1>
            <p class="page-subtitle">عرض وإدارة جميع الأصول الثابتة والمعدات</p>
        </div>
        <a href="#" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>إضافة أصل جديد
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               value="{{ search_query }}" 
                               placeholder="البحث في الأصول...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="category" class="form-select">
                        <option value="">جميع التصنيفات</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                        <option value="maintenance" {% if status_filter == 'maintenance' %}selected{% endif %}>تحت الصيانة</option>
                        <option value="disposed" {% if status_filter == 'disposed' %}selected{% endif %}>تم التخلص منه</option>
                        <option value="sold" {% if status_filter == 'sold' %}selected{% endif %}>تم البيع</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search"></i>
                        </button>
                        <a href="{% url 'assets:asset_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Assets Grid -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">قائمة الأصول</h5>
            <span class="badge bg-primary">{{ page_obj.paginator.count }} أصل</span>
        </div>
        <div class="card-body">
            {% if page_obj %}
                <div class="row g-4">
                    {% for asset in page_obj %}
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 asset-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="asset-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center">
                                            <i class="bi bi-building"></i>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#">
                                                    <i class="bi bi-eye me-2"></i>عرض التفاصيل
                                                </a></li>
                                                <li><a class="dropdown-item" href="#">
                                                    <i class="bi bi-pencil me-2"></i>تعديل
                                                </a></li>
                                                <li><a class="dropdown-item" href="#">
                                                    <i class="bi bi-tools me-2"></i>صيانة
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#">
                                                    <i class="bi bi-trash me-2"></i>حذف
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <h5 class="card-title">{{ asset.name }}</h5>
                                    <p class="text-muted small mb-2">الرمز: {{ asset.code }}</p>
                                    <p class="text-muted small mb-2">التصنيف: {{ asset.category.name }}</p>
                                    
                                    <div class="mb-3">
                                        {% if asset.status == 'active' %}
                                            <span class="badge bg-success">نشط</span>
                                        {% elif asset.status == 'maintenance' %}
                                            <span class="badge bg-warning">تحت الصيانة</span>
                                        {% elif asset.status == 'disposed' %}
                                            <span class="badge bg-danger">تم التخلص منه</span>
                                        {% elif asset.status == 'sold' %}
                                            <span class="badge bg-info">تم البيع</span>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-6">
                                            <div class="border-end">
                                                <h6 class="mb-0">{{ asset.purchase_price|floatformat:0 }}</h6>
                                                <small class="text-muted">سعر الشراء</small>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="mb-0">{{ asset.book_value|floatformat:0 }}</h6>
                                            <small class="text-muted">القيمة الدفترية</small>
                                        </div>
                                    </div>
                                    
                                    {% if asset.location %}
                                        <div class="mb-2">
                                            <i class="bi bi-geo-alt me-2 text-muted"></i>
                                            <small>{{ asset.location }}</small>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="d-flex gap-2">
                                        <a href="#" class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="bi bi-eye me-1"></i>عرض
                                        </a>
                                        <a href="#" class="btn btn-outline-success btn-sm">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="#" class="btn btn-outline-warning btn-sm">
                                            <i class="bi bi-tools"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="صفحات الأصول" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابقة</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-building text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">لا توجد أصول</h4>
                    <p class="text-muted">لم يتم العثور على أي أصول مطابقة لمعايير البحث.</p>
                    <a href="#" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>إضافة أول أصل
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .asset-card {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .asset-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .asset-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .border-end {
        border-right: 1px solid #dee2e6 !important;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
</style>
{% endblock %}
