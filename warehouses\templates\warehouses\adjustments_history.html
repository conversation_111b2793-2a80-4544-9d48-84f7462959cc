{% extends 'base.html' %}
{% load static %}

{% block title %}سجل تسويات المخزون{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .history-container {
        max-width: 1400px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .filters-section {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .history-table {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .form-control, .form-select {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .btn-info {
        background: linear-gradient(135deg, #17a2b8, #138496);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-info:hover {
        background: linear-gradient(135deg, #138496, #117a8b);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .adjustment-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .badge-increase {
        background: #d4edda;
        color: #155724;
    }

    .badge-decrease {
        background: #f8d7da;
        color: #721c24;
    }

    .badge-correction {
        background: #fff3cd;
        color: #856404;
    }

    .badge-damage {
        background: #f8d7da;
        color: #721c24;
    }

    .badge-expired {
        background: #f8d7da;
        color: #721c24;
    }

    .badge-lost {
        background: #f8d7da;
        color: #721c24;
    }

    .badge-found {
        background: #d4edda;
        color: #155724;
    }

    .badge-revalue {
        background: #cce5ff;
        color: #004085;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 15px;
        padding: 1.5rem;
        color: white;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.1);
        transform: scale(1.01);
        transition: all 0.2s ease;
    }

    .value-positive {
        color: #28a745;
        font-weight: 600;
    }

    .value-negative {
        color: #dc3545;
        font-weight: 600;
    }

    .value-neutral {
        color: #6c757d;
        font-weight: 600;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -0.5rem;
    }

    .col-md-3, .col-md-4, .col-md-6 {
        padding: 0 0.5rem;
        margin-bottom: 1rem;
    }

    .col-md-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    .col-md-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
        padding: 0 0.5rem;
    }

    @media (max-width: 768px) {
        .col-md-3, .col-md-4, .col-md-6 {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="history-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1><i class="bi bi-clock-history me-2"></i>سجل تسويات المخزون</h1>
        <p class="mb-0">مراجعة جميع عمليات التسوية التي تمت في النظام</p>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-cards">
        <div class="stat-card">
            <div class="stat-value">{{ total_adjustments }}</div>
            <div class="stat-label">إجمالي التسويات</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ total_value_impact|floatformat:0 }}</div>
            <div class="stat-label">إجمالي التأثير المالي (ج.م)</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ adjustments|length }}</div>
            <div class="stat-label">النتائج المعروضة</div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <h5 class="mb-3"><i class="bi bi-funnel me-2"></i>فلاتر البحث</h5>
        <form method="get" class="row">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="ابحث في المنتج، المخزن، السبب..." 
                       value="{{ search_query }}">
            </div>
            
            <div class="col-md-3">
                <label class="form-label">المخزن</label>
                <select name="warehouse" class="form-select">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if selected_warehouse == warehouse.id|stringformat:"s" %}selected{% endif %}>
                        {{ warehouse.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">المنتج</label>
                <select name="product" class="form-select">
                    <option value="">جميع المنتجات</option>
                    {% for product in products %}
                    <option value="{{ product.id }}" {% if selected_product == product.id|stringformat:"s" %}selected{% endif %}>
                        {{ product.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">نوع التسوية</label>
                <select name="adjustment_type" class="form-select">
                    <option value="">جميع الأنواع</option>
                    {% for type_code, type_name in adjustment_types %}
                    <option value="{{ type_code }}" {% if selected_adjustment_type == type_code %}selected{% endif %}>
                        {{ type_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-6">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            
            <div class="col-md-6">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
            
            <div class="col-12 text-center mt-3">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search me-2"></i>بحث
                </button>
                <a href="{% url 'warehouses:adjustments_history' %}" class="btn btn-secondary me-2">
                    <i class="bi bi-arrow-clockwise me-2"></i>إعادة تعيين
                </a>
                <a href="{% url 'warehouses:stock_adjustments' %}" class="btn btn-primary me-2">
                    <i class="bi bi-plus-circle me-2"></i>تسوية جديدة
                </a>
                <a href="{% url 'warehouses:inventory_list' %}" class="btn btn-info me-2">
                    <i class="bi bi-boxes me-2"></i>المخزون
                </a>
                <a href="{% url 'warehouses:dashboard' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-2"></i>لوحة تحكم المخازن
                </a>
            </div>
        </form>
    </div>

    <!-- Adjustments Table -->
    <div class="history-table">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0"><i class="bi bi-table me-2"></i>سجل التسويات</h5>
            <div class="text-muted">
                عرض {{ adjustments|length }} من أصل {{ total_adjustments }} تسوية
            </div>
        </div>

        {% if adjustments %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>التاريخ والوقت</th>
                        <th>المخزن</th>
                        <th>المنتج</th>
                        <th>نوع التسوية</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>التأثير المالي</th>
                        <th>السبب</th>
                        <th>المستخدم</th>
                        <th>الملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for adjustment in adjustments %}
                    <tr>
                        <td>
                            <div>
                                <strong>{{ adjustment.adjustment_date|date:"Y-m-d" }}</strong>
                                <br><small class="text-muted">{{ adjustment.adjustment_date|time:"H:i" }}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ adjustment.inventory_item.warehouse.name }}</strong>
                                <br><small class="text-muted">{{ adjustment.inventory_item.warehouse.code }}</small>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong>{{ adjustment.inventory_item.product.name }}</strong>
                                <br><small class="text-muted">{{ adjustment.inventory_item.product.code }}</small>
                            </div>
                        </td>
                        <td>
                            <span class="adjustment-badge badge-{{ adjustment.adjustment_type }}">
                                {% if adjustment.adjustment_type == 'increase' %}
                                    <i class="bi bi-plus-circle me-1"></i>زيادة
                                {% elif adjustment.adjustment_type == 'decrease' %}
                                    <i class="bi bi-dash-circle me-1"></i>نقص
                                {% elif adjustment.adjustment_type == 'correction' %}
                                    <i class="bi bi-arrow-clockwise me-1"></i>تصحيح
                                {% elif adjustment.adjustment_type == 'damage' %}
                                    <i class="bi bi-exclamation-triangle me-1"></i>تالف
                                {% elif adjustment.adjustment_type == 'expired' %}
                                    <i class="bi bi-calendar-x me-1"></i>منتهي الصلاحية
                                {% elif adjustment.adjustment_type == 'lost' %}
                                    <i class="bi bi-question-circle me-1"></i>مفقود
                                {% elif adjustment.adjustment_type == 'found' %}
                                    <i class="bi bi-check-circle me-1"></i>موجود
                                {% elif adjustment.adjustment_type == 'revalue' %}
                                    <i class="bi bi-currency-dollar me-1"></i>إعادة تقييم
                                {% endif %}
                            </span>
                        </td>
                        <td>
                            {% if adjustment.adjustment_type == 'revalue' %}
                                <span class="text-muted">لا تغيير</span>
                            {% else %}
                                <strong class="{% if adjustment.quantity > 0 %}text-success{% elif adjustment.quantity < 0 %}text-danger{% else %}text-muted{% endif %}">
                                    {% if adjustment.quantity > 0 %}+{% endif %}{{ adjustment.quantity|floatformat:3 }}
                                </strong>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ adjustment.unit_cost|floatformat:2 }}</strong>
                            <small class="text-muted d-block">ج.م</small>
                        </td>
                        <td>
                            <strong class="{% if adjustment.total_value > 0 %}value-positive{% elif adjustment.total_value < 0 %}value-negative{% else %}value-neutral{% endif %}">
                                {% if adjustment.total_value > 0 %}+{% endif %}{{ adjustment.total_value|floatformat:2 }}
                            </strong>
                            <small class="text-muted d-block">ج.م</small>
                        </td>
                        <td>
                            <span class="text-primary">{{ adjustment.reason }}</span>
                        </td>
                        <td>
                            <div>
                                <strong>{{ adjustment.created_by.get_full_name|default:adjustment.created_by.username }}</strong>
                                <br><small class="text-muted">{{ adjustment.created_at|date:"Y-m-d H:i" }}</small>
                            </div>
                        </td>
                        <td>
                            {% if adjustment.notes %}
                                <span class="text-muted">{{ adjustment.notes|truncatechars:50 }}</span>
                            {% else %}
                                <span class="text-muted">لا توجد</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-state">
            <i class="bi bi-inbox"></i>
            <h4>لا توجد تسويات</h4>
            <p>لم يتم العثور على تسويات تطابق معايير البحث المحددة.</p>
            <a href="{% url 'warehouses:stock_adjustments' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>إنشاء تسوية جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.closest('form').submit();
            }
        });
    }

    // تلميحات للمستخدم
    const adjustmentBadges = document.querySelectorAll('.adjustment-badge');
    adjustmentBadges.forEach(badge => {
        badge.addEventListener('mouseenter', function() {
            const type = this.classList.contains('badge-increase') ? 'زيادة في المخزون' :
                        this.classList.contains('badge-decrease') ? 'تقليل من المخزون' :
                        this.classList.contains('badge-set') ? 'تحديد كمية محددة' :
                        'إعادة تقييم التكلفة';
            this.title = type;
        });
    });
});
</script>
{% endblock %}
