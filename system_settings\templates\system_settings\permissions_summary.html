{% extends 'base.html' %}
{% load permissions_tags %}

{% block title %}ملخص نظام الصلاحيات{% endblock %}

{% block extra_css %}
<style>
    .summary-container {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin: 2rem 0;
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 15px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 1rem;
        opacity: 0.9;
    }
    
    .permissions-overview {
        background: #f8fafc;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .permission-badge {
        display: inline-block;
        background: #667eea;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        margin: 0.25rem;
    }
    
    .role-badge {
        display: inline-block;
        background: #10b981;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        margin: 0.25rem;
    }
    
    .quick-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }
    
    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
    }
    
    .btn-primary {
        background: #667eea;
        color: white;
    }
    
    .btn-primary:hover {
        background: #5a67d8;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }
    
    .btn-secondary {
        background: #6b7280;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #4b5563;
        transform: translateY(-2px);
    }
    
    .user-info-card {
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #667eea;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        font-weight: 600;
        margin-left: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1><i class="bi bi-shield-check"></i> ملخص نظام الصلاحيات</h1>
        <p>نظرة شاملة على الصلاحيات والأدوار في النظام</p>
    </div>
    
    <!-- الإحصائيات -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ total_permissions }}</div>
            <div class="stat-label">إجمالي الصلاحيات</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ total_roles }}</div>
            <div class="stat-label">إجمالي الأدوار</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ total_users_with_roles }}</div>
            <div class="stat-label">المستخدمون مع أدوار</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ active_permissions }}</div>
            <div class="stat-label">الصلاحيات النشطة</div>
        </div>
    </div>
    
    <!-- محتوى الملخص -->
    <div class="summary-container">
        <!-- معلومات المستخدم الحالي -->
        <div class="user-info-card">
            <div style="display: flex; align-items: center;">
                <div class="user-avatar">
                    {{ user.first_name.0|default:user.username.0|upper }}
                </div>
                <div>
                    <h3>{{ user.get_full_name|default:user.username }}</h3>
                    <p style="color: #6b7280; margin: 0;">{{ user.email }}</p>
                    {% if user.is_superuser %}
                        <span class="permission-badge" style="background: #ef4444;">مدير عام - جميع الصلاحيات</span>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- أدوار المستخدم الحالي -->
        {% user_roles user as current_user_roles %}
        {% if current_user_roles %}
        <div class="permissions-overview">
            <div class="section-title">
                <i class="bi bi-people"></i>
                أدوارك الحالية
            </div>
            {% for role in current_user_roles %}
                <span class="role-badge">{{ role.name }}</span>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- صلاحيات المستخدم الحالي -->
        {% user_permissions user as current_user_permissions %}
        {% if current_user_permissions %}
        <div class="permissions-overview">
            <div class="section-title">
                <i class="bi bi-shield-check"></i>
                صلاحياتك الحالية
            </div>
            {% for permission in current_user_permissions %}
                <span class="permission-badge">{{ permission.name }}</span>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- إجراءات سريعة -->
        <div class="quick-actions">
            {% if user|has_permission:"settings_view" %}
                <a href="{% url 'system_settings:permissions_management' %}" class="btn btn-primary">
                    <i class="bi bi-shield-check"></i> إدارة الصلاحيات
                </a>
                <a href="{% url 'system_settings:roles_management' %}" class="btn btn-primary">
                    <i class="bi bi-people"></i> إدارة الأدوار
                </a>
            {% endif %}
            {% if user|has_permission:"users_view" %}
                <a href="{% url 'system_settings:users_management' %}" class="btn btn-secondary">
                    <i class="bi bi-person-gear"></i> إدارة المستخدمين
                </a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
