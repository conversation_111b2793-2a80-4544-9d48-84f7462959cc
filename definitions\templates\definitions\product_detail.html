{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الصنف - {{ object.name }}{% endblock %}

{% block extra_css %}
<style>
    /* Advanced Product Detail Styles */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Hero Section */
    .detail-hero {
        background: var(--primary-gradient);
        padding: 4rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
        border-radius: 0 0 2rem 2rem;
    }

    .detail-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    }

    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
    }

    .hero-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
        text-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .hero-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 1.5rem;
    }

    .hero-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .hero-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .hero-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    /* Product Image */
    .product-image-container {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        text-align: center;
        margin-bottom: 2rem;
    }

    .product-image {
        max-width: 100%;
        max-height: 300px;
        border-radius: 16px;
        border: 2px solid #e5e7eb;
        transition: all 0.3s ease;
    }

    .product-image:hover {
        transform: scale(1.05);
        border-color: #667eea;
    }

    .product-placeholder {
        width: 200px;
        height: 200px;
        border-radius: 16px;
        background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #9ca3af;
        font-size: 4rem;
        margin: 0 auto;
    }

    /* Info Cards */
    .info-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e5e7eb;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .card-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #374151;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .card-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }

    /* Detail Items */
    .detail-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f1f5f9;
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: #6b7280;
        min-width: 150px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-value {
        color: #374151;
        font-weight: 500;
        flex: 1;
    }

    /* Status Badges */
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-flex;
        align-items: center;
        gap: 0.3rem;
    }

    .status-active {
        background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
        color: white;
    }

    .status-inactive {
        background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
        color: white;
    }

    .status-low-stock {
        background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
        color: white;
    }

    .status-out-of-stock {
        background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
        color: white;
    }

    /* Price Display */
    .price-display {
        font-size: 2rem;
        font-weight: 800;
        color: #059669;
        margin-bottom: 0.5rem;
    }

    .price-currency {
        font-size: 1rem;
        color: #6b7280;
        margin-left: 0.5rem;
    }

    .price-comparison {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .price-item {
        background: #f8fafc;
        padding: 1rem;
        border-radius: 12px;
        text-align: center;
        flex: 1;
        border: 1px solid #e5e7eb;
    }

    .price-label {
        font-size: 0.8rem;
        color: #6b7280;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .price-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #374151;
    }

    /* Stock Visualization */
    .stock-visual {
        background: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
    }

    .stock-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .stock-title {
        font-weight: 600;
        color: #374151;
    }

    .stock-percentage {
        font-size: 1.2rem;
        font-weight: 700;
    }

    .stock-bar-container {
        width: 100%;
        height: 20px;
        background: #e5e7eb;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
        margin-bottom: 1rem;
    }

    .stock-bar {
        height: 100%;
        border-radius: 10px;
        transition: width 1s ease;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .stock-bar.high {
        background: linear-gradient(90deg, #10b981 0%, #34d399 100%);
    }

    .stock-bar.medium {
        background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 100%);
    }

    .stock-bar.low {
        background: linear-gradient(90deg, #ef4444 0%, #f87171 100%);
    }

    .stock-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        text-align: center;
    }

    .stock-detail {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .stock-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #374151;
    }

    .stock-label {
        font-size: 0.8rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }

    /* Statistics Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 800;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2rem;
        }
        
        .info-card {
            padding: 1.5rem;
        }
        
        .detail-label {
            min-width: 120px;
            font-size: 0.9rem;
        }
        
        .hero-actions {
            justify-content: center;
        }
        
        .price-comparison {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="detail-hero">
    <div class="container">
        <div class="hero-content">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">
                        <i class="bi bi-box-seam me-3"></i>
                        {{ object.name }}
                    </h1>
                    <p class="hero-subtitle">
                        كود الصنف: {{ object.code }}
                        {% if object.barcode %} | الباركود: {{ object.barcode }}{% endif %}
                        {% if object.category %} | الفئة: {{ object.category.name }}{% endif %}
                    </p>
                    <div class="hero-actions">
                        <a href="{% url 'definitions:product_edit' object.pk %}" class="hero-btn">
                            <i class="bi bi-pencil"></i>تعديل
                        </a>
                        <a href="{% url 'definitions:product_duplicate' object.pk %}" class="hero-btn">
                            <i class="bi bi-files"></i>نسخ
                        </a>
                        <a href="{% url 'definitions:product_list' %}" class="hero-btn">
                            <i class="bi bi-arrow-left"></i>العودة للقائمة
                        </a>
                        <a href="{% url 'definitions:product_delete' object.pk %}" class="hero-btn"
                           onclick="return confirm('هل أنت متأكد من حذف هذا الصنف؟')">
                            <i class="bi bi-trash"></i>حذف
                        </a>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    {% if object.is_active %}
                        {% if object.current_stock <= 0 %}
                        <div class="status-badge status-out-of-stock">
                            <i class="bi bi-x-circle"></i>نفد المخزون
                        </div>
                        {% elif object.current_stock <= object.minimum_stock %}
                        <div class="status-badge status-low-stock">
                            <i class="bi bi-exclamation-triangle"></i>مخزون منخفض
                        </div>
                        {% else %}
                        <div class="status-badge status-active">
                            <i class="bi bi-check-circle"></i>متوفر
                        </div>
                        {% endif %}
                    {% else %}
                    <div class="status-badge status-inactive">
                        <i class="bi bi-x-circle"></i>غير نشط
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Product Image -->
        <div class="col-lg-4">
            <div class="product-image-container">
                {% if object.image %}
                <img src="{{ object.image.url }}" alt="{{ object.name }}" class="product-image">
                {% else %}
                <div class="product-placeholder">
                    <i class="bi bi-image"></i>
                </div>
                {% endif %}
                <h5 class="mt-3 text-center">{{ object.name }}</h5>
                <p class="text-muted text-center">{{ object.code }}</p>
            </div>

            <!-- Price Information -->
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon" style="background: var(--success-gradient);">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    معلومات التسعير
                </div>

                <div class="price-display">
                    {{ object.selling_price|floatformat:2 }}
                    <span class="price-currency">ريال</span>
                </div>
                <p class="text-muted">سعر البيع</p>

                <div class="price-comparison">
                    {% if object.cost_price %}
                    <div class="price-item">
                        <div class="price-label">سعر التكلفة</div>
                        <div class="price-value">{{ object.cost_price|floatformat:2 }} ريال</div>
                    </div>
                    {% endif %}

                    {% if object.wholesale_price %}
                    <div class="price-item">
                        <div class="price-label">سعر الجملة</div>
                        <div class="price-value">{{ object.wholesale_price|floatformat:2 }} ريال</div>
                    </div>
                    {% endif %}
                </div>

                {% if object.cost_price %}
                <div class="mt-3">
                    <div class="price-item">
                        <div class="price-label">هامش الربح</div>
                        <div class="price-value text-success">
                            {{ object.profit_margin|floatformat:1 }}%
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Product Details -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon" style="background: var(--primary-gradient);">
                        <i class="bi bi-info-circle"></i>
                    </div>
                    المعلومات الأساسية
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-hash"></i>
                        كود الصنف
                    </div>
                    <div class="detail-value">
                        <strong class="text-primary">{{ object.code }}</strong>
                    </div>
                </div>

                {% if object.barcode %}
                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-upc-scan"></i>
                        الباركود
                    </div>
                    <div class="detail-value">{{ object.barcode }}</div>
                </div>
                {% endif %}

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-tag"></i>
                        اسم الصنف
                    </div>
                    <div class="detail-value">{{ object.name }}</div>
                </div>

                {% if object.category %}
                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-tags"></i>
                        الفئة
                    </div>
                    <div class="detail-value">
                        <span class="badge bg-primary">{{ object.category.name }}</span>
                    </div>
                </div>
                {% endif %}

                {% if object.description %}
                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-file-text"></i>
                        الوصف
                    </div>
                    <div class="detail-value">{{ object.description }}</div>
                </div>
                {% endif %}

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-calendar"></i>
                        تاريخ الإنشاء
                    </div>
                    <div class="detail-value">{{ object.created_at|date:"Y/m/d H:i" }}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-person"></i>
                        أنشئ بواسطة
                    </div>
                    <div class="detail-value">{{ object.created_by.get_full_name|default:object.created_by.username }}</div>
                </div>
            </div>

            <!-- Stock Information -->
            {% if object.track_stock %}
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon" style="background: var(--warning-gradient);">
                        <i class="bi bi-boxes"></i>
                    </div>
                    معلومات المخزون
                </div>

                <div class="stock-visual">
                    <div class="stock-header">
                        <div class="stock-title">حالة المخزون</div>
                        <div class="stock-percentage
                            {% if object.stock_percentage > 50 %}text-success
                            {% elif object.stock_percentage > 20 %}text-warning
                            {% else %}text-danger{% endif %}">
                            {{ object.stock_percentage|floatformat:1 }}%
                        </div>
                    </div>

                    {% if object.maximum_stock %}
                    <div class="stock-bar-container">
                        {% with percentage=object.stock_percentage %}
                        <div class="stock-bar {% if percentage > 50 %}high{% elif percentage > 20 %}medium{% else %}low{% endif %}"
                             style="width: {{ percentage }}%">
                            {{ percentage|floatformat:1 }}%
                        </div>
                        {% endwith %}
                    </div>
                    {% endif %}

                    <div class="stock-details">
                        <div class="stock-detail">
                            <div class="stock-number text-primary">{{ object.current_stock|floatformat:0 }}</div>
                            <div class="stock-label">المخزون الحالي</div>
                        </div>

                        {% if object.minimum_stock %}
                        <div class="stock-detail">
                            <div class="stock-number text-warning">{{ object.minimum_stock|floatformat:0 }}</div>
                            <div class="stock-label">الحد الأدنى</div>
                        </div>
                        {% endif %}

                        {% if object.maximum_stock %}
                        <div class="stock-detail">
                            <div class="stock-number text-info">{{ object.maximum_stock|floatformat:0 }}</div>
                            <div class="stock-label">الحد الأقصى</div>
                        </div>
                        {% endif %}

                        {% if object.unit %}
                        <div class="stock-detail">
                            <div class="stock-number text-secondary">{{ object.unit.name }}</div>
                            <div class="stock-label">وحدة القياس</div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-graph-up"></i>
                        تتبع المخزون
                    </div>
                    <div class="detail-value">
                        {% if object.track_stock %}
                        <span class="badge bg-success">مفعل</span>
                        {% else %}
                        <span class="badge bg-secondary">معطل</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Settings -->
            <div class="info-card">
                <div class="card-title">
                    <div class="card-icon" style="background: var(--info-gradient);">
                        <i class="bi bi-gear"></i>
                    </div>
                    الإعدادات والحالة
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-toggle-on"></i>
                        حالة الصنف
                    </div>
                    <div class="detail-value">
                        {% if object.is_active %}
                        <span class="badge bg-success">نشط</span>
                        {% else %}
                        <span class="badge bg-danger">غير نشط</span>
                        {% endif %}
                    </div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-eye"></i>
                        حالة المخزون
                    </div>
                    <div class="detail-value">
                        {% if object.current_stock <= 0 %}
                        <span class="badge bg-danger">نفد المخزون</span>
                        {% elif object.current_stock <= object.minimum_stock %}
                        <span class="badge bg-warning">مخزون منخفض</span>
                        {% else %}
                        <span class="badge bg-success">متوفر</span>
                        {% endif %}
                    </div>
                </div>

                {% if object.updated_at %}
                <div class="detail-item">
                    <div class="detail-label">
                        <i class="bi bi-clock"></i>
                        آخر تحديث
                    </div>
                    <div class="detail-value">{{ object.updated_at|date:"Y/m/d H:i" }}</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
