{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل الأصل - {{ asset.name }}{% endblock %}

{% block extra_css %}
<style>
    .asset-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
    }
    
    .asset-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .asset-code {
        font-size: 1.1rem;
        opacity: 0.9;
    }
    
    .info-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #667eea;
    }
    
    .info-card h4 {
        color: #667eea;
        margin-bottom: 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f3f4f6;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #374151;
    }
    
    .info-value {
        color: #6b7280;
        text-align: right;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }
    
    .status-active { background: #d1fae5; color: #065f46; }
    .status-maintenance { background: #fef3c7; color: #92400e; }
    .status-disposed { background: #fee2e2; color: #991b1b; }
    .status-sold { background: #dbeafe; color: #1e40af; }
    
    .condition-excellent { color: #059669; }
    .condition-good { color: #0891b2; }
    .condition-fair { color: #d97706; }
    .condition-poor { color: #dc2626; }
    
    .value-card {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 2px solid #0ea5e9;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .value-amount {
        font-size: 1.8rem;
        font-weight: 700;
        color: #0369a1;
        margin-bottom: 0.5rem;
    }
    
    .value-label {
        color: #0891b2;
        font-weight: 600;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }
    
    .btn-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border: none;
    }
    
    .btn-warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
        border: none;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        border: none;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }
    
    .history-item {
        background: #f9fafb;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        border-left: 4px solid #6b7280;
    }
    
    .history-date {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
    }
    
    .history-description {
        color: #6b7280;
        margin-bottom: 0.5rem;
    }
    
    .history-cost {
        color: #059669;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="asset-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="asset-title">{{ asset.name }}</div>
                <div class="asset-code">كود الأصل: {{ asset.asset_code }}</div>
            </div>
            <div class="col-md-4 text-end">
                <span class="status-badge status-{{ asset.status }}">
                    {% if asset.status == 'active' %}نشط
                    {% elif asset.status == 'maintenance' %}تحت الصيانة
                    {% elif asset.status == 'disposed' %}تم التخلص منه
                    {% elif asset.status == 'sold' %}تم البيع
                    {% endif %}
                </span>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- المعلومات الأساسية -->
        <div class="col-lg-8">
            <div class="info-card">
                <h4>
                    <i class="bi bi-info-circle"></i>
                    المعلومات الأساسية
                </h4>
                <div class="info-row">
                    <span class="info-label">الفئة</span>
                    <span class="info-value">{{ asset.category.name }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">الوصف</span>
                    <span class="info-value">{{ asset.description|default:"غير محدد" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">الحالة</span>
                    <span class="info-value condition-{{ asset.condition }}">
                        {% if asset.condition == 'excellent' %}ممتاز
                        {% elif asset.condition == 'good' %}جيد
                        {% elif asset.condition == 'fair' %}مقبول
                        {% elif asset.condition == 'poor' %}ضعيف
                        {% endif %}
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">الموقع</span>
                    <span class="info-value">{{ asset.location|default:"غير محدد" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">المسؤول</span>
                    <span class="info-value">{{ asset.responsible_person.get_full_name|default:asset.responsible_person.username|default:"غير محدد" }}</span>
                </div>
            </div>

            <div class="info-card">
                <h4>
                    <i class="bi bi-cart"></i>
                    معلومات الشراء
                </h4>
                <div class="info-row">
                    <span class="info-label">تاريخ الشراء</span>
                    <span class="info-value">{{ asset.purchase_date }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">سعر الشراء</span>
                    <span class="info-value">{{ asset.purchase_price|floatformat:2 }} ج.م</span>
                </div>
                <div class="info-row">
                    <span class="info-label">المورد</span>
                    <span class="info-value">{{ asset.supplier|default:"غير محدد" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">العمر الافتراضي</span>
                    <span class="info-value">{{ asset.useful_life_years }} سنة</span>
                </div>
                <div class="info-row">
                    <span class="info-label">القيمة المتبقية</span>
                    <span class="info-value">{{ asset.salvage_value|floatformat:2 }} ج.م</span>
                </div>
            </div>

            <div class="info-card">
                <h4>
                    <i class="bi bi-gear"></i>
                    معلومات إضافية
                </h4>
                <div class="info-row">
                    <span class="info-label">الرقم التسلسلي</span>
                    <span class="info-value">{{ asset.serial_number|default:"غير محدد" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">الموديل</span>
                    <span class="info-value">{{ asset.model|default:"غير محدد" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">الشركة المصنعة</span>
                    <span class="info-value">{{ asset.manufacturer|default:"غير محدد" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ الإنشاء</span>
                    <span class="info-value">{{ asset.created_at|date:"Y-m-d H:i" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">آخر تحديث</span>
                    <span class="info-value">{{ asset.updated_at|date:"Y-m-d H:i" }}</span>
                </div>
            </div>
        </div>

        <!-- القيم المالية -->
        <div class="col-lg-4">
            <div class="value-card">
                <div class="value-amount">{{ asset.current_book_value|floatformat:2 }}</div>
                <div class="value-label">القيمة الدفترية الحالية (ج.م)</div>
            </div>
            
            <div class="value-card">
                <div class="value-amount">{{ asset.accumulated_depreciation|floatformat:2 }}</div>
                <div class="value-label">الاستهلاك المتراكم (ج.م)</div>
            </div>
            
            <div class="value-card">
                <div class="value-amount">{{ asset.annual_depreciation|floatformat:2 }}</div>
                <div class="value-label">الاستهلاك السنوي (ج.م)</div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="action-buttons">
                <a href="{% url 'assets:asset_edit' asset.pk %}" class="btn-action btn-primary">
                    <i class="bi bi-pencil"></i>
                    تعديل
                </a>
                
                {% if asset.status == 'active' %}
                <a href="#" class="btn-action btn-warning">
                    <i class="bi bi-tools"></i>
                    صيانة
                </a>
                
                <a href="#" class="btn-action btn-success">
                    <i class="bi bi-arrow-left-right"></i>
                    تحويل
                </a>
                {% endif %}
                
                <a href="#" class="btn-action btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الأصل؟')">
                    <i class="bi bi-trash"></i>
                    حذف
                </a>
            </div>
        </div>
    </div>

    <!-- سجل الصيانة -->
    {% if maintenances %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="info-card">
                <h4>
                    <i class="bi bi-tools"></i>
                    سجل الصيانة
                </h4>
                {% for maintenance in maintenances %}
                <div class="history-item">
                    <div class="history-date">{{ maintenance.maintenance_date }} - {{ maintenance.get_maintenance_type_display }}</div>
                    <div class="history-description">{{ maintenance.description }}</div>
                    <div class="history-cost">التكلفة: {{ maintenance.cost|floatformat:2 }} ج.م</div>
                    {% if maintenance.vendor %}
                        <div class="text-muted">مقدم الخدمة: {{ maintenance.vendor }}</div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- سجل التحويلات -->
    {% if transfers %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="info-card">
                <h4>
                    <i class="bi bi-arrow-left-right"></i>
                    سجل التحويلات
                </h4>
                {% for transfer in transfers %}
                <div class="history-item">
                    <div class="history-date">{{ transfer.transfer_date }}</div>
                    <div class="history-description">
                        من: {{ transfer.from_location }} إلى: {{ transfer.to_location }}
                    </div>
                    <div class="text-muted">السبب: {{ transfer.reason }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ تم تحميل صفحة تفاصيل الأصل');
});
</script>
{% endblock %}
