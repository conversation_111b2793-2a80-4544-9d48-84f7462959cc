{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل نوع المصروف - {{ expense_type.name }}{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        min-height: 100vh;
    }

    .detail-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 25px;
        padding: 3rem;
        margin-bottom: 2rem;
        box-shadow: 0 15px 50px rgba(31, 38, 135, 0.37);
        border: 1px solid rgba(255, 255, 255, 0.18);
        color: #333;
    }

    .detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 3rem;
        padding-bottom: 2rem;
        border-bottom: 3px solid rgba(255, 107, 107, 0.3);
    }

    .detail-title {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .expense-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 2rem;
        margin-right: 1rem;
        box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .btn {
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 700;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 0.95rem;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        border: 2px solid #6c757d;
        color: #6c757d;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-3px);
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
    }

    .btn-danger:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(220, 53, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    .detail-section {
        background: rgba(255, 107, 107, 0.1);
        backdrop-filter: blur(5px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 107, 107, 0.2);
    }

    .section-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #ff6b6b;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
    }

    .detail-label {
        font-weight: 700;
        color: #ff6b6b;
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .detail-value {
        color: #333;
        font-size: 1.1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 15px;
        border: 1px solid rgba(255, 107, 107, 0.2);
        min-height: 50px;
        display: flex;
        align-items: center;
    }

    .detail-value.empty {
        color: #999;
        font-style: italic;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .status-badge.active {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .status-badge.inactive {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
    }

    .status-badge.fixed {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        color: white;
    }

    .status-badge.variable {
        background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
        color: white;
    }

    .status-badge.operational {
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
        color: white;
    }

    .status-badge.administrative {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
    }

    .alert {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.18);
        margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
        .detail-container {
            padding: 2rem;
        }
        
        .detail-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1.5rem;
        }
        
        .detail-title {
            font-size: 2rem;
        }
        
        .action-buttons {
            width: 100%;
            justify-content: stretch;
        }
        
        .btn {
            flex: 1;
            justify-content: center;
        }
        
        .detail-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="detail-container">
                <div class="detail-header">
                    <div class="d-flex align-items-center">
                        <div class="expense-icon">
                            <i class="bi bi-receipt"></i>
                        </div>
                        <div>
                            <h1 class="detail-title">
                                <i class="bi bi-receipt"></i>
                                {{ expense_type.name }}
                            </h1>
                            <p class="text-muted mb-0" style="font-size: 1.1rem;">
                                <i class="bi bi-hash me-1"></i>
                                كود: {{ expense_type.code }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="{% url 'definitions:dashboard' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-house me-2"></i>التعريفات
                        </a>
                        <a href="{% url 'definitions:expense_type_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>قائمة أنواع المصروفات
                        </a>
                        <a href="{% url 'definitions:expense_type_edit' expense_type.id %}" class="btn btn-primary">
                            <i class="bi bi-pencil me-2"></i>تعديل
                        </a>
                        <button type="button" class="btn btn-danger" onclick="deleteExpenseType({{ expense_type.id }}, '{{ expense_type.name|escapejs }}')">
                            <i class="bi bi-trash me-2"></i>حذف
                        </button>
                    </div>
                </div>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- Basic Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        المعلومات الأساسية
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-hash"></i>كود نوع المصروف
                            </div>
                            <div class="detail-value">{{ expense_type.code }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-receipt"></i>الاسم
                            </div>
                            <div class="detail-value">{{ expense_type.name }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-translate"></i>الاسم بالإنجليزية
                            </div>
                            <div class="detail-value {% if not expense_type.name_en %}empty{% endif %}">
                                {{ expense_type.name_en|default:"غير محدد" }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-toggle-on"></i>الحالة
                            </div>
                            <div class="detail-value">
                                <span class="status-badge {% if expense_type.is_active %}active{% else %}inactive{% endif %}">
                                    {% if expense_type.is_active %}
                                        <i class="bi bi-check-circle me-1"></i>نشط
                                    {% else %}
                                        <i class="bi bi-x-circle me-1"></i>غير نشط
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Classification Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-tags"></i>
                        التصنيف
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-graph-up"></i>نوع المصروف
                            </div>
                            <div class="detail-value">
                                <span class="status-badge {{ expense_type.expense_type }}">
                                    {% if expense_type.expense_type == 'fixed' %}
                                        <i class="bi bi-lock me-1"></i>ثابت
                                    {% else %}
                                        <i class="bi bi-graph-up me-1"></i>متغير
                                    {% endif %}
                                </span>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-building"></i>تصنيف المصروف
                            </div>
                            <div class="detail-value">
                                <span class="status-badge {{ expense_type.category }}">
                                    {% if expense_type.category == 'operational' %}
                                        <i class="bi bi-gear me-1"></i>تشغيلي
                                    {% else %}
                                        <i class="bi bi-building me-1"></i>إداري
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                {% if expense_type.description or expense_type.notes %}
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-file-text"></i>
                        معلومات إضافية
                    </h3>
                    
                    <div class="detail-grid">
                        {% if expense_type.description %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-file-text"></i>الوصف
                            </div>
                            <div class="detail-value">{{ expense_type.description|linebreaks }}</div>
                        </div>
                        {% endif %}

                        {% if expense_type.notes %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-journal-text"></i>ملاحظات
                            </div>
                            <div class="detail-value">{{ expense_type.notes|linebreaks }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- System Information Section -->
                <div class="detail-section">
                    <h3 class="section-title">
                        <i class="bi bi-gear"></i>
                        معلومات النظام
                    </h3>
                    
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-person-plus"></i>أنشأ بواسطة
                            </div>
                            <div class="detail-value">
                                {{ expense_type.created_by.get_full_name|default:expense_type.created_by.username }}
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-plus"></i>تاريخ الإنشاء
                            </div>
                            <div class="detail-value">
                                {{ expense_type.created_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>

                        {% if expense_type.updated_at %}
                        <div class="detail-item">
                            <div class="detail-label">
                                <i class="bi bi-calendar-check"></i>آخر تحديث
                            </div>
                            <div class="detail-value">
                                {{ expense_type.updated_at|date:"d/m/Y H:i" }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteExpenseType(typeId, typeName) {
    if (confirm('هل أنت متأكد من حذف نوع المصروف "' + typeName + '"؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        // إنشاء نموذج مخفي وإرساله
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/definitions/expense-types/' + typeId + '/quick-delete/';
        
        // إضافة CSRF token
        var csrfToken = '{{ csrf_token }}';
        var csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        // إضافة النموذج للصفحة وإرساله
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
