# Generated by Django 5.2.4 on 2025-07-13 12:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('definitions', '0003_auto_20250713_0614'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='productlocation',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='productlocation',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='productlocation',
            name='product',
        ),
        migrations.RemoveField(
            model_name='productlocation',
            name='warehouse',
        ),
        migrations.AlterModelOptions(
            name='persondefinition',
            options={'ordering': ['name'], 'verbose_name': 'تعريف شخص', 'verbose_name_plural': 'تعريفات الأشخاص'},
        ),
        migrations.RemoveField(
            model_name='persondefinition',
            name='commercial_register',
        ),
        migrations.RemoveField(
            model_name='persondefinition',
            name='discount_percentage',
        ),
        migrations.AddField(
            model_name='persondefinition',
            name='birth_date',
            field=models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد'),
        ),
        migrations.AddField(
            model_name='persondefinition',
            name='currency',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='definitions.currencydefinition', verbose_name='العملة'),
        ),
        migrations.AddField(
            model_name='persondefinition',
            name='gender',
            field=models.CharField(blank=True, choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, verbose_name='الجنس'),
        ),
        migrations.AddField(
            model_name='persondefinition',
            name='state',
            field=models.CharField(blank=True, max_length=100, verbose_name='المحافظة'),
        ),
        migrations.AddField(
            model_name='persondefinition',
            name='tax_rate',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='معدل الضريبة %'),
        ),
        migrations.AlterField(
            model_name='persondefinition',
            name='country',
            field=models.CharField(blank=True, max_length=100, verbose_name='الدولة'),
        ),
        migrations.AlterField(
            model_name='persondefinition',
            name='credit_limit',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='حد الائتمان'),
        ),
        migrations.AlterField(
            model_name='persondefinition',
            name='national_id',
            field=models.CharField(blank=True, max_length=20, verbose_name='الرقم القومي'),
        ),
        migrations.AlterField(
            model_name='persondefinition',
            name='person_type',
            field=models.CharField(choices=[('customer', 'عميل'), ('supplier', 'مورد'), ('employee', 'موظف'), ('both', 'عميل ومورد'), ('other', 'أخرى')], max_length=20, verbose_name='نوع الشخص'),
        ),
        migrations.DeleteModel(
            name='ProductCode',
        ),
        migrations.DeleteModel(
            name='ProductLocation',
        ),
    ]
