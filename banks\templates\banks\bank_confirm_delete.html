{% extends 'base.html' %}

{% block title %}حذف البنك {{ bank.name }} - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{% url 'banks:dashboard' %}">البنوك</a></li>
                <li class="breadcrumb-item"><a href="{% url 'banks:bank_list' %}">قائمة البنوك</a></li>
                <li class="breadcrumb-item active">حذف البنك</li>
            </ol>
        </nav>
        <h1 class="page-title text-danger">حذف البنك</h1>
        <p class="page-subtitle">تأكيد حذف البنك من النظام</p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>تحذير: حذف البنك
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="bank-icon-lg bg-danger text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                            <i class="bi bi-bank"></i>
                        </div>
                        <h4 class="mb-2">{{ bank.name }}</h4>
                        <p class="text-muted">{{ bank.code }}</p>
                    </div>

                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>تنبيه مهم:</strong> هذا الإجراء لا يمكن التراجع عنه!
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">معلومات البنك المراد حذفه:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="bi bi-building me-2 text-muted"></i>
                                <strong>اسم البنك:</strong> {{ bank.name }}
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-code me-2 text-muted"></i>
                                <strong>رمز البنك:</strong> {{ bank.code }}
                            </li>
                            {% if bank.swift_code %}
                            <li class="mb-2">
                                <i class="bi bi-globe me-2 text-muted"></i>
                                <strong>رمز SWIFT:</strong> {{ bank.swift_code }}
                            </li>
                            {% endif %}
                            {% if bank.phone %}
                            <li class="mb-2">
                                <i class="bi bi-telephone me-2 text-muted"></i>
                                <strong>رقم الهاتف:</strong> {{ bank.phone }}
                            </li>
                            {% endif %}
                            {% if bank.email %}
                            <li class="mb-2">
                                <i class="bi bi-envelope me-2 text-muted"></i>
                                <strong>البريد الإلكتروني:</strong> {{ bank.email }}
                            </li>
                            {% endif %}
                            <li class="mb-2">
                                <i class="bi bi-calendar me-2 text-muted"></i>
                                <strong>تاريخ الإضافة:</strong> {{ bank.created_at|date:"Y/m/d H:i" }}
                            </li>
                        </ul>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> سيتم حذف جميع البيانات المرتبطة بهذا البنك بما في ذلك:
                        <ul class="mt-2 mb-0">
                            <li>جميع الحسابات البنكية المرتبطة</li>
                            <li>المعاملات المالية والتحويلات</li>
                            <li>أي تقارير مرتبطة بالبنك</li>
                        </ul>
                    </div>

                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'banks:bank_list' %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-right me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger" id="deleteBtn">
                                <i class="bi bi-trash me-2"></i>تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Alternative Actions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightbulb me-2"></i>بدائل أخرى
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">بدلاً من حذف البنك، يمكنك:</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'banks:bank_edit' bank.pk %}" class="btn btn-outline-primary">
                            <i class="bi bi-pencil me-2"></i>تعديل بيانات البنك
                        </a>
                        {% if bank.is_active %}
                            <button class="btn btn-outline-warning" onclick="deactivateBank()">
                                <i class="bi bi-pause-circle me-2"></i>إلغاء تفعيل البنك بدلاً من الحذف
                            </button>
                        {% endif %}
                        <a href="{% url 'banks:bank_list' %}" class="btn btn-outline-secondary">
                            <i class="bi bi-list me-2"></i>العودة إلى قائمة البنوك
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .bank-icon-lg {
        width: 80px;
        height: 80px;
        font-size: 2rem;
        font-weight: 700;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .card-header {
        border-bottom: 1px solid #dee2e6;
    }
    
    .list-unstyled li {
        padding: 0.25rem 0;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const deleteBtn = document.getElementById('deleteBtn');
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show confirmation dialog
            if (confirm('هل أنت متأكد تماماً من حذف هذا البنك؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                deleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحذف...';
                deleteBtn.disabled = true;
                
                // Submit the form
                form.submit();
            }
        });
    });
    
    function deactivateBank() {
        if (confirm('هل تريد إلغاء تفعيل البنك بدلاً من حذفه؟')) {
            // Redirect to edit page with deactivation
            window.location.href = '{% url "banks:bank_edit" bank.pk %}?deactivate=1';
        }
    }
</script>
{% endblock %}
