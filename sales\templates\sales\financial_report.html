<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير المالي - نظام أوساريك</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .filter-section {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .financial-summary {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 30px;
            color: #333;
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 15px;
        }
        
        .financial-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .financial-item:hover {
            background-color: #f8f9fa;
            padding-left: 10px;
            padding-right: 10px;
            border-radius: 10px;
        }
        
        .financial-item:last-child {
            border-bottom: none;
            font-size: 1.3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 25px 20px;
            margin-top: 20px;
        }
        
        .financial-label {
            font-weight: 600;
            color: #495057;
            display: flex;
            align-items: center;
        }
        
        .financial-label i {
            margin-left: 10px;
            color: #667eea;
            font-size: 1.2rem;
        }
        
        .financial-value {
            font-weight: 700;
            font-size: 1.2rem;
        }
        
        .value-positive { color: #28a745; }
        .value-negative { color: #dc3545; }
        .value-neutral { color: #333; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #667eea;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 20px;
            color: white;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 1rem;
            font-weight: 500;
        }
        
        .icon-revenue { background: linear-gradient(45deg, #28a745, #20c997); }
        .icon-expenses { background: linear-gradient(45deg, #dc3545, #c82333); }
        .icon-profit { background: linear-gradient(45deg, #ffc107, #e0a800); }
        .icon-margin { background: linear-gradient(45deg, #17a2b8, #138496); }
        
        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .btn-export {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .breakdown-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .breakdown-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .breakdown-item:hover {
            background: #e9ecef;
            transform: translateX(-5px);
        }
        
        .breakdown-label {
            font-weight: 600;
            color: #333;
        }
        
        .breakdown-value {
            font-weight: 700;
            font-size: 1.1rem;
        }
        
        .progress-section {
            margin-top: 20px;
        }
        
        .progress-item {
            margin-bottom: 15px;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }
        
        .progress {
            height: 12px;
            border-radius: 10px;
            background: #e9ecef;
        }
        
        .progress-bar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 10px;
        }
        
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .kpi-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }
        
        .kpi-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .kpi-label {
            font-size: 1rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- رأس الصفحة -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-calculator"></i>
                        التقرير المالي
                    </h1>
                    <p class="mb-0">تحليل شامل للوضع المالي والأرباح والخسائر</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-export me-2" onclick="exportReport()">
                        <i class="bi bi-download"></i>
                        تصدير التقرير
                    </button>
                    <a href="{% url 'sales:reports_dashboard' %}" class="btn btn-outline-light">
                        <i class="bi bi-arrow-left"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
        </div>

        <!-- قسم الفلترة -->
        <div class="filter-section">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">نوع التقرير</label>
                    <select name="report_type" class="form-select">
                        <option value="profit_loss" {% if report_type == 'profit_loss' %}selected{% endif %}>الأرباح والخسائر</option>
                        <option value="cash_flow" {% if report_type == 'cash_flow' %}selected{% endif %}>التدفق النقدي</option>
                        <option value="balance_sheet" {% if report_type == 'balance_sheet' %}selected{% endif %}>الميزانية العمومية</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                        تطبيق الفلتر
                    </button>
                </div>
            </form>
        </div>

        <!-- مؤشرات الأداء الرئيسية -->
        <div class="kpi-grid">
            <div class="kpi-card">
                <div class="kpi-value">{{ profit_margin|floatformat:1 }}%</div>
                <div class="kpi-label">هامش الربح</div>
            </div>
            
            <div class="kpi-card">
                <div class="kpi-value">{{ roi|floatformat:1 }}%</div>
                <div class="kpi-label">العائد على الاستثمار</div>
            </div>
            
            <div class="kpi-card">
                <div class="kpi-value">{{ growth_rate|floatformat:1 }}%</div>
                <div class="kpi-label">معدل النمو</div>
            </div>
            
            <div class="kpi-card">
                <div class="kpi-value">{{ liquidity_ratio|floatformat:2 }}</div>
                <div class="kpi-label">نسبة السيولة</div>
            </div>
        </div>

        <!-- الإحصائيات المالية -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon icon-revenue">
                    <i class="bi bi-arrow-up-circle"></i>
                </div>
                <div class="stat-number">{{ total_revenue|floatformat:0 }}</div>
                <div class="stat-label">إجمالي الإيرادات (ج.م)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-expenses">
                    <i class="bi bi-arrow-down-circle"></i>
                </div>
                <div class="stat-number">{{ total_expenses|floatformat:0 }}</div>
                <div class="stat-label">إجمالي المصروفات (ج.م)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-profit">
                    <i class="bi bi-trophy"></i>
                </div>
                <div class="stat-number">{{ net_profit|floatformat:0 }}</div>
                <div class="stat-label">صافي الربح (ج.م)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon icon-margin">
                    <i class="bi bi-percent"></i>
                </div>
                <div class="stat-number">{{ profit_margin|floatformat:1 }}%</div>
                <div class="stat-label">هامش الربح</div>
            </div>
        </div>

        <!-- قائمة الأرباح والخسائر -->
        <div class="financial-summary">
            <h3 class="section-title">قائمة الأرباح والخسائر</h3>
            
            <div class="financial-item">
                <div class="financial-label">
                    <i class="bi bi-graph-up"></i>
                    إجمالي المبيعات:
                </div>
                <div class="financial-value value-positive">{{ gross_sales|floatformat:2 }} ج.م</div>
            </div>
            
            <div class="financial-item">
                <div class="financial-label">
                    <i class="bi bi-dash-circle"></i>
                    خصومات المبيعات:
                </div>
                <div class="financial-value value-negative">{{ sales_discounts|floatformat:2 }} ج.م</div>
            </div>
            
            <div class="financial-item">
                <div class="financial-label">
                    <i class="bi bi-arrow-return-left"></i>
                    مرتجعات المبيعات:
                </div>
                <div class="financial-value value-negative">{{ sales_returns|floatformat:2 }} ج.م</div>
            </div>
            
            <div class="financial-item">
                <div class="financial-label">
                    <i class="bi bi-calculator"></i>
                    صافي المبيعات:
                </div>
                <div class="financial-value value-positive">{{ net_sales|floatformat:2 }} ج.م</div>
            </div>
            
            <div class="financial-item">
                <div class="financial-label">
                    <i class="bi bi-box"></i>
                    تكلفة البضاعة المباعة:
                </div>
                <div class="financial-value value-negative">{{ cost_of_goods_sold|floatformat:2 }} ج.م</div>
            </div>
            
            <div class="financial-item">
                <div class="financial-label">
                    <i class="bi bi-graph-down"></i>
                    مجمل الربح:
                </div>
                <div class="financial-value value-positive">{{ gross_profit|floatformat:2 }} ج.م</div>
            </div>
            
            <div class="financial-item">
                <div class="financial-label">
                    <i class="bi bi-building"></i>
                    المصروفات التشغيلية:
                </div>
                <div class="financial-value value-negative">{{ operating_expenses|floatformat:2 }} ج.م</div>
            </div>
            
            <div class="financial-item">
                <div class="financial-label">
                    <i class="bi bi-trophy-fill"></i>
                    صافي الربح:
                </div>
                <div class="financial-value">{{ net_profit|floatformat:2 }} ج.م</div>
            </div>
        </div>

        <!-- تفصيل المصروفات -->
        <div class="breakdown-section">
            <h3 class="section-title">تفصيل المصروفات</h3>
            
            {% for expense in expense_breakdown %}
                <div class="breakdown-item">
                    <div class="breakdown-label">{{ expense.category }}</div>
                    <div class="breakdown-value value-negative">{{ expense.amount|floatformat:2 }} ج.م</div>
                </div>
            {% empty %}
                <div class="text-center text-muted py-4">
                    <i class="bi bi-receipt" style="font-size: 3rem; opacity: 0.5;"></i>
                    <p class="mt-2">لا توجد بيانات مصروفات</p>
                </div>
            {% endfor %}
        </div>

        <!-- نسب الأداء -->
        <div class="breakdown-section">
            <h3 class="section-title">نسب الأداء المالي</h3>
            
            <div class="progress-section">
                <div class="progress-item">
                    <div class="progress-label">
                        <span>هامش مجمل الربح</span>
                        <span>{{ gross_profit_margin|floatformat:1 }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" style="width: {{ gross_profit_margin }}%"></div>
                    </div>
                </div>
                
                <div class="progress-item">
                    <div class="progress-label">
                        <span>هامش صافي الربح</span>
                        <span>{{ net_profit_margin|floatformat:1 }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" style="width: {{ net_profit_margin }}%"></div>
                    </div>
                </div>
                
                <div class="progress-item">
                    <div class="progress-label">
                        <span>نسبة المصروفات التشغيلية</span>
                        <span>{{ operating_expense_ratio|floatformat:1 }}%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" style="width: {{ operating_expense_ratio }}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسوم بيانية -->
        <div class="row">
            <div class="col-md-6">
                <div class="chart-container">
                    <h3 class="section-title">الأرباح الشهرية</h3>
                    <canvas id="profitChart" width="400" height="300"></canvas>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="chart-container">
                    <h3 class="section-title">توزيع المصروفات</h3>
                    <canvas id="expenseChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات بصرية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card, .financial-summary, .breakdown-section');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });

            // رسم بياني للأرباح الشهرية
            const profitCtx = document.getElementById('profitChart').getContext('2d');
            const profitChart = new Chart(profitCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                    datasets: [{
                        label: 'صافي الربح (ج.م)',
                        data: {{ monthly_profits|safe }},
                        borderColor: 'rgb(102, 126, 234)',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString() + ' ج.م';
                                }
                            }
                        }
                    }
                }
            });

            // رسم بياني لتوزيع المصروفات
            const expenseCtx = document.getElementById('expenseChart').getContext('2d');
            const expenseChart = new Chart(expenseCtx, {
                type: 'pie',
                data: {
                    labels: {{ expense_categories|safe }},
                    datasets: [{
                        data: {{ expense_amounts|safe }},
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#ff9a9e',
                            '#fecfef',
                            '#a8edea',
                            '#fed6e3'
                        ],
                        borderWidth: 3,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        });

        function exportReport() {
            // تصدير التقرير إلى PDF أو Excel
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'pdf');
            window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
        }
    </script>
</body>
</html>
